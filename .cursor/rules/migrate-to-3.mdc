---
description: 
globs: **/*.ts
alwaysApply: false
---
很多代码是从 cocos creator 2.x 移植过来，所以碰到问题的时候使用以下规则进行改写

- canvas.instance: 通过 director.getScene().getChildByName("Canvas") 获取
- cc.winSize: cc.view.getVisibleSize()
- canvas.designResolution: cc.view.getDesignResolutionSize
- node的 anchor point: 这个需要转换到 UITransform
- zindex: 3 中的 zindex 废弃，使用 node.setSiblingIndex 来替代

## Action系统

- node.stopAllAction: 使用 cc.Tween.stopAllByTarget(this.node);

### Action Sequence 改写

使用 mcp 查阅文档

## Sprite Frame

2 中 cc.SpriteFrame的构造函数为 
```
new cc.SpriteFrame(
    texture,
    rect,
    rotated,
    offset,
    originalSize
)
```

到 3 中需要改写为
```
const sf = new cc.SpriteFrame(name);
sf.texture = this.texture;
sf.rect = new cc.Rect(x, y, w, h);
sf.rotated = rotated;
sf.offset = offset;
sf.originalSize = originalSize;
```


