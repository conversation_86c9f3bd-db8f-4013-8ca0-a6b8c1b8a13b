<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>login_move_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{2,1}</string>
                <key>spriteSize</key>
                <string>{108,106}</string>
                <key>spriteSourceSize</key>
                <string>{114,114}</string>
                <key>textureRect</key>
                <string>{{217,1},{108,106}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>login_move_2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{2,0}</string>
                <key>spriteSize</key>
                <string>{106,104}</string>
                <key>spriteSourceSize</key>
                <string>{114,114}</string>
                <key>textureRect</key>
                <string>{{645,1},{106,104}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>login_move_3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{1,-3}</string>
                <key>spriteSize</key>
                <string>{106,108}</string>
                <key>spriteSourceSize</key>
                <string>{114,114}</string>
                <key>textureRect</key>
                <string>{{1,1},{106,108}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>login_move_4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,-3}</string>
                <key>spriteSize</key>
                <string>{104,106}</string>
                <key>spriteSourceSize</key>
                <string>{114,114}</string>
                <key>textureRect</key>
                <string>{{433,1},{104,106}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>login_move_5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-3,-2}</string>
                <key>spriteSize</key>
                <string>{108,106}</string>
                <key>spriteSourceSize</key>
                <string>{114,114}</string>
                <key>textureRect</key>
                <string>{{325,1},{108,106}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>login_move_6.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-2,-1}</string>
                <key>spriteSize</key>
                <string>{106,104}</string>
                <key>spriteSourceSize</key>
                <string>{114,114}</string>
                <key>textureRect</key>
                <string>{{751,1},{106,104}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>login_move_7.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-2,2}</string>
                <key>spriteSize</key>
                <string>{106,108}</string>
                <key>spriteSourceSize</key>
                <string>{114,114}</string>
                <key>textureRect</key>
                <string>{{109,1},{106,108}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>login_move_8.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{-1,1}</string>
                <key>spriteSize</key>
                <string>{104,106}</string>
                <key>spriteSourceSize</key>
                <string>{114,114}</string>
                <key>textureRect</key>
                <string>{{539,1},{104,106}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>main.png</string>
            <key>size</key>
            <string>{856,110}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:1f92054dc9e71c605da9fd72a9fe2dd8:0c42fdec3c43cf5f87dd3b4f50573ae5:693584bbba79ecadf35d6b2cf3a849c9$</string>
            <key>textureFileName</key>
            <string>main.png</string>
        </dict>
    </dict>
</plist>
