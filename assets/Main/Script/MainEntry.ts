import { _decorator, Component, Node } from 'cc';
import * as cc from 'cc';
import { AppConfig } from '../../Scripts/Config/GameConfig';

const { ccclass, property } = _decorator;

export enum TimingType0 {
    NONE = 0,
    REQ_RES_URL_BEGIN = 1,//根据版本号请求资源路径
    REQ_RES_URL_SUCESS = 2,//根据版本号请求资源路径成功
    REQ_RES_URL_FAIL = 3,//根据版本号请求资源路径失败
    COUNT = REQ_RES_URL_FAIL + 1
}

enum eChannelId {
    Mock = 2,
}


@ccclass('MainEntry')
export class MainEntry extends Component {
    /**加载动画 */
    @property(cc.Animation)
    LoadAnim: cc.Animation = null;

    /**加载进度 */

    @property(cc.Label)
    LoadProgress: cc.Label = null;

    /** 是否调试模式 */
    @property(cc.CCBoolean)
    debug: boolean = false;

    /** 是否内网测试环境 */
    @property(cc.CCBoolean)
    test: boolean = false;

    /** 渠道 120地址为1，  微信为2*/
    @property({ type: cc.Enum(eChannelId), serializable: true, displayName: "渠道" })
    channelId: eChannelId = eChannelId.Mock;

    /** 远程资源地址 */
    @property(cc.CCString)
    remoteUrl: string = "";

    /** Api调用地址 */
    @property(cc.CCString)
    apiUrl: string = "http://100.124.217.61:7010";

    /**游戏版本号*/
    @property(cc.CCInteger)
    gameVersion: number = 0;

    /**socket前置*/
    @property(cc.CCString)
    socketPre: string = ""

    @property(cc.CCString)
    sdkName: String = "sdkBase";

    onLoad() {
        this.initGameParams();

        this.LoadAnim.play();
        this.LoadProgress.string = "0";
        let loadGameScene = () => {
            //加载子包.
            //console.log("zhangzhen__MainEntry__onLoad_loadGemeScene", Date.now());
            cc.assetManager.loadBundle("Game", (error: Error, bundle: cc.AssetManager.Bundle) => {
                if (error) {
                    console.error("加载loading包异常!", error);
                } else {
                    //console.log("zhangzhen__MainEntry__onLoad_LoadingScene", Date.now());
                    bundle.loadScene("LoadingScene", (finish: number, total: number, item: cc.AssetManager.RequestItem) => {
                        this.LoadProgress.string = Math.round(100 * finish / total) + "";
                    }, (error: Error, sceneAsset: cc.SceneAsset) => {
                        //console.log("zhangzhen__MainEntry__onLoad_LoadingScene_finish", Date.now());
                        cc.director.runSceneImmediate(sceneAsset, () => { }, () => { });
                    });
                }
            })
        }

        if (this.isAndroidRunTime()) {
            let loadUpdateScene = () => {
                //加载子包.
                console.log("start loa", Date.now());
                cc.assetManager.loadBundle("Game", (error: Error, bundle: cc.AssetManager.Bundle) => {
                    if (error) {
                        console.error("加载updatescene包异常!", error);
                    } else {
                        console.log("zhangzhen__MainEntry__onLoad_loadUpdateScene", Date.now());
                        bundle.loadScene("HotUpdateScene", (finish: number, total: number, item: cc.AssetManager.RequestItem) => {
                            this.LoadProgress.string = Math.round(100 * finish / total) + "";
                        }, (error: Error, sceneAsset: cc.SceneAsset) => {
                            console.log("zhangzhen__MainEntry__onLoad_loadUpdateScene_finish", Date.now());
                            cc.director.runSceneImmediate(sceneAsset, () => { }, () => { });
                        });
                    }
                })
            }
            console.log("zhangzhen__MainEntry__onLoad_loadUpdateScene start:", Date.now());
            loadUpdateScene();
        }
        else if (!this.isWxContext()) {
            loadGameScene();
        }
        else {

            this.requestRemoteVersionInfo((responseText: string) => {
                //解析远程资源版本信息
                var response = responseText;
                let json = JSON.parse(response);
                this.remoteUrl = json.data.remoteUrl;
                window["gameParams"]["remoteUrl"] = this.remoteUrl;
                //确定了资源版本号才能从res加载资源.
                if (cc.sys.platform == cc.sys.Platform.ANDROID || cc.sys.platform == cc.sys.Platform.IOS) {
                    window["gameParams"]["resourceVersion"] = json.data.resourceVersion;
                    window["gameParams"]["resourceMd5"] = json.data.resourceVersion;
                }
                else {
                    window["gameParams"]["resourceVersion"] = json.data.pcResourceVersion;
                    window["gameParams"]["resourceMd5"] = json.data.pcResourceVersion;
                }

                loadGameScene();
            });
        }
    }

    /**请求服务器列表 */
    private requestRemoteVersionInfo(callback: any): void {
        let xhr = new XMLHttpRequest();
        this.requestDot(this.channelId, TimingType0.REQ_RES_URL_BEGIN);
        xhr.onreadystatechange = () => {
            if (xhr.readyState === 4 && xhr.status >= 200 && xhr.status < 300) {
                this.requestDot(this.channelId, TimingType0.REQ_RES_URL_SUCESS);
                callback(xhr.responseText)
                xhr.onreadystatechange = null;
                xhr = null;
            }
            else {
                // 请求失败，每N秒后重新发送请求
                if (xhr.readyState === 4)
                    this.requestDot(this.channelId, TimingType0.REQ_RES_URL_FAIL);
            }
        };
        let url = this.RemoteVersionInfoUrl(this.channelId, this.gameVersion);
        xhr.open("GET", url, true);
        if (cc.sys.isNative) {
            xhr.setRequestHeader("Accept-Encoding", "gzip,deflate");
        }
        xhr.timeout = 5000; // 5 seconds for timeout
        xhr.send();
    }

    /**获取远程的版本信息 */
    RemoteVersionInfoUrl(channel: number = 1, gameVersion: number = 0): string {
        return this.apiUrl + "/api/versionInfo?channelId=" + channel + "&versionId=" + gameVersion; //请求大区路径（channelId和openId日后改成配置）
    }
    protected onDestroy(): void {
        // console.error("MainEntry onDestroy");
    }

    /**是否微信小程序环境 */
    private isWxContext(): boolean {
        let isWeChat: boolean = false;
        //头条全局变量有wx跟tt这两个属性。。
        if (window.hasOwnProperty("wx") == true && window.hasOwnProperty("tt") == false) {
            isWeChat = true;
        }
        return isWeChat;
    }

    /** 是否是安卓原生环境 */
    public isAndroidRunTime(): boolean {
        if (cc.sys.isNative && (cc.sys.os == cc.sys.OS.ANDROID || cc.sys.os == cc.sys.OS.WINDOWS)) {
            return true;
        }
        return false;
    }
    /**打点 */
    public requestDot(channelId: number, timing: TimingType0) {
        if (timing <= TimingType0.NONE || timing >= TimingType0.COUNT) {
            return;
        }
        let timer;
        let xhr = new XMLHttpRequest();
        let logURL = this.apiUrl + "/api/loginDot?";
        let dataStr: string = logURL;
        let paramsUrl = ""
        paramsUrl += "channelId=" + channelId;
        paramsUrl += "&openId=";
        paramsUrl += "&timing=" + Number(timing);
        dataStr += encodeURI(paramsUrl);
        xhr.onreadystatechange = () => {
            if (xhr.readyState === 4 && xhr.status >= 200 && xhr.status < 300) {
                clearTimeout(timer);
                xhr.onreadystatechange = null;
                xhr = null;
            } else {
                // 请求失败，每N秒后重新发送请求
                if (xhr.readyState === 4)
                    if (!timer) {
                        timer = setTimeout(() => {
                            clearTimeout(timer);
                            this.requestDot(channelId, timing);
                        }, 5000);
                    }
            }
        }
        xhr.timeout = 5000;
        xhr.open("GET", dataStr);
        xhr.send();
    }

    protected initGameParams() {
        //游戏参数.

        let gameParams = {};
        gameParams["channelId"] = this.channelId;
        gameParams["gameVersion"] = this.gameVersion;
        const url = decodeURI(window.location.search);
        const arrSearch = url.split('?').pop().split('#').shift().split('&');
        arrSearch.forEach((item) => {
            const [k, v] = item.split('=');
            gameParams[k] = v;
        });

        let isDebug = gameParams["debug"] && gameParams["debug"] === "1";
        let isTest = gameParams["test"] && gameParams["test"] === "1";

        if (!isDebug && this.debug) gameParams["debug"] = "1";
        if (!isTest && this.test) gameParams["test"] = "1";

        gameParams["remoteUrl"] = this.remoteUrl;
        gameParams["apiUrl"] = this.apiUrl;

        gameParams["socketPre"] = this.socketPre;
        AppConfig.mergeParams(gameParams);

        // window["gameParams"] = gameParams;

        // window["sdkName"] = this.sdkName;
        //关闭合批处理. 目前在小游戏和原生平台上默认会禁用动态合图，但如果你的项目内存空间仍有富余的话建议开启。
        //发现在切换Scene. 切换副本时, 管理器中的图集没有被删掉。导致自动清除时报错.
        cc.DynamicAtlasManager.instance.enabled = false;

        //加大加载并发数量.
        cc.assetManager.downloader.maxConcurrency = 30;
        cc.assetManager.downloader.maxRequestsPerFrame = 30;

        isDebug = gameParams["debug"] && gameParams["debug"] === "1";
        if (!isDebug) {
            cc.profiler.hideStats();
        }
    }

}
