{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "81a0c8a4-ccc9-49e9-a175-3a988bc8f9e1", "files": [".json", ".png"], "subMetas": {"flatlands_001": {"ver": "1.0.6", "uuid": "66dd2a5a-fa9e-4481-b50e-03a829ec0aa9", "importer": "sprite-frame", "rawTextureUuid": "81a0c8a4-ccc9-49e9-a175-3a988bc8f9e1", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 8, "offsetY": -16.5, "trimX": 16, "trimY": 36, "width": 863, "height": 961, "rawWidth": 879, "rawHeight": 1000, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "81a0c8a4-ccc9-49e9-a175-3a988bc8f9e1@6c48a", "displayName": "flatlands_001", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "81a0c8a4-ccc9-49e9-a175-3a988bc8f9e1", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "81a0c8a4-ccc9-49e9-a175-3a988bc8f9e1@f9941", "displayName": "flatlands_001", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 8, "offsetY": -16.5, "trimX": 16, "trimY": 36, "width": 863, "height": 961, "rawWidth": 879, "rawHeight": 1000, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-431.5, -480.5, 0, 431.5, -480.5, 0, -431.5, 480.5, 0, 431.5, 480.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [16, 964, 879, 964, 16, 3, 879, 3], "nuv": [0.01820250284414107, 0.003, 1, 0.003, 0.01820250284414107, 0.964, 1, 0.964], "minPos": [-431.5, -480.5, 0], "maxPos": [431.5, 480.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "81a0c8a4-ccc9-49e9-a175-3a988bc8f9e1@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "81a0c8a4-ccc9-49e9-a175-3a988bc8f9e1@6c48a"}}