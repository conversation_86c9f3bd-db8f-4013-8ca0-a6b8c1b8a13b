{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "378be328-d7d5-4e80-88b1-13ad340a2cbb", "files": [".json", ".png"], "subMetas": {"boss_002": {"ver": "1.0.6", "uuid": "259901c0-4200-4539-b2b2-4474293d8eb4", "importer": "sprite-frame", "rawTextureUuid": "378be328-d7d5-4e80-88b1-13ad340a2cbb", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 400, "height": 233, "rawWidth": 400, "rawHeight": 233, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "378be328-d7d5-4e80-88b1-13ad340a2cbb@6c48a", "displayName": "boss_002", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "378be328-d7d5-4e80-88b1-13ad340a2cbb", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "378be328-d7d5-4e80-88b1-13ad340a2cbb@f9941", "displayName": "boss_002", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 400, "height": 233, "rawWidth": 400, "rawHeight": 233, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-200, -116.5, 0, 200, -116.5, 0, -200, 116.5, 0, 200, 116.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 233, 400, 233, 0, 0, 400, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-200, -116.5, 0], "maxPos": [200, 116.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "378be328-d7d5-4e80-88b1-13ad340a2cbb@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "378be328-d7d5-4e80-88b1-13ad340a2cbb@6c48a"}}