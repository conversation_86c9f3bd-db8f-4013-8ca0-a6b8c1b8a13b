{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "67ed2207-8587-44c3-bc47-7e18562603fe", "files": [".json", ".png"], "subMetas": {"dian": {"ver": "1.0.6", "uuid": "746539d6-6b09-48a6-8fd3-989e78242be5", "importer": "sprite-frame", "rawTextureUuid": "67ed2207-8587-44c3-bc47-7e18562603fe", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 15, "height": 20, "rawWidth": 15, "rawHeight": 20, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "67ed2207-8587-44c3-bc47-7e18562603fe@6c48a", "displayName": "dian", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "67ed2207-8587-44c3-bc47-7e18562603fe", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "67ed2207-8587-44c3-bc47-7e18562603fe@f9941", "displayName": "dian", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 15, "height": 20, "rawWidth": 15, "rawHeight": 20, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-7.5, -10, 0, 7.5, -10, 0, -7.5, 10, 0, 7.5, 10, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 20, 15, 20, 0, 0, 15, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-7.5, -10, 0], "maxPos": [7.5, 10, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "67ed2207-8587-44c3-bc47-7e18562603fe@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "67ed2207-8587-44c3-bc47-7e18562603fe@6c48a"}}