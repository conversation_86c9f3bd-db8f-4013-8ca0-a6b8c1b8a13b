{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "f49089a5-1840-47e6-841c-82e8aa7c7717", "files": [".json", ".png"], "subMetas": {"segment_001": {"ver": "1.0.6", "uuid": "5ce7e83c-13c9-4729-ad8b-d4ba517995f7", "importer": "sprite-frame", "rawTextureUuid": "f49089a5-1840-47e6-841c-82e8aa7c7717", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 1000, "height": 960, "rawWidth": 1000, "rawHeight": 960, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "f49089a5-1840-47e6-841c-82e8aa7c7717@6c48a", "displayName": "segment_001", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "f49089a5-1840-47e6-841c-82e8aa7c7717", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "f49089a5-1840-47e6-841c-82e8aa7c7717@f9941", "displayName": "segment_001", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 1000, "height": 960, "rawWidth": 1000, "rawHeight": 960, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-500, -480, 0, 500, -480, 0, -500, 480, 0, 500, 480, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 960, 1000, 960, 0, 0, 1000, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-500, -480, 0], "maxPos": [500, 480, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "f49089a5-1840-47e6-841c-82e8aa7c7717@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "f49089a5-1840-47e6-841c-82e8aa7c7717@6c48a"}}