{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "b640a88e-ad7f-4d5f-a2f2-087736fd351f", "files": [".json", ".png"], "subMetas": {"Monster_006_attack_01": {"ver": "1.0.6", "uuid": "796a6ab3-b715-4a03-8c95-bcc043c13076", "importer": "sprite-frame", "rawTextureUuid": "b640a88e-ad7f-4d5f-a2f2-087736fd351f", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 83, "height": 59, "rawWidth": 83, "rawHeight": 59, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "b640a88e-ad7f-4d5f-a2f2-087736fd351f@6c48a", "displayName": "Monster_006_attack_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "b640a88e-ad7f-4d5f-a2f2-087736fd351f", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "b640a88e-ad7f-4d5f-a2f2-087736fd351f@f9941", "displayName": "Monster_006_attack_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 83, "height": 59, "rawWidth": 83, "rawHeight": 59, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-41.5, -29.5, 0, 41.5, -29.5, 0, -41.5, 29.5, 0, 41.5, 29.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 59, 83, 59, 0, 0, 83, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-41.5, -29.5, 0], "maxPos": [41.5, 29.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "b640a88e-ad7f-4d5f-a2f2-087736fd351f@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "b640a88e-ad7f-4d5f-a2f2-087736fd351f@6c48a"}}