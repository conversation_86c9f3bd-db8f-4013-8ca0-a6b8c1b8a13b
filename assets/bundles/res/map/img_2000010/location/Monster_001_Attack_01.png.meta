{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "04769a62-0b45-4897-ae5a-808629cadf75", "files": [".json", ".png"], "subMetas": {"Monster_001_Attack_01": {"ver": "1.0.6", "uuid": "67d64bf8-f7f4-4aa8-b8e1-09cfcadf7a2c", "importer": "sprite-frame", "rawTextureUuid": "04769a62-0b45-4897-ae5a-808629cadf75", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 49, "height": 52, "rawWidth": 49, "rawHeight": 52, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "04769a62-0b45-4897-ae5a-808629cadf75@6c48a", "displayName": "Monster_001_Attack_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "04769a62-0b45-4897-ae5a-808629cadf75", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "04769a62-0b45-4897-ae5a-808629cadf75@f9941", "displayName": "Monster_001_Attack_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 49, "height": 52, "rawWidth": 49, "rawHeight": 52, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-24.5, -26, 0, 24.5, -26, 0, -24.5, 26, 0, 24.5, 26, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 52, 49, 52, 0, 0, 49, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-24.5, -26, 0], "maxPos": [24.5, 26, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "04769a62-0b45-4897-ae5a-808629cadf75@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "04769a62-0b45-4897-ae5a-808629cadf75@6c48a"}}