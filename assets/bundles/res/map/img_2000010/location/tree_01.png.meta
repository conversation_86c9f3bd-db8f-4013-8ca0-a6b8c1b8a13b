{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "01e448a0-6482-48fc-9bce-47a4539b7ea1", "files": [".json", ".png"], "subMetas": {"tree_01": {"ver": "1.0.6", "uuid": "41d87a2e-ed8a-4b98-b5aa-c5fb9698ed63", "importer": "sprite-frame", "rawTextureUuid": "01e448a0-6482-48fc-9bce-47a4539b7ea1", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0.5, "trimX": 0, "trimY": 0, "width": 95, "height": 73, "rawWidth": 95, "rawHeight": 74, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "01e448a0-6482-48fc-9bce-47a4539b7ea1@6c48a", "displayName": "tree_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "01e448a0-6482-48fc-9bce-47a4539b7ea1", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "01e448a0-6482-48fc-9bce-47a4539b7ea1@f9941", "displayName": "tree_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0.5, "trimX": 0, "trimY": 0, "width": 95, "height": 73, "rawWidth": 95, "rawHeight": 74, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-47.5, -36.5, 0, 47.5, -36.5, 0, -47.5, 36.5, 0, 47.5, 36.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 74, 95, 74, 0, 1, 95, 1], "nuv": [0, 0.013513513513513514, 1, 0.013513513513513514, 0, 1, 1, 1], "minPos": [-47.5, -36.5, 0], "maxPos": [47.5, 36.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "01e448a0-6482-48fc-9bce-47a4539b7ea1@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "01e448a0-6482-48fc-9bce-47a4539b7ea1@6c48a"}}