{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "809da750-7792-4f09-bcfb-d06bd09e36e4", "files": [".json", ".png"], "subMetas": {"Monster_005_Idle_01": {"ver": "1.0.6", "uuid": "dadd84c4-0bcb-4c80-80b2-af6ba7be2932", "importer": "sprite-frame", "rawTextureUuid": "809da750-7792-4f09-bcfb-d06bd09e36e4", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 59, "height": 71, "rawWidth": 59, "rawHeight": 71, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "809da750-7792-4f09-bcfb-d06bd09e36e4@6c48a", "displayName": "Monster_005_Idle_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "809da750-7792-4f09-bcfb-d06bd09e36e4", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "809da750-7792-4f09-bcfb-d06bd09e36e4@f9941", "displayName": "Monster_005_Idle_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 59, "height": 71, "rawWidth": 59, "rawHeight": 71, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-29.5, -35.5, 0, 29.5, -35.5, 0, -29.5, 35.5, 0, 29.5, 35.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 71, 59, 71, 0, 0, 59, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-29.5, -35.5, 0], "maxPos": [29.5, 35.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "809da750-7792-4f09-bcfb-d06bd09e36e4@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "809da750-7792-4f09-bcfb-d06bd09e36e4@6c48a"}}