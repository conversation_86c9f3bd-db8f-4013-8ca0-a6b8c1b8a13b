{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "999fb983-ffa0-4309-b903-dbe8272e7b8f", "files": [".json", ".png"], "subMetas": {"tree_04": {"ver": "1.0.6", "uuid": "c1596602-31a1-4eb3-81ef-87fc4e789c87", "importer": "sprite-frame", "rawTextureUuid": "999fb983-ffa0-4309-b903-dbe8272e7b8f", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 110, "height": 146, "rawWidth": 110, "rawHeight": 146, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "999fb983-ffa0-4309-b903-dbe8272e7b8f@6c48a", "displayName": "tree_04", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "999fb983-ffa0-4309-b903-dbe8272e7b8f", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "999fb983-ffa0-4309-b903-dbe8272e7b8f@f9941", "displayName": "tree_04", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 110, "height": 146, "rawWidth": 110, "rawHeight": 146, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-55, -73, 0, 55, -73, 0, -55, 73, 0, 55, 73, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 146, 110, 146, 0, 0, 110, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-55, -73, 0], "maxPos": [55, 73, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "999fb983-ffa0-4309-b903-dbe8272e7b8f@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "999fb983-ffa0-4309-b903-dbe8272e7b8f@6c48a"}}