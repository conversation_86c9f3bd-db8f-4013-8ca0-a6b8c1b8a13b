{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "ff80e41f-0fb2-41b3-8317-76312f55f995", "files": [".json", ".png"], "subMetas": {"Monster_013_Attack_01": {"ver": "1.0.6", "uuid": "a9f1c332-89d5-4d5f-91a2-d07926083cb2", "importer": "sprite-frame", "rawTextureUuid": "ff80e41f-0fb2-41b3-8317-76312f55f995", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 57, "height": 81, "rawWidth": 57, "rawHeight": 81, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "ff80e41f-0fb2-41b3-8317-76312f55f995@6c48a", "displayName": "Monster_013_Attack_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "ff80e41f-0fb2-41b3-8317-76312f55f995", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "ff80e41f-0fb2-41b3-8317-76312f55f995@f9941", "displayName": "Monster_013_Attack_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 57, "height": 81, "rawWidth": 57, "rawHeight": 81, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-28.5, -40.5, 0, 28.5, -40.5, 0, -28.5, 40.5, 0, 28.5, 40.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 81, 57, 81, 0, 0, 57, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-28.5, -40.5, 0], "maxPos": [28.5, 40.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "ff80e41f-0fb2-41b3-8317-76312f55f995@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "ff80e41f-0fb2-41b3-8317-76312f55f995@6c48a"}}