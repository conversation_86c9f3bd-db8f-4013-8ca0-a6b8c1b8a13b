{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "678e75e3-13d0-49d5-91cf-389c0f857be0", "files": [".json", ".png"], "subMetas": {"Monster_009_Attack_08": {"ver": "1.0.6", "uuid": "6687d9c8-30bf-493e-b0d6-f73cdc7d2a11", "importer": "sprite-frame", "rawTextureUuid": "678e75e3-13d0-49d5-91cf-389c0f857be0", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 69, "height": 73, "rawWidth": 69, "rawHeight": 73, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "678e75e3-13d0-49d5-91cf-389c0f857be0@6c48a", "displayName": "Monster_009_Attack_08", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "678e75e3-13d0-49d5-91cf-389c0f857be0", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "678e75e3-13d0-49d5-91cf-389c0f857be0@f9941", "displayName": "Monster_009_Attack_08", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 69, "height": 73, "rawWidth": 69, "rawHeight": 73, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-34.5, -36.5, 0, 34.5, -36.5, 0, -34.5, 36.5, 0, 34.5, 36.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 73, 69, 73, 0, 0, 69, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-34.5, -36.5, 0], "maxPos": [34.5, 36.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "678e75e3-13d0-49d5-91cf-389c0f857be0@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "678e75e3-13d0-49d5-91cf-389c0f857be0@6c48a"}}