{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "586a5ca7-f5c3-497e-92c3-ea95307579d2", "files": [".json", ".png"], "subMetas": {"Monster_010_Attack_01": {"ver": "1.0.6", "uuid": "7f57387e-52be-4de3-99bc-47c8966bc8df", "importer": "sprite-frame", "rawTextureUuid": "586a5ca7-f5c3-497e-92c3-ea95307579d2", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 57, "height": 69, "rawWidth": 57, "rawHeight": 69, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "586a5ca7-f5c3-497e-92c3-ea95307579d2@6c48a", "displayName": "Monster_010_Attack_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "586a5ca7-f5c3-497e-92c3-ea95307579d2", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "586a5ca7-f5c3-497e-92c3-ea95307579d2@f9941", "displayName": "Monster_010_Attack_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 57, "height": 69, "rawWidth": 57, "rawHeight": 69, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-28.5, -34.5, 0, 28.5, -34.5, 0, -28.5, 34.5, 0, 28.5, 34.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 69, 57, 69, 0, 0, 57, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-28.5, -34.5, 0], "maxPos": [28.5, 34.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "586a5ca7-f5c3-497e-92c3-ea95307579d2@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "586a5ca7-f5c3-497e-92c3-ea95307579d2@6c48a"}}