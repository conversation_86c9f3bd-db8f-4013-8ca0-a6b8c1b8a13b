{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "d861d02c-e75c-4071-9f48-d8b86f178f1e", "files": [".json", ".png"], "subMetas": {"tree_02": {"ver": "1.0.6", "uuid": "1ad834d1-18f7-4c1d-8649-e97bc56abded", "importer": "sprite-frame", "rawTextureUuid": "d861d02c-e75c-4071-9f48-d8b86f178f1e", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 105, "height": 159, "rawWidth": 105, "rawHeight": 159, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "d861d02c-e75c-4071-9f48-d8b86f178f1e@6c48a", "displayName": "tree_02", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "d861d02c-e75c-4071-9f48-d8b86f178f1e", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "d861d02c-e75c-4071-9f48-d8b86f178f1e@f9941", "displayName": "tree_02", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 105, "height": 159, "rawWidth": 105, "rawHeight": 159, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-52.5, -79.5, 0, 52.5, -79.5, 0, -52.5, 79.5, 0, 52.5, 79.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 159, 105, 159, 0, 0, 105, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-52.5, -79.5, 0], "maxPos": [52.5, 79.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "d861d02c-e75c-4071-9f48-d8b86f178f1e@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "d861d02c-e75c-4071-9f48-d8b86f178f1e@6c48a"}}