{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "d6ab13ef-71ad-4f9a-8c1a-9985ff3878a7", "files": [".json", ".png"], "subMetas": {"area": {"ver": "1.0.6", "uuid": "c214b894-4a12-4dcc-bc12-052d46818321", "importer": "sprite-frame", "rawTextureUuid": "d6ab13ef-71ad-4f9a-8c1a-9985ff3878a7", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -56, "offsetY": 76, "trimX": 0, "trimY": 0, "width": 400, "height": 360, "rawWidth": 512, "rawHeight": 512, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "d6ab13ef-71ad-4f9a-8c1a-9985ff3878a7@6c48a", "displayName": "area", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "d6ab13ef-71ad-4f9a-8c1a-9985ff3878a7", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "d6ab13ef-71ad-4f9a-8c1a-9985ff3878a7@f9941", "displayName": "area", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": -56, "offsetY": 76, "trimX": 0, "trimY": 0, "width": 400, "height": 360, "rawWidth": 512, "rawHeight": 512, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-200, -180, 0, 200, -180, 0, -200, 180, 0, 200, 180, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 512, 400, 512, 0, 152, 400, 152], "nuv": [0, 0.296875, 0.78125, 0.296875, 0, 1, 0.78125, 1], "minPos": [-200, -180, 0], "maxPos": [200, 180, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "d6ab13ef-71ad-4f9a-8c1a-9985ff3878a7@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "d6ab13ef-71ad-4f9a-8c1a-9985ff3878a7@6c48a"}}