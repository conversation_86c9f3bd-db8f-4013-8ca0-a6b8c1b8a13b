{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "bc4f2e6c-aab6-4f8a-81c9-3da4903c9471", "files": [".json", ".png"], "subMetas": {"big_mountain_004": {"ver": "1.0.6", "uuid": "a9791bdd-c4ad-4b96-bd9b-2928e03e7d0c", "importer": "sprite-frame", "rawTextureUuid": "bc4f2e6c-aab6-4f8a-81c9-3da4903c9471", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 450, "height": 512, "rawWidth": 450, "rawHeight": 512, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "bc4f2e6c-aab6-4f8a-81c9-3da4903c9471@6c48a", "displayName": "big_mountain_004", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "bc4f2e6c-aab6-4f8a-81c9-3da4903c9471", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "bc4f2e6c-aab6-4f8a-81c9-3da4903c9471@f9941", "displayName": "big_mountain_004", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 450, "height": 512, "rawWidth": 450, "rawHeight": 512, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-225, -256, 0, 225, -256, 0, -225, 256, 0, 225, 256, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 512, 450, 512, 0, 0, 450, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-225, -256, 0], "maxPos": [225, 256, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "bc4f2e6c-aab6-4f8a-81c9-3da4903c9471@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "bc4f2e6c-aab6-4f8a-81c9-3da4903c9471@6c48a"}}