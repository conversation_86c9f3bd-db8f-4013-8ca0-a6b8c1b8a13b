{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "1c9bcede-e546-4f45-a444-f7679c1b06a2", "files": [".json", ".png"], "subMetas": {"pedestal_001": {"ver": "1.0.6", "uuid": "d1e1788d-c940-4432-921c-5deff856ae8b", "importer": "sprite-frame", "rawTextureUuid": "1c9bcede-e546-4f45-a444-f7679c1b06a2", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 332, "height": 151, "rawWidth": 332, "rawHeight": 151, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "1c9bcede-e546-4f45-a444-f7679c1b06a2@6c48a", "displayName": "pedestal_001", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "1c9bcede-e546-4f45-a444-f7679c1b06a2", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "1c9bcede-e546-4f45-a444-f7679c1b06a2@f9941", "displayName": "pedestal_001", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 332, "height": 151, "rawWidth": 332, "rawHeight": 151, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-166, -75.5, 0, 166, -75.5, 0, -166, 75.5, 0, 166, 75.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 151, 332, 151, 0, 0, 332, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-166, -75.5, 0], "maxPos": [166, 75.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "1c9bcede-e546-4f45-a444-f7679c1b06a2@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "1c9bcede-e546-4f45-a444-f7679c1b06a2@6c48a"}}