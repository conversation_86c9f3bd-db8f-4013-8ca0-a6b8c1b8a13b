{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "79c489cb-3d4d-44a1-ba15-472c16407164", "files": [".json", ".png"], "subMetas": {"marsh_001": {"ver": "1.0.6", "uuid": "1a6a3918-aeb2-4707-bba4-782dbb49912b", "importer": "sprite-frame", "rawTextureUuid": "79c489cb-3d4d-44a1-ba15-472c16407164", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -8.5, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 757, "height": 388, "rawWidth": 774, "rawHeight": 388, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "79c489cb-3d4d-44a1-ba15-472c16407164@6c48a", "displayName": "marsh_001", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "79c489cb-3d4d-44a1-ba15-472c16407164", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "79c489cb-3d4d-44a1-ba15-472c16407164@f9941", "displayName": "marsh_001", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": -8.5, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 757, "height": 388, "rawWidth": 774, "rawHeight": 388, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-378.5, -194, 0, 378.5, -194, 0, -378.5, 194, 0, 378.5, 194, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 388, 757, 388, 0, 0, 757, 0], "nuv": [0, 0, 0.9780361757105943, 0, 0, 1, 0.9780361757105943, 1], "minPos": [-378.5, -194, 0], "maxPos": [378.5, 194, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "79c489cb-3d4d-44a1-ba15-472c16407164@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "79c489cb-3d4d-44a1-ba15-472c16407164@6c48a"}}