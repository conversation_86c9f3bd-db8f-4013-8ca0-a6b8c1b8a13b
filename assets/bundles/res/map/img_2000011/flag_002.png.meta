{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "ce68f5e4-bb41-47cc-9f30-8a1d3a83c29f", "files": [".json", ".png"], "subMetas": {"flag_002": {"ver": "1.0.6", "uuid": "4c224f91-73b3-413e-87d6-0d6c117b0eda", "importer": "sprite-frame", "rawTextureUuid": "ce68f5e4-bb41-47cc-9f30-8a1d3a83c29f", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -1, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 67, "height": 154, "rawWidth": 69, "rawHeight": 154, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "ce68f5e4-bb41-47cc-9f30-8a1d3a83c29f@6c48a", "displayName": "flag_002", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "ce68f5e4-bb41-47cc-9f30-8a1d3a83c29f", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "ce68f5e4-bb41-47cc-9f30-8a1d3a83c29f@f9941", "displayName": "flag_002", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": -1, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 67, "height": 154, "rawWidth": 69, "rawHeight": 154, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-33.5, -77, 0, 33.5, -77, 0, -33.5, 77, 0, 33.5, 77, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 154, 67, 154, 0, 0, 67, 0], "nuv": [0, 0, 0.9710144927536232, 0, 0, 1, 0.9710144927536232, 1], "minPos": [-33.5, -77, 0], "maxPos": [33.5, 77, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "ce68f5e4-bb41-47cc-9f30-8a1d3a83c29f@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "ce68f5e4-bb41-47cc-9f30-8a1d3a83c29f@6c48a"}}