{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "49ed28fb-920e-4e52-bbb4-af5a5c73bc85", "files": [".json", ".png"], "subMetas": {"rock_026": {"ver": "1.0.6", "uuid": "85916818-c48b-4c49-9607-c505e1b8408e", "importer": "sprite-frame", "rawTextureUuid": "49ed28fb-920e-4e52-bbb4-af5a5c73bc85", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 401, "height": 255, "rawWidth": 401, "rawHeight": 255, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "49ed28fb-920e-4e52-bbb4-af5a5c73bc85@6c48a", "displayName": "rock_026", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "49ed28fb-920e-4e52-bbb4-af5a5c73bc85", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "49ed28fb-920e-4e52-bbb4-af5a5c73bc85@f9941", "displayName": "rock_026", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 401, "height": 255, "rawWidth": 401, "rawHeight": 255, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-200.5, -127.5, 0, 200.5, -127.5, 0, -200.5, 127.5, 0, 200.5, 127.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 255, 401, 255, 0, 0, 401, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-200.5, -127.5, 0], "maxPos": [200.5, 127.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "49ed28fb-920e-4e52-bbb4-af5a5c73bc85@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "49ed28fb-920e-4e52-bbb4-af5a5c73bc85@6c48a"}}