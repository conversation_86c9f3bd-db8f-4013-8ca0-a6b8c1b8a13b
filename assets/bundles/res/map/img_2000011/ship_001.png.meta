{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "7c260137-ed6d-4e96-b905-65da90522da4", "files": [".json", ".png"], "subMetas": {"ship_001": {"ver": "1.0.6", "uuid": "26d48f38-25d7-4f54-bc20-58b2e9568ec1", "importer": "sprite-frame", "rawTextureUuid": "7c260137-ed6d-4e96-b905-65da90522da4", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 244, "height": 181, "rawWidth": 244, "rawHeight": 181, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "7c260137-ed6d-4e96-b905-65da90522da4@6c48a", "displayName": "ship_001", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "7c260137-ed6d-4e96-b905-65da90522da4", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "7c260137-ed6d-4e96-b905-65da90522da4@f9941", "displayName": "ship_001", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 244, "height": 181, "rawWidth": 244, "rawHeight": 181, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-122, -90.5, 0, 122, -90.5, 0, -122, 90.5, 0, 122, 90.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 181, 244, 181, 0, 0, 244, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-122, -90.5, 0], "maxPos": [122, 90.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "7c260137-ed6d-4e96-b905-65da90522da4@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "7c260137-ed6d-4e96-b905-65da90522da4@6c48a"}}