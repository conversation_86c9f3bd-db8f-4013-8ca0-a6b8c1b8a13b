{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "9a41c535-38aa-4d21-969a-f00083725024", "files": [".json", ".png"], "subMetas": {"segment_006": {"ver": "1.0.6", "uuid": "057ba2a6-a979-40fa-993e-6492d5dcba49", "importer": "sprite-frame", "rawTextureUuid": "9a41c535-38aa-4d21-969a-f00083725024", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -100, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 800, "height": 960, "rawWidth": 1000, "rawHeight": 960, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "9a41c535-38aa-4d21-969a-f00083725024@6c48a", "displayName": "segment_006", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "9a41c535-38aa-4d21-969a-f00083725024", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "9a41c535-38aa-4d21-969a-f00083725024@f9941", "displayName": "segment_006", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": -100, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 800, "height": 960, "rawWidth": 1000, "rawHeight": 960, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-400, -480, 0, 400, -480, 0, -400, 480, 0, 400, 480, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 960, 800, 960, 0, 0, 800, 0], "nuv": [0, 0, 0.8, 0, 0, 1, 0.8, 1], "minPos": [-400, -480, 0], "maxPos": [400, 480, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "9a41c535-38aa-4d21-969a-f00083725024@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "9a41c535-38aa-4d21-969a-f00083725024@6c48a"}}