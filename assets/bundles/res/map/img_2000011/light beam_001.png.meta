{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "78f079ae-fece-43bd-a50d-ef3253966828", "files": [".json", ".png"], "subMetas": {"light beam_001": {"ver": "1.0.6", "uuid": "6d3b95f1-6ae4-4354-8952-be6a575d15a6", "importer": "sprite-frame", "rawTextureUuid": "78f079ae-fece-43bd-a50d-ef3253966828", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 212, "height": 138, "rawWidth": 212, "rawHeight": 138, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "78f079ae-fece-43bd-a50d-ef3253966828@6c48a", "displayName": "light beam_001", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "78f079ae-fece-43bd-a50d-ef3253966828", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "78f079ae-fece-43bd-a50d-ef3253966828@f9941", "displayName": "light beam_001", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 212, "height": 138, "rawWidth": 212, "rawHeight": 138, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-106, -69, 0, 106, -69, 0, -106, 69, 0, 106, 69, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 138, 212, 138, 0, 0, 212, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-106, -69, 0], "maxPos": [106, 69, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "78f079ae-fece-43bd-a50d-ef3253966828@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "78f079ae-fece-43bd-a50d-ef3253966828@6c48a"}}