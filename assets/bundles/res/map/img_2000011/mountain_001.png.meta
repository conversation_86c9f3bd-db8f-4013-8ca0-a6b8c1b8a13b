{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "23610be9-28a3-46f5-a927-18c1d76f0383", "files": [".json", ".png"], "subMetas": {"mountain_001": {"ver": "1.0.6", "uuid": "5ab9373d-9527-4a4b-b2be-e71e6c4b711c", "importer": "sprite-frame", "rawTextureUuid": "23610be9-28a3-46f5-a927-18c1d76f0383", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 792, "height": 837, "rawWidth": 792, "rawHeight": 837, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "23610be9-28a3-46f5-a927-18c1d76f0383@6c48a", "displayName": "mountain_001", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "23610be9-28a3-46f5-a927-18c1d76f0383", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "23610be9-28a3-46f5-a927-18c1d76f0383@f9941", "displayName": "mountain_001", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 792, "height": 837, "rawWidth": 792, "rawHeight": 837, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-396, -418.5, 0, 396, -418.5, 0, -396, 418.5, 0, 396, 418.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 837, 792, 837, 0, 0, 792, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-396, -418.5, 0], "maxPos": [396, 418.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "23610be9-28a3-46f5-a927-18c1d76f0383@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "23610be9-28a3-46f5-a927-18c1d76f0383@6c48a"}}