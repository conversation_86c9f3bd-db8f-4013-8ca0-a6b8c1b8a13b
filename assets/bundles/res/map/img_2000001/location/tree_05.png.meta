{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "33dea6ea-27b0-451a-b877-b5bf9ad4a1fc", "files": [".json", ".png"], "subMetas": {"tree_05": {"ver": "1.0.6", "uuid": "d967fcb4-62be-4d83-b05c-4c690de6103c", "importer": "sprite-frame", "rawTextureUuid": "33dea6ea-27b0-451a-b877-b5bf9ad4a1fc", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 105, "height": 157, "rawWidth": 105, "rawHeight": 157, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "33dea6ea-27b0-451a-b877-b5bf9ad4a1fc@6c48a", "displayName": "tree_05", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "33dea6ea-27b0-451a-b877-b5bf9ad4a1fc", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "33dea6ea-27b0-451a-b877-b5bf9ad4a1fc@f9941", "displayName": "tree_05", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 105, "height": 157, "rawWidth": 105, "rawHeight": 157, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-52.5, -78.5, 0, 52.5, -78.5, 0, -52.5, 78.5, 0, 52.5, 78.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 157, 105, 157, 0, 0, 105, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-52.5, -78.5, 0], "maxPos": [52.5, 78.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "33dea6ea-27b0-451a-b877-b5bf9ad4a1fc@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "33dea6ea-27b0-451a-b877-b5bf9ad4a1fc@6c48a"}}