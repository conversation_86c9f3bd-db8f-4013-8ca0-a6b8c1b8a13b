{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "b4872f33-339c-42ff-87cf-33e20ad71cc8", "files": [".json", ".png"], "subMetas": {"Monster_027_Attack_01": {"ver": "1.0.6", "uuid": "dc173912-ea18-4fb3-b3af-35d43ed9e40e", "importer": "sprite-frame", "rawTextureUuid": "b4872f33-339c-42ff-87cf-33e20ad71cc8", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 58, "height": 81, "rawWidth": 58, "rawHeight": 81, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "b4872f33-339c-42ff-87cf-33e20ad71cc8@6c48a", "displayName": "Monster_027_Attack_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "b4872f33-339c-42ff-87cf-33e20ad71cc8", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "b4872f33-339c-42ff-87cf-33e20ad71cc8@f9941", "displayName": "Monster_027_Attack_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 58, "height": 81, "rawWidth": 58, "rawHeight": 81, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-29, -40.5, 0, 29, -40.5, 0, -29, 40.5, 0, 29, 40.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 81, 58, 81, 0, 0, 58, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-29, -40.5, 0], "maxPos": [29, 40.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "b4872f33-339c-42ff-87cf-33e20ad71cc8@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "b4872f33-339c-42ff-87cf-33e20ad71cc8@6c48a"}}