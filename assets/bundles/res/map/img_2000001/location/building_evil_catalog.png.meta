{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "987f1b24-7090-4e9c-baa5-171b934f4695", "files": [".json", ".png"], "subMetas": {"building_evil_catalog": {"ver": "1.0.6", "uuid": "4b2a6144-11d1-4e8a-97a3-25e3972e3419", "importer": "sprite-frame", "rawTextureUuid": "987f1b24-7090-4e9c-baa5-171b934f4695", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 266, "height": 219, "rawWidth": 266, "rawHeight": 219, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "987f1b24-7090-4e9c-baa5-171b934f4695@6c48a", "displayName": "building_evil_catalog", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "987f1b24-7090-4e9c-baa5-171b934f4695", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "987f1b24-7090-4e9c-baa5-171b934f4695@f9941", "displayName": "building_evil_catalog", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 266, "height": 219, "rawWidth": 266, "rawHeight": 219, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-133, -109.5, 0, 133, -109.5, 0, -133, 109.5, 0, 133, 109.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 219, 266, 219, 0, 0, 266, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-133, -109.5, 0], "maxPos": [133, 109.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "987f1b24-7090-4e9c-baa5-171b934f4695@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "987f1b24-7090-4e9c-baa5-171b934f4695@6c48a"}}