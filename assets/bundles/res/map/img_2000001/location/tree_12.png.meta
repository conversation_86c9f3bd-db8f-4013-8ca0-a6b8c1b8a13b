{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "0deee3f7-4875-41d8-b8b1-948f45ab2190", "files": [".json", ".png"], "subMetas": {"tree_12": {"ver": "1.0.6", "uuid": "49f13a99-9bfd-4867-a32b-3121bf263748", "importer": "sprite-frame", "rawTextureUuid": "0deee3f7-4875-41d8-b8b1-948f45ab2190", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 98, "height": 75, "rawWidth": 98, "rawHeight": 75, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "0deee3f7-4875-41d8-b8b1-948f45ab2190@6c48a", "displayName": "tree_12", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "0deee3f7-4875-41d8-b8b1-948f45ab2190", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "0deee3f7-4875-41d8-b8b1-948f45ab2190@f9941", "displayName": "tree_12", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 98, "height": 75, "rawWidth": 98, "rawHeight": 75, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-49, -37.5, 0, 49, -37.5, 0, -49, 37.5, 0, 49, 37.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 75, 98, 75, 0, 0, 98, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-49, -37.5, 0], "maxPos": [49, 37.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "0deee3f7-4875-41d8-b8b1-948f45ab2190@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "0deee3f7-4875-41d8-b8b1-948f45ab2190@6c48a"}}