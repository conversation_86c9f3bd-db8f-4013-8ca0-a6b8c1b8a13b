{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "2bea0043-225a-4e15-9f74-371a5d01f137", "files": [".json", ".png"], "subMetas": {"Monster_101_Idle_01": {"ver": "1.0.6", "uuid": "305711e3-8a7d-44e0-9ea9-6a6ae4f46c17", "importer": "sprite-frame", "rawTextureUuid": "2bea0043-225a-4e15-9f74-371a5d01f137", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 69, "height": 71, "rawWidth": 69, "rawHeight": 71, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "2bea0043-225a-4e15-9f74-371a5d01f137@6c48a", "displayName": "Monster_101_Idle_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "2bea0043-225a-4e15-9f74-371a5d01f137", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "2bea0043-225a-4e15-9f74-371a5d01f137@f9941", "displayName": "Monster_101_Idle_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 69, "height": 71, "rawWidth": 69, "rawHeight": 71, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-34.5, -35.5, 0, 34.5, -35.5, 0, -34.5, 35.5, 0, 34.5, 35.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 71, 69, 71, 0, 0, 69, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-34.5, -35.5, 0], "maxPos": [34.5, 35.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "2bea0043-225a-4e15-9f74-371a5d01f137@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "2bea0043-225a-4e15-9f74-371a5d01f137@6c48a"}}