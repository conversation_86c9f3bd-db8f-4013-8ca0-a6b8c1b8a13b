{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "91a7e195-2312-4429-88f6-4fdb2206837e", "files": [".json", ".png"], "subMetas": {"building_daily_dup": {"ver": "1.0.6", "uuid": "5ad423e9-0621-4a63-b085-75d33ddea9e9", "importer": "sprite-frame", "rawTextureUuid": "91a7e195-2312-4429-88f6-4fdb2206837e", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 196, "height": 202, "rawWidth": 196, "rawHeight": 202, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "91a7e195-2312-4429-88f6-4fdb2206837e@6c48a", "displayName": "building_daily_dup", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "91a7e195-2312-4429-88f6-4fdb2206837e", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "91a7e195-2312-4429-88f6-4fdb2206837e@f9941", "displayName": "building_daily_dup", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 196, "height": 202, "rawWidth": 196, "rawHeight": 202, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-98, -101, 0, 98, -101, 0, -98, 101, 0, 98, 101, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 202, 196, 202, 0, 0, 196, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-98, -101, 0], "maxPos": [98, 101, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "91a7e195-2312-4429-88f6-4fdb2206837e@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "91a7e195-2312-4429-88f6-4fdb2206837e@6c48a"}}