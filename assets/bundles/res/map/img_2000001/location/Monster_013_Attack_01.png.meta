{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "27830b6e-7c7e-4212-8e4a-4a9f953be766", "files": [".json", ".png"], "subMetas": {"Monster_013_Attack_01": {"ver": "1.0.6", "uuid": "d60009d3-81f5-470f-8912-60286dd5ef40", "importer": "sprite-frame", "rawTextureUuid": "27830b6e-7c7e-4212-8e4a-4a9f953be766", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 57, "height": 81, "rawWidth": 57, "rawHeight": 81, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "27830b6e-7c7e-4212-8e4a-4a9f953be766@6c48a", "displayName": "Monster_013_Attack_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "27830b6e-7c7e-4212-8e4a-4a9f953be766", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "27830b6e-7c7e-4212-8e4a-4a9f953be766@f9941", "displayName": "Monster_013_Attack_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 57, "height": 81, "rawWidth": 57, "rawHeight": 81, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-28.5, -40.5, 0, 28.5, -40.5, 0, -28.5, 40.5, 0, 28.5, 40.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 81, 57, 81, 0, 0, 57, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-28.5, -40.5, 0], "maxPos": [28.5, 40.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "27830b6e-7c7e-4212-8e4a-4a9f953be766@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "27830b6e-7c7e-4212-8e4a-4a9f953be766@6c48a"}}