{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "3b10768a-c9f6-460a-be34-33dde82b7c67", "files": [".json", ".png"], "subMetas": {"mine_04": {"ver": "1.0.6", "uuid": "afdb0f7c-528f-443b-94f9-931e84841e84", "importer": "sprite-frame", "rawTextureUuid": "3b10768a-c9f6-460a-be34-33dde82b7c67", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": -1, "trimX": 0, "trimY": 2, "width": 80, "height": 85, "rawWidth": 80, "rawHeight": 87, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "3b10768a-c9f6-460a-be34-33dde82b7c67@6c48a", "displayName": "mine_04", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "3b10768a-c9f6-460a-be34-33dde82b7c67", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "3b10768a-c9f6-460a-be34-33dde82b7c67@f9941", "displayName": "mine_04", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": -1, "trimX": 0, "trimY": 2, "width": 80, "height": 85, "rawWidth": 80, "rawHeight": 87, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-40, -42.5, 0, 40, -42.5, 0, -40, 42.5, 0, 40, 42.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 85, 80, 85, 0, 0, 80, 0], "nuv": [0, 0, 1, 0, 0, 0.9770114942528736, 1, 0.9770114942528736], "minPos": [-40, -42.5, 0], "maxPos": [40, 42.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "3b10768a-c9f6-460a-be34-33dde82b7c67@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "3b10768a-c9f6-460a-be34-33dde82b7c67@6c48a"}}