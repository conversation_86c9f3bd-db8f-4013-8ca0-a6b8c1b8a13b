{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "6a7edf99-d032-4d48-be3f-4cf8017f8e67", "files": [".json", ".png"], "subMetas": {"Monster_507_Attack_01": {"ver": "1.0.6", "uuid": "e08fe02f-c5c9-49c0-b3de-3eb000aeabd4", "importer": "sprite-frame", "rawTextureUuid": "6a7edf99-d032-4d48-be3f-4cf8017f8e67", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 195, "height": 245, "rawWidth": 195, "rawHeight": 245, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "6a7edf99-d032-4d48-be3f-4cf8017f8e67@6c48a", "displayName": "Monster_507_Attack_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "6a7edf99-d032-4d48-be3f-4cf8017f8e67", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "6a7edf99-d032-4d48-be3f-4cf8017f8e67@f9941", "displayName": "Monster_507_Attack_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 195, "height": 245, "rawWidth": 195, "rawHeight": 245, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-97.5, -122.5, 0, 97.5, -122.5, 0, -97.5, 122.5, 0, 97.5, 122.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 245, 195, 245, 0, 0, 195, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-97.5, -122.5, 0], "maxPos": [97.5, 122.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "6a7edf99-d032-4d48-be3f-4cf8017f8e67@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "6a7edf99-d032-4d48-be3f-4cf8017f8e67@6c48a"}}