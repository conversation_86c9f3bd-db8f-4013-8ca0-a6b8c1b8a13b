{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "f0b33563-b4c7-4ed7-9e68-65865f269977", "files": [".json", ".png"], "subMetas": {"Monster_006_attack_01": {"ver": "1.0.6", "uuid": "d53f0e4d-9baf-4bf7-826b-9bb1bcb32e51", "importer": "sprite-frame", "rawTextureUuid": "f0b33563-b4c7-4ed7-9e68-65865f269977", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 83, "height": 59, "rawWidth": 83, "rawHeight": 59, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "f0b33563-b4c7-4ed7-9e68-65865f269977@6c48a", "displayName": "Monster_006_attack_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "f0b33563-b4c7-4ed7-9e68-65865f269977", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "f0b33563-b4c7-4ed7-9e68-65865f269977@f9941", "displayName": "Monster_006_attack_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 83, "height": 59, "rawWidth": 83, "rawHeight": 59, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-41.5, -29.5, 0, 41.5, -29.5, 0, -41.5, 29.5, 0, 41.5, 29.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 59, 83, 59, 0, 0, 83, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-41.5, -29.5, 0], "maxPos": [41.5, 29.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "f0b33563-b4c7-4ed7-9e68-65865f269977@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "f0b33563-b4c7-4ed7-9e68-65865f269977@6c48a"}}