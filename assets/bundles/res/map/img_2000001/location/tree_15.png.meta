{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "ce19aa1a-66be-49fc-aa18-de675837188f", "files": [".json", ".png"], "subMetas": {"tree_15": {"ver": "1.0.6", "uuid": "dbc1e575-d757-4daa-a5eb-1e35e57ee629", "importer": "sprite-frame", "rawTextureUuid": "ce19aa1a-66be-49fc-aa18-de675837188f", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 67, "height": 131, "rawWidth": 67, "rawHeight": 131, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "ce19aa1a-66be-49fc-aa18-de675837188f@6c48a", "displayName": "tree_15", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "ce19aa1a-66be-49fc-aa18-de675837188f", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "ce19aa1a-66be-49fc-aa18-de675837188f@f9941", "displayName": "tree_15", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 67, "height": 131, "rawWidth": 67, "rawHeight": 131, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-33.5, -65.5, 0, 33.5, -65.5, 0, -33.5, 65.5, 0, 33.5, 65.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 131, 67, 131, 0, 0, 67, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-33.5, -65.5, 0], "maxPos": [33.5, 65.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "ce19aa1a-66be-49fc-aa18-de675837188f@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "ce19aa1a-66be-49fc-aa18-de675837188f@6c48a"}}