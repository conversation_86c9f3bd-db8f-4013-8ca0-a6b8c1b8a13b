{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "54799bf8-97b9-4de2-8d27-13d49097c759", "files": [".json", ".png"], "subMetas": {"Monster_015_attack_01": {"ver": "1.0.6", "uuid": "f85a7b77-a398-4c7f-b313-2674b841367a", "importer": "sprite-frame", "rawTextureUuid": "54799bf8-97b9-4de2-8d27-13d49097c759", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 71, "height": 80, "rawWidth": 71, "rawHeight": 80, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "54799bf8-97b9-4de2-8d27-13d49097c759@6c48a", "displayName": "Monster_015_attack_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "54799bf8-97b9-4de2-8d27-13d49097c759", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "54799bf8-97b9-4de2-8d27-13d49097c759@f9941", "displayName": "Monster_015_attack_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 71, "height": 80, "rawWidth": 71, "rawHeight": 80, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-35.5, -40, 0, 35.5, -40, 0, -35.5, 40, 0, 35.5, 40, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 80, 71, 80, 0, 0, 71, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-35.5, -40, 0], "maxPos": [35.5, 40, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "54799bf8-97b9-4de2-8d27-13d49097c759@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "54799bf8-97b9-4de2-8d27-13d49097c759@6c48a"}}