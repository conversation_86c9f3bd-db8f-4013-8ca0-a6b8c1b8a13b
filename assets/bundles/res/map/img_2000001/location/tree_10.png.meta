{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "573fc5cd-46b2-4cdc-9d13-049e9d54d50f", "files": [".json", ".png"], "subMetas": {"tree_10": {"ver": "1.0.6", "uuid": "b86f1e40-2e68-4e01-af1a-b528a2cbec7e", "importer": "sprite-frame", "rawTextureUuid": "573fc5cd-46b2-4cdc-9d13-049e9d54d50f", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 141, "height": 199, "rawWidth": 141, "rawHeight": 199, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "573fc5cd-46b2-4cdc-9d13-049e9d54d50f@6c48a", "displayName": "tree_10", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "573fc5cd-46b2-4cdc-9d13-049e9d54d50f", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "573fc5cd-46b2-4cdc-9d13-049e9d54d50f@f9941", "displayName": "tree_10", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 141, "height": 199, "rawWidth": 141, "rawHeight": 199, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-70.5, -99.5, 0, 70.5, -99.5, 0, -70.5, 99.5, 0, 70.5, 99.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 199, 141, 199, 0, 0, 141, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-70.5, -99.5, 0], "maxPos": [70.5, 99.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "573fc5cd-46b2-4cdc-9d13-049e9d54d50f@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "573fc5cd-46b2-4cdc-9d13-049e9d54d50f@6c48a"}}