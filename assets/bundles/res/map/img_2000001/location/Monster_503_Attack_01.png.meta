{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "7f3a0780-a9f6-41b9-b411-e970de8196f7", "files": [".json", ".png"], "subMetas": {"Monster_503_Attack_01": {"ver": "1.0.6", "uuid": "837426cc-2ae2-486c-9e51-899f5165c5e1", "importer": "sprite-frame", "rawTextureUuid": "7f3a0780-a9f6-41b9-b411-e970de8196f7", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 249, "height": 218, "rawWidth": 249, "rawHeight": 218, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "7f3a0780-a9f6-41b9-b411-e970de8196f7@6c48a", "displayName": "Monster_503_Attack_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "7f3a0780-a9f6-41b9-b411-e970de8196f7", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "7f3a0780-a9f6-41b9-b411-e970de8196f7@f9941", "displayName": "Monster_503_Attack_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 249, "height": 218, "rawWidth": 249, "rawHeight": 218, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-124.5, -109, 0, 124.5, -109, 0, -124.5, 109, 0, 124.5, 109, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 218, 249, 218, 0, 0, 249, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-124.5, -109, 0], "maxPos": [124.5, 109, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "7f3a0780-a9f6-41b9-b411-e970de8196f7@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "7f3a0780-a9f6-41b9-b411-e970de8196f7@6c48a"}}