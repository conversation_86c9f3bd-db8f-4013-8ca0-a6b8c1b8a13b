{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "f3915575-5e0a-4f24-b8fa-fb07dff43bff", "files": [".json", ".png"], "subMetas": {"Monster_011_attack_01": {"ver": "1.0.6", "uuid": "cf68e92d-e950-4d89-a026-8cac7d7c7824", "importer": "sprite-frame", "rawTextureUuid": "f3915575-5e0a-4f24-b8fa-fb07dff43bff", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 81, "height": 79, "rawWidth": 81, "rawHeight": 79, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "f3915575-5e0a-4f24-b8fa-fb07dff43bff@6c48a", "displayName": "Monster_011_attack_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "f3915575-5e0a-4f24-b8fa-fb07dff43bff", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "f3915575-5e0a-4f24-b8fa-fb07dff43bff@f9941", "displayName": "Monster_011_attack_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 81, "height": 79, "rawWidth": 81, "rawHeight": 79, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-40.5, -39.5, 0, 40.5, -39.5, 0, -40.5, 39.5, 0, 40.5, 39.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 79, 81, 79, 0, 0, 81, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-40.5, -39.5, 0], "maxPos": [40.5, 39.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "f3915575-5e0a-4f24-b8fa-fb07dff43bff@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "f3915575-5e0a-4f24-b8fa-fb07dff43bff@6c48a"}}