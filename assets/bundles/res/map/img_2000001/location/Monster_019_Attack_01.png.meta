{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "68f1b97a-39de-4af4-8e76-3dc2ac4c341b", "files": [".json", ".png"], "subMetas": {"Monster_019_Attack_01": {"ver": "1.0.6", "uuid": "77bfe171-f7d8-4498-81d9-3aff09d8cdce", "importer": "sprite-frame", "rawTextureUuid": "68f1b97a-39de-4af4-8e76-3dc2ac4c341b", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 67, "height": 79, "rawWidth": 67, "rawHeight": 79, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "68f1b97a-39de-4af4-8e76-3dc2ac4c341b@6c48a", "displayName": "Monster_019_Attack_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "68f1b97a-39de-4af4-8e76-3dc2ac4c341b", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "68f1b97a-39de-4af4-8e76-3dc2ac4c341b@f9941", "displayName": "Monster_019_Attack_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 67, "height": 79, "rawWidth": 67, "rawHeight": 79, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-33.5, -39.5, 0, 33.5, -39.5, 0, -33.5, 39.5, 0, 33.5, 39.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 79, 67, 79, 0, 0, 67, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-33.5, -39.5, 0], "maxPos": [33.5, 39.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "68f1b97a-39de-4af4-8e76-3dc2ac4c341b@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "68f1b97a-39de-4af4-8e76-3dc2ac4c341b@6c48a"}}