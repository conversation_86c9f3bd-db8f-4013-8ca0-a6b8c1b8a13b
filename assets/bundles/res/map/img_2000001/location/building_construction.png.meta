{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "9b19e097-b5a1-4eeb-8191-f3e3c5cbd0c6", "files": [".json", ".png"], "subMetas": {"building_construction": {"ver": "1.0.6", "uuid": "6f569dc1-a2cd-4629-b97e-0926185587a9", "importer": "sprite-frame", "rawTextureUuid": "9b19e097-b5a1-4eeb-8191-f3e3c5cbd0c6", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 200, "height": 152, "rawWidth": 200, "rawHeight": 152, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "9b19e097-b5a1-4eeb-8191-f3e3c5cbd0c6@6c48a", "displayName": "building_construction", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "9b19e097-b5a1-4eeb-8191-f3e3c5cbd0c6", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "9b19e097-b5a1-4eeb-8191-f3e3c5cbd0c6@f9941", "displayName": "building_construction", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 200, "height": 152, "rawWidth": 200, "rawHeight": 152, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-100, -76, 0, 100, -76, 0, -100, 76, 0, 100, 76, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 152, 200, 152, 0, 0, 200, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-100, -76, 0], "maxPos": [100, 76, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "9b19e097-b5a1-4eeb-8191-f3e3c5cbd0c6@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "9b19e097-b5a1-4eeb-8191-f3e3c5cbd0c6@6c48a"}}