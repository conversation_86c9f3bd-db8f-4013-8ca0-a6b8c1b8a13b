{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "6b056092-1ac3-4eeb-806d-45df50f43a46", "files": [".json", ".png"], "subMetas": {"tree_14": {"ver": "1.0.6", "uuid": "563b13d7-66fc-4b9b-a5c9-056bf1cdb34a", "importer": "sprite-frame", "rawTextureUuid": "6b056092-1ac3-4eeb-806d-45df50f43a46", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 91, "height": 161, "rawWidth": 91, "rawHeight": 161, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "6b056092-1ac3-4eeb-806d-45df50f43a46@6c48a", "displayName": "tree_14", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "6b056092-1ac3-4eeb-806d-45df50f43a46", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "6b056092-1ac3-4eeb-806d-45df50f43a46@f9941", "displayName": "tree_14", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 91, "height": 161, "rawWidth": 91, "rawHeight": 161, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-45.5, -80.5, 0, 45.5, -80.5, 0, -45.5, 80.5, 0, 45.5, 80.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 161, 91, 161, 0, 0, 91, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-45.5, -80.5, 0], "maxPos": [45.5, 80.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "6b056092-1ac3-4eeb-806d-45df50f43a46@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "6b056092-1ac3-4eeb-806d-45df50f43a46@6c48a"}}