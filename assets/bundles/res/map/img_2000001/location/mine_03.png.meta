{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "6df8bf33-74c4-4ce0-8fe4-bc34acacb7a2", "files": [".json", ".png"], "subMetas": {"mine_03": {"ver": "1.0.6", "uuid": "1dd3417c-d555-41db-9fd4-cb351012004b", "importer": "sprite-frame", "rawTextureUuid": "6df8bf33-74c4-4ce0-8fe4-bc34acacb7a2", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": -2, "trimX": 0, "trimY": 4, "width": 81, "height": 69, "rawWidth": 81, "rawHeight": 73, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "6df8bf33-74c4-4ce0-8fe4-bc34acacb7a2@6c48a", "displayName": "mine_03", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "6df8bf33-74c4-4ce0-8fe4-bc34acacb7a2", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "6df8bf33-74c4-4ce0-8fe4-bc34acacb7a2@f9941", "displayName": "mine_03", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": -2, "trimX": 0, "trimY": 4, "width": 81, "height": 69, "rawWidth": 81, "rawHeight": 73, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-40.5, -34.5, 0, 40.5, -34.5, 0, -40.5, 34.5, 0, 40.5, 34.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 69, 81, 69, 0, 0, 81, 0], "nuv": [0, 0, 1, 0, 0, 0.9452054794520548, 1, 0.9452054794520548], "minPos": [-40.5, -34.5, 0], "maxPos": [40.5, 34.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "6df8bf33-74c4-4ce0-8fe4-bc34acacb7a2@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "6df8bf33-74c4-4ce0-8fe4-bc34acacb7a2@6c48a"}}