{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "d8f665a7-f633-48ce-ae62-4a4467e83538", "files": [".json", ".png"], "subMetas": {"building_shop": {"ver": "1.0.6", "uuid": "f898ec18-b025-4c1f-a07d-************", "importer": "sprite-frame", "rawTextureUuid": "d8f665a7-f633-48ce-ae62-4a4467e83538", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 214, "height": 259, "rawWidth": 214, "rawHeight": 259, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "d8f665a7-f633-48ce-ae62-4a4467e83538@6c48a", "displayName": "building_shop", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "d8f665a7-f633-48ce-ae62-4a4467e83538", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "d8f665a7-f633-48ce-ae62-4a4467e83538@f9941", "displayName": "building_shop", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 214, "height": 259, "rawWidth": 214, "rawHeight": 259, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-107, -129.5, 0, 107, -129.5, 0, -107, 129.5, 0, 107, 129.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 259, 214, 259, 0, 0, 214, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-107, -129.5, 0], "maxPos": [107, 129.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "d8f665a7-f633-48ce-ae62-4a4467e83538@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "d8f665a7-f633-48ce-ae62-4a4467e83538@6c48a"}}