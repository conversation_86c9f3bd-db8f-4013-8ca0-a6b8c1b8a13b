{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "b2538952-3b9f-41ad-a6a0-6d60137237ef", "files": [".json", ".png"], "subMetas": {"mine_02": {"ver": "1.0.6", "uuid": "666d7bc2-502f-41d7-adf3-d90161fa37d4", "importer": "sprite-frame", "rawTextureUuid": "b2538952-3b9f-41ad-a6a0-6d60137237ef", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 83, "height": 76, "rawWidth": 83, "rawHeight": 76, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "b2538952-3b9f-41ad-a6a0-6d60137237ef@6c48a", "displayName": "mine_02", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "b2538952-3b9f-41ad-a6a0-6d60137237ef", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "b2538952-3b9f-41ad-a6a0-6d60137237ef@f9941", "displayName": "mine_02", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 83, "height": 76, "rawWidth": 83, "rawHeight": 76, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-41.5, -38, 0, 41.5, -38, 0, -41.5, 38, 0, 41.5, 38, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 76, 83, 76, 0, 0, 83, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-41.5, -38, 0], "maxPos": [41.5, 38, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "b2538952-3b9f-41ad-a6a0-6d60137237ef@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "b2538952-3b9f-41ad-a6a0-6d60137237ef@6c48a"}}