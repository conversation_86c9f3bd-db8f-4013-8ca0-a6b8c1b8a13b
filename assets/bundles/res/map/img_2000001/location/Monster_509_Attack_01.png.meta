{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "bb59bd18-7fc9-4b9c-9e27-ce10bcb5fc3e", "files": [".json", ".png"], "subMetas": {"Monster_509_Attack_01": {"ver": "1.0.6", "uuid": "0f59b868-6eb8-4afc-8541-2c70e972defc", "importer": "sprite-frame", "rawTextureUuid": "bb59bd18-7fc9-4b9c-9e27-ce10bcb5fc3e", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 235, "height": 202, "rawWidth": 235, "rawHeight": 202, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "bb59bd18-7fc9-4b9c-9e27-ce10bcb5fc3e@6c48a", "displayName": "Monster_509_Attack_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "bb59bd18-7fc9-4b9c-9e27-ce10bcb5fc3e", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "bb59bd18-7fc9-4b9c-9e27-ce10bcb5fc3e@f9941", "displayName": "Monster_509_Attack_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 235, "height": 202, "rawWidth": 235, "rawHeight": 202, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-117.5, -101, 0, 117.5, -101, 0, -117.5, 101, 0, 117.5, 101, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 202, 235, 202, 0, 0, 235, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-117.5, -101, 0], "maxPos": [117.5, 101, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "bb59bd18-7fc9-4b9c-9e27-ce10bcb5fc3e@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "bb59bd18-7fc9-4b9c-9e27-ce10bcb5fc3e@6c48a"}}