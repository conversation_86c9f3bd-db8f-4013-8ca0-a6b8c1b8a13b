{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "6353219e-7ac4-4d47-9ccc-7b9437bc8d20", "files": [".json", ".png"], "subMetas": {"building_store_house": {"ver": "1.0.6", "uuid": "99bcfce8-a81e-4b2b-8407-5a3a27ccbd16", "importer": "sprite-frame", "rawTextureUuid": "6353219e-7ac4-4d47-9ccc-7b9437bc8d20", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 229, "height": 165, "rawWidth": 229, "rawHeight": 165, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "6353219e-7ac4-4d47-9ccc-7b9437bc8d20@6c48a", "displayName": "building_store_house", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "6353219e-7ac4-4d47-9ccc-7b9437bc8d20", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "6353219e-7ac4-4d47-9ccc-7b9437bc8d20@f9941", "displayName": "building_store_house", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 229, "height": 165, "rawWidth": 229, "rawHeight": 165, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-114.5, -82.5, 0, 114.5, -82.5, 0, -114.5, 82.5, 0, 114.5, 82.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 165, 229, 165, 0, 0, 229, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-114.5, -82.5, 0], "maxPos": [114.5, 82.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "6353219e-7ac4-4d47-9ccc-7b9437bc8d20@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "6353219e-7ac4-4d47-9ccc-7b9437bc8d20@6c48a"}}