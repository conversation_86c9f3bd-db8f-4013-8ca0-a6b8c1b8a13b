{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "407f272e-8e27-4a29-a1f3-af3db96bf24e", "files": [".json", ".png"], "subMetas": {"Monster_028_Attack_01": {"ver": "1.0.6", "uuid": "6e07cf1c-60f1-479c-ad7c-cec4aebe092a", "importer": "sprite-frame", "rawTextureUuid": "407f272e-8e27-4a29-a1f3-af3db96bf24e", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 67, "height": 79, "rawWidth": 67, "rawHeight": 79, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "407f272e-8e27-4a29-a1f3-af3db96bf24e@6c48a", "displayName": "Monster_028_Attack_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "407f272e-8e27-4a29-a1f3-af3db96bf24e", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "407f272e-8e27-4a29-a1f3-af3db96bf24e@f9941", "displayName": "Monster_028_Attack_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 67, "height": 79, "rawWidth": 67, "rawHeight": 79, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-33.5, -39.5, 0, 33.5, -39.5, 0, -33.5, 39.5, 0, 33.5, 39.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 79, 67, 79, 0, 0, 67, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-33.5, -39.5, 0], "maxPos": [33.5, 39.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "407f272e-8e27-4a29-a1f3-af3db96bf24e@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "407f272e-8e27-4a29-a1f3-af3db96bf24e@6c48a"}}