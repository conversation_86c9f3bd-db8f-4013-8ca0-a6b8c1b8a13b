{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "93ccb23b-9d12-4cbb-99b0-b225d0dd3e0a", "files": [".json", ".png"], "subMetas": {"Monster_013_Idle_01": {"ver": "1.0.6", "uuid": "950d0ca8-e5f9-4e70-80be-0f6b2b826829", "importer": "sprite-frame", "rawTextureUuid": "93ccb23b-9d12-4cbb-99b0-b225d0dd3e0a", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 65, "height": 65, "rawWidth": 65, "rawHeight": 65, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "93ccb23b-9d12-4cbb-99b0-b225d0dd3e0a@6c48a", "displayName": "Monster_013_Idle_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "93ccb23b-9d12-4cbb-99b0-b225d0dd3e0a", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "93ccb23b-9d12-4cbb-99b0-b225d0dd3e0a@f9941", "displayName": "Monster_013_Idle_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 65, "height": 65, "rawWidth": 65, "rawHeight": 65, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-32.5, -32.5, 0, 32.5, -32.5, 0, -32.5, 32.5, 0, 32.5, 32.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 65, 65, 65, 0, 0, 65, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-32.5, -32.5, 0], "maxPos": [32.5, 32.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "93ccb23b-9d12-4cbb-99b0-b225d0dd3e0a@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "93ccb23b-9d12-4cbb-99b0-b225d0dd3e0a@6c48a"}}