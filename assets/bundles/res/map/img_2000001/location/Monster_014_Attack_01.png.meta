{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "3a2f4f5e-6e48-4d69-a220-fd7ae08f9269", "files": [".json", ".png"], "subMetas": {"Monster_014_Attack_01": {"ver": "1.0.6", "uuid": "036a01af-88d4-495b-8057-4f45b8eea96a", "importer": "sprite-frame", "rawTextureUuid": "3a2f4f5e-6e48-4d69-a220-fd7ae08f9269", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 65, "height": 72, "rawWidth": 65, "rawHeight": 72, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "3a2f4f5e-6e48-4d69-a220-fd7ae08f9269@6c48a", "displayName": "Monster_014_Attack_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "3a2f4f5e-6e48-4d69-a220-fd7ae08f9269", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "3a2f4f5e-6e48-4d69-a220-fd7ae08f9269@f9941", "displayName": "Monster_014_Attack_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 65, "height": 72, "rawWidth": 65, "rawHeight": 72, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-32.5, -36, 0, 32.5, -36, 0, -32.5, 36, 0, 32.5, 36, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 72, 65, 72, 0, 0, 65, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-32.5, -36, 0], "maxPos": [32.5, 36, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "3a2f4f5e-6e48-4d69-a220-fd7ae08f9269@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "3a2f4f5e-6e48-4d69-a220-fd7ae08f9269@6c48a"}}