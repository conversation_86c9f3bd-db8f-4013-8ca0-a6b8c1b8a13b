{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "e88cef36-2073-4a2b-96a7-66f24a30cddd", "files": [".json", ".png"], "subMetas": {"tree_16": {"ver": "1.0.6", "uuid": "b5e23627-8eb0-466c-a682-12a0349ef57f", "importer": "sprite-frame", "rawTextureUuid": "e88cef36-2073-4a2b-96a7-66f24a30cddd", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 114, "height": 172, "rawWidth": 114, "rawHeight": 172, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "e88cef36-2073-4a2b-96a7-66f24a30cddd@6c48a", "displayName": "tree_16", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "e88cef36-2073-4a2b-96a7-66f24a30cddd", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "e88cef36-2073-4a2b-96a7-66f24a30cddd@f9941", "displayName": "tree_16", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 114, "height": 172, "rawWidth": 114, "rawHeight": 172, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-57, -86, 0, 57, -86, 0, -57, 86, 0, 57, 86, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 172, 114, 172, 0, 0, 114, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-57, -86, 0], "maxPos": [57, 86, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "e88cef36-2073-4a2b-96a7-66f24a30cddd@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "e88cef36-2073-4a2b-96a7-66f24a30cddd@6c48a"}}