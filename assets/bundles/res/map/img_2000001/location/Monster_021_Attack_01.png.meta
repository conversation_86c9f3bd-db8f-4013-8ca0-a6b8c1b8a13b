{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "bd28364a-3b78-4972-911b-7e92347e5a4b", "files": [".json", ".png"], "subMetas": {"Monster_021_Attack_01": {"ver": "1.0.6", "uuid": "c1237b9f-8336-4791-aac8-f24b3ae1ceea", "importer": "sprite-frame", "rawTextureUuid": "bd28364a-3b78-4972-911b-7e92347e5a4b", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 69, "height": 81, "rawWidth": 69, "rawHeight": 81, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "bd28364a-3b78-4972-911b-7e92347e5a4b@6c48a", "displayName": "Monster_021_Attack_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "bd28364a-3b78-4972-911b-7e92347e5a4b", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "bd28364a-3b78-4972-911b-7e92347e5a4b@f9941", "displayName": "Monster_021_Attack_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 69, "height": 81, "rawWidth": 69, "rawHeight": 81, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-34.5, -40.5, 0, 34.5, -40.5, 0, -34.5, 40.5, 0, 34.5, 40.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 81, 69, 81, 0, 0, 69, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-34.5, -40.5, 0], "maxPos": [34.5, 40.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "bd28364a-3b78-4972-911b-7e92347e5a4b@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "bd28364a-3b78-4972-911b-7e92347e5a4b@6c48a"}}