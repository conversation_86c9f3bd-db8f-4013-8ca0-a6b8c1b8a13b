{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "48fd8241-699a-49d1-8036-962e0445e1eb", "files": [".json", ".png"], "subMetas": {"Monster_020_Attack_01": {"ver": "1.0.6", "uuid": "9f18df92-3fcc-4efa-91d2-87a1d8ea7f9d", "importer": "sprite-frame", "rawTextureUuid": "48fd8241-699a-49d1-8036-962e0445e1eb", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 49, "height": 52, "rawWidth": 49, "rawHeight": 52, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "48fd8241-699a-49d1-8036-962e0445e1eb@6c48a", "displayName": "Monster_020_Attack_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "48fd8241-699a-49d1-8036-962e0445e1eb", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "48fd8241-699a-49d1-8036-962e0445e1eb@f9941", "displayName": "Monster_020_Attack_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 49, "height": 52, "rawWidth": 49, "rawHeight": 52, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-24.5, -26, 0, 24.5, -26, 0, -24.5, 26, 0, 24.5, 26, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 52, 49, 52, 0, 0, 49, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-24.5, -26, 0], "maxPos": [24.5, 26, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "48fd8241-699a-49d1-8036-962e0445e1eb@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "48fd8241-699a-49d1-8036-962e0445e1eb@6c48a"}}