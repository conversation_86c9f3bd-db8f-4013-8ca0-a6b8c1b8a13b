{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "a774d653-a085-4dab-ab87-c7fb25619933", "files": [".json", ".png"], "subMetas": {"building_mint_shop": {"ver": "1.0.6", "uuid": "edb211c4-7c35-4425-b8e4-8c5b5009ff01", "importer": "sprite-frame", "rawTextureUuid": "a774d653-a085-4dab-ab87-c7fb25619933", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 360, "height": 246, "rawWidth": 360, "rawHeight": 246, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "a774d653-a085-4dab-ab87-c7fb25619933@6c48a", "displayName": "building_mint_shop", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "a774d653-a085-4dab-ab87-c7fb25619933", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "a774d653-a085-4dab-ab87-c7fb25619933@f9941", "displayName": "building_mint_shop", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 360, "height": 246, "rawWidth": 360, "rawHeight": 246, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-180, -123, 0, 180, -123, 0, -180, 123, 0, 180, 123, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 246, 360, 246, 0, 0, 360, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-180, -123, 0], "maxPos": [180, 123, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "a774d653-a085-4dab-ab87-c7fb25619933@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "a774d653-a085-4dab-ab87-c7fb25619933@6c48a"}}