{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "da13a83b-5981-42f9-a302-0ef608baf7ab", "files": [".json", ".png"], "subMetas": {"Monster_102_Attack_01": {"ver": "1.0.6", "uuid": "a33c5287-7c99-4589-bdca-b5abeec6ce0a", "importer": "sprite-frame", "rawTextureUuid": "da13a83b-5981-42f9-a302-0ef608baf7ab", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 80, "height": 79, "rawWidth": 80, "rawHeight": 79, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "da13a83b-5981-42f9-a302-0ef608baf7ab@6c48a", "displayName": "Monster_102_Attack_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "da13a83b-5981-42f9-a302-0ef608baf7ab", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "da13a83b-5981-42f9-a302-0ef608baf7ab@f9941", "displayName": "Monster_102_Attack_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 80, "height": 79, "rawWidth": 80, "rawHeight": 79, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-40, -39.5, 0, 40, -39.5, 0, -40, 39.5, 0, 40, 39.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 79, 80, 79, 0, 0, 80, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-40, -39.5, 0], "maxPos": [40, 39.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "da13a83b-5981-42f9-a302-0ef608baf7ab@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "da13a83b-5981-42f9-a302-0ef608baf7ab@6c48a"}}