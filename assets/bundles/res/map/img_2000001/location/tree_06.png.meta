{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "babf8e50-1ec4-419c-84df-70ece1f64eb5", "files": [".json", ".png"], "subMetas": {"tree_06": {"ver": "1.0.6", "uuid": "2c3249f1-c9ac-4cc7-9819-643066c9ef07", "importer": "sprite-frame", "rawTextureUuid": "babf8e50-1ec4-419c-84df-70ece1f64eb5", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 91, "height": 170, "rawWidth": 91, "rawHeight": 170, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "babf8e50-1ec4-419c-84df-70ece1f64eb5@6c48a", "displayName": "tree_06", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "babf8e50-1ec4-419c-84df-70ece1f64eb5", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "babf8e50-1ec4-419c-84df-70ece1f64eb5@f9941", "displayName": "tree_06", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 91, "height": 170, "rawWidth": 91, "rawHeight": 170, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-45.5, -85, 0, 45.5, -85, 0, -45.5, 85, 0, 45.5, 85, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 170, 91, 170, 0, 0, 91, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-45.5, -85, 0], "maxPos": [45.5, 85, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "babf8e50-1ec4-419c-84df-70ece1f64eb5@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "babf8e50-1ec4-419c-84df-70ece1f64eb5@6c48a"}}