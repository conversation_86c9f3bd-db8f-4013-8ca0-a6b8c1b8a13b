{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "ec933fe5-f632-49e2-8bc2-4eb0dd9881d2", "files": [".json", ".png"], "subMetas": {"Monster_017_Attack_01": {"ver": "1.0.6", "uuid": "ba2e7205-12c0-42aa-84b4-f0603a59817a", "importer": "sprite-frame", "rawTextureUuid": "ec933fe5-f632-49e2-8bc2-4eb0dd9881d2", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 81, "height": 81, "rawWidth": 81, "rawHeight": 81, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "ec933fe5-f632-49e2-8bc2-4eb0dd9881d2@6c48a", "displayName": "Monster_017_Attack_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "ec933fe5-f632-49e2-8bc2-4eb0dd9881d2", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "ec933fe5-f632-49e2-8bc2-4eb0dd9881d2@f9941", "displayName": "Monster_017_Attack_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 81, "height": 81, "rawWidth": 81, "rawHeight": 81, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-40.5, -40.5, 0, 40.5, -40.5, 0, -40.5, 40.5, 0, 40.5, 40.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 81, 81, 81, 0, 0, 81, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-40.5, -40.5, 0], "maxPos": [40.5, 40.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "ec933fe5-f632-49e2-8bc2-4eb0dd9881d2@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "ec933fe5-f632-49e2-8bc2-4eb0dd9881d2@6c48a"}}