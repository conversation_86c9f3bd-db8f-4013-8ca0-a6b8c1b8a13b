{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "bc36bd1b-8cd6-4a56-bff0-f1e7d1477819", "files": [".json", ".png"], "subMetas": {"building_psychic_pulse": {"ver": "1.0.6", "uuid": "70320b09-13c0-4b05-9a9f-fa72a8dac8e1", "importer": "sprite-frame", "rawTextureUuid": "bc36bd1b-8cd6-4a56-bff0-f1e7d1477819", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 285, "height": 293, "rawWidth": 285, "rawHeight": 293, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "bc36bd1b-8cd6-4a56-bff0-f1e7d1477819@6c48a", "displayName": "building_psychic_pulse", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "bc36bd1b-8cd6-4a56-bff0-f1e7d1477819", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "bc36bd1b-8cd6-4a56-bff0-f1e7d1477819@f9941", "displayName": "building_psychic_pulse", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 285, "height": 293, "rawWidth": 285, "rawHeight": 293, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-142.5, -146.5, 0, 142.5, -146.5, 0, -142.5, 146.5, 0, 142.5, 146.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 293, 285, 293, 0, 0, 285, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-142.5, -146.5, 0], "maxPos": [142.5, 146.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "bc36bd1b-8cd6-4a56-bff0-f1e7d1477819@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "bc36bd1b-8cd6-4a56-bff0-f1e7d1477819@6c48a"}}