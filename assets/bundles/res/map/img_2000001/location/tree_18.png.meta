{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "47fd5168-1354-4403-9d8c-36d24989110d", "files": [".json", ".png"], "subMetas": {"tree_18": {"ver": "1.0.6", "uuid": "a88071ea-3f33-45a7-9552-5ffa43be39d9", "importer": "sprite-frame", "rawTextureUuid": "47fd5168-1354-4403-9d8c-36d24989110d", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 114, "height": 201, "rawWidth": 114, "rawHeight": 201, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "47fd5168-1354-4403-9d8c-36d24989110d@6c48a", "displayName": "tree_18", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "47fd5168-1354-4403-9d8c-36d24989110d", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "47fd5168-1354-4403-9d8c-36d24989110d@f9941", "displayName": "tree_18", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 114, "height": 201, "rawWidth": 114, "rawHeight": 201, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-57, -100.5, 0, 57, -100.5, 0, -57, 100.5, 0, 57, 100.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 201, 114, 201, 0, 0, 114, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-57, -100.5, 0], "maxPos": [57, 100.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "47fd5168-1354-4403-9d8c-36d24989110d@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "47fd5168-1354-4403-9d8c-36d24989110d@6c48a"}}