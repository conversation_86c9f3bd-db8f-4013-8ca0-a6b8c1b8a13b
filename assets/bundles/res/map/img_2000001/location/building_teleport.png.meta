{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "c7bc5972-9770-43bb-b5a6-bc39e70259c6", "files": [".json", ".png"], "subMetas": {"building_teleport": {"ver": "1.0.6", "uuid": "ebabb0ea-7804-466f-8e09-13a043152781", "importer": "sprite-frame", "rawTextureUuid": "c7bc5972-9770-43bb-b5a6-bc39e70259c6", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 162, "height": 130, "rawWidth": 162, "rawHeight": 130, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "c7bc5972-9770-43bb-b5a6-bc39e70259c6@6c48a", "displayName": "building_teleport", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "c7bc5972-9770-43bb-b5a6-bc39e70259c6", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "c7bc5972-9770-43bb-b5a6-bc39e70259c6@f9941", "displayName": "building_teleport", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 162, "height": 130, "rawWidth": 162, "rawHeight": 130, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-81, -65, 0, 81, -65, 0, -81, 65, 0, 81, 65, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 130, 162, 130, 0, 0, 162, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-81, -65, 0], "maxPos": [81, 65, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "c7bc5972-9770-43bb-b5a6-bc39e70259c6@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "c7bc5972-9770-43bb-b5a6-bc39e70259c6@6c48a"}}