{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "46fa5aa6-12e1-4141-b1ee-a5a8b335411e", "files": [".json", ".png"], "subMetas": {"building_lumberyard": {"ver": "1.0.6", "uuid": "0e050012-f898-490e-bd2c-d250600bad9b", "importer": "sprite-frame", "rawTextureUuid": "46fa5aa6-12e1-4141-b1ee-a5a8b335411e", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 365, "height": 287, "rawWidth": 365, "rawHeight": 287, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "46fa5aa6-12e1-4141-b1ee-a5a8b335411e@6c48a", "displayName": "building_lumberyard", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "46fa5aa6-12e1-4141-b1ee-a5a8b335411e", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "46fa5aa6-12e1-4141-b1ee-a5a8b335411e@f9941", "displayName": "building_lumberyard", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 365, "height": 287, "rawWidth": 365, "rawHeight": 287, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-182.5, -143.5, 0, 182.5, -143.5, 0, -182.5, 143.5, 0, 182.5, 143.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 287, 365, 287, 0, 0, 365, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-182.5, -143.5, 0], "maxPos": [182.5, 143.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "46fa5aa6-12e1-4141-b1ee-a5a8b335411e@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "46fa5aa6-12e1-4141-b1ee-a5a8b335411e@6c48a"}}