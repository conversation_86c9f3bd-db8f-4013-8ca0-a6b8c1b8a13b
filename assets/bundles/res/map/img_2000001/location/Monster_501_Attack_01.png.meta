{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "d90aa755-1c17-4752-9cc8-d600513a53d0", "files": [".json", ".png"], "subMetas": {"Monster_501_Attack_01": {"ver": "1.0.6", "uuid": "8882e355-b2c6-4ce7-90ec-8a6ec908101e", "importer": "sprite-frame", "rawTextureUuid": "d90aa755-1c17-4752-9cc8-d600513a53d0", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 185, "height": 193, "rawWidth": 185, "rawHeight": 193, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "d90aa755-1c17-4752-9cc8-d600513a53d0@6c48a", "displayName": "Monster_501_Attack_01", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "d90aa755-1c17-4752-9cc8-d600513a53d0", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "d90aa755-1c17-4752-9cc8-d600513a53d0@f9941", "displayName": "Monster_501_Attack_01", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 185, "height": 193, "rawWidth": 185, "rawHeight": 193, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-92.5, -96.5, 0, 92.5, -96.5, 0, -92.5, 96.5, 0, 92.5, 96.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 193, 185, 193, 0, 0, 185, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-92.5, -96.5, 0], "maxPos": [92.5, 96.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "d90aa755-1c17-4752-9cc8-d600513a53d0@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "d90aa755-1c17-4752-9cc8-d600513a53d0@6c48a"}}