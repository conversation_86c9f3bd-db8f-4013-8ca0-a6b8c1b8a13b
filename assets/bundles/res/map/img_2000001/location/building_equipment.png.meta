{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "dff66d84-bf09-40c9-b7a4-cc19fd7102eb", "files": [".json", ".png"], "subMetas": {"building_equipment": {"ver": "1.0.6", "uuid": "33666e5f-96fd-40a1-b100-9f0dfebb3e90", "importer": "sprite-frame", "rawTextureUuid": "dff66d84-bf09-40c9-b7a4-cc19fd7102eb", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 482, "height": 461, "rawWidth": 482, "rawHeight": 461, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "dff66d84-bf09-40c9-b7a4-cc19fd7102eb@6c48a", "displayName": "building_equipment", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "dff66d84-bf09-40c9-b7a4-cc19fd7102eb", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "dff66d84-bf09-40c9-b7a4-cc19fd7102eb@f9941", "displayName": "building_equipment", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 482, "height": 461, "rawWidth": 482, "rawHeight": 461, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-241, -230.5, 0, 241, -230.5, 0, -241, 230.5, 0, 241, 230.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 461, 482, 461, 0, 0, 482, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-241, -230.5, 0], "maxPos": [241, 230.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "dff66d84-bf09-40c9-b7a4-cc19fd7102eb@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "dff66d84-bf09-40c9-b7a4-cc19fd7102eb@6c48a"}}