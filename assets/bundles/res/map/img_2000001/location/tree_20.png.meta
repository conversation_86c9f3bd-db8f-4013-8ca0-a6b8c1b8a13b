{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "7d9c2465-8794-41be-8e20-90e995b883b1", "files": [".json", ".png"], "subMetas": {"tree_20": {"ver": "1.0.6", "uuid": "196e8b7b-0676-4b89-8233-9f6ca952fc00", "importer": "sprite-frame", "rawTextureUuid": "7d9c2465-8794-41be-8e20-90e995b883b1", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 143, "height": 216, "rawWidth": 143, "rawHeight": 216, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "7d9c2465-8794-41be-8e20-90e995b883b1@6c48a", "displayName": "tree_20", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "7d9c2465-8794-41be-8e20-90e995b883b1", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "7d9c2465-8794-41be-8e20-90e995b883b1@f9941", "displayName": "tree_20", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 143, "height": 216, "rawWidth": 143, "rawHeight": 216, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-71.5, -108, 0, 71.5, -108, 0, -71.5, 108, 0, 71.5, 108, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 216, 143, 216, 0, 0, 143, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-71.5, -108, 0], "maxPos": [71.5, 108, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "7d9c2465-8794-41be-8e20-90e995b883b1@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "7d9c2465-8794-41be-8e20-90e995b883b1@6c48a"}}