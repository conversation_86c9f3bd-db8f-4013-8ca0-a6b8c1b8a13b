{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "318c4b8e-851d-4713-8dc9-a8a373e34387", "files": [".json", ".png"], "subMetas": {"bamboo_002": {"ver": "1.0.6", "uuid": "2d9dac22-27ab-47b8-96f7-4637442fc447", "importer": "sprite-frame", "rawTextureUuid": "318c4b8e-851d-4713-8dc9-a8a373e34387", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 240, "height": 245, "rawWidth": 240, "rawHeight": 245, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "318c4b8e-851d-4713-8dc9-a8a373e34387@6c48a", "displayName": "bamboo_002", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "318c4b8e-851d-4713-8dc9-a8a373e34387", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "318c4b8e-851d-4713-8dc9-a8a373e34387@f9941", "displayName": "bamboo_002", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 240, "height": 245, "rawWidth": 240, "rawHeight": 245, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-120, -122.5, 0, 120, -122.5, 0, -120, 122.5, 0, 120, 122.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 245, 240, 245, 0, 0, 240, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-120, -122.5, 0], "maxPos": [120, 122.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "318c4b8e-851d-4713-8dc9-a8a373e34387@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "318c4b8e-851d-4713-8dc9-a8a373e34387@6c48a"}}