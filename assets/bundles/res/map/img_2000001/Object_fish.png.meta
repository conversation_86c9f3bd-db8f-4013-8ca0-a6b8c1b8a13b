{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "686ea81c-b32f-4cab-b605-5eeb69d4b095", "files": [".json", ".png"], "subMetas": {"Object_fish": {"ver": "1.0.6", "uuid": "4f29032b-d86a-4abb-8fb7-3974e2ee60c2", "importer": "sprite-frame", "rawTextureUuid": "686ea81c-b32f-4cab-b605-5eeb69d4b095", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 166, "height": 115, "rawWidth": 166, "rawHeight": 115, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "686ea81c-b32f-4cab-b605-5eeb69d4b095@6c48a", "displayName": "Object_fish", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "686ea81c-b32f-4cab-b605-5eeb69d4b095", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "686ea81c-b32f-4cab-b605-5eeb69d4b095@f9941", "displayName": "Object_fish", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 166, "height": 115, "rawWidth": 166, "rawHeight": 115, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-83, -57.5, 0, 83, -57.5, 0, -83, 57.5, 0, 83, 57.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 115, 166, 115, 0, 0, 166, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-83, -57.5, 0], "maxPos": [83, 57.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "686ea81c-b32f-4cab-b605-5eeb69d4b095@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"wrapModeS": "repeat", "wrapModeT": "repeat", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "686ea81c-b32f-4cab-b605-5eeb69d4b095@6c48a"}}