import { _decorator, Component, Button, Event } from "cc";
import * as cc from "cc";
import { default as pb } from "./Proto/pb.js";
console.log(pb)

const { ccclass, property } = _decorator;

@ccclass("Test")
export class Test extends Component {

    protected onLoad(): void {
        console.log('in test')
        this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
    }

    public onTouchStart() {
        console.log("onTouchStart");
    }

    public onClick() {
        console.log("onClick");
    }
}