// DO NOT EDIT! This is a generated file. Edit the JSDoc in src/*.js instead and run 'npm run build:types'.

export = pb;

declare namespace pb {


    namespace pb {

        interface IEnterBattleRequest {
            battleType?: (number|null);
            mapId?: (number|null);
            mainHero?: (number|null);
            battleParams?: (Uint8Array|null);
            posList?: (pb.PbFormationPos[]|null);
            type?: (number|null);
        }

        class EnterBattleRequest implements IEnterBattleRequest {
            constructor(p?: pb.IEnterBattleRequest);
            public battleType: number;
            public mapId: number;
            public mainHero: number;
            public battleParams: Uint8Array;
            public posList: pb.PbFormationPos[];
            public type: number;
            public static create(properties?: pb.IEnterBattleRequest): pb.EnterBattleRequest;
            public static encode(m: pb.EnterBattleRequest, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.EnterBattleRequest;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace EnterBattleRequest {

            enum Proto {
                UNKNOWN = 0,
                ID = 300001
            }
        }

        interface IEnterBattleResponse {
            battleType?: (number|null);
            mapId?: (number|null);
            heroes?: (pb.PbSprite[]|null);
            mainHero?: (number|null);
            battleParams?: (Uint8Array|null);
            type?: (number|null);
        }

        class EnterBattleResponse implements IEnterBattleResponse {
            constructor(p?: pb.IEnterBattleResponse);
            public battleType: number;
            public mapId: number;
            public heroes: pb.PbSprite[];
            public mainHero: number;
            public battleParams: Uint8Array;
            public type: number;
            public static create(properties?: pb.IEnterBattleResponse): pb.EnterBattleResponse;
            public static encode(m: pb.EnterBattleResponse, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.EnterBattleResponse;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace EnterBattleResponse {

            enum Proto {
                UNKNOWN = 0,
                ID = 300001
            }
        }

        interface IEnterZoneRequest {
            zoneId?: (number|null);
        }

        class EnterZoneRequest implements IEnterZoneRequest {
            constructor(p?: pb.IEnterZoneRequest);
            public zoneId: number;
            public static create(properties?: pb.IEnterZoneRequest): pb.EnterZoneRequest;
            public static encode(m: pb.EnterZoneRequest, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.EnterZoneRequest;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace EnterZoneRequest {

            enum Proto {
                UNKNOWN = 0,
                ID = 300002
            }
        }

        interface IEnterZoneResponse {
            zoneId?: (number|null);
        }

        class EnterZoneResponse implements IEnterZoneResponse {
            constructor(p?: pb.IEnterZoneResponse);
            public zoneId: number;
            public static create(properties?: pb.IEnterZoneResponse): pb.EnterZoneResponse;
            public static encode(m: pb.EnterZoneResponse, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.EnterZoneResponse;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace EnterZoneResponse {

            enum Proto {
                UNKNOWN = 0,
                ID = 300002
            }
        }

        interface IZoneInfoRequest {
            zoneId?: (number|null);
        }

        class ZoneInfoRequest implements IZoneInfoRequest {
            constructor(p?: pb.IZoneInfoRequest);
            public zoneId: number;
            public static create(properties?: pb.IZoneInfoRequest): pb.ZoneInfoRequest;
            public static encode(m: pb.ZoneInfoRequest, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.ZoneInfoRequest;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace ZoneInfoRequest {

            enum Proto {
                UNKNOWN = 0,
                ID = 300003
            }
        }

        interface IZoneInfoResponse {
            zone?: (pb.PbZone|null);
        }

        class ZoneInfoResponse implements IZoneInfoResponse {
            constructor(p?: pb.IZoneInfoResponse);
            public zone?: (pb.PbZone|null);
            public static create(properties?: pb.IZoneInfoResponse): pb.ZoneInfoResponse;
            public static encode(m: pb.ZoneInfoResponse, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.ZoneInfoResponse;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace ZoneInfoResponse {

            enum Proto {
                UNKNOWN = 0,
                ID = 300003
            }
        }

        interface IBattleReviveRequest {
        }

        class BattleReviveRequest implements IBattleReviveRequest {
            constructor(p?: pb.IBattleReviveRequest);
            public static create(properties?: pb.IBattleReviveRequest): pb.BattleReviveRequest;
            public static encode(m: pb.BattleReviveRequest, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.BattleReviveRequest;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace BattleReviveRequest {

            enum Proto {
                UNKNOWN = 0,
                ID = 300006
            }
        }

        interface IBattleReviveResponse {
            heroes?: (pb.PbSprite[]|null);
        }

        class BattleReviveResponse implements IBattleReviveResponse {
            constructor(p?: pb.IBattleReviveResponse);
            public heroes: pb.PbSprite[];
            public static create(properties?: pb.IBattleReviveResponse): pb.BattleReviveResponse;
            public static encode(m: pb.BattleReviveResponse, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.BattleReviveResponse;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace BattleReviveResponse {

            enum Proto {
                UNKNOWN = 0,
                ID = 300006
            }
        }

        interface IBattleGiveUpRequest {
        }

        class BattleGiveUpRequest implements IBattleGiveUpRequest {
            constructor(p?: pb.IBattleGiveUpRequest);
            public static create(properties?: pb.IBattleGiveUpRequest): pb.BattleGiveUpRequest;
            public static encode(m: pb.BattleGiveUpRequest, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.BattleGiveUpRequest;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace BattleGiveUpRequest {

            enum Proto {
                UNKNOWN = 0,
                ID = 300007
            }
        }

        interface IBattleGiveUpResponse {
        }

        class BattleGiveUpResponse implements IBattleGiveUpResponse {
            constructor(p?: pb.IBattleGiveUpResponse);
            public static create(properties?: pb.IBattleGiveUpResponse): pb.BattleGiveUpResponse;
            public static encode(m: pb.BattleGiveUpResponse, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.BattleGiveUpResponse;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace BattleGiveUpResponse {

            enum Proto {
                UNKNOWN = 0,
                ID = 300007
            }
        }

        interface IBattleKillSpriteRequest {
            spriteIds?: (number[]|null);
            serialId?: (number|null);
        }

        class BattleKillSpriteRequest implements IBattleKillSpriteRequest {
            constructor(p?: pb.IBattleKillSpriteRequest);
            public spriteIds: number[];
            public serialId: number;
            public static create(properties?: pb.IBattleKillSpriteRequest): pb.BattleKillSpriteRequest;
            public static encode(m: pb.BattleKillSpriteRequest, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.BattleKillSpriteRequest;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace BattleKillSpriteRequest {

            enum Proto {
                UNKNOWN = 0,
                ID = 300008
            }
        }

        interface IBattleKillSpriteResponse {
            sprites?: (pb.PbKillSprite[]|null);
            serialId?: (number|null);
            endData?: (pb.PbBattleEndData|null);
        }

        class BattleKillSpriteResponse implements IBattleKillSpriteResponse {
            constructor(p?: pb.IBattleKillSpriteResponse);
            public sprites: pb.PbKillSprite[];
            public serialId: number;
            public endData?: (pb.PbBattleEndData|null);
            public static create(properties?: pb.IBattleKillSpriteResponse): pb.BattleKillSpriteResponse;
            public static encode(m: pb.BattleKillSpriteResponse, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.BattleKillSpriteResponse;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace BattleKillSpriteResponse {

            enum Proto {
                UNKNOWN = 0,
                ID = 300008
            }
        }

        interface IBattleSyncKillMonsterRequest {
            killMonsters?: (pb.PbKeyV[]|null);
        }

        class BattleSyncKillMonsterRequest implements IBattleSyncKillMonsterRequest {
            constructor(p?: pb.IBattleSyncKillMonsterRequest);
            public killMonsters: pb.PbKeyV[];
            public static create(properties?: pb.IBattleSyncKillMonsterRequest): pb.BattleSyncKillMonsterRequest;
            public static encode(m: pb.BattleSyncKillMonsterRequest, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.BattleSyncKillMonsterRequest;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace BattleSyncKillMonsterRequest {

            enum Proto {
                UNKNOWN = 0,
                ID = 300009
            }
        }

        interface IBattleSyncKillMonsterResponse {
        }

        class BattleSyncKillMonsterResponse implements IBattleSyncKillMonsterResponse {
            constructor(p?: pb.IBattleSyncKillMonsterResponse);
            public static create(properties?: pb.IBattleSyncKillMonsterResponse): pb.BattleSyncKillMonsterResponse;
            public static encode(m: pb.BattleSyncKillMonsterResponse, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.BattleSyncKillMonsterResponse;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace BattleSyncKillMonsterResponse {

            enum Proto {
                UNKNOWN = 0,
                ID = 300009
            }
        }

        interface IBattleRebornSpriteRequest {
            spriteIds?: (number[]|null);
        }

        class BattleRebornSpriteRequest implements IBattleRebornSpriteRequest {
            constructor(p?: pb.IBattleRebornSpriteRequest);
            public spriteIds: number[];
            public static create(properties?: pb.IBattleRebornSpriteRequest): pb.BattleRebornSpriteRequest;
            public static encode(m: pb.BattleRebornSpriteRequest, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.BattleRebornSpriteRequest;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace BattleRebornSpriteRequest {

            enum Proto {
                UNKNOWN = 0,
                ID = 300011
            }
        }

        interface IBattleRebornSpriteResponse {
            sprites?: (pb.PbSprite[]|null);
        }

        class BattleRebornSpriteResponse implements IBattleRebornSpriteResponse {
            constructor(p?: pb.IBattleRebornSpriteResponse);
            public sprites: pb.PbSprite[];
            public static create(properties?: pb.IBattleRebornSpriteResponse): pb.BattleRebornSpriteResponse;
            public static encode(m: pb.BattleRebornSpriteResponse, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.BattleRebornSpriteResponse;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace BattleRebornSpriteResponse {

            enum Proto {
                UNKNOWN = 0,
                ID = 300011
            }
        }

        interface IBattleSettleRequest {
        }

        class BattleSettleRequest implements IBattleSettleRequest {
            constructor(p?: pb.IBattleSettleRequest);
            public static create(properties?: pb.IBattleSettleRequest): pb.BattleSettleRequest;
            public static encode(m: pb.BattleSettleRequest, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.BattleSettleRequest;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace BattleSettleRequest {

            enum Proto {
                UNKNOWN = 0,
                ID = 300012
            }
        }

        interface IBattleSettleResponse {
            endData?: (pb.PbBattleEndData|null);
        }

        class BattleSettleResponse implements IBattleSettleResponse {
            constructor(p?: pb.IBattleSettleResponse);
            public endData?: (pb.PbBattleEndData|null);
            public static create(properties?: pb.IBattleSettleResponse): pb.BattleSettleResponse;
            public static encode(m: pb.BattleSettleResponse, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.BattleSettleResponse;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace BattleSettleResponse {

            enum Proto {
                UNKNOWN = 0,
                ID = 300012
            }
        }

        interface IBattleSpawnMonsterGmMessage {
            battleType?: (number|null);
            sprite?: (pb.PbSprite|null);
        }

        class BattleSpawnMonsterGmMessage implements IBattleSpawnMonsterGmMessage {
            constructor(p?: pb.IBattleSpawnMonsterGmMessage);
            public battleType: number;
            public sprite?: (pb.PbSprite|null);
            public static create(properties?: pb.IBattleSpawnMonsterGmMessage): pb.BattleSpawnMonsterGmMessage;
            public static encode(m: pb.BattleSpawnMonsterGmMessage, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.BattleSpawnMonsterGmMessage;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace BattleSpawnMonsterGmMessage {

            enum Proto {
                UNKNOWN = 0,
                ID = 300013
            }
        }

        interface ISwitchBattleHeroRequest {
            mainHero?: (number|null);
            posList?: (pb.PbFormationPos[]|null);
        }

        class SwitchBattleHeroRequest implements ISwitchBattleHeroRequest {
            constructor(p?: pb.ISwitchBattleHeroRequest);
            public mainHero: number;
            public posList: pb.PbFormationPos[];
            public static create(properties?: pb.ISwitchBattleHeroRequest): pb.SwitchBattleHeroRequest;
            public static encode(m: pb.SwitchBattleHeroRequest, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.SwitchBattleHeroRequest;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace SwitchBattleHeroRequest {

            enum Proto {
                UNKNOWN = 0,
                ID = 300101
            }
        }

        interface ISwitchBattleHeroResponse {
            heroes?: (pb.PbSprite[]|null);
            mainHero?: (number|null);
        }

        class SwitchBattleHeroResponse implements ISwitchBattleHeroResponse {
            constructor(p?: pb.ISwitchBattleHeroResponse);
            public heroes: pb.PbSprite[];
            public mainHero: number;
            public static create(properties?: pb.ISwitchBattleHeroResponse): pb.SwitchBattleHeroResponse;
            public static encode(m: pb.SwitchBattleHeroResponse, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.SwitchBattleHeroResponse;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace SwitchBattleHeroResponse {

            enum Proto {
                UNKNOWN = 0,
                ID = 300101
            }
        }

        interface IPbTreasureEvent {
            itemId?: (number|null);
            num?: (Long|null);
            upItemId?: (number|null);
            upNum?: (Long|null);
            disappearTime?: (Long|null);
        }

        class PbTreasureEvent implements IPbTreasureEvent {
            constructor(p?: pb.IPbTreasureEvent);
            public itemId: number;
            public num: Long;
            public upItemId: number;
            public upNum: Long;
            public disappearTime: Long;
            public static create(properties?: pb.IPbTreasureEvent): pb.PbTreasureEvent;
            public static encode(m: pb.PbTreasureEvent, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.PbTreasureEvent;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        interface IPbSprite {
            id?: (number|null);
            type?: (pb.PbSpriteType|null);
            identity?: (number|null);
            level?: (number|null);
            rebornTime?: (Long|null);
            ctype?: (number|null);
            cx?: (number|null);
            cy?: (number|null);
            battleAttr?: (pb.PbBattleAttr|null);
            configId?: (number|null);
            fogArea?: (number|null);
        }

        class PbSprite implements IPbSprite {
            constructor(p?: pb.IPbSprite);
            public id: number;
            public type: pb.PbSpriteType;
            public identity: number;
            public level: number;
            public rebornTime: Long;
            public ctype: number;
            public cx: number;
            public cy: number;
            public battleAttr?: (pb.PbBattleAttr|null);
            public configId: number;
            public fogArea: number;
            public static create(properties?: pb.IPbSprite): pb.PbSprite;
            public static encode(m: pb.PbSprite, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.PbSprite;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        interface IPbKillSprite {
            id?: (number|null);
            rebornTime?: (Long|null);
            drops?: (pb.PbItem[]|null);
            hasBox?: (boolean|null);
            book?: (number|null);
        }

        class PbKillSprite implements IPbKillSprite {
            constructor(p?: pb.IPbKillSprite);
            public id: number;
            public rebornTime: Long;
            public drops: pb.PbItem[];
            public hasBox: boolean;
            public book: number;
            public static create(properties?: pb.IPbKillSprite): pb.PbKillSprite;
            public static encode(m: pb.PbKillSprite, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.PbKillSprite;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        enum PbSpriteType {
            UNKNOWN_SPRITE_TYPE = 0,
            HERO = 1,
            MONSTER = 2
        }

        interface IPbZone {
            zoneId?: (number|null);
            sprites?: (pb.PbSprite[]|null);
            harvests?: (pb.PbHarvest[]|null);
            aidSprite?: (pb.PbSprite|null);
            removeSpriteId?: (number|null);
        }

        class PbZone implements IPbZone {
            constructor(p?: pb.IPbZone);
            public zoneId: number;
            public sprites: pb.PbSprite[];
            public harvests: pb.PbHarvest[];
            public aidSprite?: (pb.PbSprite|null);
            public removeSpriteId: number;
            public static create(properties?: pb.IPbZone): pb.PbZone;
            public static encode(m: pb.PbZone, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.PbZone;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        interface IPbHarvest {
            id?: (number|null);
            identity?: (number|null);
            rebornTime?: (Long|null);
            ctype?: (number|null);
            cx?: (number|null);
            cy?: (number|null);
            fogArea?: (number|null);
        }

        class PbHarvest implements IPbHarvest {
            constructor(p?: pb.IPbHarvest);
            public id: number;
            public identity: number;
            public rebornTime: Long;
            public ctype: number;
            public cx: number;
            public cy: number;
            public fogArea: number;
            public static create(properties?: pb.IPbHarvest): pb.PbHarvest;
            public static encode(m: pb.PbHarvest, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.PbHarvest;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        enum PbBattleEndStatus {
            NO_STATUS = 0,
            BATTLING = 1,
            WAIT_REVIVE = 2,
            ATK_ALL_DEAD_CAMPING = 3,
            DEF_ALL_DEAD_NEXT = 4,
            WIN = 5,
            FAIL = 6
        }

        interface IPbBattleEndData {
            status?: (pb.PbBattleEndStatus|null);
            data?: (Uint8Array|null);
        }

        class PbBattleEndData implements IPbBattleEndData {
            constructor(p?: pb.IPbBattleEndData);
            public status: pb.PbBattleEndStatus;
            public data: Uint8Array;
            public static create(properties?: pb.IPbBattleEndData): pb.PbBattleEndData;
            public static encode(m: pb.PbBattleEndData, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.PbBattleEndData;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        interface IPbThing {
            identity?: (number|null);
            num?: (Long|null);
            data?: (Uint8Array|null);
        }

        class PbThing implements IPbThing {
            constructor(p?: pb.IPbThing);
            public identity: number;
            public num: Long;
            public data: Uint8Array;
            public static create(properties?: pb.IPbThing): pb.PbThing;
            public static encode(m: pb.PbThing, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.PbThing;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        interface IPbThingReceipt {
            things?: (pb.PbThing[]|null);
        }

        class PbThingReceipt implements IPbThingReceipt {
            constructor(p?: pb.IPbThingReceipt);
            public things: pb.PbThing[];
            public static create(properties?: pb.IPbThingReceipt): pb.PbThingReceipt;
            public static encode(m: pb.PbThingReceipt, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.PbThingReceipt;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        interface IPbItem {
            item?: (number|null);
            num?: (Long|null);
        }

        class PbItem implements IPbItem {
            constructor(p?: pb.IPbItem);
            public item: number;
            public num: Long;
            public static create(properties?: pb.IPbItem): pb.PbItem;
            public static encode(m: pb.PbItem, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.PbItem;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        interface IPbTask {
            id?: (number|null);
            progress?: (Long|null);
            status?: (number|null);
        }

        class PbTask implements IPbTask {
            constructor(p?: pb.IPbTask);
            public id: number;
            public progress: Long;
            public status: number;
            public static create(properties?: pb.IPbTask): pb.PbTask;
            public static encode(m: pb.PbTask, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.PbTask;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        interface IPbPlayerExp {
            oldLevel?: (number|null);
            newLevel?: (number|null);
            openFunctions?: (number[]|null);
            currExp?: (Long|null);
        }

        class PbPlayerExp implements IPbPlayerExp {
            constructor(p?: pb.IPbPlayerExp);
            public oldLevel: number;
            public newLevel: number;
            public openFunctions: number[];
            public currExp: Long;
            public static create(properties?: pb.IPbPlayerExp): pb.PbPlayerExp;
            public static encode(m: pb.PbPlayerExp, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.PbPlayerExp;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        interface IPbPlayerVipExp {
            oldLevel?: (number|null);
            newLevel?: (number|null);
            currExp?: (Long|null);
        }

        class PbPlayerVipExp implements IPbPlayerVipExp {
            constructor(p?: pb.IPbPlayerVipExp);
            public oldLevel: number;
            public newLevel: number;
            public currExp: Long;
            public static create(properties?: pb.IPbPlayerVipExp): pb.PbPlayerVipExp;
            public static encode(m: pb.PbPlayerVipExp, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.PbPlayerVipExp;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        interface IPbSceneOpening {
            id?: (number|null);
            pay?: (pb.PbItem[]|null);
        }

        class PbSceneOpening implements IPbSceneOpening {
            constructor(p?: pb.IPbSceneOpening);
            public id: number;
            public pay: pb.PbItem[];
            public static create(properties?: pb.IPbSceneOpening): pb.PbSceneOpening;
            public static encode(m: pb.PbSceneOpening, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.PbSceneOpening;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        interface IPbKeyV {
            key?: (number|null);
            value?: (number|null);
        }

        class PbKeyV implements IPbKeyV {
            constructor(p?: pb.IPbKeyV);
            public key: number;
            public value: number;
            public static create(properties?: pb.IPbKeyV): pb.PbKeyV;
            public static encode(m: pb.PbKeyV, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.PbKeyV;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        interface IPbAttr {
            attrId?: (number|null);
            value?: (Long|null);
        }

        class PbAttr implements IPbAttr {
            constructor(p?: pb.IPbAttr);
            public attrId: number;
            public value: Long;
            public static create(properties?: pb.IPbAttr): pb.PbAttr;
            public static encode(m: pb.PbAttr, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.PbAttr;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        interface IFormationInfoResponse {
            formations?: (pb.PbFormation[]|null);
        }

        class FormationInfoResponse implements IFormationInfoResponse {
            constructor(p?: pb.IFormationInfoResponse);
            public formations: pb.PbFormation[];
            public static create(properties?: pb.IFormationInfoResponse): pb.FormationInfoResponse;
            public static encode(m: pb.FormationInfoResponse, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.FormationInfoResponse;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace FormationInfoResponse {

            enum Proto {
                UNKNOWN = 0,
                ID = 1000001
            }
        }

        interface IFormationChangeMessage {
            formations?: (pb.PbFormation[]|null);
        }

        class FormationChangeMessage implements IFormationChangeMessage {
            constructor(p?: pb.IFormationChangeMessage);
            public formations: pb.PbFormation[];
            public static create(properties?: pb.IFormationChangeMessage): pb.FormationChangeMessage;
            public static encode(m: pb.FormationChangeMessage, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.FormationChangeMessage;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace FormationChangeMessage {

            enum Proto {
                UNKNOWN = 0,
                ID = 1000002
            }
        }

        interface IFormationSaveRequest {
            type?: (number|null);
            mainHero?: (number|null);
            posList?: (pb.PbFormationPos[]|null);
        }

        class FormationSaveRequest implements IFormationSaveRequest {
            constructor(p?: pb.IFormationSaveRequest);
            public type: number;
            public mainHero: number;
            public posList: pb.PbFormationPos[];
            public static create(properties?: pb.IFormationSaveRequest): pb.FormationSaveRequest;
            public static encode(m: pb.FormationSaveRequest, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.FormationSaveRequest;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace FormationSaveRequest {

            enum Proto {
                UNKNOWN = 0,
                ID = 1000003
            }
        }

        interface IFormationSaveResponse {
            formation?: (pb.PbFormation|null);
        }

        class FormationSaveResponse implements IFormationSaveResponse {
            constructor(p?: pb.IFormationSaveResponse);
            public formation?: (pb.PbFormation|null);
            public static create(properties?: pb.IFormationSaveResponse): pb.FormationSaveResponse;
            public static encode(m: pb.FormationSaveResponse, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.FormationSaveResponse;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace FormationSaveResponse {

            enum Proto {
                UNKNOWN = 0,
                ID = 1000003
            }
        }

        interface IPbFormation {
            type?: (number|null);
            mainHeroIdentity?: (number|null);
            posList?: (pb.PbFormationPos[]|null);
            fighting?: (Long|null);
            equipFighting?: (Long|null);
            manualChange?: (boolean|null);
        }

        class PbFormation implements IPbFormation {
            constructor(p?: pb.IPbFormation);
            public type: number;
            public mainHeroIdentity: number;
            public posList: pb.PbFormationPos[];
            public fighting: Long;
            public equipFighting: Long;
            public manualChange: boolean;
            public static create(properties?: pb.IPbFormation): pb.PbFormation;
            public static encode(m: pb.PbFormation, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.PbFormation;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        interface IPbFormationPos {
            pos?: (number|null);
            identity?: (number|null);
        }

        class PbFormationPos implements IPbFormationPos {
            constructor(p?: pb.IPbFormationPos);
            public pos: number;
            public identity: number;
            public static create(properties?: pb.IPbFormationPos): pb.PbFormationPos;
            public static encode(m: pb.PbFormationPos, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.PbFormationPos;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        enum PbFormationType {
            NO_FORMATION = 0,
            MAIN = 1,
            TOWER = 2,
            TOWER_STRENGTH = 3,
            TOWER_AGILITY = 4,
            TOWER_WISDOM = 5,
            GOLDEN_PIG = 6
        }

        interface IPbBattleAttr {
            hp?: (Long|null);
            maxHp?: (Long|null);
            atk?: (Long|null);
            def?: (Long|null);
            hpRate?: (Long|null);
            atkRate?: (Long|null);
            defRate?: (Long|null);
            dmgRate?: (Long|null);
            hit?: (Long|null);
            dodge?: (Long|null);
            crit?: (Long|null);
            critDmgRate?: (Long|null);
            vampireRate?: (Long|null);
            reduceDmg?: (Long|null);
            reduceDmgRate?: (Long|null);
            cdSpdRate?: (Long|null);
            moveSpd?: (Long|null);
            moveSpdRate?: (Long|null);
            atkSpd?: (Long|null);
            atkSpdRate?: (Long|null);
            dmgbl?: (Long|null);
            dmg?: (Long|null);
            llmaxRate?: (Long|null);
            lldmgRate?: (Long|null);
            mjmaxRate?: (Long|null);
            mjdmgRate?: (Long|null);
            zlmaxRate?: (Long|null);
            zldmgRate?: (Long|null);
            woodDmg?: (Long|null);
            mineDmg?: (Long|null);
            llmax?: (Long|null);
            lldmg?: (Long|null);
            mjmax?: (Long|null);
            mjdmg?: (Long|null);
            zlmax?: (Long|null);
            zldmg?: (Long|null);
            battleRange?: (Long|null);
            llpvedmg?: (Long|null);
            mjpvedmg?: (Long|null);
            zlpvedmg?: (Long|null);
            pvedmg?: (Long|null);
            fovRange?: (Long|null);
            reducecrit?: (Long|null);
            hpBrate?: (Long|null);
            atkBrate?: (Long|null);
        }

        class PbBattleAttr implements IPbBattleAttr {
            constructor(p?: pb.IPbBattleAttr);
            public hp: Long;
            public maxHp: Long;
            public atk: Long;
            public def: Long;
            public hpRate: Long;
            public atkRate: Long;
            public defRate: Long;
            public dmgRate: Long;
            public hit: Long;
            public dodge: Long;
            public crit: Long;
            public critDmgRate: Long;
            public vampireRate: Long;
            public reduceDmg: Long;
            public reduceDmgRate: Long;
            public cdSpdRate: Long;
            public moveSpd: Long;
            public moveSpdRate: Long;
            public atkSpd: Long;
            public atkSpdRate: Long;
            public dmgbl: Long;
            public dmg: Long;
            public llmaxRate: Long;
            public lldmgRate: Long;
            public mjmaxRate: Long;
            public mjdmgRate: Long;
            public zlmaxRate: Long;
            public zldmgRate: Long;
            public woodDmg: Long;
            public mineDmg: Long;
            public llmax: Long;
            public lldmg: Long;
            public mjmax: Long;
            public mjdmg: Long;
            public zlmax: Long;
            public zldmg: Long;
            public battleRange: Long;
            public llpvedmg: Long;
            public mjpvedmg: Long;
            public zlpvedmg: Long;
            public pvedmg: Long;
            public fovRange: Long;
            public reducecrit: Long;
            public hpBrate: Long;
            public atkBrate: Long;
            public static create(properties?: pb.IPbBattleAttr): pb.PbBattleAttr;
            public static encode(m: pb.PbBattleAttr, w?: $protobuf.Writer): $protobuf.Writer;
            public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): pb.PbBattleAttr;
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }
    }

    interface IMessageWrapper {
        id?: (number|null);
        protoId?: (number|null);
        data?: (Uint8Array|null);
        toReplyId?: (number|null);
        replyId?: (number|null);
    }

    class MessageWrapper implements IMessageWrapper {
        constructor(p?: IMessageWrapper);
        public id: number;
        public protoId: number;
        public data: Uint8Array;
        public toReplyId: number;
        public replyId: number;
        public static create(properties?: IMessageWrapper): MessageWrapper;
        public static encode(m: MessageWrapper, w?: $protobuf.Writer): $protobuf.Writer;
        public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): MessageWrapper;
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    interface ILoginRequest {
        openId?: (string|null);
        serverId?: (number|null);
        deviceType?: (string|null);
        deviceId?: (string|null);
        channelId?: (number|null);
        channelIdentity?: (string|null);
        channelData?: (Uint8Array|null);
        reConnect?: (boolean|null);
    }

    class LoginRequest implements ILoginRequest {
        constructor(p?: ILoginRequest);
        public openId: string;
        public serverId: number;
        public deviceType: string;
        public deviceId: string;
        public channelId: number;
        public channelIdentity: string;
        public channelData: Uint8Array;
        public reConnect: boolean;
        public static create(properties?: ILoginRequest): LoginRequest;
        public static encode(m: LoginRequest, w?: $protobuf.Writer): $protobuf.Writer;
        public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): LoginRequest;
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    namespace LoginRequest {

        enum Proto {
            UNKNOWN = 0,
            ID = 200001
        }
    }

    interface ILoginResponse {
        status?: (LoginStatus|null);
        pid?: (Long|null);
        serverTime?: (Long|null);
        offsetUtc?: (Long|null);
        nick?: (string|null);
        head?: (string|null);
        level?: (number|null);
        exp?: (Long|null);
        forbindEndTime?: (Long|null);
        changeNameTimes?: (number|null);
        buyPrivilege?: (boolean|null);
        vipLevel?: (number|null);
        openDay?: (number|null);
        openGm?: (boolean|null);
        isCreate?: (boolean|null);
        createTime?: (Long|null);
        vipExp?: (Long|null);
    }

    class LoginResponse implements ILoginResponse {
        constructor(p?: ILoginResponse);
        public status: LoginStatus;
        public pid: Long;
        public serverTime: Long;
        public offsetUtc: Long;
        public nick: string;
        public head: string;
        public level: number;
        public exp: Long;
        public forbindEndTime: Long;
        public changeNameTimes: number;
        public buyPrivilege: boolean;
        public vipLevel: number;
        public openDay: number;
        public openGm: boolean;
        public isCreate: boolean;
        public createTime: Long;
        public vipExp: Long;
        public static create(properties?: ILoginResponse): LoginResponse;
        public static encode(m: LoginResponse, w?: $protobuf.Writer): $protobuf.Writer;
        public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): LoginResponse;
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    namespace LoginResponse {

        enum Proto {
            UNKNOWN = 0,
            ID = 200001
        }
    }

    interface ILogoutRequest {
    }

    class LogoutRequest implements ILogoutRequest {
        constructor(p?: ILogoutRequest);
        public static create(properties?: ILogoutRequest): LogoutRequest;
        public static encode(m: LogoutRequest, w?: $protobuf.Writer): $protobuf.Writer;
        public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): LogoutRequest;
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    namespace LogoutRequest {

        enum Proto {
            UNKNOWN = 0,
            ID = 200003
        }
    }

    interface ILogoutMessage {
        type?: (number|null);
        forbindEndTime?: (Long|null);
    }

    class LogoutMessage implements ILogoutMessage {
        constructor(p?: ILogoutMessage);
        public type: number;
        public forbindEndTime: Long;
        public static create(properties?: ILogoutMessage): LogoutMessage;
        public static encode(m: LogoutMessage, w?: $protobuf.Writer): $protobuf.Writer;
        public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): LogoutMessage;
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    namespace LogoutMessage {

        enum Proto {
            UNKNOWN = 0,
            ID = 200004
        }
    }

    interface IHeartBeatRequest {
    }

    class HeartBeatRequest implements IHeartBeatRequest {
        constructor(p?: IHeartBeatRequest);
        public static create(properties?: IHeartBeatRequest): HeartBeatRequest;
        public static encode(m: HeartBeatRequest, w?: $protobuf.Writer): $protobuf.Writer;
        public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): HeartBeatRequest;
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    namespace HeartBeatRequest {

        enum Proto {
            UNKNOWN = 0,
            ID = 200005
        }
    }

    interface IHeartBeatResponse {
        serverTime?: (Long|null);
    }

    class HeartBeatResponse implements IHeartBeatResponse {
        constructor(p?: IHeartBeatResponse);
        public serverTime: Long;
        public static create(properties?: IHeartBeatResponse): HeartBeatResponse;
        public static encode(m: HeartBeatResponse, w?: $protobuf.Writer): $protobuf.Writer;
        public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): HeartBeatResponse;
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    namespace HeartBeatResponse {

        enum Proto {
            UNKNOWN = 0,
            ID = 200005
        }
    }

    interface IChangeNameRequest {
        newName?: (string|null);
        channelIdentity?: (string|null);
    }

    class ChangeNameRequest implements IChangeNameRequest {
        constructor(p?: IChangeNameRequest);
        public newName: string;
        public channelIdentity: string;
        public static create(properties?: IChangeNameRequest): ChangeNameRequest;
        public static encode(m: ChangeNameRequest, w?: $protobuf.Writer): $protobuf.Writer;
        public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): ChangeNameRequest;
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    namespace ChangeNameRequest {

        enum Proto {
            UNKNOWN = 0,
            ID = 200006
        }
    }

    interface IChangeNameResponse {
        newName?: (string|null);
        changeNameTimes?: (number|null);
    }

    class ChangeNameResponse implements IChangeNameResponse {
        constructor(p?: IChangeNameResponse);
        public newName: string;
        public changeNameTimes: number;
        public static create(properties?: IChangeNameResponse): ChangeNameResponse;
        public static encode(m: ChangeNameResponse, w?: $protobuf.Writer): $protobuf.Writer;
        public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): ChangeNameResponse;
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    namespace ChangeNameResponse {

        enum Proto {
            UNKNOWN = 0,
            ID = 200006
        }
    }

    interface IPlayerInfoChangeMessage {
        buyPrivilege?: (boolean|null);
    }

    class PlayerInfoChangeMessage implements IPlayerInfoChangeMessage {
        constructor(p?: IPlayerInfoChangeMessage);
        public buyPrivilege: boolean;
        public static create(properties?: IPlayerInfoChangeMessage): PlayerInfoChangeMessage;
        public static encode(m: PlayerInfoChangeMessage, w?: $protobuf.Writer): $protobuf.Writer;
        public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): PlayerInfoChangeMessage;
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    namespace PlayerInfoChangeMessage {

        enum Proto {
            UNKNOWN = 0,
            ID = 200007
        }
    }

    interface IPlayerPushEndMessage {
    }

    class PlayerPushEndMessage implements IPlayerPushEndMessage {
        constructor(p?: IPlayerPushEndMessage);
        public static create(properties?: IPlayerPushEndMessage): PlayerPushEndMessage;
        public static encode(m: PlayerPushEndMessage, w?: $protobuf.Writer): $protobuf.Writer;
        public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): PlayerPushEndMessage;
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    namespace PlayerPushEndMessage {

        enum Proto {
            UNKNOWN = 0,
            ID = 200008
        }
    }

    interface IPlayerModuleInfoRequest {
        module?: (string|null);
    }

    class PlayerModuleInfoRequest implements IPlayerModuleInfoRequest {
        constructor(p?: IPlayerModuleInfoRequest);
        public module: string;
        public static create(properties?: IPlayerModuleInfoRequest): PlayerModuleInfoRequest;
        public static encode(m: PlayerModuleInfoRequest, w?: $protobuf.Writer): $protobuf.Writer;
        public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): PlayerModuleInfoRequest;
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    namespace PlayerModuleInfoRequest {

        enum Proto {
            UNKNOWN = 0,
            ID = 200009
        }
    }

    interface IPlayerModuleInfoResponse {
    }

    class PlayerModuleInfoResponse implements IPlayerModuleInfoResponse {
        constructor(p?: IPlayerModuleInfoResponse);
        public static create(properties?: IPlayerModuleInfoResponse): PlayerModuleInfoResponse;
        public static encode(m: PlayerModuleInfoResponse, w?: $protobuf.Writer): $protobuf.Writer;
        public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): PlayerModuleInfoResponse;
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    namespace PlayerModuleInfoResponse {

        enum Proto {
            UNKNOWN = 0,
            ID = 200009
        }
    }

    interface IPlayerMoreInfoRequest {
    }

    class PlayerMoreInfoRequest implements IPlayerMoreInfoRequest {
        constructor(p?: IPlayerMoreInfoRequest);
        public static create(properties?: IPlayerMoreInfoRequest): PlayerMoreInfoRequest;
        public static encode(m: PlayerMoreInfoRequest, w?: $protobuf.Writer): $protobuf.Writer;
        public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): PlayerMoreInfoRequest;
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    namespace PlayerMoreInfoRequest {

        enum Proto {
            UNKNOWN = 0,
            ID = 200010
        }
    }

    interface IPlayerMoreInfoResponse {
    }

    class PlayerMoreInfoResponse implements IPlayerMoreInfoResponse {
        constructor(p?: IPlayerMoreInfoResponse);
        public static create(properties?: IPlayerMoreInfoResponse): PlayerMoreInfoResponse;
        public static encode(m: PlayerMoreInfoResponse, w?: $protobuf.Writer): $protobuf.Writer;
        public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): PlayerMoreInfoResponse;
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    namespace PlayerMoreInfoResponse {

        enum Proto {
            UNKNOWN = 0,
            ID = 200010
        }
    }

    interface IPlayerMoreInfoEndMessage {
    }

    class PlayerMoreInfoEndMessage implements IPlayerMoreInfoEndMessage {
        constructor(p?: IPlayerMoreInfoEndMessage);
        public static create(properties?: IPlayerMoreInfoEndMessage): PlayerMoreInfoEndMessage;
        public static encode(m: PlayerMoreInfoEndMessage, w?: $protobuf.Writer): $protobuf.Writer;
        public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): PlayerMoreInfoEndMessage;
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    namespace PlayerMoreInfoEndMessage {

        enum Proto {
            UNKNOWN = 0,
            ID = 200011
        }
    }

    interface IPlayerWiretapRequest {
        playerId?: (Long|null);
    }

    class PlayerWiretapRequest implements IPlayerWiretapRequest {
        constructor(p?: IPlayerWiretapRequest);
        public playerId: Long;
        public static create(properties?: IPlayerWiretapRequest): PlayerWiretapRequest;
        public static encode(m: PlayerWiretapRequest, w?: $protobuf.Writer): $protobuf.Writer;
        public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): PlayerWiretapRequest;
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    namespace PlayerWiretapRequest {

        enum Proto {
            UNKNOWN = 0,
            ID = 200012
        }
    }

    interface IPlayerWiretapResponse {
    }

    class PlayerWiretapResponse implements IPlayerWiretapResponse {
        constructor(p?: IPlayerWiretapResponse);
        public static create(properties?: IPlayerWiretapResponse): PlayerWiretapResponse;
        public static encode(m: PlayerWiretapResponse, w?: $protobuf.Writer): $protobuf.Writer;
        public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): PlayerWiretapResponse;
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    namespace PlayerWiretapResponse {

        enum Proto {
            UNKNOWN = 0,
            ID = 200012
        }
    }

    interface IEasyLoginRequest {
        openId?: (string|null);
        serverId?: (number|null);
        deviceType?: (string|null);
    }

    class EasyLoginRequest implements IEasyLoginRequest {
        constructor(p?: IEasyLoginRequest);
        public openId: string;
        public serverId: number;
        public deviceType: string;
        public static create(properties?: IEasyLoginRequest): EasyLoginRequest;
        public static encode(m: EasyLoginRequest, w?: $protobuf.Writer): $protobuf.Writer;
        public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): EasyLoginRequest;
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    namespace EasyLoginRequest {

        enum Proto {
            UNKNOWN = 0,
            ID = 200013
        }
    }

    enum LoginStatus {
        NONE = 0,
        NORMAL = 1,
        FORBID = 2,
        MAINTAIN = 3,
        VIOLATION = 4
    }

    class Pb37ChannelData implements IPb37ChannelData {
        constructor(p?: IPb37ChannelData);
        public appid: string;
        public gameId: number;
        public fxCGameId: string;
        public appExt: string;
        public guid: string;
        public static create(properties?: IPb37ChannelData): Pb37ChannelData;
        public static encode(m: Pb37ChannelData, w?: $protobuf.Writer): $protobuf.Writer;
        public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): Pb37ChannelData;
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    class PbYiLeChannelData implements IPbYiLeChannelData {
        constructor(p?: IPbYiLeChannelData);
        public uid: string;
        public token: string;
        public loginTime: string;
        public static create(properties?: IPbYiLeChannelData): PbYiLeChannelData;
        public static encode(m: PbYiLeChannelData, w?: $protobuf.Writer): $protobuf.Writer;
        public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): PbYiLeChannelData;
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    class PbWaboChannelData implements IPbWaboChannelData {
        constructor(p?: IPbWaboChannelData);
        public token: string;
        public uid: string;
        public static create(properties?: IPbWaboChannelData): PbWaboChannelData;
        public static encode(m: PbWaboChannelData, w?: $protobuf.Writer): $protobuf.Writer;
        public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): PbWaboChannelData;
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    class PbFuShunChannelData implements IPbFuShunChannelData {
        constructor(p?: IPbFuShunChannelData);
        public code: string;
        public static create(properties?: IPbFuShunChannelData): PbFuShunChannelData;
        public static encode(m: PbFuShunChannelData, w?: $protobuf.Writer): $protobuf.Writer;
        public static decode(r: ($protobuf.Reader|Uint8Array), l?: number): PbFuShunChannelData;
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }
}
