/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
import * as $protobuf from "protobufjs/minimal.js";
import Long from 'long';
$protobuf.default.util.Long = Long;
$protobuf.default.configure();

const $Reader = $protobuf.default.Reader, $Writer = $protobuf.default.Writer, $util = $protobuf.default.util;

const $root = {};

export const pb = $root.pb = (() => {

    const pb = {};

    pb.EnterBattleRequest = (function() {

        function EnterBattleRequest(p) {
            this.posList = [];
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        EnterBattleRequest.prototype.battleType = 0;
        EnterBattleRequest.prototype.mapId = 0;
        EnterBattleRequest.prototype.mainHero = 0;
        EnterBattleRequest.prototype.battleParams = $util.newBuffer([]);
        EnterBattleRequest.prototype.posList = $util.emptyArray;
        EnterBattleRequest.prototype.type = 0;

        EnterBattleRequest.create = function create(properties) {
            return new EnterBattleRequest(properties);
        };

        EnterBattleRequest.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.battleType != null && Object.hasOwnProperty.call(m, "battleType"))
                w.uint32(8).int32(m.battleType);
            if (m.mapId != null && Object.hasOwnProperty.call(m, "mapId"))
                w.uint32(16).int32(m.mapId);
            if (m.mainHero != null && Object.hasOwnProperty.call(m, "mainHero"))
                w.uint32(24).int32(m.mainHero);
            if (m.battleParams != null && Object.hasOwnProperty.call(m, "battleParams"))
                w.uint32(34).bytes(m.battleParams);
            if (m.posList != null && m.posList.length) {
                for (var i = 0; i < m.posList.length; ++i)
                    $root.pb.PbFormationPos.encode(m.posList[i], w.uint32(42).fork()).ldelim();
            }
            if (m.type != null && Object.hasOwnProperty.call(m, "type"))
                w.uint32(48).int32(m.type);
            return w;
        };

        EnterBattleRequest.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.EnterBattleRequest();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.battleType = r.int32();
                        break;
                    }
                case 2: {
                        m.mapId = r.int32();
                        break;
                    }
                case 3: {
                        m.mainHero = r.int32();
                        break;
                    }
                case 4: {
                        m.battleParams = r.bytes();
                        break;
                    }
                case 5: {
                        if (!(m.posList && m.posList.length))
                            m.posList = [];
                        m.posList.push($root.pb.PbFormationPos.decode(r, r.uint32()));
                        break;
                    }
                case 6: {
                        m.type = r.int32();
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        EnterBattleRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.EnterBattleRequest";
        };

        EnterBattleRequest.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[300001] = "ID"] = 300001;
            return values;
        })();

        return EnterBattleRequest;
    })();

    pb.EnterBattleResponse = (function() {

        function EnterBattleResponse(p) {
            this.heroes = [];
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        EnterBattleResponse.prototype.battleType = 0;
        EnterBattleResponse.prototype.mapId = 0;
        EnterBattleResponse.prototype.heroes = $util.emptyArray;
        EnterBattleResponse.prototype.mainHero = 0;
        EnterBattleResponse.prototype.battleParams = $util.newBuffer([]);
        EnterBattleResponse.prototype.type = 0;

        EnterBattleResponse.create = function create(properties) {
            return new EnterBattleResponse(properties);
        };

        EnterBattleResponse.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.battleType != null && Object.hasOwnProperty.call(m, "battleType"))
                w.uint32(8).int32(m.battleType);
            if (m.mapId != null && Object.hasOwnProperty.call(m, "mapId"))
                w.uint32(16).int32(m.mapId);
            if (m.heroes != null && m.heroes.length) {
                for (var i = 0; i < m.heroes.length; ++i)
                    $root.pb.PbSprite.encode(m.heroes[i], w.uint32(26).fork()).ldelim();
            }
            if (m.mainHero != null && Object.hasOwnProperty.call(m, "mainHero"))
                w.uint32(32).int32(m.mainHero);
            if (m.battleParams != null && Object.hasOwnProperty.call(m, "battleParams"))
                w.uint32(42).bytes(m.battleParams);
            if (m.type != null && Object.hasOwnProperty.call(m, "type"))
                w.uint32(48).int32(m.type);
            return w;
        };

        EnterBattleResponse.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.EnterBattleResponse();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.battleType = r.int32();
                        break;
                    }
                case 2: {
                        m.mapId = r.int32();
                        break;
                    }
                case 3: {
                        if (!(m.heroes && m.heroes.length))
                            m.heroes = [];
                        m.heroes.push($root.pb.PbSprite.decode(r, r.uint32()));
                        break;
                    }
                case 4: {
                        m.mainHero = r.int32();
                        break;
                    }
                case 5: {
                        m.battleParams = r.bytes();
                        break;
                    }
                case 6: {
                        m.type = r.int32();
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        EnterBattleResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.EnterBattleResponse";
        };

        EnterBattleResponse.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[300001] = "ID"] = 300001;
            return values;
        })();

        return EnterBattleResponse;
    })();

    pb.EnterZoneRequest = (function() {

        function EnterZoneRequest(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        EnterZoneRequest.prototype.zoneId = 0;

        EnterZoneRequest.create = function create(properties) {
            return new EnterZoneRequest(properties);
        };

        EnterZoneRequest.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.zoneId != null && Object.hasOwnProperty.call(m, "zoneId"))
                w.uint32(8).int32(m.zoneId);
            return w;
        };

        EnterZoneRequest.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.EnterZoneRequest();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.zoneId = r.int32();
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        EnterZoneRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.EnterZoneRequest";
        };

        EnterZoneRequest.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[300002] = "ID"] = 300002;
            return values;
        })();

        return EnterZoneRequest;
    })();

    pb.EnterZoneResponse = (function() {

        function EnterZoneResponse(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        EnterZoneResponse.prototype.zoneId = 0;

        EnterZoneResponse.create = function create(properties) {
            return new EnterZoneResponse(properties);
        };

        EnterZoneResponse.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.zoneId != null && Object.hasOwnProperty.call(m, "zoneId"))
                w.uint32(8).int32(m.zoneId);
            return w;
        };

        EnterZoneResponse.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.EnterZoneResponse();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.zoneId = r.int32();
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        EnterZoneResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.EnterZoneResponse";
        };

        EnterZoneResponse.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[300002] = "ID"] = 300002;
            return values;
        })();

        return EnterZoneResponse;
    })();

    pb.ZoneInfoRequest = (function() {

        function ZoneInfoRequest(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        ZoneInfoRequest.prototype.zoneId = 0;

        ZoneInfoRequest.create = function create(properties) {
            return new ZoneInfoRequest(properties);
        };

        ZoneInfoRequest.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.zoneId != null && Object.hasOwnProperty.call(m, "zoneId"))
                w.uint32(8).int32(m.zoneId);
            return w;
        };

        ZoneInfoRequest.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.ZoneInfoRequest();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.zoneId = r.int32();
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        ZoneInfoRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.ZoneInfoRequest";
        };

        ZoneInfoRequest.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[300003] = "ID"] = 300003;
            return values;
        })();

        return ZoneInfoRequest;
    })();

    pb.ZoneInfoResponse = (function() {

        function ZoneInfoResponse(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        ZoneInfoResponse.prototype.zone = null;

        ZoneInfoResponse.create = function create(properties) {
            return new ZoneInfoResponse(properties);
        };

        ZoneInfoResponse.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.zone != null && Object.hasOwnProperty.call(m, "zone"))
                $root.pb.PbZone.encode(m.zone, w.uint32(10).fork()).ldelim();
            return w;
        };

        ZoneInfoResponse.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.ZoneInfoResponse();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.zone = $root.pb.PbZone.decode(r, r.uint32());
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        ZoneInfoResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.ZoneInfoResponse";
        };

        ZoneInfoResponse.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[300003] = "ID"] = 300003;
            return values;
        })();

        return ZoneInfoResponse;
    })();

    pb.BattleReviveRequest = (function() {

        function BattleReviveRequest(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        BattleReviveRequest.create = function create(properties) {
            return new BattleReviveRequest(properties);
        };

        BattleReviveRequest.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            return w;
        };

        BattleReviveRequest.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.BattleReviveRequest();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        BattleReviveRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.BattleReviveRequest";
        };

        BattleReviveRequest.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[300006] = "ID"] = 300006;
            return values;
        })();

        return BattleReviveRequest;
    })();

    pb.BattleReviveResponse = (function() {

        function BattleReviveResponse(p) {
            this.heroes = [];
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        BattleReviveResponse.prototype.heroes = $util.emptyArray;

        BattleReviveResponse.create = function create(properties) {
            return new BattleReviveResponse(properties);
        };

        BattleReviveResponse.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.heroes != null && m.heroes.length) {
                for (var i = 0; i < m.heroes.length; ++i)
                    $root.pb.PbSprite.encode(m.heroes[i], w.uint32(10).fork()).ldelim();
            }
            return w;
        };

        BattleReviveResponse.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.BattleReviveResponse();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        if (!(m.heroes && m.heroes.length))
                            m.heroes = [];
                        m.heroes.push($root.pb.PbSprite.decode(r, r.uint32()));
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        BattleReviveResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.BattleReviveResponse";
        };

        BattleReviveResponse.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[300006] = "ID"] = 300006;
            return values;
        })();

        return BattleReviveResponse;
    })();

    pb.BattleGiveUpRequest = (function() {

        function BattleGiveUpRequest(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        BattleGiveUpRequest.create = function create(properties) {
            return new BattleGiveUpRequest(properties);
        };

        BattleGiveUpRequest.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            return w;
        };

        BattleGiveUpRequest.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.BattleGiveUpRequest();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        BattleGiveUpRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.BattleGiveUpRequest";
        };

        BattleGiveUpRequest.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[300007] = "ID"] = 300007;
            return values;
        })();

        return BattleGiveUpRequest;
    })();

    pb.BattleGiveUpResponse = (function() {

        function BattleGiveUpResponse(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        BattleGiveUpResponse.create = function create(properties) {
            return new BattleGiveUpResponse(properties);
        };

        BattleGiveUpResponse.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            return w;
        };

        BattleGiveUpResponse.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.BattleGiveUpResponse();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        BattleGiveUpResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.BattleGiveUpResponse";
        };

        BattleGiveUpResponse.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[300007] = "ID"] = 300007;
            return values;
        })();

        return BattleGiveUpResponse;
    })();

    pb.BattleKillSpriteRequest = (function() {

        function BattleKillSpriteRequest(p) {
            this.spriteIds = [];
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        BattleKillSpriteRequest.prototype.spriteIds = $util.emptyArray;
        BattleKillSpriteRequest.prototype.serialId = 0;

        BattleKillSpriteRequest.create = function create(properties) {
            return new BattleKillSpriteRequest(properties);
        };

        BattleKillSpriteRequest.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.spriteIds != null && m.spriteIds.length) {
                w.uint32(10).fork();
                for (var i = 0; i < m.spriteIds.length; ++i)
                    w.int32(m.spriteIds[i]);
                w.ldelim();
            }
            if (m.serialId != null && Object.hasOwnProperty.call(m, "serialId"))
                w.uint32(16).int32(m.serialId);
            return w;
        };

        BattleKillSpriteRequest.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.BattleKillSpriteRequest();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        if (!(m.spriteIds && m.spriteIds.length))
                            m.spriteIds = [];
                        if ((t & 7) === 2) {
                            var c2 = r.uint32() + r.pos;
                            while (r.pos < c2)
                                m.spriteIds.push(r.int32());
                        } else
                            m.spriteIds.push(r.int32());
                        break;
                    }
                case 2: {
                        m.serialId = r.int32();
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        BattleKillSpriteRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.BattleKillSpriteRequest";
        };

        BattleKillSpriteRequest.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[300008] = "ID"] = 300008;
            return values;
        })();

        return BattleKillSpriteRequest;
    })();

    pb.BattleKillSpriteResponse = (function() {

        function BattleKillSpriteResponse(p) {
            this.sprites = [];
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        BattleKillSpriteResponse.prototype.sprites = $util.emptyArray;
        BattleKillSpriteResponse.prototype.serialId = 0;
        BattleKillSpriteResponse.prototype.endData = null;

        BattleKillSpriteResponse.create = function create(properties) {
            return new BattleKillSpriteResponse(properties);
        };

        BattleKillSpriteResponse.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.sprites != null && m.sprites.length) {
                for (var i = 0; i < m.sprites.length; ++i)
                    $root.pb.PbKillSprite.encode(m.sprites[i], w.uint32(10).fork()).ldelim();
            }
            if (m.serialId != null && Object.hasOwnProperty.call(m, "serialId"))
                w.uint32(16).int32(m.serialId);
            if (m.endData != null && Object.hasOwnProperty.call(m, "endData"))
                $root.pb.PbBattleEndData.encode(m.endData, w.uint32(26).fork()).ldelim();
            return w;
        };

        BattleKillSpriteResponse.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.BattleKillSpriteResponse();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        if (!(m.sprites && m.sprites.length))
                            m.sprites = [];
                        m.sprites.push($root.pb.PbKillSprite.decode(r, r.uint32()));
                        break;
                    }
                case 2: {
                        m.serialId = r.int32();
                        break;
                    }
                case 3: {
                        m.endData = $root.pb.PbBattleEndData.decode(r, r.uint32());
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        BattleKillSpriteResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.BattleKillSpriteResponse";
        };

        BattleKillSpriteResponse.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[300008] = "ID"] = 300008;
            return values;
        })();

        return BattleKillSpriteResponse;
    })();

    pb.BattleSyncKillMonsterRequest = (function() {

        function BattleSyncKillMonsterRequest(p) {
            this.killMonsters = [];
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        BattleSyncKillMonsterRequest.prototype.killMonsters = $util.emptyArray;

        BattleSyncKillMonsterRequest.create = function create(properties) {
            return new BattleSyncKillMonsterRequest(properties);
        };

        BattleSyncKillMonsterRequest.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.killMonsters != null && m.killMonsters.length) {
                for (var i = 0; i < m.killMonsters.length; ++i)
                    $root.pb.PbKeyV.encode(m.killMonsters[i], w.uint32(10).fork()).ldelim();
            }
            return w;
        };

        BattleSyncKillMonsterRequest.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.BattleSyncKillMonsterRequest();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        if (!(m.killMonsters && m.killMonsters.length))
                            m.killMonsters = [];
                        m.killMonsters.push($root.pb.PbKeyV.decode(r, r.uint32()));
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        BattleSyncKillMonsterRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.BattleSyncKillMonsterRequest";
        };

        BattleSyncKillMonsterRequest.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[300009] = "ID"] = 300009;
            return values;
        })();

        return BattleSyncKillMonsterRequest;
    })();

    pb.BattleSyncKillMonsterResponse = (function() {

        function BattleSyncKillMonsterResponse(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        BattleSyncKillMonsterResponse.create = function create(properties) {
            return new BattleSyncKillMonsterResponse(properties);
        };

        BattleSyncKillMonsterResponse.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            return w;
        };

        BattleSyncKillMonsterResponse.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.BattleSyncKillMonsterResponse();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        BattleSyncKillMonsterResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.BattleSyncKillMonsterResponse";
        };

        BattleSyncKillMonsterResponse.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[300009] = "ID"] = 300009;
            return values;
        })();

        return BattleSyncKillMonsterResponse;
    })();

    pb.BattleRebornSpriteRequest = (function() {

        function BattleRebornSpriteRequest(p) {
            this.spriteIds = [];
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        BattleRebornSpriteRequest.prototype.spriteIds = $util.emptyArray;

        BattleRebornSpriteRequest.create = function create(properties) {
            return new BattleRebornSpriteRequest(properties);
        };

        BattleRebornSpriteRequest.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.spriteIds != null && m.spriteIds.length) {
                w.uint32(10).fork();
                for (var i = 0; i < m.spriteIds.length; ++i)
                    w.int32(m.spriteIds[i]);
                w.ldelim();
            }
            return w;
        };

        BattleRebornSpriteRequest.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.BattleRebornSpriteRequest();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        if (!(m.spriteIds && m.spriteIds.length))
                            m.spriteIds = [];
                        if ((t & 7) === 2) {
                            var c2 = r.uint32() + r.pos;
                            while (r.pos < c2)
                                m.spriteIds.push(r.int32());
                        } else
                            m.spriteIds.push(r.int32());
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        BattleRebornSpriteRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.BattleRebornSpriteRequest";
        };

        BattleRebornSpriteRequest.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[300011] = "ID"] = 300011;
            return values;
        })();

        return BattleRebornSpriteRequest;
    })();

    pb.BattleRebornSpriteResponse = (function() {

        function BattleRebornSpriteResponse(p) {
            this.sprites = [];
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        BattleRebornSpriteResponse.prototype.sprites = $util.emptyArray;

        BattleRebornSpriteResponse.create = function create(properties) {
            return new BattleRebornSpriteResponse(properties);
        };

        BattleRebornSpriteResponse.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.sprites != null && m.sprites.length) {
                for (var i = 0; i < m.sprites.length; ++i)
                    $root.pb.PbSprite.encode(m.sprites[i], w.uint32(10).fork()).ldelim();
            }
            return w;
        };

        BattleRebornSpriteResponse.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.BattleRebornSpriteResponse();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        if (!(m.sprites && m.sprites.length))
                            m.sprites = [];
                        m.sprites.push($root.pb.PbSprite.decode(r, r.uint32()));
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        BattleRebornSpriteResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.BattleRebornSpriteResponse";
        };

        BattleRebornSpriteResponse.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[300011] = "ID"] = 300011;
            return values;
        })();

        return BattleRebornSpriteResponse;
    })();

    pb.BattleSettleRequest = (function() {

        function BattleSettleRequest(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        BattleSettleRequest.create = function create(properties) {
            return new BattleSettleRequest(properties);
        };

        BattleSettleRequest.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            return w;
        };

        BattleSettleRequest.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.BattleSettleRequest();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        BattleSettleRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.BattleSettleRequest";
        };

        BattleSettleRequest.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[300012] = "ID"] = 300012;
            return values;
        })();

        return BattleSettleRequest;
    })();

    pb.BattleSettleResponse = (function() {

        function BattleSettleResponse(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        BattleSettleResponse.prototype.endData = null;

        BattleSettleResponse.create = function create(properties) {
            return new BattleSettleResponse(properties);
        };

        BattleSettleResponse.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.endData != null && Object.hasOwnProperty.call(m, "endData"))
                $root.pb.PbBattleEndData.encode(m.endData, w.uint32(10).fork()).ldelim();
            return w;
        };

        BattleSettleResponse.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.BattleSettleResponse();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.endData = $root.pb.PbBattleEndData.decode(r, r.uint32());
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        BattleSettleResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.BattleSettleResponse";
        };

        BattleSettleResponse.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[300012] = "ID"] = 300012;
            return values;
        })();

        return BattleSettleResponse;
    })();

    pb.BattleSpawnMonsterGmMessage = (function() {

        function BattleSpawnMonsterGmMessage(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        BattleSpawnMonsterGmMessage.prototype.battleType = 0;
        BattleSpawnMonsterGmMessage.prototype.sprite = null;

        BattleSpawnMonsterGmMessage.create = function create(properties) {
            return new BattleSpawnMonsterGmMessage(properties);
        };

        BattleSpawnMonsterGmMessage.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.battleType != null && Object.hasOwnProperty.call(m, "battleType"))
                w.uint32(8).int32(m.battleType);
            if (m.sprite != null && Object.hasOwnProperty.call(m, "sprite"))
                $root.pb.PbSprite.encode(m.sprite, w.uint32(18).fork()).ldelim();
            return w;
        };

        BattleSpawnMonsterGmMessage.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.BattleSpawnMonsterGmMessage();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.battleType = r.int32();
                        break;
                    }
                case 2: {
                        m.sprite = $root.pb.PbSprite.decode(r, r.uint32());
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        BattleSpawnMonsterGmMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.BattleSpawnMonsterGmMessage";
        };

        BattleSpawnMonsterGmMessage.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[300013] = "ID"] = 300013;
            return values;
        })();

        return BattleSpawnMonsterGmMessage;
    })();

    pb.SwitchBattleHeroRequest = (function() {

        function SwitchBattleHeroRequest(p) {
            this.posList = [];
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        SwitchBattleHeroRequest.prototype.mainHero = 0;
        SwitchBattleHeroRequest.prototype.posList = $util.emptyArray;

        SwitchBattleHeroRequest.create = function create(properties) {
            return new SwitchBattleHeroRequest(properties);
        };

        SwitchBattleHeroRequest.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.mainHero != null && Object.hasOwnProperty.call(m, "mainHero"))
                w.uint32(8).int32(m.mainHero);
            if (m.posList != null && m.posList.length) {
                for (var i = 0; i < m.posList.length; ++i)
                    $root.pb.PbFormationPos.encode(m.posList[i], w.uint32(18).fork()).ldelim();
            }
            return w;
        };

        SwitchBattleHeroRequest.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.SwitchBattleHeroRequest();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.mainHero = r.int32();
                        break;
                    }
                case 2: {
                        if (!(m.posList && m.posList.length))
                            m.posList = [];
                        m.posList.push($root.pb.PbFormationPos.decode(r, r.uint32()));
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        SwitchBattleHeroRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.SwitchBattleHeroRequest";
        };

        SwitchBattleHeroRequest.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[300101] = "ID"] = 300101;
            return values;
        })();

        return SwitchBattleHeroRequest;
    })();

    pb.SwitchBattleHeroResponse = (function() {

        function SwitchBattleHeroResponse(p) {
            this.heroes = [];
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        SwitchBattleHeroResponse.prototype.heroes = $util.emptyArray;
        SwitchBattleHeroResponse.prototype.mainHero = 0;

        SwitchBattleHeroResponse.create = function create(properties) {
            return new SwitchBattleHeroResponse(properties);
        };

        SwitchBattleHeroResponse.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.heroes != null && m.heroes.length) {
                for (var i = 0; i < m.heroes.length; ++i)
                    $root.pb.PbSprite.encode(m.heroes[i], w.uint32(10).fork()).ldelim();
            }
            if (m.mainHero != null && Object.hasOwnProperty.call(m, "mainHero"))
                w.uint32(16).int32(m.mainHero);
            return w;
        };

        SwitchBattleHeroResponse.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.SwitchBattleHeroResponse();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        if (!(m.heroes && m.heroes.length))
                            m.heroes = [];
                        m.heroes.push($root.pb.PbSprite.decode(r, r.uint32()));
                        break;
                    }
                case 2: {
                        m.mainHero = r.int32();
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        SwitchBattleHeroResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.SwitchBattleHeroResponse";
        };

        SwitchBattleHeroResponse.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[300101] = "ID"] = 300101;
            return values;
        })();

        return SwitchBattleHeroResponse;
    })();

    pb.PbTreasureEvent = (function() {

        function PbTreasureEvent(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        PbTreasureEvent.prototype.itemId = 0;
        PbTreasureEvent.prototype.num = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbTreasureEvent.prototype.upItemId = 0;
        PbTreasureEvent.prototype.upNum = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbTreasureEvent.prototype.disappearTime = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        PbTreasureEvent.create = function create(properties) {
            return new PbTreasureEvent(properties);
        };

        PbTreasureEvent.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.itemId != null && Object.hasOwnProperty.call(m, "itemId"))
                w.uint32(8).int32(m.itemId);
            if (m.num != null && Object.hasOwnProperty.call(m, "num"))
                w.uint32(16).int64(m.num);
            if (m.upItemId != null && Object.hasOwnProperty.call(m, "upItemId"))
                w.uint32(24).int32(m.upItemId);
            if (m.upNum != null && Object.hasOwnProperty.call(m, "upNum"))
                w.uint32(32).int64(m.upNum);
            if (m.disappearTime != null && Object.hasOwnProperty.call(m, "disappearTime"))
                w.uint32(40).int64(m.disappearTime);
            return w;
        };

        PbTreasureEvent.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.PbTreasureEvent();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.itemId = r.int32();
                        break;
                    }
                case 2: {
                        m.num = r.int64();
                        break;
                    }
                case 3: {
                        m.upItemId = r.int32();
                        break;
                    }
                case 4: {
                        m.upNum = r.int64();
                        break;
                    }
                case 5: {
                        m.disappearTime = r.int64();
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        PbTreasureEvent.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.PbTreasureEvent";
        };

        return PbTreasureEvent;
    })();

    pb.PbSprite = (function() {

        function PbSprite(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        PbSprite.prototype.id = 0;
        PbSprite.prototype.type = 0;
        PbSprite.prototype.identity = 0;
        PbSprite.prototype.level = 0;
        PbSprite.prototype.rebornTime = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbSprite.prototype.ctype = 0;
        PbSprite.prototype.cx = 0;
        PbSprite.prototype.cy = 0;
        PbSprite.prototype.battleAttr = null;
        PbSprite.prototype.configId = 0;
        PbSprite.prototype.fogArea = 0;

        PbSprite.create = function create(properties) {
            return new PbSprite(properties);
        };

        PbSprite.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.id != null && Object.hasOwnProperty.call(m, "id"))
                w.uint32(8).int32(m.id);
            if (m.type != null && Object.hasOwnProperty.call(m, "type"))
                w.uint32(16).int32(m.type);
            if (m.identity != null && Object.hasOwnProperty.call(m, "identity"))
                w.uint32(24).int32(m.identity);
            if (m.level != null && Object.hasOwnProperty.call(m, "level"))
                w.uint32(32).int32(m.level);
            if (m.rebornTime != null && Object.hasOwnProperty.call(m, "rebornTime"))
                w.uint32(40).int64(m.rebornTime);
            if (m.ctype != null && Object.hasOwnProperty.call(m, "ctype"))
                w.uint32(48).int32(m.ctype);
            if (m.cx != null && Object.hasOwnProperty.call(m, "cx"))
                w.uint32(56).int32(m.cx);
            if (m.cy != null && Object.hasOwnProperty.call(m, "cy"))
                w.uint32(64).int32(m.cy);
            if (m.battleAttr != null && Object.hasOwnProperty.call(m, "battleAttr"))
                $root.pb.PbBattleAttr.encode(m.battleAttr, w.uint32(74).fork()).ldelim();
            if (m.configId != null && Object.hasOwnProperty.call(m, "configId"))
                w.uint32(80).int32(m.configId);
            if (m.fogArea != null && Object.hasOwnProperty.call(m, "fogArea"))
                w.uint32(88).int32(m.fogArea);
            return w;
        };

        PbSprite.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.PbSprite();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.id = r.int32();
                        break;
                    }
                case 2: {
                        m.type = r.int32();
                        break;
                    }
                case 3: {
                        m.identity = r.int32();
                        break;
                    }
                case 4: {
                        m.level = r.int32();
                        break;
                    }
                case 5: {
                        m.rebornTime = r.int64();
                        break;
                    }
                case 6: {
                        m.ctype = r.int32();
                        break;
                    }
                case 7: {
                        m.cx = r.int32();
                        break;
                    }
                case 8: {
                        m.cy = r.int32();
                        break;
                    }
                case 9: {
                        m.battleAttr = $root.pb.PbBattleAttr.decode(r, r.uint32());
                        break;
                    }
                case 10: {
                        m.configId = r.int32();
                        break;
                    }
                case 11: {
                        m.fogArea = r.int32();
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        PbSprite.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.PbSprite";
        };

        return PbSprite;
    })();

    pb.PbKillSprite = (function() {

        function PbKillSprite(p) {
            this.drops = [];
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        PbKillSprite.prototype.id = 0;
        PbKillSprite.prototype.rebornTime = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbKillSprite.prototype.drops = $util.emptyArray;
        PbKillSprite.prototype.hasBox = false;
        PbKillSprite.prototype.book = 0;

        PbKillSprite.create = function create(properties) {
            return new PbKillSprite(properties);
        };

        PbKillSprite.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.id != null && Object.hasOwnProperty.call(m, "id"))
                w.uint32(8).int32(m.id);
            if (m.rebornTime != null && Object.hasOwnProperty.call(m, "rebornTime"))
                w.uint32(16).int64(m.rebornTime);
            if (m.drops != null && m.drops.length) {
                for (var i = 0; i < m.drops.length; ++i)
                    $root.pb.PbItem.encode(m.drops[i], w.uint32(26).fork()).ldelim();
            }
            if (m.hasBox != null && Object.hasOwnProperty.call(m, "hasBox"))
                w.uint32(32).bool(m.hasBox);
            if (m.book != null && Object.hasOwnProperty.call(m, "book"))
                w.uint32(40).int32(m.book);
            return w;
        };

        PbKillSprite.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.PbKillSprite();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.id = r.int32();
                        break;
                    }
                case 2: {
                        m.rebornTime = r.int64();
                        break;
                    }
                case 3: {
                        if (!(m.drops && m.drops.length))
                            m.drops = [];
                        m.drops.push($root.pb.PbItem.decode(r, r.uint32()));
                        break;
                    }
                case 4: {
                        m.hasBox = r.bool();
                        break;
                    }
                case 5: {
                        m.book = r.int32();
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        PbKillSprite.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.PbKillSprite";
        };

        return PbKillSprite;
    })();

    pb.PbSpriteType = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "UNKNOWN_SPRITE_TYPE"] = 0;
        values[valuesById[1] = "HERO"] = 1;
        values[valuesById[2] = "MONSTER"] = 2;
        return values;
    })();

    pb.PbZone = (function() {

        function PbZone(p) {
            this.sprites = [];
            this.harvests = [];
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        PbZone.prototype.zoneId = 0;
        PbZone.prototype.sprites = $util.emptyArray;
        PbZone.prototype.harvests = $util.emptyArray;
        PbZone.prototype.aidSprite = null;
        PbZone.prototype.removeSpriteId = 0;

        PbZone.create = function create(properties) {
            return new PbZone(properties);
        };

        PbZone.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.zoneId != null && Object.hasOwnProperty.call(m, "zoneId"))
                w.uint32(8).int32(m.zoneId);
            if (m.sprites != null && m.sprites.length) {
                for (var i = 0; i < m.sprites.length; ++i)
                    $root.pb.PbSprite.encode(m.sprites[i], w.uint32(18).fork()).ldelim();
            }
            if (m.harvests != null && m.harvests.length) {
                for (var i = 0; i < m.harvests.length; ++i)
                    $root.pb.PbHarvest.encode(m.harvests[i], w.uint32(26).fork()).ldelim();
            }
            if (m.aidSprite != null && Object.hasOwnProperty.call(m, "aidSprite"))
                $root.pb.PbSprite.encode(m.aidSprite, w.uint32(34).fork()).ldelim();
            if (m.removeSpriteId != null && Object.hasOwnProperty.call(m, "removeSpriteId"))
                w.uint32(40).int32(m.removeSpriteId);
            return w;
        };

        PbZone.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.PbZone();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.zoneId = r.int32();
                        break;
                    }
                case 2: {
                        if (!(m.sprites && m.sprites.length))
                            m.sprites = [];
                        m.sprites.push($root.pb.PbSprite.decode(r, r.uint32()));
                        break;
                    }
                case 3: {
                        if (!(m.harvests && m.harvests.length))
                            m.harvests = [];
                        m.harvests.push($root.pb.PbHarvest.decode(r, r.uint32()));
                        break;
                    }
                case 4: {
                        m.aidSprite = $root.pb.PbSprite.decode(r, r.uint32());
                        break;
                    }
                case 5: {
                        m.removeSpriteId = r.int32();
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        PbZone.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.PbZone";
        };

        return PbZone;
    })();

    pb.PbHarvest = (function() {

        function PbHarvest(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        PbHarvest.prototype.id = 0;
        PbHarvest.prototype.identity = 0;
        PbHarvest.prototype.rebornTime = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbHarvest.prototype.ctype = 0;
        PbHarvest.prototype.cx = 0;
        PbHarvest.prototype.cy = 0;
        PbHarvest.prototype.fogArea = 0;

        PbHarvest.create = function create(properties) {
            return new PbHarvest(properties);
        };

        PbHarvest.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.id != null && Object.hasOwnProperty.call(m, "id"))
                w.uint32(8).int32(m.id);
            if (m.identity != null && Object.hasOwnProperty.call(m, "identity"))
                w.uint32(16).int32(m.identity);
            if (m.rebornTime != null && Object.hasOwnProperty.call(m, "rebornTime"))
                w.uint32(24).int64(m.rebornTime);
            if (m.ctype != null && Object.hasOwnProperty.call(m, "ctype"))
                w.uint32(32).int32(m.ctype);
            if (m.cx != null && Object.hasOwnProperty.call(m, "cx"))
                w.uint32(40).int32(m.cx);
            if (m.cy != null && Object.hasOwnProperty.call(m, "cy"))
                w.uint32(48).int32(m.cy);
            if (m.fogArea != null && Object.hasOwnProperty.call(m, "fogArea"))
                w.uint32(56).int32(m.fogArea);
            return w;
        };

        PbHarvest.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.PbHarvest();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.id = r.int32();
                        break;
                    }
                case 2: {
                        m.identity = r.int32();
                        break;
                    }
                case 3: {
                        m.rebornTime = r.int64();
                        break;
                    }
                case 4: {
                        m.ctype = r.int32();
                        break;
                    }
                case 5: {
                        m.cx = r.int32();
                        break;
                    }
                case 6: {
                        m.cy = r.int32();
                        break;
                    }
                case 7: {
                        m.fogArea = r.int32();
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        PbHarvest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.PbHarvest";
        };

        return PbHarvest;
    })();

    pb.PbBattleEndStatus = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "NO_STATUS"] = 0;
        values[valuesById[1] = "BATTLING"] = 1;
        values[valuesById[2] = "WAIT_REVIVE"] = 2;
        values[valuesById[3] = "ATK_ALL_DEAD_CAMPING"] = 3;
        values[valuesById[4] = "DEF_ALL_DEAD_NEXT"] = 4;
        values[valuesById[5] = "WIN"] = 5;
        values[valuesById[6] = "FAIL"] = 6;
        return values;
    })();

    pb.PbBattleEndData = (function() {

        function PbBattleEndData(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        PbBattleEndData.prototype.status = 0;
        PbBattleEndData.prototype.data = $util.newBuffer([]);

        PbBattleEndData.create = function create(properties) {
            return new PbBattleEndData(properties);
        };

        PbBattleEndData.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.status != null && Object.hasOwnProperty.call(m, "status"))
                w.uint32(8).int32(m.status);
            if (m.data != null && Object.hasOwnProperty.call(m, "data"))
                w.uint32(18).bytes(m.data);
            return w;
        };

        PbBattleEndData.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.PbBattleEndData();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.status = r.int32();
                        break;
                    }
                case 2: {
                        m.data = r.bytes();
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        PbBattleEndData.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.PbBattleEndData";
        };

        return PbBattleEndData;
    })();

    pb.PbThing = (function() {

        function PbThing(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        PbThing.prototype.identity = 0;
        PbThing.prototype.num = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbThing.prototype.data = $util.newBuffer([]);

        PbThing.create = function create(properties) {
            return new PbThing(properties);
        };

        PbThing.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.identity != null && Object.hasOwnProperty.call(m, "identity"))
                w.uint32(8).int32(m.identity);
            if (m.num != null && Object.hasOwnProperty.call(m, "num"))
                w.uint32(16).int64(m.num);
            if (m.data != null && Object.hasOwnProperty.call(m, "data"))
                w.uint32(26).bytes(m.data);
            return w;
        };

        PbThing.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.PbThing();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.identity = r.int32();
                        break;
                    }
                case 2: {
                        m.num = r.int64();
                        break;
                    }
                case 3: {
                        m.data = r.bytes();
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        PbThing.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.PbThing";
        };

        return PbThing;
    })();

    pb.PbThingReceipt = (function() {

        function PbThingReceipt(p) {
            this.things = [];
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        PbThingReceipt.prototype.things = $util.emptyArray;

        PbThingReceipt.create = function create(properties) {
            return new PbThingReceipt(properties);
        };

        PbThingReceipt.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.things != null && m.things.length) {
                for (var i = 0; i < m.things.length; ++i)
                    $root.pb.PbThing.encode(m.things[i], w.uint32(10).fork()).ldelim();
            }
            return w;
        };

        PbThingReceipt.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.PbThingReceipt();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        if (!(m.things && m.things.length))
                            m.things = [];
                        m.things.push($root.pb.PbThing.decode(r, r.uint32()));
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        PbThingReceipt.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.PbThingReceipt";
        };

        return PbThingReceipt;
    })();

    pb.PbItem = (function() {

        function PbItem(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        PbItem.prototype.item = 0;
        PbItem.prototype.num = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        PbItem.create = function create(properties) {
            return new PbItem(properties);
        };

        PbItem.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.item != null && Object.hasOwnProperty.call(m, "item"))
                w.uint32(8).int32(m.item);
            if (m.num != null && Object.hasOwnProperty.call(m, "num"))
                w.uint32(16).int64(m.num);
            return w;
        };

        PbItem.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.PbItem();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.item = r.int32();
                        break;
                    }
                case 2: {
                        m.num = r.int64();
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        PbItem.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.PbItem";
        };

        return PbItem;
    })();

    pb.PbTask = (function() {

        function PbTask(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        PbTask.prototype.id = 0;
        PbTask.prototype.progress = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbTask.prototype.status = 0;

        PbTask.create = function create(properties) {
            return new PbTask(properties);
        };

        PbTask.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.id != null && Object.hasOwnProperty.call(m, "id"))
                w.uint32(8).int32(m.id);
            if (m.progress != null && Object.hasOwnProperty.call(m, "progress"))
                w.uint32(16).int64(m.progress);
            if (m.status != null && Object.hasOwnProperty.call(m, "status"))
                w.uint32(24).int32(m.status);
            return w;
        };

        PbTask.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.PbTask();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.id = r.int32();
                        break;
                    }
                case 2: {
                        m.progress = r.int64();
                        break;
                    }
                case 3: {
                        m.status = r.int32();
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        PbTask.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.PbTask";
        };

        return PbTask;
    })();

    pb.PbPlayerExp = (function() {

        function PbPlayerExp(p) {
            this.openFunctions = [];
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        PbPlayerExp.prototype.oldLevel = 0;
        PbPlayerExp.prototype.newLevel = 0;
        PbPlayerExp.prototype.openFunctions = $util.emptyArray;
        PbPlayerExp.prototype.currExp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        PbPlayerExp.create = function create(properties) {
            return new PbPlayerExp(properties);
        };

        PbPlayerExp.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.oldLevel != null && Object.hasOwnProperty.call(m, "oldLevel"))
                w.uint32(8).int32(m.oldLevel);
            if (m.newLevel != null && Object.hasOwnProperty.call(m, "newLevel"))
                w.uint32(16).int32(m.newLevel);
            if (m.openFunctions != null && m.openFunctions.length) {
                w.uint32(26).fork();
                for (var i = 0; i < m.openFunctions.length; ++i)
                    w.int32(m.openFunctions[i]);
                w.ldelim();
            }
            if (m.currExp != null && Object.hasOwnProperty.call(m, "currExp"))
                w.uint32(32).int64(m.currExp);
            return w;
        };

        PbPlayerExp.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.PbPlayerExp();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.oldLevel = r.int32();
                        break;
                    }
                case 2: {
                        m.newLevel = r.int32();
                        break;
                    }
                case 3: {
                        if (!(m.openFunctions && m.openFunctions.length))
                            m.openFunctions = [];
                        if ((t & 7) === 2) {
                            var c2 = r.uint32() + r.pos;
                            while (r.pos < c2)
                                m.openFunctions.push(r.int32());
                        } else
                            m.openFunctions.push(r.int32());
                        break;
                    }
                case 4: {
                        m.currExp = r.int64();
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        PbPlayerExp.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.PbPlayerExp";
        };

        return PbPlayerExp;
    })();

    pb.PbPlayerVipExp = (function() {

        function PbPlayerVipExp(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        PbPlayerVipExp.prototype.oldLevel = 0;
        PbPlayerVipExp.prototype.newLevel = 0;
        PbPlayerVipExp.prototype.currExp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        PbPlayerVipExp.create = function create(properties) {
            return new PbPlayerVipExp(properties);
        };

        PbPlayerVipExp.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.oldLevel != null && Object.hasOwnProperty.call(m, "oldLevel"))
                w.uint32(8).int32(m.oldLevel);
            if (m.newLevel != null && Object.hasOwnProperty.call(m, "newLevel"))
                w.uint32(16).int32(m.newLevel);
            if (m.currExp != null && Object.hasOwnProperty.call(m, "currExp"))
                w.uint32(24).int64(m.currExp);
            return w;
        };

        PbPlayerVipExp.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.PbPlayerVipExp();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.oldLevel = r.int32();
                        break;
                    }
                case 2: {
                        m.newLevel = r.int32();
                        break;
                    }
                case 3: {
                        m.currExp = r.int64();
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        PbPlayerVipExp.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.PbPlayerVipExp";
        };

        return PbPlayerVipExp;
    })();

    pb.PbSceneOpening = (function() {

        function PbSceneOpening(p) {
            this.pay = [];
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        PbSceneOpening.prototype.id = 0;
        PbSceneOpening.prototype.pay = $util.emptyArray;

        PbSceneOpening.create = function create(properties) {
            return new PbSceneOpening(properties);
        };

        PbSceneOpening.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.id != null && Object.hasOwnProperty.call(m, "id"))
                w.uint32(8).int32(m.id);
            if (m.pay != null && m.pay.length) {
                for (var i = 0; i < m.pay.length; ++i)
                    $root.pb.PbItem.encode(m.pay[i], w.uint32(18).fork()).ldelim();
            }
            return w;
        };

        PbSceneOpening.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.PbSceneOpening();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.id = r.int32();
                        break;
                    }
                case 2: {
                        if (!(m.pay && m.pay.length))
                            m.pay = [];
                        m.pay.push($root.pb.PbItem.decode(r, r.uint32()));
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        PbSceneOpening.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.PbSceneOpening";
        };

        return PbSceneOpening;
    })();

    pb.PbKeyV = (function() {

        function PbKeyV(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        PbKeyV.prototype.key = 0;
        PbKeyV.prototype.value = 0;

        PbKeyV.create = function create(properties) {
            return new PbKeyV(properties);
        };

        PbKeyV.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.key != null && Object.hasOwnProperty.call(m, "key"))
                w.uint32(8).int32(m.key);
            if (m.value != null && Object.hasOwnProperty.call(m, "value"))
                w.uint32(16).int32(m.value);
            return w;
        };

        PbKeyV.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.PbKeyV();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.key = r.int32();
                        break;
                    }
                case 2: {
                        m.value = r.int32();
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        PbKeyV.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.PbKeyV";
        };

        return PbKeyV;
    })();

    pb.PbAttr = (function() {

        function PbAttr(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        PbAttr.prototype.attrId = 0;
        PbAttr.prototype.value = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        PbAttr.create = function create(properties) {
            return new PbAttr(properties);
        };

        PbAttr.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.attrId != null && Object.hasOwnProperty.call(m, "attrId"))
                w.uint32(8).int32(m.attrId);
            if (m.value != null && Object.hasOwnProperty.call(m, "value"))
                w.uint32(16).int64(m.value);
            return w;
        };

        PbAttr.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.PbAttr();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.attrId = r.int32();
                        break;
                    }
                case 2: {
                        m.value = r.int64();
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        PbAttr.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.PbAttr";
        };

        return PbAttr;
    })();

    pb.FormationInfoResponse = (function() {

        function FormationInfoResponse(p) {
            this.formations = [];
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        FormationInfoResponse.prototype.formations = $util.emptyArray;

        FormationInfoResponse.create = function create(properties) {
            return new FormationInfoResponse(properties);
        };

        FormationInfoResponse.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.formations != null && m.formations.length) {
                for (var i = 0; i < m.formations.length; ++i)
                    $root.pb.PbFormation.encode(m.formations[i], w.uint32(10).fork()).ldelim();
            }
            return w;
        };

        FormationInfoResponse.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.FormationInfoResponse();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        if (!(m.formations && m.formations.length))
                            m.formations = [];
                        m.formations.push($root.pb.PbFormation.decode(r, r.uint32()));
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        FormationInfoResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.FormationInfoResponse";
        };

        FormationInfoResponse.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[1000001] = "ID"] = 1000001;
            return values;
        })();

        return FormationInfoResponse;
    })();

    pb.FormationChangeMessage = (function() {

        function FormationChangeMessage(p) {
            this.formations = [];
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        FormationChangeMessage.prototype.formations = $util.emptyArray;

        FormationChangeMessage.create = function create(properties) {
            return new FormationChangeMessage(properties);
        };

        FormationChangeMessage.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.formations != null && m.formations.length) {
                for (var i = 0; i < m.formations.length; ++i)
                    $root.pb.PbFormation.encode(m.formations[i], w.uint32(10).fork()).ldelim();
            }
            return w;
        };

        FormationChangeMessage.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.FormationChangeMessage();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        if (!(m.formations && m.formations.length))
                            m.formations = [];
                        m.formations.push($root.pb.PbFormation.decode(r, r.uint32()));
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        FormationChangeMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.FormationChangeMessage";
        };

        FormationChangeMessage.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[1000002] = "ID"] = 1000002;
            return values;
        })();

        return FormationChangeMessage;
    })();

    pb.FormationSaveRequest = (function() {

        function FormationSaveRequest(p) {
            this.posList = [];
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        FormationSaveRequest.prototype.type = 0;
        FormationSaveRequest.prototype.mainHero = 0;
        FormationSaveRequest.prototype.posList = $util.emptyArray;

        FormationSaveRequest.create = function create(properties) {
            return new FormationSaveRequest(properties);
        };

        FormationSaveRequest.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.type != null && Object.hasOwnProperty.call(m, "type"))
                w.uint32(8).int32(m.type);
            if (m.mainHero != null && Object.hasOwnProperty.call(m, "mainHero"))
                w.uint32(16).int32(m.mainHero);
            if (m.posList != null && m.posList.length) {
                for (var i = 0; i < m.posList.length; ++i)
                    $root.pb.PbFormationPos.encode(m.posList[i], w.uint32(26).fork()).ldelim();
            }
            return w;
        };

        FormationSaveRequest.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.FormationSaveRequest();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.type = r.int32();
                        break;
                    }
                case 2: {
                        m.mainHero = r.int32();
                        break;
                    }
                case 3: {
                        if (!(m.posList && m.posList.length))
                            m.posList = [];
                        m.posList.push($root.pb.PbFormationPos.decode(r, r.uint32()));
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        FormationSaveRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.FormationSaveRequest";
        };

        FormationSaveRequest.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[1000003] = "ID"] = 1000003;
            return values;
        })();

        return FormationSaveRequest;
    })();

    pb.FormationSaveResponse = (function() {

        function FormationSaveResponse(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        FormationSaveResponse.prototype.formation = null;

        FormationSaveResponse.create = function create(properties) {
            return new FormationSaveResponse(properties);
        };

        FormationSaveResponse.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.formation != null && Object.hasOwnProperty.call(m, "formation"))
                $root.pb.PbFormation.encode(m.formation, w.uint32(10).fork()).ldelim();
            return w;
        };

        FormationSaveResponse.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.FormationSaveResponse();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.formation = $root.pb.PbFormation.decode(r, r.uint32());
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        FormationSaveResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.FormationSaveResponse";
        };

        FormationSaveResponse.Proto = (function() {
            const valuesById = {}, values = Object.create(valuesById);
            values[valuesById[0] = "UNKNOWN"] = 0;
            values[valuesById[1000003] = "ID"] = 1000003;
            return values;
        })();

        return FormationSaveResponse;
    })();

    pb.PbFormation = (function() {

        function PbFormation(p) {
            this.posList = [];
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        PbFormation.prototype.type = 0;
        PbFormation.prototype.mainHeroIdentity = 0;
        PbFormation.prototype.posList = $util.emptyArray;
        PbFormation.prototype.fighting = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbFormation.prototype.equipFighting = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbFormation.prototype.manualChange = false;

        PbFormation.create = function create(properties) {
            return new PbFormation(properties);
        };

        PbFormation.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.type != null && Object.hasOwnProperty.call(m, "type"))
                w.uint32(8).int32(m.type);
            if (m.mainHeroIdentity != null && Object.hasOwnProperty.call(m, "mainHeroIdentity"))
                w.uint32(16).int32(m.mainHeroIdentity);
            if (m.posList != null && m.posList.length) {
                for (var i = 0; i < m.posList.length; ++i)
                    $root.pb.PbFormationPos.encode(m.posList[i], w.uint32(26).fork()).ldelim();
            }
            if (m.fighting != null && Object.hasOwnProperty.call(m, "fighting"))
                w.uint32(32).int64(m.fighting);
            if (m.equipFighting != null && Object.hasOwnProperty.call(m, "equipFighting"))
                w.uint32(40).int64(m.equipFighting);
            if (m.manualChange != null && Object.hasOwnProperty.call(m, "manualChange"))
                w.uint32(48).bool(m.manualChange);
            return w;
        };

        PbFormation.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.PbFormation();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.type = r.int32();
                        break;
                    }
                case 2: {
                        m.mainHeroIdentity = r.int32();
                        break;
                    }
                case 3: {
                        if (!(m.posList && m.posList.length))
                            m.posList = [];
                        m.posList.push($root.pb.PbFormationPos.decode(r, r.uint32()));
                        break;
                    }
                case 4: {
                        m.fighting = r.int64();
                        break;
                    }
                case 5: {
                        m.equipFighting = r.int64();
                        break;
                    }
                case 6: {
                        m.manualChange = r.bool();
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        PbFormation.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.PbFormation";
        };

        return PbFormation;
    })();

    pb.PbFormationPos = (function() {

        function PbFormationPos(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        PbFormationPos.prototype.pos = 0;
        PbFormationPos.prototype.identity = 0;

        PbFormationPos.create = function create(properties) {
            return new PbFormationPos(properties);
        };

        PbFormationPos.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.pos != null && Object.hasOwnProperty.call(m, "pos"))
                w.uint32(8).int32(m.pos);
            if (m.identity != null && Object.hasOwnProperty.call(m, "identity"))
                w.uint32(16).int32(m.identity);
            return w;
        };

        PbFormationPos.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.PbFormationPos();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.pos = r.int32();
                        break;
                    }
                case 2: {
                        m.identity = r.int32();
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        PbFormationPos.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.PbFormationPos";
        };

        return PbFormationPos;
    })();

    pb.PbFormationType = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "NO_FORMATION"] = 0;
        values[valuesById[1] = "MAIN"] = 1;
        values[valuesById[2] = "TOWER"] = 2;
        values[valuesById[3] = "TOWER_STRENGTH"] = 3;
        values[valuesById[4] = "TOWER_AGILITY"] = 4;
        values[valuesById[5] = "TOWER_WISDOM"] = 5;
        values[valuesById[6] = "GOLDEN_PIG"] = 6;
        return values;
    })();

    pb.PbBattleAttr = (function() {

        function PbBattleAttr(p) {
            if (p)
                for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                    if (p[ks[i]] != null)
                        this[ks[i]] = p[ks[i]];
        }

        PbBattleAttr.prototype.hp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.maxHp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.atk = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.def = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.hpRate = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.atkRate = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.defRate = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.dmgRate = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.hit = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.dodge = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.crit = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.critDmgRate = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.vampireRate = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.reduceDmg = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.reduceDmgRate = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.cdSpdRate = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.moveSpd = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.moveSpdRate = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.atkSpd = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.atkSpdRate = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.dmgbl = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.dmg = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.llmaxRate = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.lldmgRate = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.mjmaxRate = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.mjdmgRate = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.zlmaxRate = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.zldmgRate = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.woodDmg = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.mineDmg = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.llmax = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.lldmg = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.mjmax = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.mjdmg = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.zlmax = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.zldmg = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.battleRange = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.llpvedmg = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.mjpvedmg = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.zlpvedmg = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.pvedmg = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.fovRange = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.reducecrit = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.hpBrate = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
        PbBattleAttr.prototype.atkBrate = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        PbBattleAttr.create = function create(properties) {
            return new PbBattleAttr(properties);
        };

        PbBattleAttr.encode = function encode(m, w) {
            if (!w)
                w = $Writer.create();
            if (m.hp != null && Object.hasOwnProperty.call(m, "hp"))
                w.uint32(8).int64(m.hp);
            if (m.maxHp != null && Object.hasOwnProperty.call(m, "maxHp"))
                w.uint32(16).int64(m.maxHp);
            if (m.atk != null && Object.hasOwnProperty.call(m, "atk"))
                w.uint32(24).int64(m.atk);
            if (m.def != null && Object.hasOwnProperty.call(m, "def"))
                w.uint32(32).int64(m.def);
            if (m.hpRate != null && Object.hasOwnProperty.call(m, "hpRate"))
                w.uint32(40).int64(m.hpRate);
            if (m.atkRate != null && Object.hasOwnProperty.call(m, "atkRate"))
                w.uint32(48).int64(m.atkRate);
            if (m.defRate != null && Object.hasOwnProperty.call(m, "defRate"))
                w.uint32(56).int64(m.defRate);
            if (m.dmgRate != null && Object.hasOwnProperty.call(m, "dmgRate"))
                w.uint32(64).int64(m.dmgRate);
            if (m.hit != null && Object.hasOwnProperty.call(m, "hit"))
                w.uint32(72).int64(m.hit);
            if (m.dodge != null && Object.hasOwnProperty.call(m, "dodge"))
                w.uint32(80).int64(m.dodge);
            if (m.crit != null && Object.hasOwnProperty.call(m, "crit"))
                w.uint32(88).int64(m.crit);
            if (m.critDmgRate != null && Object.hasOwnProperty.call(m, "critDmgRate"))
                w.uint32(96).int64(m.critDmgRate);
            if (m.vampireRate != null && Object.hasOwnProperty.call(m, "vampireRate"))
                w.uint32(104).int64(m.vampireRate);
            if (m.reduceDmg != null && Object.hasOwnProperty.call(m, "reduceDmg"))
                w.uint32(112).int64(m.reduceDmg);
            if (m.reduceDmgRate != null && Object.hasOwnProperty.call(m, "reduceDmgRate"))
                w.uint32(120).int64(m.reduceDmgRate);
            if (m.cdSpdRate != null && Object.hasOwnProperty.call(m, "cdSpdRate"))
                w.uint32(128).int64(m.cdSpdRate);
            if (m.moveSpd != null && Object.hasOwnProperty.call(m, "moveSpd"))
                w.uint32(136).int64(m.moveSpd);
            if (m.moveSpdRate != null && Object.hasOwnProperty.call(m, "moveSpdRate"))
                w.uint32(144).int64(m.moveSpdRate);
            if (m.atkSpd != null && Object.hasOwnProperty.call(m, "atkSpd"))
                w.uint32(152).int64(m.atkSpd);
            if (m.atkSpdRate != null && Object.hasOwnProperty.call(m, "atkSpdRate"))
                w.uint32(160).int64(m.atkSpdRate);
            if (m.dmgbl != null && Object.hasOwnProperty.call(m, "dmgbl"))
                w.uint32(168).int64(m.dmgbl);
            if (m.dmg != null && Object.hasOwnProperty.call(m, "dmg"))
                w.uint32(176).int64(m.dmg);
            if (m.llmaxRate != null && Object.hasOwnProperty.call(m, "llmaxRate"))
                w.uint32(184).int64(m.llmaxRate);
            if (m.lldmgRate != null && Object.hasOwnProperty.call(m, "lldmgRate"))
                w.uint32(192).int64(m.lldmgRate);
            if (m.mjmaxRate != null && Object.hasOwnProperty.call(m, "mjmaxRate"))
                w.uint32(200).int64(m.mjmaxRate);
            if (m.mjdmgRate != null && Object.hasOwnProperty.call(m, "mjdmgRate"))
                w.uint32(208).int64(m.mjdmgRate);
            if (m.zlmaxRate != null && Object.hasOwnProperty.call(m, "zlmaxRate"))
                w.uint32(216).int64(m.zlmaxRate);
            if (m.zldmgRate != null && Object.hasOwnProperty.call(m, "zldmgRate"))
                w.uint32(224).int64(m.zldmgRate);
            if (m.woodDmg != null && Object.hasOwnProperty.call(m, "woodDmg"))
                w.uint32(232).int64(m.woodDmg);
            if (m.mineDmg != null && Object.hasOwnProperty.call(m, "mineDmg"))
                w.uint32(240).int64(m.mineDmg);
            if (m.llmax != null && Object.hasOwnProperty.call(m, "llmax"))
                w.uint32(248).int64(m.llmax);
            if (m.lldmg != null && Object.hasOwnProperty.call(m, "lldmg"))
                w.uint32(256).int64(m.lldmg);
            if (m.mjmax != null && Object.hasOwnProperty.call(m, "mjmax"))
                w.uint32(264).int64(m.mjmax);
            if (m.mjdmg != null && Object.hasOwnProperty.call(m, "mjdmg"))
                w.uint32(272).int64(m.mjdmg);
            if (m.zlmax != null && Object.hasOwnProperty.call(m, "zlmax"))
                w.uint32(280).int64(m.zlmax);
            if (m.zldmg != null && Object.hasOwnProperty.call(m, "zldmg"))
                w.uint32(288).int64(m.zldmg);
            if (m.battleRange != null && Object.hasOwnProperty.call(m, "battleRange"))
                w.uint32(296).int64(m.battleRange);
            if (m.llpvedmg != null && Object.hasOwnProperty.call(m, "llpvedmg"))
                w.uint32(304).int64(m.llpvedmg);
            if (m.mjpvedmg != null && Object.hasOwnProperty.call(m, "mjpvedmg"))
                w.uint32(312).int64(m.mjpvedmg);
            if (m.zlpvedmg != null && Object.hasOwnProperty.call(m, "zlpvedmg"))
                w.uint32(320).int64(m.zlpvedmg);
            if (m.pvedmg != null && Object.hasOwnProperty.call(m, "pvedmg"))
                w.uint32(328).int64(m.pvedmg);
            if (m.fovRange != null && Object.hasOwnProperty.call(m, "fovRange"))
                w.uint32(336).int64(m.fovRange);
            if (m.reducecrit != null && Object.hasOwnProperty.call(m, "reducecrit"))
                w.uint32(344).int64(m.reducecrit);
            if (m.hpBrate != null && Object.hasOwnProperty.call(m, "hpBrate"))
                w.uint32(352).int64(m.hpBrate);
            if (m.atkBrate != null && Object.hasOwnProperty.call(m, "atkBrate"))
                w.uint32(360).int64(m.atkBrate);
            return w;
        };

        PbBattleAttr.decode = function decode(r, l, e) {
            if (!(r instanceof $Reader))
                r = $Reader.create(r);
            var c = l === undefined ? r.len : r.pos + l, m = new $root.pb.PbBattleAttr();
            while (r.pos < c) {
                var t = r.uint32();
                if (t === e)
                    break;
                switch (t >>> 3) {
                case 1: {
                        m.hp = r.int64();
                        break;
                    }
                case 2: {
                        m.maxHp = r.int64();
                        break;
                    }
                case 3: {
                        m.atk = r.int64();
                        break;
                    }
                case 4: {
                        m.def = r.int64();
                        break;
                    }
                case 5: {
                        m.hpRate = r.int64();
                        break;
                    }
                case 6: {
                        m.atkRate = r.int64();
                        break;
                    }
                case 7: {
                        m.defRate = r.int64();
                        break;
                    }
                case 8: {
                        m.dmgRate = r.int64();
                        break;
                    }
                case 9: {
                        m.hit = r.int64();
                        break;
                    }
                case 10: {
                        m.dodge = r.int64();
                        break;
                    }
                case 11: {
                        m.crit = r.int64();
                        break;
                    }
                case 12: {
                        m.critDmgRate = r.int64();
                        break;
                    }
                case 13: {
                        m.vampireRate = r.int64();
                        break;
                    }
                case 14: {
                        m.reduceDmg = r.int64();
                        break;
                    }
                case 15: {
                        m.reduceDmgRate = r.int64();
                        break;
                    }
                case 16: {
                        m.cdSpdRate = r.int64();
                        break;
                    }
                case 17: {
                        m.moveSpd = r.int64();
                        break;
                    }
                case 18: {
                        m.moveSpdRate = r.int64();
                        break;
                    }
                case 19: {
                        m.atkSpd = r.int64();
                        break;
                    }
                case 20: {
                        m.atkSpdRate = r.int64();
                        break;
                    }
                case 21: {
                        m.dmgbl = r.int64();
                        break;
                    }
                case 22: {
                        m.dmg = r.int64();
                        break;
                    }
                case 23: {
                        m.llmaxRate = r.int64();
                        break;
                    }
                case 24: {
                        m.lldmgRate = r.int64();
                        break;
                    }
                case 25: {
                        m.mjmaxRate = r.int64();
                        break;
                    }
                case 26: {
                        m.mjdmgRate = r.int64();
                        break;
                    }
                case 27: {
                        m.zlmaxRate = r.int64();
                        break;
                    }
                case 28: {
                        m.zldmgRate = r.int64();
                        break;
                    }
                case 29: {
                        m.woodDmg = r.int64();
                        break;
                    }
                case 30: {
                        m.mineDmg = r.int64();
                        break;
                    }
                case 31: {
                        m.llmax = r.int64();
                        break;
                    }
                case 32: {
                        m.lldmg = r.int64();
                        break;
                    }
                case 33: {
                        m.mjmax = r.int64();
                        break;
                    }
                case 34: {
                        m.mjdmg = r.int64();
                        break;
                    }
                case 35: {
                        m.zlmax = r.int64();
                        break;
                    }
                case 36: {
                        m.zldmg = r.int64();
                        break;
                    }
                case 37: {
                        m.battleRange = r.int64();
                        break;
                    }
                case 38: {
                        m.llpvedmg = r.int64();
                        break;
                    }
                case 39: {
                        m.mjpvedmg = r.int64();
                        break;
                    }
                case 40: {
                        m.zlpvedmg = r.int64();
                        break;
                    }
                case 41: {
                        m.pvedmg = r.int64();
                        break;
                    }
                case 42: {
                        m.fovRange = r.int64();
                        break;
                    }
                case 43: {
                        m.reducecrit = r.int64();
                        break;
                    }
                case 44: {
                        m.hpBrate = r.int64();
                        break;
                    }
                case 45: {
                        m.atkBrate = r.int64();
                        break;
                    }
                default:
                    r.skipType(t & 7);
                    break;
                }
            }
            return m;
        };

        PbBattleAttr.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/pb.PbBattleAttr";
        };

        return PbBattleAttr;
    })();

    return pb;
})();

export const MessageWrapper = $root.MessageWrapper = (() => {

    function MessageWrapper(p) {
        if (p)
            for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                if (p[ks[i]] != null)
                    this[ks[i]] = p[ks[i]];
    }

    MessageWrapper.prototype.id = 0;
    MessageWrapper.prototype.protoId = 0;
    MessageWrapper.prototype.data = $util.newBuffer([]);
    MessageWrapper.prototype.toReplyId = 0;
    MessageWrapper.prototype.replyId = 0;

    MessageWrapper.create = function create(properties) {
        return new MessageWrapper(properties);
    };

    MessageWrapper.encode = function encode(m, w) {
        if (!w)
            w = $Writer.create();
        if (m.id != null && Object.hasOwnProperty.call(m, "id"))
            w.uint32(8).uint32(m.id);
        if (m.protoId != null && Object.hasOwnProperty.call(m, "protoId"))
            w.uint32(16).uint32(m.protoId);
        if (m.data != null && Object.hasOwnProperty.call(m, "data"))
            w.uint32(26).bytes(m.data);
        if (m.toReplyId != null && Object.hasOwnProperty.call(m, "toReplyId"))
            w.uint32(32).uint32(m.toReplyId);
        if (m.replyId != null && Object.hasOwnProperty.call(m, "replyId"))
            w.uint32(40).uint32(m.replyId);
        return w;
    };

    MessageWrapper.decode = function decode(r, l, e) {
        if (!(r instanceof $Reader))
            r = $Reader.create(r);
        var c = l === undefined ? r.len : r.pos + l, m = new $root.MessageWrapper();
        while (r.pos < c) {
            var t = r.uint32();
            if (t === e)
                break;
            switch (t >>> 3) {
            case 1: {
                    m.id = r.uint32();
                    break;
                }
            case 2: {
                    m.protoId = r.uint32();
                    break;
                }
            case 3: {
                    m.data = r.bytes();
                    break;
                }
            case 4: {
                    m.toReplyId = r.uint32();
                    break;
                }
            case 5: {
                    m.replyId = r.uint32();
                    break;
                }
            default:
                r.skipType(t & 7);
                break;
            }
        }
        return m;
    };

    MessageWrapper.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/MessageWrapper";
    };

    return MessageWrapper;
})();

export const LoginRequest = $root.LoginRequest = (() => {

    function LoginRequest(p) {
        if (p)
            for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                if (p[ks[i]] != null)
                    this[ks[i]] = p[ks[i]];
    }

    LoginRequest.prototype.openId = "";
    LoginRequest.prototype.serverId = 0;
    LoginRequest.prototype.deviceType = "";
    LoginRequest.prototype.deviceId = "";
    LoginRequest.prototype.channelId = 0;
    LoginRequest.prototype.channelIdentity = "";
    LoginRequest.prototype.channelData = $util.newBuffer([]);
    LoginRequest.prototype.reConnect = false;

    LoginRequest.create = function create(properties) {
        return new LoginRequest(properties);
    };

    LoginRequest.encode = function encode(m, w) {
        if (!w)
            w = $Writer.create();
        if (m.openId != null && Object.hasOwnProperty.call(m, "openId"))
            w.uint32(10).string(m.openId);
        if (m.serverId != null && Object.hasOwnProperty.call(m, "serverId"))
            w.uint32(16).int32(m.serverId);
        if (m.deviceType != null && Object.hasOwnProperty.call(m, "deviceType"))
            w.uint32(26).string(m.deviceType);
        if (m.deviceId != null && Object.hasOwnProperty.call(m, "deviceId"))
            w.uint32(34).string(m.deviceId);
        if (m.channelId != null && Object.hasOwnProperty.call(m, "channelId"))
            w.uint32(40).int32(m.channelId);
        if (m.channelIdentity != null && Object.hasOwnProperty.call(m, "channelIdentity"))
            w.uint32(50).string(m.channelIdentity);
        if (m.channelData != null && Object.hasOwnProperty.call(m, "channelData"))
            w.uint32(58).bytes(m.channelData);
        if (m.reConnect != null && Object.hasOwnProperty.call(m, "reConnect"))
            w.uint32(64).bool(m.reConnect);
        return w;
    };

    LoginRequest.decode = function decode(r, l, e) {
        if (!(r instanceof $Reader))
            r = $Reader.create(r);
        var c = l === undefined ? r.len : r.pos + l, m = new $root.LoginRequest();
        while (r.pos < c) {
            var t = r.uint32();
            if (t === e)
                break;
            switch (t >>> 3) {
            case 1: {
                    m.openId = r.string();
                    break;
                }
            case 2: {
                    m.serverId = r.int32();
                    break;
                }
            case 3: {
                    m.deviceType = r.string();
                    break;
                }
            case 4: {
                    m.deviceId = r.string();
                    break;
                }
            case 5: {
                    m.channelId = r.int32();
                    break;
                }
            case 6: {
                    m.channelIdentity = r.string();
                    break;
                }
            case 7: {
                    m.channelData = r.bytes();
                    break;
                }
            case 8: {
                    m.reConnect = r.bool();
                    break;
                }
            default:
                r.skipType(t & 7);
                break;
            }
        }
        return m;
    };

    LoginRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/LoginRequest";
    };

    LoginRequest.Proto = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "UNKNOWN"] = 0;
        values[valuesById[200001] = "ID"] = 200001;
        return values;
    })();

    return LoginRequest;
})();

export const LoginResponse = $root.LoginResponse = (() => {

    function LoginResponse(p) {
        if (p)
            for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                if (p[ks[i]] != null)
                    this[ks[i]] = p[ks[i]];
    }

    LoginResponse.prototype.status = 0;
    LoginResponse.prototype.pid = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
    LoginResponse.prototype.serverTime = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
    LoginResponse.prototype.offsetUtc = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
    LoginResponse.prototype.nick = "";
    LoginResponse.prototype.head = "";
    LoginResponse.prototype.level = 0;
    LoginResponse.prototype.exp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
    LoginResponse.prototype.forbindEndTime = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
    LoginResponse.prototype.changeNameTimes = 0;
    LoginResponse.prototype.buyPrivilege = false;
    LoginResponse.prototype.vipLevel = 0;
    LoginResponse.prototype.openDay = 0;
    LoginResponse.prototype.openGm = false;
    LoginResponse.prototype.isCreate = false;
    LoginResponse.prototype.createTime = $util.Long ? $util.Long.fromBits(0,0,false) : 0;
    LoginResponse.prototype.vipExp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

    LoginResponse.create = function create(properties) {
        return new LoginResponse(properties);
    };

    LoginResponse.encode = function encode(m, w) {
        if (!w)
            w = $Writer.create();
        if (m.status != null && Object.hasOwnProperty.call(m, "status"))
            w.uint32(8).int32(m.status);
        if (m.pid != null && Object.hasOwnProperty.call(m, "pid"))
            w.uint32(16).int64(m.pid);
        if (m.serverTime != null && Object.hasOwnProperty.call(m, "serverTime"))
            w.uint32(24).int64(m.serverTime);
        if (m.offsetUtc != null && Object.hasOwnProperty.call(m, "offsetUtc"))
            w.uint32(32).int64(m.offsetUtc);
        if (m.nick != null && Object.hasOwnProperty.call(m, "nick"))
            w.uint32(42).string(m.nick);
        if (m.head != null && Object.hasOwnProperty.call(m, "head"))
            w.uint32(50).string(m.head);
        if (m.level != null && Object.hasOwnProperty.call(m, "level"))
            w.uint32(56).int32(m.level);
        if (m.exp != null && Object.hasOwnProperty.call(m, "exp"))
            w.uint32(64).int64(m.exp);
        if (m.forbindEndTime != null && Object.hasOwnProperty.call(m, "forbindEndTime"))
            w.uint32(72).int64(m.forbindEndTime);
        if (m.changeNameTimes != null && Object.hasOwnProperty.call(m, "changeNameTimes"))
            w.uint32(80).int32(m.changeNameTimes);
        if (m.buyPrivilege != null && Object.hasOwnProperty.call(m, "buyPrivilege"))
            w.uint32(88).bool(m.buyPrivilege);
        if (m.vipLevel != null && Object.hasOwnProperty.call(m, "vipLevel"))
            w.uint32(96).int32(m.vipLevel);
        if (m.openDay != null && Object.hasOwnProperty.call(m, "openDay"))
            w.uint32(104).int32(m.openDay);
        if (m.openGm != null && Object.hasOwnProperty.call(m, "openGm"))
            w.uint32(112).bool(m.openGm);
        if (m.isCreate != null && Object.hasOwnProperty.call(m, "isCreate"))
            w.uint32(120).bool(m.isCreate);
        if (m.createTime != null && Object.hasOwnProperty.call(m, "createTime"))
            w.uint32(128).int64(m.createTime);
        if (m.vipExp != null && Object.hasOwnProperty.call(m, "vipExp"))
            w.uint32(136).int64(m.vipExp);
        return w;
    };

    LoginResponse.decode = function decode(r, l, e) {
        if (!(r instanceof $Reader))
            r = $Reader.create(r);
        var c = l === undefined ? r.len : r.pos + l, m = new $root.LoginResponse();
        while (r.pos < c) {
            var t = r.uint32();
            if (t === e)
                break;
            switch (t >>> 3) {
            case 1: {
                    m.status = r.int32();
                    break;
                }
            case 2: {
                    m.pid = r.int64();
                    break;
                }
            case 3: {
                    m.serverTime = r.int64();
                    break;
                }
            case 4: {
                    m.offsetUtc = r.int64();
                    break;
                }
            case 5: {
                    m.nick = r.string();
                    break;
                }
            case 6: {
                    m.head = r.string();
                    break;
                }
            case 7: {
                    m.level = r.int32();
                    break;
                }
            case 8: {
                    m.exp = r.int64();
                    break;
                }
            case 9: {
                    m.forbindEndTime = r.int64();
                    break;
                }
            case 10: {
                    m.changeNameTimes = r.int32();
                    break;
                }
            case 11: {
                    m.buyPrivilege = r.bool();
                    break;
                }
            case 12: {
                    m.vipLevel = r.int32();
                    break;
                }
            case 13: {
                    m.openDay = r.int32();
                    break;
                }
            case 14: {
                    m.openGm = r.bool();
                    break;
                }
            case 15: {
                    m.isCreate = r.bool();
                    break;
                }
            case 16: {
                    m.createTime = r.int64();
                    break;
                }
            case 17: {
                    m.vipExp = r.int64();
                    break;
                }
            default:
                r.skipType(t & 7);
                break;
            }
        }
        return m;
    };

    LoginResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/LoginResponse";
    };

    LoginResponse.Proto = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "UNKNOWN"] = 0;
        values[valuesById[200001] = "ID"] = 200001;
        return values;
    })();

    return LoginResponse;
})();

export const LogoutRequest = $root.LogoutRequest = (() => {

    function LogoutRequest(p) {
        if (p)
            for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                if (p[ks[i]] != null)
                    this[ks[i]] = p[ks[i]];
    }

    LogoutRequest.create = function create(properties) {
        return new LogoutRequest(properties);
    };

    LogoutRequest.encode = function encode(m, w) {
        if (!w)
            w = $Writer.create();
        return w;
    };

    LogoutRequest.decode = function decode(r, l, e) {
        if (!(r instanceof $Reader))
            r = $Reader.create(r);
        var c = l === undefined ? r.len : r.pos + l, m = new $root.LogoutRequest();
        while (r.pos < c) {
            var t = r.uint32();
            if (t === e)
                break;
            switch (t >>> 3) {
            default:
                r.skipType(t & 7);
                break;
            }
        }
        return m;
    };

    LogoutRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/LogoutRequest";
    };

    LogoutRequest.Proto = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "UNKNOWN"] = 0;
        values[valuesById[200003] = "ID"] = 200003;
        return values;
    })();

    return LogoutRequest;
})();

export const LogoutMessage = $root.LogoutMessage = (() => {

    function LogoutMessage(p) {
        if (p)
            for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                if (p[ks[i]] != null)
                    this[ks[i]] = p[ks[i]];
    }

    LogoutMessage.prototype.type = 0;
    LogoutMessage.prototype.forbindEndTime = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

    LogoutMessage.create = function create(properties) {
        return new LogoutMessage(properties);
    };

    LogoutMessage.encode = function encode(m, w) {
        if (!w)
            w = $Writer.create();
        if (m.type != null && Object.hasOwnProperty.call(m, "type"))
            w.uint32(8).int32(m.type);
        if (m.forbindEndTime != null && Object.hasOwnProperty.call(m, "forbindEndTime"))
            w.uint32(16).int64(m.forbindEndTime);
        return w;
    };

    LogoutMessage.decode = function decode(r, l, e) {
        if (!(r instanceof $Reader))
            r = $Reader.create(r);
        var c = l === undefined ? r.len : r.pos + l, m = new $root.LogoutMessage();
        while (r.pos < c) {
            var t = r.uint32();
            if (t === e)
                break;
            switch (t >>> 3) {
            case 1: {
                    m.type = r.int32();
                    break;
                }
            case 2: {
                    m.forbindEndTime = r.int64();
                    break;
                }
            default:
                r.skipType(t & 7);
                break;
            }
        }
        return m;
    };

    LogoutMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/LogoutMessage";
    };

    LogoutMessage.Proto = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "UNKNOWN"] = 0;
        values[valuesById[200004] = "ID"] = 200004;
        return values;
    })();

    return LogoutMessage;
})();

export const HeartBeatRequest = $root.HeartBeatRequest = (() => {

    function HeartBeatRequest(p) {
        if (p)
            for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                if (p[ks[i]] != null)
                    this[ks[i]] = p[ks[i]];
    }

    HeartBeatRequest.create = function create(properties) {
        return new HeartBeatRequest(properties);
    };

    HeartBeatRequest.encode = function encode(m, w) {
        if (!w)
            w = $Writer.create();
        return w;
    };

    HeartBeatRequest.decode = function decode(r, l, e) {
        if (!(r instanceof $Reader))
            r = $Reader.create(r);
        var c = l === undefined ? r.len : r.pos + l, m = new $root.HeartBeatRequest();
        while (r.pos < c) {
            var t = r.uint32();
            if (t === e)
                break;
            switch (t >>> 3) {
            default:
                r.skipType(t & 7);
                break;
            }
        }
        return m;
    };

    HeartBeatRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/HeartBeatRequest";
    };

    HeartBeatRequest.Proto = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "UNKNOWN"] = 0;
        values[valuesById[200005] = "ID"] = 200005;
        return values;
    })();

    return HeartBeatRequest;
})();

export const HeartBeatResponse = $root.HeartBeatResponse = (() => {

    function HeartBeatResponse(p) {
        if (p)
            for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                if (p[ks[i]] != null)
                    this[ks[i]] = p[ks[i]];
    }

    HeartBeatResponse.prototype.serverTime = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

    HeartBeatResponse.create = function create(properties) {
        return new HeartBeatResponse(properties);
    };

    HeartBeatResponse.encode = function encode(m, w) {
        if (!w)
            w = $Writer.create();
        if (m.serverTime != null && Object.hasOwnProperty.call(m, "serverTime"))
            w.uint32(8).int64(m.serverTime);
        return w;
    };

    HeartBeatResponse.decode = function decode(r, l, e) {
        if (!(r instanceof $Reader))
            r = $Reader.create(r);
        var c = l === undefined ? r.len : r.pos + l, m = new $root.HeartBeatResponse();
        while (r.pos < c) {
            var t = r.uint32();
            if (t === e)
                break;
            switch (t >>> 3) {
            case 1: {
                    m.serverTime = r.int64();
                    break;
                }
            default:
                r.skipType(t & 7);
                break;
            }
        }
        return m;
    };

    HeartBeatResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/HeartBeatResponse";
    };

    HeartBeatResponse.Proto = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "UNKNOWN"] = 0;
        values[valuesById[200005] = "ID"] = 200005;
        return values;
    })();

    return HeartBeatResponse;
})();

export const ChangeNameRequest = $root.ChangeNameRequest = (() => {

    function ChangeNameRequest(p) {
        if (p)
            for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                if (p[ks[i]] != null)
                    this[ks[i]] = p[ks[i]];
    }

    ChangeNameRequest.prototype.newName = "";
    ChangeNameRequest.prototype.channelIdentity = "";

    ChangeNameRequest.create = function create(properties) {
        return new ChangeNameRequest(properties);
    };

    ChangeNameRequest.encode = function encode(m, w) {
        if (!w)
            w = $Writer.create();
        if (m.newName != null && Object.hasOwnProperty.call(m, "newName"))
            w.uint32(10).string(m.newName);
        if (m.channelIdentity != null && Object.hasOwnProperty.call(m, "channelIdentity"))
            w.uint32(18).string(m.channelIdentity);
        return w;
    };

    ChangeNameRequest.decode = function decode(r, l, e) {
        if (!(r instanceof $Reader))
            r = $Reader.create(r);
        var c = l === undefined ? r.len : r.pos + l, m = new $root.ChangeNameRequest();
        while (r.pos < c) {
            var t = r.uint32();
            if (t === e)
                break;
            switch (t >>> 3) {
            case 1: {
                    m.newName = r.string();
                    break;
                }
            case 2: {
                    m.channelIdentity = r.string();
                    break;
                }
            default:
                r.skipType(t & 7);
                break;
            }
        }
        return m;
    };

    ChangeNameRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/ChangeNameRequest";
    };

    ChangeNameRequest.Proto = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "UNKNOWN"] = 0;
        values[valuesById[200006] = "ID"] = 200006;
        return values;
    })();

    return ChangeNameRequest;
})();

export const ChangeNameResponse = $root.ChangeNameResponse = (() => {

    function ChangeNameResponse(p) {
        if (p)
            for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                if (p[ks[i]] != null)
                    this[ks[i]] = p[ks[i]];
    }

    ChangeNameResponse.prototype.newName = "";
    ChangeNameResponse.prototype.changeNameTimes = 0;

    ChangeNameResponse.create = function create(properties) {
        return new ChangeNameResponse(properties);
    };

    ChangeNameResponse.encode = function encode(m, w) {
        if (!w)
            w = $Writer.create();
        if (m.newName != null && Object.hasOwnProperty.call(m, "newName"))
            w.uint32(10).string(m.newName);
        if (m.changeNameTimes != null && Object.hasOwnProperty.call(m, "changeNameTimes"))
            w.uint32(16).int32(m.changeNameTimes);
        return w;
    };

    ChangeNameResponse.decode = function decode(r, l, e) {
        if (!(r instanceof $Reader))
            r = $Reader.create(r);
        var c = l === undefined ? r.len : r.pos + l, m = new $root.ChangeNameResponse();
        while (r.pos < c) {
            var t = r.uint32();
            if (t === e)
                break;
            switch (t >>> 3) {
            case 1: {
                    m.newName = r.string();
                    break;
                }
            case 2: {
                    m.changeNameTimes = r.int32();
                    break;
                }
            default:
                r.skipType(t & 7);
                break;
            }
        }
        return m;
    };

    ChangeNameResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/ChangeNameResponse";
    };

    ChangeNameResponse.Proto = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "UNKNOWN"] = 0;
        values[valuesById[200006] = "ID"] = 200006;
        return values;
    })();

    return ChangeNameResponse;
})();

export const PlayerInfoChangeMessage = $root.PlayerInfoChangeMessage = (() => {

    function PlayerInfoChangeMessage(p) {
        if (p)
            for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                if (p[ks[i]] != null)
                    this[ks[i]] = p[ks[i]];
    }

    PlayerInfoChangeMessage.prototype.buyPrivilege = false;

    PlayerInfoChangeMessage.create = function create(properties) {
        return new PlayerInfoChangeMessage(properties);
    };

    PlayerInfoChangeMessage.encode = function encode(m, w) {
        if (!w)
            w = $Writer.create();
        if (m.buyPrivilege != null && Object.hasOwnProperty.call(m, "buyPrivilege"))
            w.uint32(8).bool(m.buyPrivilege);
        return w;
    };

    PlayerInfoChangeMessage.decode = function decode(r, l, e) {
        if (!(r instanceof $Reader))
            r = $Reader.create(r);
        var c = l === undefined ? r.len : r.pos + l, m = new $root.PlayerInfoChangeMessage();
        while (r.pos < c) {
            var t = r.uint32();
            if (t === e)
                break;
            switch (t >>> 3) {
            case 1: {
                    m.buyPrivilege = r.bool();
                    break;
                }
            default:
                r.skipType(t & 7);
                break;
            }
        }
        return m;
    };

    PlayerInfoChangeMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/PlayerInfoChangeMessage";
    };

    PlayerInfoChangeMessage.Proto = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "UNKNOWN"] = 0;
        values[valuesById[200007] = "ID"] = 200007;
        return values;
    })();

    return PlayerInfoChangeMessage;
})();

export const PlayerPushEndMessage = $root.PlayerPushEndMessage = (() => {

    function PlayerPushEndMessage(p) {
        if (p)
            for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                if (p[ks[i]] != null)
                    this[ks[i]] = p[ks[i]];
    }

    PlayerPushEndMessage.create = function create(properties) {
        return new PlayerPushEndMessage(properties);
    };

    PlayerPushEndMessage.encode = function encode(m, w) {
        if (!w)
            w = $Writer.create();
        return w;
    };

    PlayerPushEndMessage.decode = function decode(r, l, e) {
        if (!(r instanceof $Reader))
            r = $Reader.create(r);
        var c = l === undefined ? r.len : r.pos + l, m = new $root.PlayerPushEndMessage();
        while (r.pos < c) {
            var t = r.uint32();
            if (t === e)
                break;
            switch (t >>> 3) {
            default:
                r.skipType(t & 7);
                break;
            }
        }
        return m;
    };

    PlayerPushEndMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/PlayerPushEndMessage";
    };

    PlayerPushEndMessage.Proto = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "UNKNOWN"] = 0;
        values[valuesById[200008] = "ID"] = 200008;
        return values;
    })();

    return PlayerPushEndMessage;
})();

export const PlayerModuleInfoRequest = $root.PlayerModuleInfoRequest = (() => {

    function PlayerModuleInfoRequest(p) {
        if (p)
            for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                if (p[ks[i]] != null)
                    this[ks[i]] = p[ks[i]];
    }

    PlayerModuleInfoRequest.prototype.module = "";

    PlayerModuleInfoRequest.create = function create(properties) {
        return new PlayerModuleInfoRequest(properties);
    };

    PlayerModuleInfoRequest.encode = function encode(m, w) {
        if (!w)
            w = $Writer.create();
        if (m.module != null && Object.hasOwnProperty.call(m, "module"))
            w.uint32(10).string(m.module);
        return w;
    };

    PlayerModuleInfoRequest.decode = function decode(r, l, e) {
        if (!(r instanceof $Reader))
            r = $Reader.create(r);
        var c = l === undefined ? r.len : r.pos + l, m = new $root.PlayerModuleInfoRequest();
        while (r.pos < c) {
            var t = r.uint32();
            if (t === e)
                break;
            switch (t >>> 3) {
            case 1: {
                    m.module = r.string();
                    break;
                }
            default:
                r.skipType(t & 7);
                break;
            }
        }
        return m;
    };

    PlayerModuleInfoRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/PlayerModuleInfoRequest";
    };

    PlayerModuleInfoRequest.Proto = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "UNKNOWN"] = 0;
        values[valuesById[200009] = "ID"] = 200009;
        return values;
    })();

    return PlayerModuleInfoRequest;
})();

export const PlayerModuleInfoResponse = $root.PlayerModuleInfoResponse = (() => {

    function PlayerModuleInfoResponse(p) {
        if (p)
            for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                if (p[ks[i]] != null)
                    this[ks[i]] = p[ks[i]];
    }

    PlayerModuleInfoResponse.create = function create(properties) {
        return new PlayerModuleInfoResponse(properties);
    };

    PlayerModuleInfoResponse.encode = function encode(m, w) {
        if (!w)
            w = $Writer.create();
        return w;
    };

    PlayerModuleInfoResponse.decode = function decode(r, l, e) {
        if (!(r instanceof $Reader))
            r = $Reader.create(r);
        var c = l === undefined ? r.len : r.pos + l, m = new $root.PlayerModuleInfoResponse();
        while (r.pos < c) {
            var t = r.uint32();
            if (t === e)
                break;
            switch (t >>> 3) {
            default:
                r.skipType(t & 7);
                break;
            }
        }
        return m;
    };

    PlayerModuleInfoResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/PlayerModuleInfoResponse";
    };

    PlayerModuleInfoResponse.Proto = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "UNKNOWN"] = 0;
        values[valuesById[200009] = "ID"] = 200009;
        return values;
    })();

    return PlayerModuleInfoResponse;
})();

export const PlayerMoreInfoRequest = $root.PlayerMoreInfoRequest = (() => {

    function PlayerMoreInfoRequest(p) {
        if (p)
            for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                if (p[ks[i]] != null)
                    this[ks[i]] = p[ks[i]];
    }

    PlayerMoreInfoRequest.create = function create(properties) {
        return new PlayerMoreInfoRequest(properties);
    };

    PlayerMoreInfoRequest.encode = function encode(m, w) {
        if (!w)
            w = $Writer.create();
        return w;
    };

    PlayerMoreInfoRequest.decode = function decode(r, l, e) {
        if (!(r instanceof $Reader))
            r = $Reader.create(r);
        var c = l === undefined ? r.len : r.pos + l, m = new $root.PlayerMoreInfoRequest();
        while (r.pos < c) {
            var t = r.uint32();
            if (t === e)
                break;
            switch (t >>> 3) {
            default:
                r.skipType(t & 7);
                break;
            }
        }
        return m;
    };

    PlayerMoreInfoRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/PlayerMoreInfoRequest";
    };

    PlayerMoreInfoRequest.Proto = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "UNKNOWN"] = 0;
        values[valuesById[200010] = "ID"] = 200010;
        return values;
    })();

    return PlayerMoreInfoRequest;
})();

export const PlayerMoreInfoResponse = $root.PlayerMoreInfoResponse = (() => {

    function PlayerMoreInfoResponse(p) {
        if (p)
            for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                if (p[ks[i]] != null)
                    this[ks[i]] = p[ks[i]];
    }

    PlayerMoreInfoResponse.create = function create(properties) {
        return new PlayerMoreInfoResponse(properties);
    };

    PlayerMoreInfoResponse.encode = function encode(m, w) {
        if (!w)
            w = $Writer.create();
        return w;
    };

    PlayerMoreInfoResponse.decode = function decode(r, l, e) {
        if (!(r instanceof $Reader))
            r = $Reader.create(r);
        var c = l === undefined ? r.len : r.pos + l, m = new $root.PlayerMoreInfoResponse();
        while (r.pos < c) {
            var t = r.uint32();
            if (t === e)
                break;
            switch (t >>> 3) {
            default:
                r.skipType(t & 7);
                break;
            }
        }
        return m;
    };

    PlayerMoreInfoResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/PlayerMoreInfoResponse";
    };

    PlayerMoreInfoResponse.Proto = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "UNKNOWN"] = 0;
        values[valuesById[200010] = "ID"] = 200010;
        return values;
    })();

    return PlayerMoreInfoResponse;
})();

export const PlayerMoreInfoEndMessage = $root.PlayerMoreInfoEndMessage = (() => {

    function PlayerMoreInfoEndMessage(p) {
        if (p)
            for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                if (p[ks[i]] != null)
                    this[ks[i]] = p[ks[i]];
    }

    PlayerMoreInfoEndMessage.create = function create(properties) {
        return new PlayerMoreInfoEndMessage(properties);
    };

    PlayerMoreInfoEndMessage.encode = function encode(m, w) {
        if (!w)
            w = $Writer.create();
        return w;
    };

    PlayerMoreInfoEndMessage.decode = function decode(r, l, e) {
        if (!(r instanceof $Reader))
            r = $Reader.create(r);
        var c = l === undefined ? r.len : r.pos + l, m = new $root.PlayerMoreInfoEndMessage();
        while (r.pos < c) {
            var t = r.uint32();
            if (t === e)
                break;
            switch (t >>> 3) {
            default:
                r.skipType(t & 7);
                break;
            }
        }
        return m;
    };

    PlayerMoreInfoEndMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/PlayerMoreInfoEndMessage";
    };

    PlayerMoreInfoEndMessage.Proto = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "UNKNOWN"] = 0;
        values[valuesById[200011] = "ID"] = 200011;
        return values;
    })();

    return PlayerMoreInfoEndMessage;
})();

export const PlayerWiretapRequest = $root.PlayerWiretapRequest = (() => {

    function PlayerWiretapRequest(p) {
        if (p)
            for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                if (p[ks[i]] != null)
                    this[ks[i]] = p[ks[i]];
    }

    PlayerWiretapRequest.prototype.playerId = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

    PlayerWiretapRequest.create = function create(properties) {
        return new PlayerWiretapRequest(properties);
    };

    PlayerWiretapRequest.encode = function encode(m, w) {
        if (!w)
            w = $Writer.create();
        if (m.playerId != null && Object.hasOwnProperty.call(m, "playerId"))
            w.uint32(8).int64(m.playerId);
        return w;
    };

    PlayerWiretapRequest.decode = function decode(r, l, e) {
        if (!(r instanceof $Reader))
            r = $Reader.create(r);
        var c = l === undefined ? r.len : r.pos + l, m = new $root.PlayerWiretapRequest();
        while (r.pos < c) {
            var t = r.uint32();
            if (t === e)
                break;
            switch (t >>> 3) {
            case 1: {
                    m.playerId = r.int64();
                    break;
                }
            default:
                r.skipType(t & 7);
                break;
            }
        }
        return m;
    };

    PlayerWiretapRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/PlayerWiretapRequest";
    };

    PlayerWiretapRequest.Proto = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "UNKNOWN"] = 0;
        values[valuesById[200012] = "ID"] = 200012;
        return values;
    })();

    return PlayerWiretapRequest;
})();

export const PlayerWiretapResponse = $root.PlayerWiretapResponse = (() => {

    function PlayerWiretapResponse(p) {
        if (p)
            for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                if (p[ks[i]] != null)
                    this[ks[i]] = p[ks[i]];
    }

    PlayerWiretapResponse.create = function create(properties) {
        return new PlayerWiretapResponse(properties);
    };

    PlayerWiretapResponse.encode = function encode(m, w) {
        if (!w)
            w = $Writer.create();
        return w;
    };

    PlayerWiretapResponse.decode = function decode(r, l, e) {
        if (!(r instanceof $Reader))
            r = $Reader.create(r);
        var c = l === undefined ? r.len : r.pos + l, m = new $root.PlayerWiretapResponse();
        while (r.pos < c) {
            var t = r.uint32();
            if (t === e)
                break;
            switch (t >>> 3) {
            default:
                r.skipType(t & 7);
                break;
            }
        }
        return m;
    };

    PlayerWiretapResponse.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/PlayerWiretapResponse";
    };

    PlayerWiretapResponse.Proto = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "UNKNOWN"] = 0;
        values[valuesById[200012] = "ID"] = 200012;
        return values;
    })();

    return PlayerWiretapResponse;
})();

export const EasyLoginRequest = $root.EasyLoginRequest = (() => {

    function EasyLoginRequest(p) {
        if (p)
            for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                if (p[ks[i]] != null)
                    this[ks[i]] = p[ks[i]];
    }

    EasyLoginRequest.prototype.openId = "";
    EasyLoginRequest.prototype.serverId = 0;
    EasyLoginRequest.prototype.deviceType = "";

    EasyLoginRequest.create = function create(properties) {
        return new EasyLoginRequest(properties);
    };

    EasyLoginRequest.encode = function encode(m, w) {
        if (!w)
            w = $Writer.create();
        if (m.openId != null && Object.hasOwnProperty.call(m, "openId"))
            w.uint32(10).string(m.openId);
        if (m.serverId != null && Object.hasOwnProperty.call(m, "serverId"))
            w.uint32(16).int32(m.serverId);
        if (m.deviceType != null && Object.hasOwnProperty.call(m, "deviceType"))
            w.uint32(26).string(m.deviceType);
        return w;
    };

    EasyLoginRequest.decode = function decode(r, l, e) {
        if (!(r instanceof $Reader))
            r = $Reader.create(r);
        var c = l === undefined ? r.len : r.pos + l, m = new $root.EasyLoginRequest();
        while (r.pos < c) {
            var t = r.uint32();
            if (t === e)
                break;
            switch (t >>> 3) {
            case 1: {
                    m.openId = r.string();
                    break;
                }
            case 2: {
                    m.serverId = r.int32();
                    break;
                }
            case 3: {
                    m.deviceType = r.string();
                    break;
                }
            default:
                r.skipType(t & 7);
                break;
            }
        }
        return m;
    };

    EasyLoginRequest.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/EasyLoginRequest";
    };

    EasyLoginRequest.Proto = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "UNKNOWN"] = 0;
        values[valuesById[200013] = "ID"] = 200013;
        return values;
    })();

    return EasyLoginRequest;
})();

export const LoginStatus = $root.LoginStatus = (() => {
    const valuesById = {}, values = Object.create(valuesById);
    values[valuesById[0] = "NONE"] = 0;
    values[valuesById[1] = "NORMAL"] = 1;
    values[valuesById[2] = "FORBID"] = 2;
    values[valuesById[3] = "MAINTAIN"] = 3;
    values[valuesById[4] = "VIOLATION"] = 4;
    return values;
})();

export const Pb37ChannelData = $root.Pb37ChannelData = (() => {

    function Pb37ChannelData(p) {
        if (p)
            for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                if (p[ks[i]] != null)
                    this[ks[i]] = p[ks[i]];
    }

    Pb37ChannelData.prototype.appid = "";
    Pb37ChannelData.prototype.gameId = 0;
    Pb37ChannelData.prototype.fxCGameId = "";
    Pb37ChannelData.prototype.appExt = "";
    Pb37ChannelData.prototype.guid = "";

    Pb37ChannelData.create = function create(properties) {
        return new Pb37ChannelData(properties);
    };

    Pb37ChannelData.encode = function encode(m, w) {
        if (!w)
            w = $Writer.create();
        if (m.appid != null && Object.hasOwnProperty.call(m, "appid"))
            w.uint32(10).string(m.appid);
        if (m.gameId != null && Object.hasOwnProperty.call(m, "gameId"))
            w.uint32(16).int32(m.gameId);
        if (m.fxCGameId != null && Object.hasOwnProperty.call(m, "fxCGameId"))
            w.uint32(26).string(m.fxCGameId);
        if (m.appExt != null && Object.hasOwnProperty.call(m, "appExt"))
            w.uint32(34).string(m.appExt);
        if (m.guid != null && Object.hasOwnProperty.call(m, "guid"))
            w.uint32(42).string(m.guid);
        return w;
    };

    Pb37ChannelData.decode = function decode(r, l, e) {
        if (!(r instanceof $Reader))
            r = $Reader.create(r);
        var c = l === undefined ? r.len : r.pos + l, m = new $root.Pb37ChannelData();
        while (r.pos < c) {
            var t = r.uint32();
            if (t === e)
                break;
            switch (t >>> 3) {
            case 1: {
                    m.appid = r.string();
                    break;
                }
            case 2: {
                    m.gameId = r.int32();
                    break;
                }
            case 3: {
                    m.fxCGameId = r.string();
                    break;
                }
            case 4: {
                    m.appExt = r.string();
                    break;
                }
            case 5: {
                    m.guid = r.string();
                    break;
                }
            default:
                r.skipType(t & 7);
                break;
            }
        }
        return m;
    };

    Pb37ChannelData.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/Pb37ChannelData";
    };

    return Pb37ChannelData;
})();

export const PbYiLeChannelData = $root.PbYiLeChannelData = (() => {

    function PbYiLeChannelData(p) {
        if (p)
            for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                if (p[ks[i]] != null)
                    this[ks[i]] = p[ks[i]];
    }

    PbYiLeChannelData.prototype.uid = "";
    PbYiLeChannelData.prototype.token = "";
    PbYiLeChannelData.prototype.loginTime = "";

    PbYiLeChannelData.create = function create(properties) {
        return new PbYiLeChannelData(properties);
    };

    PbYiLeChannelData.encode = function encode(m, w) {
        if (!w)
            w = $Writer.create();
        if (m.uid != null && Object.hasOwnProperty.call(m, "uid"))
            w.uint32(10).string(m.uid);
        if (m.token != null && Object.hasOwnProperty.call(m, "token"))
            w.uint32(18).string(m.token);
        if (m.loginTime != null && Object.hasOwnProperty.call(m, "loginTime"))
            w.uint32(26).string(m.loginTime);
        return w;
    };

    PbYiLeChannelData.decode = function decode(r, l, e) {
        if (!(r instanceof $Reader))
            r = $Reader.create(r);
        var c = l === undefined ? r.len : r.pos + l, m = new $root.PbYiLeChannelData();
        while (r.pos < c) {
            var t = r.uint32();
            if (t === e)
                break;
            switch (t >>> 3) {
            case 1: {
                    m.uid = r.string();
                    break;
                }
            case 2: {
                    m.token = r.string();
                    break;
                }
            case 3: {
                    m.loginTime = r.string();
                    break;
                }
            default:
                r.skipType(t & 7);
                break;
            }
        }
        return m;
    };

    PbYiLeChannelData.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/PbYiLeChannelData";
    };

    return PbYiLeChannelData;
})();

export const PbWaboChannelData = $root.PbWaboChannelData = (() => {

    function PbWaboChannelData(p) {
        if (p)
            for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                if (p[ks[i]] != null)
                    this[ks[i]] = p[ks[i]];
    }

    PbWaboChannelData.prototype.token = "";
    PbWaboChannelData.prototype.uid = "";

    PbWaboChannelData.create = function create(properties) {
        return new PbWaboChannelData(properties);
    };

    PbWaboChannelData.encode = function encode(m, w) {
        if (!w)
            w = $Writer.create();
        if (m.token != null && Object.hasOwnProperty.call(m, "token"))
            w.uint32(10).string(m.token);
        if (m.uid != null && Object.hasOwnProperty.call(m, "uid"))
            w.uint32(18).string(m.uid);
        return w;
    };

    PbWaboChannelData.decode = function decode(r, l, e) {
        if (!(r instanceof $Reader))
            r = $Reader.create(r);
        var c = l === undefined ? r.len : r.pos + l, m = new $root.PbWaboChannelData();
        while (r.pos < c) {
            var t = r.uint32();
            if (t === e)
                break;
            switch (t >>> 3) {
            case 1: {
                    m.token = r.string();
                    break;
                }
            case 2: {
                    m.uid = r.string();
                    break;
                }
            default:
                r.skipType(t & 7);
                break;
            }
        }
        return m;
    };

    PbWaboChannelData.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/PbWaboChannelData";
    };

    return PbWaboChannelData;
})();

export const PbFuShunChannelData = $root.PbFuShunChannelData = (() => {

    function PbFuShunChannelData(p) {
        if (p)
            for (var ks = Object.keys(p), i = 0; i < ks.length; ++i)
                if (p[ks[i]] != null)
                    this[ks[i]] = p[ks[i]];
    }

    PbFuShunChannelData.prototype.code = "";

    PbFuShunChannelData.create = function create(properties) {
        return new PbFuShunChannelData(properties);
    };

    PbFuShunChannelData.encode = function encode(m, w) {
        if (!w)
            w = $Writer.create();
        if (m.code != null && Object.hasOwnProperty.call(m, "code"))
            w.uint32(10).string(m.code);
        return w;
    };

    PbFuShunChannelData.decode = function decode(r, l, e) {
        if (!(r instanceof $Reader))
            r = $Reader.create(r);
        var c = l === undefined ? r.len : r.pos + l, m = new $root.PbFuShunChannelData();
        while (r.pos < c) {
            var t = r.uint32();
            if (t === e)
                break;
            switch (t >>> 3) {
            case 1: {
                    m.code = r.string();
                    break;
                }
            default:
                r.skipType(t & 7);
                break;
            }
        }
        return m;
    };

    PbFuShunChannelData.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
        if (typeUrlPrefix === undefined) {
            typeUrlPrefix = "type.googleapis.com";
        }
        return typeUrlPrefix + "/PbFuShunChannelData";
    };

    return PbFuShunChannelData;
})();

export { $root as default };
