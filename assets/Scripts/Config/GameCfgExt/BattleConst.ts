import { reloadCfg } from "../CFGUtils";
import CFG_BattleConst from "../GameCfg/CFG_BattleConst";

export default class BattleConst {

    static mm: Map<string, string> = new Map();
    
    static realod(): void {
        const list = reloadCfg(CFG_BattleConst, CFG_BattleConst.filePath);
        for (const item of list) {
            this.mm.set(item.keyCol, item.valueCol);
        }
    }

    static getValue(key: string): string | null {
        return this.mm.get(key) || null;
    }
}
