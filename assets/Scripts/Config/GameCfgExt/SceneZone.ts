import { reloadCfg } from "../CFGUtils";
import CFG_SceneZone from "../GameCfg/CFG_SceneZone";

export default class SceneZoneCache {

    static mm: Map<string, CFG_SceneZone> = new Map();
    static maxZoneMap: Map<number, number> = new Map();
    
    static reload(): void {
        const list: CFG_SceneZone[] = reloadCfg(CFG_SceneZone, CFG_SceneZone.filePath);
        this.mm.clear();
        this.maxZoneMap.clear();
        for (const item of list) {
            const key = `${item.sceneId}_${item.zoneId}`;
            this.mm.set(key, item);
        }

        for (let i = 0, len = list.length; i < len; i++) {
            const element = list[i];
            if (this.maxZoneMap.get(element.sceneId) == undefined) {
                this.maxZoneMap.set(element.sceneId, element.zoneId);
            } else {
                if (this.maxZoneMap.get(element.sceneId) < element.zoneId) {
                    this.maxZoneMap.set(element.sceneId, element.zoneId);
                }
            }
        }
    }

    static getData(sceneId: number, zoneId: number): CFG_SceneZone | null {
        const key = `${sceneId}_${zoneId}`;
        return this.mm.get(key) || null;
    }

    static getMaxZone(sceneId: number): number {
        const maxZone = this.maxZoneMap.get(sceneId);
        if (maxZone == undefined) {
            throw new Error(`SceneZoneCache.getMaxZone: sceneId ${sceneId} not found`);
        }
        return maxZone;
    }
}
