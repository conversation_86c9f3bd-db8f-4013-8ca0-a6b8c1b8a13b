import { reloadCfg } from "../CFGUtils";
import CFG_Monster  from "../GameCfg/CFG_Monster";

export default class Monster {
    private static _mm: Map<number, Monster> = new Map();

    static reload() {
        const list = reloadCfg(CFG_Monster, CFG_Monster.filePath);
        for (const item of list) {
            this._mm.set(item.id, new Monster(item));
        }
    }

    static get(id: number): Monster {
        if (this._mm.size == 0) this.reload();
        return this._mm.get(id) || null;
    }

    static destroy() {
        this._mm.clear();
    }

    private _data: CFG_Monster;
    private _attObj: { [key: number]: number } = {};

    constructor(data: CFG_Monster) {
        this._data = data;
    }

    get data(): CFG_Monster {
        return this._data;
    }

    getSkill(): { id: number, level: number }[] {
        if (!this._data.skills) return [];

        // 6000001#1|6000002#1
        let skillArr = this._data.skills.split("|");
        let skills = [];
        for (let i = 0; i < skillArr.length; i++) {
            let skillId = parseInt(skillArr[i].split("#")[0]);
            let skillLevel = parseInt(skillArr[i].split("#")[1]);
            skills.push({ id: skillId, level: skillLevel });
        }
        return skills;
    }

    getProduce() {
        // 1#1|3#10
        let produceArr = this._data.produce == undefined ? [] : this._data.produce.split("|");
        let produce = [];
        for (let i = 0; i < produceArr.length; i++) {
            let itemId = parseInt(produceArr[i].split("#")[0]);
            let itemNum = parseInt(produceArr[i].split("#")[1]);
            produce.push({ id: itemId, num: itemNum });
        }
        return produce;
    }

    /**
     * 获取属性
     * @param type 
     * @param defaultValue 
     * @returns 
     */
    getAtt(type: number, defaultValue: number = 0): number {
        if (!this._attObj) {
            this._attObj = {};

            let attArr: string[] = this._data.attribute.split("|");
            for (let i: number = 0, n: number = attArr.length; i < n; i++) {
                let arr: string[] = attArr[i].split("#");
                this._attObj[Number(arr[0])] = Number(arr[1]);
            }
        }
        return this._attObj[type] || defaultValue;
    }
}