interface IItemBase {
    getId(): number | string;
}

// Define an interface for the constructor with static methods
interface IItemConstructor<T extends IItemBase> {
    new(): T;
    reload(): void;
    get(id: number | string): T;
    list: T[];
}

type Key = number | string;
export function buildMap<T extends IItemBase>(itemClass: IItemConstructor<T>) {
    const map = new Map<Key, T>();
    const rebuildMap = () => {
        map.clear();
        for (const item of itemClass.list) {
            map.set(item.getId(), item);
        }
    }

    return {
        reload: () => {
            itemClass.reload();
            rebuildMap();
        },
        get: (id: Key) => {
            return itemClass.get(id);
        },
        getOrNull: (id: Key) => {
            return map.get(id) || null;
        }
    }
}
