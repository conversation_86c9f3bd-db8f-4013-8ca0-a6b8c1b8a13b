// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { reloadCfg } from "../CFGUtils";
import CFG_HeroLevel from "../GameCfg/CFG_HeroLevel";
import { SkillLv } from "./struct/SkillLv";


export interface IHeroLevelAttr {
    type: number; 
    value: number;
}

export interface IHeroLevelMaterial {
    id: number; // 物品ID
    num: number; // 数量
}

export default class HeroLevel {

    static mm: Map<number, HeroLevel[]> = new Map();
    static filePath: string = "gamecfg/Y英雄等级表_HeroLevel";

    static reload() {
        const list: CFG_HeroLevel[] = reloadCfg(CFG_HeroLevel, this.filePath);
        this.mm.clear();
        for (const item of list) { 
            let l = this.mm.get(item.heroIdentity);
            if (l) {
                l.push(new HeroLevel(item));
            }
        }

        for (const item of this.mm.values()) {
            item.sort((a, b) => {
                return a._data.level - b._data.level;
            });
        }
    }


    static get(heroIdentity: number): HeroLevel[] {
        return this.mm.get(heroIdentity);
    }

    static getByLevel(heroIdentity: number, lv: number): HeroLevel {
        const heroLevelList = this.mm.get(heroIdentity);
        if (heroLevelList) {
            for (let i = 0; i < heroLevelList.length; i++) {
                if (heroLevelList[i]._data.level == lv) {
                    return heroLevelList[i];
                }
            }
        }
        return null;
    }

    private _data: CFG_HeroLevel;
    private _skills: SkillLv[] = [];
    private _levelAttrs: IHeroLevelAttr[] = [];
    private _costMaterial: IHeroLevelMaterial[] = [];

    constructor(c: CFG_HeroLevel) {
        this._data = c;
    }

    /**
     * 获取技能{技能id,技能等级}列表
     */
    getSkills(): SkillLv[] {
        return this._skills;
    }

    /**
     * 获取除本体外的消耗 返回{id,num}列表
     */
    getcostMaterial(): IHeroLevelMaterial[] {
        if (this._costMaterial.length == 0) {
            this._parseCostMaterial();
        }
        return this._costMaterial;
    }


    getLevelAttribute() {
        if (this._levelAttrs.length == 0) { 
            this._parseLevelAttribute();
        }
        return this._levelAttrs;
    }

    // 获取英雄属性 ，BattleAttr
    getHeroAtrr(type: number): number {
        if (this._levelAttrs.length == 0) {
            this._parseLevelAttribute();
        }
        let list = this.getLevelAttribute();
        if (list.length) {
            for (let index = 0; index < list.length; index++) {
                if (list[index].type == type) {
                    return list[index].value;
                }

            }
        }
        return 0;
    }

    private _parseCostMaterial() {
        let material = [];
        if (this._data.costMaterial && this._data.costMaterial.length > 0) {
            let costArr = this._data.costMaterial.split("|");
            for (let i = 0; i < costArr.length; i++) {
                let id = parseInt(costArr[i].split("#")[0]);
                let num = parseInt(costArr[i].split("#")[1]);
                material.push({ id: id, num: num });
            }
        }

        this._costMaterial = material;
    }

    private _parseLevelAttribute() {    
                // 202#100|203#2
        let levelAttr = this._data.levelAttribute.split("|");
        let attr = [];
        for (let i = 0; i < levelAttr.length; i++) {
            let type = parseInt(levelAttr[i].split("#")[0]);
            let value = parseInt(levelAttr[i].split("#")[1]);
            attr.push({ type: type, value: value });
        }
        this._levelAttrs = attr;
    }

    private _parseSkills() {
        let skillArr = this._data.skills.split("|");
        let skills: SkillLv[] = [];
        for (let i = 0; i < skillArr.length; i++) {
            let skillLv: SkillLv = new SkillLv();
            skillLv.id = parseInt(skillArr[i].split("#")[0]);
            skillLv.level = parseInt(skillArr[i].split("#")[1]);
            skills.push(skillLv);
        }
        this._skills = skills;
    }


}
