// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_SkillModel
{
    /** 技能表现ID */
    public id: number = 0;
    /** 施法特效：
挂在施法者身上的特效都配置在这里
特效名称#特效节点位置#方向
显示位置：
1、人物前（特效挡住人物）
2、人物后（人物挡住特效）
3、人物身上（特效跟随人物移动）
4、地图下层（特效贴在地面上）
5、人物层（特效参与排序，根据位置显示身前或背后)
6、地图上层（特效显示在地图上层，比如：暴风雪技能）
方向:
0、无方向
1、人物面向目标
2、人物面向目标(根据情况翻转)
 */
    public selfModel: string = "";
    /** 攻击特效：非施法者自身的特效都配置在这里
特效名称#特效节点位置#方向
显示位置：
1、人物前（特效挡住人物）
2、人物后（人物挡住特效）
3、人物身上（特效跟随人物移动）
4、地图下层（特效贴在地面上）
5、人物层（特效参与排序，根据位置显示身前或背后)
6、地图上层（特效显示在地图上层，比如：暴风雪技能）
方向:
0、无方向
1、人物面向目标
2、人物面向目标(根据情况翻转)
 */
    public targetModel: string = "";
    /** 受击特效
Administrator:
特效名称#特效节点位置#方向

显示位置：
1、人物前（特效挡住人物）
2、人物后（人物挡住特效）
3、人物身上（特效跟随人物移动）
4、地图下层（特效贴在地面上）
5、人物层（特效参与排序，根据位置显示身前或背后)
6、地图上层（特效显示在地图上层，比如：暴风雪技能）
方向:
0、无方向
1、人物面向目标
2、人物面向目标(根据情况翻转)
 */
    public hitModel: string = "";
    /** 特效位置：
0. 头部
1. 腰部
2. 脚底
 */
    public targetSeat: number = 0;
    /** 偏移值 */
    public offset: string = "";
    /** 技能音效延迟 */
    public soundDelay: number = 0;
    /** 技能音效 */
    public skillSound: string = "";
    /** 受击音效 */
    public hitSound: string = "";
    /** 飘字类型 */
    public floaterType: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_SkillModel[] = [];
    static filePath: string = "gamecfg/2技能效果表_SkillModel";
    
    public static get list(): CFG_SkillModel[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_SkillModel {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_SkillModel, this.filePath);
    }
}