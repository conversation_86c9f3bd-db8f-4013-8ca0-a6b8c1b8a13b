// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_LanguageAuto
{
    /** 序号 */
    public id: number = 0;
    /** 语言ID */
    public langId: string = "";
    /** 简体中文 */
    public cn: string = "";
    /** 英文 */
    public en: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_LanguageAuto[] = [];
    static filePath: string = "gamecfg/YLanguageAuto_LanguageAuto";
    
    public static get list(): CFG_LanguageAuto[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_LanguageAuto {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_LanguageAuto, this.filePath);
    }
}