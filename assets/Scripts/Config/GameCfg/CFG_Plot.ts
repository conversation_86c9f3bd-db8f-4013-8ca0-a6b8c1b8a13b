// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_Plot
{
    /** 对话ID */
    public id: number = 0;
    /** 对话组 */
    public group: number = 0;
    /** 步骤 */
    public subId: number = 0;
    /** 头像显示位置 */
    public diction: number = 0;
    /** npc名字 */
    public nameLang: string = "";
    /** 半身像 */
    public icon: number = 0;
    /** 对话文本 */
    public dialogueLang: string = "";
    /** 是否自动播放 */
    public autorun: number = 0;
    /** 剧情对话音效 */
    public plotSound: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_Plot[] = [];
    static filePath: string = "gamecfg/J剧情配置表_Plot";
    
    public static get list(): CFG_Plot[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_Plot {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_Plot, this.filePath);
    }
}