// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";
import CFG_Item from './CFG_Item';
import CFG_BulletEffect from './CFG_BulletEffect';
import CFG_SkillModel from './CFG_SkillModel';
import CFG_SkillLevel from './CFG_SkillLevel';
import CFG_BattleConst from './CFG_BattleConst';
import CFG_BattleAttr from './CFG_BattleAttr';
import CFG_BuffType from './CFG_BuffType';
import CFG_SkillBuff from './CFG_SkillBuff';
import CFG_GameHelp from './CFG_GameHelp';
import CFG_RechargeProduct from './CFG_RechargeProduct';
import CFG_SceneZone from './CFG_SceneZone';
import CFG_MainlineTreasureBox from './CFG_MainlineTreasureBox';
import CFG_SceneStage from './CFG_SceneStage';
import CFG_AchievementTask from './CFG_AchievementTask';
import CFG_AchievementPoints from './CFG_AchievementPoints';
import CFG_DrawRate from './CFG_DrawRate';
import CFG_DrawType from './CFG_DrawType';
import CFG_ExchangeShop from './CFG_ExchangeShop';
import CFG_GiftCode from './CFG_GiftCode';
import CFG_ItemRandomPack from './CFG_ItemRandomPack';
import CFG_LoginGift from './CFG_LoginGift';
import CFG_TroopPosition from './CFG_TroopPosition';
import CFG_DungeonBook from './CFG_DungeonBook';
import CFG_SysMainTemplate from './CFG_SysMainTemplate';
import CFG_GmDefine from './CFG_GmDefine';
import CFG_FunctionOpen from './CFG_FunctionOpen';
import CFG_Monster from './CFG_Monster';
import CFG_ActivityRewards from './CFG_ActivityRewards';
import CFG_ActiveRankRewards from './CFG_ActiveRankRewards';
import CFG_ActiveTemplate from './CFG_ActiveTemplate';
import CFG_Access from './CFG_Access';
import CFG_GoldenPigStage from './CFG_GoldenPigStage';
import CFG_Plot from './CFG_Plot';
import CFG_OutputPath from './CFG_OutputPath';
import CFG_HomeProducePool from './CFG_HomeProducePool';
import CFG_HomeStoreHouse from './CFG_HomeStoreHouse';
import CFG_NpcBuilding from './CFG_NpcBuilding';
import CFG_ArenaDailyReward from './CFG_ArenaDailyReward';
import CFG_ArenaSeasonReward from './CFG_ArenaSeasonReward';
import CFG_PlayerLevel from './CFG_PlayerLevel';
import CFG_ExpLimit from './CFG_ExpLimit';
import CFG_QuickBuying from './CFG_QuickBuying';
import CFG_PurgatoryAttr from './CFG_PurgatoryAttr';
import CFG_PurgatoryRank from './CFG_PurgatoryRank';
import CFG_PurgatoryRefine from './CFG_PurgatoryRefine';
import CFG_Purgatory from './CFG_Purgatory';
import CFG_PurgatoryMonster from './CFG_PurgatoryMonster';
import CFG_PurgatorySeason from './CFG_PurgatorySeason';
import CFG_GiftGroup from './CFG_GiftGroup';
import CFG_OfflineBenefits from './CFG_OfflineBenefits';
import CFG_ComboAttr from './CFG_ComboAttr';
import CFG_Bubbling from './CFG_Bubbling';
import CFG_NameRandom from './CFG_NameRandom';
import CFG_SensitiveWords from './CFG_SensitiveWords';
import CFG_ModelHeight from './CFG_ModelHeight';
import CFG_DayRecharge from './CFG_DayRecharge';
import CFG_DungeonMonster from './CFG_DungeonMonster';
import CFG_Dungeon from './CFG_Dungeon';
import CFG_NpcModel from './CFG_NpcModel';
import CFG_NpcNatter from './CFG_NpcNatter';
import CFG_ZhanLingExp from './CFG_ZhanLingExp';
import CFG_ZhanLingGoal from './CFG_ZhanLingGoal';
import CFG_TongTianTaTeQuanProduct from './CFG_TongTianTaTeQuanProduct';
import CFG_TongTianTaTeQuanReward from './CFG_TongTianTaTeQuanReward';
import CFG_XianShiLiBao from './CFG_XianShiLiBao';
import CFG_ChengZhangJiJin from './CFG_ChengZhangJiJin';
import CFG_QiRiMuBiaoTask from './CFG_QiRiMuBiaoTask';
import CFG_QiRiMuBiaoStage from './CFG_QiRiMuBiaoStage';
import CFG_ZhiGouLiBao from './CFG_ZhiGouLiBao';
import CFG_MeiRiChongZhi from './CFG_MeiRiChongZhi';
import CFG_RankReward from './CFG_RankReward';
import CFG_PrivilegeNormal from './CFG_PrivilegeNormal';
import CFG_Bubble from './CFG_Bubble';
import CFG_SignGift from './CFG_SignGift';
import CFG_TaskType from './CFG_TaskType';
import CFG_DailyWeeklyTask from './CFG_DailyWeeklyTask';
import CFG_DailyWeeklyGiftBox from './CFG_DailyWeeklyGiftBox';
import CFG_ShopType from './CFG_ShopType';
import CFG_Harvest from './CFG_Harvest';
import CFG_firstRecharge from './CFG_firstRecharge';
import CFG_HandbookIdentity from './CFG_HandbookIdentity';
import CFG_HandbookSuit from './CFG_HandbookSuit';
import CFG_HandbookCatalog from './CFG_HandbookCatalog';
import CFG_HandbookProcessor from './CFG_HandbookProcessor';
import CFG_HandbookLevel from './CFG_HandbookLevel';
import CFG_ChallengeCopy from './CFG_ChallengeCopy';
import CFG_Tower from './CFG_Tower';
import CFG_PrivilegeBoostType from './CFG_PrivilegeBoostType';
import CFG_PrivilegeBoost from './CFG_PrivilegeBoost';
import CFG_Vip from './CFG_Vip';
import CFG_Fairyland from './CFG_Fairyland';
import CFG_FairylandMonster from './CFG_FairylandMonster';
import CFG_VillageRank from './CFG_VillageRank';
import CFG_Guide from './CFG_Guide';
import CFG_TrainingGeneralCamp from './CFG_TrainingGeneralCamp';
import CFG_TrainingExpertCamp from './CFG_TrainingExpertCamp';
import CFG_LanguageAuto from './CFG_LanguageAuto';
import CFG_LanguageTips from './CFG_LanguageTips';
import CFG_LanguageCustomize from './CFG_LanguageCustomize';
import CFG_EvilSuit from './CFG_EvilSuit';
import CFG_EvilType from './CFG_EvilType';
import CFG_EvilCatalog from './CFG_EvilCatalog';
import CFG_EvilSoul from './CFG_EvilSoul';
import CFG_HeroPrizedraw from './CFG_HeroPrizedraw';
import CFG_HeroTactics from './CFG_HeroTactics';
import CFG_HeroRecruitSpecial from './CFG_HeroRecruitSpecial';
import CFG_HeroLevel from './CFG_HeroLevel';
import CFG_HeroType from './CFG_HeroType';
import CFG_HeroAwaken from './CFG_HeroAwaken';
import CFG_Mail from './CFG_Mail';
import CFG_MainlineTask from './CFG_MainlineTask';
import CFG_MainlineScene from './CFG_MainlineScene';
import CFG_SceneNpc from './CFG_SceneNpc';
import CFG_SceneTeleport from './CFG_SceneTeleport';
import CFG_SceneFog from './CFG_SceneFog';
import CFG_BattleType from './CFG_BattleType';
import CFG_ZhangLingNormal from './CFG_ZhangLingNormal';
import CFG_TreasureHouse from './CFG_TreasureHouse';
import CFG_Rune from './CFG_Rune';
import CFG_EquipRate from './CFG_EquipRate';
import CFG_EquipSuitAttr from './CFG_EquipSuitAttr';
import CFG_Equip from './CFG_Equip';
import CFG_EquipattrStash from './CFG_EquipattrStash';

export default class CFGManager {
    static classes = {
    CFG_Item,
    CFG_BulletEffect,
    CFG_SkillModel,
    CFG_SkillLevel,
    CFG_BattleConst,
    CFG_BattleAttr,
    CFG_BuffType,
    CFG_SkillBuff,
    CFG_GameHelp,
    CFG_RechargeProduct,
    CFG_SceneZone,
    CFG_MainlineTreasureBox,
    CFG_SceneStage,
    CFG_AchievementTask,
    CFG_AchievementPoints,
    CFG_DrawRate,
    CFG_DrawType,
    CFG_ExchangeShop,
    CFG_GiftCode,
    CFG_ItemRandomPack,
    CFG_LoginGift,
    CFG_TroopPosition,
    CFG_DungeonBook,
    CFG_SysMainTemplate,
    CFG_GmDefine,
    CFG_FunctionOpen,
    CFG_Monster,
    CFG_ActivityRewards,
    CFG_ActiveRankRewards,
    CFG_ActiveTemplate,
    CFG_Access,
    CFG_GoldenPigStage,
    CFG_Plot,
    CFG_OutputPath,
    CFG_HomeProducePool,
    CFG_HomeStoreHouse,
    CFG_NpcBuilding,
    CFG_ArenaDailyReward,
    CFG_ArenaSeasonReward,
    CFG_PlayerLevel,
    CFG_ExpLimit,
    CFG_QuickBuying,
    CFG_PurgatoryAttr,
    CFG_PurgatoryRank,
    CFG_PurgatoryRefine,
    CFG_Purgatory,
    CFG_PurgatoryMonster,
    CFG_PurgatorySeason,
    CFG_GiftGroup,
    CFG_OfflineBenefits,
    CFG_ComboAttr,
    CFG_Bubbling,
    CFG_NameRandom,
    CFG_SensitiveWords,
    CFG_ModelHeight,
    CFG_DayRecharge,
    CFG_DungeonMonster,
    CFG_Dungeon,
    CFG_NpcModel,
    CFG_NpcNatter,
    CFG_ZhanLingExp,
    CFG_ZhanLingGoal,
    CFG_TongTianTaTeQuanProduct,
    CFG_TongTianTaTeQuanReward,
    CFG_XianShiLiBao,
    CFG_ChengZhangJiJin,
    CFG_QiRiMuBiaoTask,
    CFG_QiRiMuBiaoStage,
    CFG_ZhiGouLiBao,
    CFG_MeiRiChongZhi,
    CFG_RankReward,
    CFG_PrivilegeNormal,
    CFG_Bubble,
    CFG_SignGift,
    CFG_TaskType,
    CFG_DailyWeeklyTask,
    CFG_DailyWeeklyGiftBox,
    CFG_ShopType,
    CFG_Harvest,
    CFG_firstRecharge,
    CFG_HandbookIdentity,
    CFG_HandbookSuit,
    CFG_HandbookCatalog,
    CFG_HandbookProcessor,
    CFG_HandbookLevel,
    CFG_ChallengeCopy,
    CFG_Tower,
    CFG_PrivilegeBoostType,
    CFG_PrivilegeBoost,
    CFG_Vip,
    CFG_Fairyland,
    CFG_FairylandMonster,
    CFG_VillageRank,
    CFG_Guide,
    CFG_TrainingGeneralCamp,
    CFG_TrainingExpertCamp,
    CFG_LanguageAuto,
    CFG_LanguageTips,
    CFG_LanguageCustomize,
    CFG_EvilSuit,
    CFG_EvilType,
    CFG_EvilCatalog,
    CFG_EvilSoul,
    CFG_HeroPrizedraw,
    CFG_HeroTactics,
    CFG_HeroRecruitSpecial,
    CFG_HeroLevel,
    CFG_HeroType,
    CFG_HeroAwaken,
    CFG_Mail,
    CFG_MainlineTask,
    CFG_MainlineScene,
    CFG_SceneNpc,
    CFG_SceneTeleport,
    CFG_SceneFog,
    CFG_BattleType,
    CFG_ZhangLingNormal,
    CFG_TreasureHouse,
    CFG_Rune,
    CFG_EquipRate,
    CFG_EquipSuitAttr,
    CFG_Equip,
    CFG_EquipattrStash
    };

    /**
     * Reload all configuration classes
     */
    static reload(): void {
        CFG_Item.reload();
        CFG_BulletEffect.reload();
        CFG_SkillModel.reload();
        CFG_SkillLevel.reload();
        CFG_BattleConst.reload();
        CFG_BattleAttr.reload();
        CFG_BuffType.reload();
        CFG_SkillBuff.reload();
        CFG_GameHelp.reload();
        CFG_RechargeProduct.reload();
        CFG_SceneZone.reload();
        CFG_MainlineTreasureBox.reload();
        CFG_SceneStage.reload();
        CFG_AchievementTask.reload();
        CFG_AchievementPoints.reload();
        CFG_DrawRate.reload();
        CFG_DrawType.reload();
        CFG_ExchangeShop.reload();
        CFG_GiftCode.reload();
        CFG_ItemRandomPack.reload();
        CFG_LoginGift.reload();
        CFG_TroopPosition.reload();
        CFG_DungeonBook.reload();
        CFG_SysMainTemplate.reload();
        CFG_GmDefine.reload();
        CFG_FunctionOpen.reload();
        CFG_Monster.reload();
        CFG_ActivityRewards.reload();
        CFG_ActiveRankRewards.reload();
        CFG_ActiveTemplate.reload();
        CFG_Access.reload();
        CFG_GoldenPigStage.reload();
        CFG_Plot.reload();
        CFG_OutputPath.reload();
        CFG_HomeProducePool.reload();
        CFG_HomeStoreHouse.reload();
        CFG_NpcBuilding.reload();
        CFG_ArenaDailyReward.reload();
        CFG_ArenaSeasonReward.reload();
        CFG_PlayerLevel.reload();
        CFG_ExpLimit.reload();
        CFG_QuickBuying.reload();
        CFG_PurgatoryAttr.reload();
        CFG_PurgatoryRank.reload();
        CFG_PurgatoryRefine.reload();
        CFG_Purgatory.reload();
        CFG_PurgatoryMonster.reload();
        CFG_PurgatorySeason.reload();
        CFG_GiftGroup.reload();
        CFG_OfflineBenefits.reload();
        CFG_ComboAttr.reload();
        CFG_Bubbling.reload();
        CFG_NameRandom.reload();
        CFG_SensitiveWords.reload();
        CFG_ModelHeight.reload();
        CFG_DayRecharge.reload();
        CFG_DungeonMonster.reload();
        CFG_Dungeon.reload();
        CFG_NpcModel.reload();
        CFG_NpcNatter.reload();
        CFG_ZhanLingExp.reload();
        CFG_ZhanLingGoal.reload();
        CFG_TongTianTaTeQuanProduct.reload();
        CFG_TongTianTaTeQuanReward.reload();
        CFG_XianShiLiBao.reload();
        CFG_ChengZhangJiJin.reload();
        CFG_QiRiMuBiaoTask.reload();
        CFG_QiRiMuBiaoStage.reload();
        CFG_ZhiGouLiBao.reload();
        CFG_MeiRiChongZhi.reload();
        CFG_RankReward.reload();
        CFG_PrivilegeNormal.reload();
        CFG_Bubble.reload();
        CFG_SignGift.reload();
        CFG_TaskType.reload();
        CFG_DailyWeeklyTask.reload();
        CFG_DailyWeeklyGiftBox.reload();
        CFG_ShopType.reload();
        CFG_Harvest.reload();
        CFG_firstRecharge.reload();
        CFG_HandbookIdentity.reload();
        CFG_HandbookSuit.reload();
        CFG_HandbookCatalog.reload();
        CFG_HandbookProcessor.reload();
        CFG_HandbookLevel.reload();
        CFG_ChallengeCopy.reload();
        CFG_Tower.reload();
        CFG_PrivilegeBoostType.reload();
        CFG_PrivilegeBoost.reload();
        CFG_Vip.reload();
        CFG_Fairyland.reload();
        CFG_FairylandMonster.reload();
        CFG_VillageRank.reload();
        CFG_Guide.reload();
        CFG_TrainingGeneralCamp.reload();
        CFG_TrainingExpertCamp.reload();
        CFG_LanguageAuto.reload();
        CFG_LanguageTips.reload();
        CFG_LanguageCustomize.reload();
        CFG_EvilSuit.reload();
        CFG_EvilType.reload();
        CFG_EvilCatalog.reload();
        CFG_EvilSoul.reload();
        CFG_HeroPrizedraw.reload();
        CFG_HeroTactics.reload();
        CFG_HeroRecruitSpecial.reload();
        CFG_HeroLevel.reload();
        CFG_HeroType.reload();
        CFG_HeroAwaken.reload();
        CFG_Mail.reload();
        CFG_MainlineTask.reload();
        CFG_MainlineScene.reload();
        CFG_SceneNpc.reload();
        CFG_SceneTeleport.reload();
        CFG_SceneFog.reload();
        CFG_BattleType.reload();
        CFG_ZhangLingNormal.reload();
        CFG_TreasureHouse.reload();
        CFG_Rune.reload();
        CFG_EquipRate.reload();
        CFG_EquipSuitAttr.reload();
        CFG_Equip.reload();
        CFG_EquipattrStash.reload();
    }
}

export {
    CFG_Item,
    CFG_BulletEffect,
    CFG_SkillModel,
    CFG_SkillLevel,
    CFG_BattleConst,
    CFG_BattleAttr,
    CFG_BuffType,
    CFG_SkillBuff,
    CFG_GameHelp,
    CFG_RechargeProduct,
    CFG_SceneZone,
    CFG_MainlineTreasureBox,
    CFG_SceneStage,
    CFG_AchievementTask,
    CFG_AchievementPoints,
    CFG_DrawRate,
    CFG_DrawType,
    CFG_ExchangeShop,
    CFG_GiftCode,
    CFG_ItemRandomPack,
    CFG_LoginGift,
    CFG_TroopPosition,
    CFG_DungeonBook,
    CFG_SysMainTemplate,
    CFG_GmDefine,
    CFG_FunctionOpen,
    CFG_Monster,
    CFG_ActivityRewards,
    CFG_ActiveRankRewards,
    CFG_ActiveTemplate,
    CFG_Access,
    CFG_GoldenPigStage,
    CFG_Plot,
    CFG_OutputPath,
    CFG_HomeProducePool,
    CFG_HomeStoreHouse,
    CFG_NpcBuilding,
    CFG_ArenaDailyReward,
    CFG_ArenaSeasonReward,
    CFG_PlayerLevel,
    CFG_ExpLimit,
    CFG_QuickBuying,
    CFG_PurgatoryAttr,
    CFG_PurgatoryRank,
    CFG_PurgatoryRefine,
    CFG_Purgatory,
    CFG_PurgatoryMonster,
    CFG_PurgatorySeason,
    CFG_GiftGroup,
    CFG_OfflineBenefits,
    CFG_ComboAttr,
    CFG_Bubbling,
    CFG_NameRandom,
    CFG_SensitiveWords,
    CFG_ModelHeight,
    CFG_DayRecharge,
    CFG_DungeonMonster,
    CFG_Dungeon,
    CFG_NpcModel,
    CFG_NpcNatter,
    CFG_ZhanLingExp,
    CFG_ZhanLingGoal,
    CFG_TongTianTaTeQuanProduct,
    CFG_TongTianTaTeQuanReward,
    CFG_XianShiLiBao,
    CFG_ChengZhangJiJin,
    CFG_QiRiMuBiaoTask,
    CFG_QiRiMuBiaoStage,
    CFG_ZhiGouLiBao,
    CFG_MeiRiChongZhi,
    CFG_RankReward,
    CFG_PrivilegeNormal,
    CFG_Bubble,
    CFG_SignGift,
    CFG_TaskType,
    CFG_DailyWeeklyTask,
    CFG_DailyWeeklyGiftBox,
    CFG_ShopType,
    CFG_Harvest,
    CFG_firstRecharge,
    CFG_HandbookIdentity,
    CFG_HandbookSuit,
    CFG_HandbookCatalog,
    CFG_HandbookProcessor,
    CFG_HandbookLevel,
    CFG_ChallengeCopy,
    CFG_Tower,
    CFG_PrivilegeBoostType,
    CFG_PrivilegeBoost,
    CFG_Vip,
    CFG_Fairyland,
    CFG_FairylandMonster,
    CFG_VillageRank,
    CFG_Guide,
    CFG_TrainingGeneralCamp,
    CFG_TrainingExpertCamp,
    CFG_LanguageAuto,
    CFG_LanguageTips,
    CFG_LanguageCustomize,
    CFG_EvilSuit,
    CFG_EvilType,
    CFG_EvilCatalog,
    CFG_EvilSoul,
    CFG_HeroPrizedraw,
    CFG_HeroTactics,
    CFG_HeroRecruitSpecial,
    CFG_HeroLevel,
    CFG_HeroType,
    CFG_HeroAwaken,
    CFG_Mail,
    CFG_MainlineTask,
    CFG_MainlineScene,
    CFG_SceneNpc,
    CFG_SceneTeleport,
    CFG_SceneFog,
    CFG_BattleType,
    CFG_ZhangLingNormal,
    CFG_TreasureHouse,
    CFG_Rune,
    CFG_EquipRate,
    CFG_EquipSuitAttr,
    CFG_Equip,
    CFG_EquipattrStash
};