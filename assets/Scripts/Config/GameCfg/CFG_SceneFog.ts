// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_SceneFog
{
    /** 序号 */
    public id: number = 0;
    /** 场景ID */
    public mainlineId: number = 0;
    /** 迷雾点ID */
    public fogId: number = 0;
    /** 开启消耗 */
    public openCosts: string = "";
    /** 开启奖励 */
    public openRewards: string = "";
    /** 开启等级 */
    public openLevel: number = 0;
    /** NPC模型状态处理 */
    public activeOperate: number = 0;
    /** 解锁点所在迷雾区域 */
    public unlockArea: number = 0;
    /** 解锁进度 */
    public chapter: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_SceneFog[] = [];
    static filePath: string = "gamecfg/Z主线野外迷雾表_SceneFog";
    
    public static get list(): CFG_SceneFog[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_SceneFog {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_SceneFog, this.filePath);
    }
}