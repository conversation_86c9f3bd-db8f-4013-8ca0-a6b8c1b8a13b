// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_TreasureHouse
{
    /** 序号 */
    public id: number = 0;
    /** 类型 */
    public type: number = 0;
    /** 期数 */
    public period: number = 0;
    /** 充值商品表ID */
    public rechargeId: number = 0;
    /** 是否为免费商品 */
    public rewardId: number = 0;
    /** 限制条件 */
    public condition: string = "";
    /** 限购类型 */
    public limitType: number = 0;
    /** 限购数量 */
    public limitNum: number = 0;
    /** 商品内容 */
    public reward: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_TreasureHouse[] = [];
    static filePath: string = "gamecfg/Z珍宝阁_TreasureHouse";
    
    public static get list(): CFG_TreasureHouse[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_TreasureHouse {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_TreasureHouse, this.filePath);
    }
}