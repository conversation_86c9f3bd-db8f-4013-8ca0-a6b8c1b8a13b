// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_DailyWeeklyTask
{
    /** 任务ID */
    public id: number = 0;
    /** 日常周常 */
    public type: number = 0;
    /** 任务描述 */
    public desc: string = "";
    /** 任务描述 */
    public descLang: string = "";
    /** 任务类型 */
    public taskType: string = "";
    /** 任务参数 */
    public taskParams: string = "";
    /** 任务完成目标数量 */
    public taskTargetNum: string = "";
    /** 奖励 */
    public rewardPoints: string = "";
    /** 显示等级 */
    public displayLevel: number = 0;
    /** 跳转类型 */
    public guideType: string = "";
    /** 跳转参数 */
    public guideParams: string = "";
    /** 跳转参数 */
    public guideParamsLang: string = "";
    /** 奖励类型 */
    public rewardType: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_DailyWeeklyTask[] = [];
    static filePath: string = "gamecfg/R日常周常任务表_DailyWeeklyTask";
    
    public static get list(): CFG_DailyWeeklyTask[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_DailyWeeklyTask {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_DailyWeeklyTask, this.filePath);
    }
}