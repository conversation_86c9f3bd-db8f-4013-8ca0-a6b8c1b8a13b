// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_Bubble
{
    /** 气泡ID */
    public id: number = 0;
    /** 对象类型 */
    public objectType: number = 0;
    /** 播放对象 */
    public playObject: number = 0;
    /** 对话文本 */
    public dialogueLang: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_Bubble[] = [];
    static filePath: string = "gamecfg/Q气泡文本配置表_Bubble";
    
    public static get list(): CFG_Bubble[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_Bubble {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_Bubble, this.filePath);
    }
}