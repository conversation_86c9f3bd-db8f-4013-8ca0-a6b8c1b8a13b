// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_Vip
{
    /** vip等级 */
    public level: number = 0;
    /** 贵族经验奖励 */
    public needExp: string = "";
    /** vip特权 */
    public privilege: string = "";
    /** vip奖励 */
    public rewards: string = "";
    /** 原价 */
    public originalPrice: number = 0;
    /** 现价 */
    public currentPrice: number = 0;

    getId(): number | string {
        return this.level;
    }

    static _list: CFG_Vip[] = [];
    static filePath: string = "gamecfg/VIP配置表_Vip";
    
    public static get list(): CFG_Vip[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(level: number): CFG_Vip {
        for (let it of this.list) {
            if (it.level == level) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_Vip, this.filePath);
    }
}