// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_Mail
{
    /** 邮件模板ID */
    public id: number = 0;
    /** 邮件过期天数 */
    public ExpirDay: number = 0;
    /** 标题前端参数格式 */
    public clientTitleFormat: string = "";
    /** 内容前端参数格式 */
    public clientContentFormat: string = "";
    /** 标题 */
    public titleLang: string = "";
    /** 内容 */
    public contentLang: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_Mail[] = [];
    static filePath: string = "gamecfg/Y邮件表_Mail";
    
    public static get list(): CFG_Mail[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_Mail {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_Mail, this.filePath);
    }
}