// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_SceneZone
{
    /** 序号 */
    public id: number = 0;
    /** 场景ID */
    public sceneId: number = 0;
    /** 场景类型 */
    public sceneType: number = 0;
    /** 区域ID */
    public zoneId: number = 0;
    /** 地区刷挂数据文件 */
    public mapData: string = "";
    /** 是否安全区 */
    public safeZone: number = 0;
    /** 背景音效 */
    public bgMusic: string = "";
    /** 地图资源 */
    public mapResource: string = "";
    /** 场景名字程序字 */
    public nameLang: string = "";
    /** 场景名字艺术字 */
    public mapName: string = "";
    /** 英雄视野 */
    public heroVision: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_SceneZone[] = [];
    static filePath: string = "gamecfg/C场景区域表_SceneZone";
    
    public static get list(): CFG_SceneZone[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_SceneZone {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_SceneZone, this.filePath);
    }
}