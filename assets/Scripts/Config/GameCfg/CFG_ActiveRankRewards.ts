// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_ActiveRankRewards
{
    /** 序号 */
    public id: number = 0;
    /** 活动id */
    public activeId: number = 0;
    /** 排名上限 */
    public rankUp: number = 0;
    /** 排名下限 */
    public rankDown: number = 0;
    /** 排名奖励 */
    public rewards: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_ActiveRankRewards[] = [];
    static filePath: string = "gamecfg/H活动-排行榜奖励配置表_ActiveRankRewards";
    
    public static get list(): CFG_ActiveRankRewards[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_ActiveRankRewards {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_ActiveRankRewards, this.filePath);
    }
}