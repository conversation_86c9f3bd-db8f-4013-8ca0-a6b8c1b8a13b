// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_ModelHeight
{
    /** 模型id */
    public id: number = 0;
    /** 影子缩放比列 */
    public shadowScale: number = 0;
    /** 受击高度 */
    public beAttackHeight: number = 0;
    /** 碰撞体积半径 */
    public bodyRadius: number = 0;
    /** 血条高度 */
    public hpHeight: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_ModelHeight[] = [];
    static filePath: string = "gamecfg/M模型高度表_ModelHeight";
    
    public static get list(): CFG_ModelHeight[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_ModelHeight {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_ModelHeight, this.filePath);
    }
}