// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_HandbookProcessor
{
    /** 等级 */
    public level: number = 0;
    /** 所需积分 */
    public needPoint: number = 0;
    /** 奖励 */
    public rewards: string = "";

    getId(): number | string {
        return this.level;
    }

    static _list: CFG_HandbookProcessor[] = [];
    static filePath: string = "gamecfg/T图鉴收集进度奖励表_HandbookProcessor";
    
    public static get list(): CFG_HandbookProcessor[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(level: number): CFG_HandbookProcessor {
        for (let it of this.list) {
            if (it.level == level) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_HandbookProcessor, this.filePath);
    }
}