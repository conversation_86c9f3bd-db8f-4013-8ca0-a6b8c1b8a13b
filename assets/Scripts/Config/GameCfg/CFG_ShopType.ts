// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_ShopType
{
    /** 商店ID */
    public shopId: number = 0;
    /** 商店名称 */
    public shopNameLang: string = "";
    /** 重置类型 */
    public resetType: number = 0;
    /** 开启条件 */
    public functionId: number = 0;
    /** 商店类型 */
    public shopType: number = 0;
    /** 展示的消耗货币 */
    public showCost: string = "";
    /** 页签排序 */
    public sort: number = 0;

    getId(): number | string {
        return this.shopId;
    }

    static _list: CFG_ShopType[] = [];
    static filePath: string = "gamecfg/S商店类型_ShopType";
    
    public static get list(): CFG_ShopType[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(shopId: number): CFG_ShopType {
        for (let it of this.list) {
            if (it.shopId == shopId) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_ShopType, this.filePath);
    }
}