// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_ActiveTemplate
{
    /** 活动ID（唯一） */
    public id: number = 0;
    /** 活动逻辑分类 */
    public logicType: number = 0;
    /** 跨服类型 */
    public crossType: number = 0;
    /** 是否开启 */
    public status: number = 0;
    /** 入口图标 */
    public entryIcon: string = "";
    /** 模块名称 */
    public moduleType: string = "";
    /** 页签名称 */
    public tabIcon: string = "";
    /** 背景图资源 */
    public bgPic: string = "";
    /** 入口位置 */
    public layout: number = 0;
    /** 入口排序 */
    public layoutSort: number = 0;
    /** 活动排序ID */
    public sortId: number = 0;
    /** 开始时间 */
    public startTime: string = "";
    /** 结束时间 */
    public endTime: string = "";
    /** 活动参数 */
    public openParams: string = "";
    /** 持续天数 */
    public openDuration: number = 0;
    /** 开启周期 */
    public openPeriod: string = "";
    /** 开服天数  */
    public openDay: number = 0;
    /** 拓展字段2 */
    public content2: string = "";
    /** 拓展字段2 */
    public content2Lang: string = "";
    /** 渠道ID */
    public channelId: string = "";
    /** 最小等级 */
    public minLevel: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_ActiveTemplate[] = [];
    static filePath: string = "gamecfg/H活动-类型配置表_ActiveTemplate";
    
    public static get list(): CFG_ActiveTemplate[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_ActiveTemplate {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_ActiveTemplate, this.filePath);
    }
}