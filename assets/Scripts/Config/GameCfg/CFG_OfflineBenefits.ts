// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_OfflineBenefits
{
    /** 序号 */
    public id: number = 0;
    /** 怪物id */
    public monster: number = 0;
    /** 道具id */
    public item: number = 0;
    /** 最小数量 */
    public min: number = 0;
    /** 最大数量 */
    public max: number = 0;
    /** 基础时间（单位：分） */
    public time: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_OfflineBenefits[] = [];
    static filePath: string = "gamecfg/L离线收益表_OfflineBenefits";
    
    public static get list(): CFG_OfflineBenefits[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_OfflineBenefits {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_OfflineBenefits, this.filePath);
    }
}