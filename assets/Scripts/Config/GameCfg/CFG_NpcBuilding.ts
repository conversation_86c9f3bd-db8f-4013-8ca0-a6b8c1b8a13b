// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_NpcBuilding
{
    /** 建筑物ID */
    public id: number = 0;
    /** 建筑物名称 */
    public name: string = "";
    /** 建筑物名称 */
    public nameLang: string = "";
    /** 建筑类型 */
    public type: number = 0;
    /** 缩略图ID */
    public icon: string = "";
    /** 预制体路径 */
    public prefabPath: string = "";
    /** 前置建筑ID */
    public preBuildingId: number = 0;
    /** 功能表id */
    public functionId: number = 0;
    /** 开启条件 */
    public openCondition: number = 0;
    /** 解锁消耗道具 */
    public costThing: string = "";
    /** 建筑等级 */
    public level: number = 0;
    /** NPC模型状态处理 */
    public activeOperate: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_NpcBuilding[] = [];
    static filePath: string = "gamecfg/J建筑物表_NpcBuilding";
    
    public static get list(): CFG_NpcBuilding[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_NpcBuilding {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_NpcBuilding, this.filePath);
    }
}