// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_EvilSoul
{
    /** 妖魂ID */
    public id: number = 0;
    /** 名称 */
    public name: string = "";
    /** 品质 */
    public quality: number = 0;
    /** 炼化时长/时 */
    public refineTime: number = 0;
    /** 固定奖励 */
    public reward: string = "";
    /** 权重奖励 */
    public wightReward: string = "";
    /** 随机奖励 */
    public randomReward: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_EvilSoul[] = [];
    static filePath: string = "gamecfg/Y妖魂_EvilSoul";
    
    public static get list(): CFG_EvilSoul[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_EvilSoul {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_EvilSoul, this.filePath);
    }
}