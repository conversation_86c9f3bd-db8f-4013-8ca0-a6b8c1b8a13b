// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_GiftGroup
{
    /** id */
    public id: number = 0;
    /** 类型 */
    public type: number = 0;
    /** 备注 */
    public commentLang: string = "";
    /** 礼品组 */
    public groupId: number = 0;
    /** 等级 */
    public level: number = 0;
    /** 固定奖励 */
    public fixationReward: string = "";
    /** 随机奖励 */
    public randomReward: string = "";
    /** 自选奖励 */
    public optionReward: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_GiftGroup[] = [];
    static filePath: string = "gamecfg/L礼品组_GiftGroup";
    
    public static get list(): CFG_GiftGroup[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_GiftGroup {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_GiftGroup, this.filePath);
    }
}