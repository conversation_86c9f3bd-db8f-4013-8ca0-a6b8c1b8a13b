// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_RankReward
{
    /** 序号 */
    public id: number = 0;
    /** 类型 */
    public type: number = 0;
    /** 奖励ID */
    public rewardId: number = 0;
    /** 达到条件 */
    public condition: number = 0;
    /** 奖励 */
    public reward: string = "";
    /** 条件描述 */
    public tex: string = "";
    /** 条件描述 */
    public texLang: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_RankReward[] = [];
    static filePath: string = "gamecfg/P排行榜奖励表_RankReward";
    
    public static get list(): CFG_RankReward[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_RankReward {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_RankReward, this.filePath);
    }
}