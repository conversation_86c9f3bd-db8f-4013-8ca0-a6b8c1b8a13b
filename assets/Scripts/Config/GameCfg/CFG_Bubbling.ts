// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_Bubbling
{
    /** 冒泡ID */
    public id: number = 0;
    /** 触发类型 */
    public objectType: number = 0;
    /** 对话文本 */
    public dialogue: string = "";
    /** 对话文本 */
    public dialogueLang: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_Bubbling[] = [];
    static filePath: string = "gamecfg/M冒泡配置表_Bubbling";
    
    public static get list(): CFG_Bubbling[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_Bubbling {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_Bubbling, this.filePath);
    }
}