// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_EquipRate
{
    /** 序号 */
    public id: number = 0;
    /** 属性下限 */
    public attrDown: number = 0;
    /** 属性上限 */
    public attrUp: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_EquipRate[] = [];
    static filePath: string = "gamecfg/Z装备品质概率表_EquipRate";
    
    public static get list(): CFG_EquipRate[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_EquipRate {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_EquipRate, this.filePath);
    }
}