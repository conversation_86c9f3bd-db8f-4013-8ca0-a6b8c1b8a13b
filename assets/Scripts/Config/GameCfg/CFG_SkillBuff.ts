// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_SkillBuff
{
    /** buffID */
    public id: number = 0;
    /** buff名称 */
    public name: string = "";
    /** buff名称 */
    public nameLang: string = "";
    /** buff描述 */
    public describeLang: string = "";
    /** buff功能ID：是标识时不同的功能，
 */
    public buffEnum: number = 0;
    /** 属性参数 */
    public attrParams: string = "";
    /** 状态系列：
不同类型的buff统一都是共同作用 */
    public series: number = 0;
    /** buff执行优先级 */
    public priority: number = 0;
    /** 叠加方式
1、覆盖：同类型buff，优先级高的覆盖优先级低的
2、叠加：同类型buff，共存叠加层数
3、共存，buff共同各自作用
 */
    public stackWay: number = 0;
    /** 最大叠加层数： */
    public maxStack: number = 0;
    /** 层数叠满触发的buff */
    public maxStackTrigger: string = "";
    /** BUFF机制参数 */
    public intParams: string = "";
    /** 作用范围 */
    public rangeWay: string = "";
    /** 作用对象 */
    public targetType: string = "";
    /** 目标数量上限 */
    public targetLimit: string = "";
    /** 持续时间毫秒 */
    public lastTime: number = 0;
    /** buff图标id */
    public icon: string = "";
    /** buff特效id */
    public animEffect: string = "";
    /** 受击特效 */
    public hitModel: string = "";
    /** buff放置的节点类型 */
    public animNodeType: number = 0;
    /** 特效位置 */
    public animPosition: number = 0;
    /** 特效是否循环播放 */
    public animLoop: string = "";
    /** 增减益 */
    public gain_or_loss: number = 0;
    /** 驱散方式 */
    public remove_type: number = 0;
    /** 羁绊英雄列表 */
    public buffHeroList: string = "";
    /** 羁绊主英雄 */
    public buffMainHero: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_SkillBuff[] = [];
    static filePath: string = "gamecfg/Buff表_SkillBuff";
    
    public static get list(): CFG_SkillBuff[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_SkillBuff {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_SkillBuff, this.filePath);
    }
}