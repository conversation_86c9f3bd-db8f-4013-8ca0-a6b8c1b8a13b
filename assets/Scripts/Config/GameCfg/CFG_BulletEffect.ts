// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_BulletEffect
{
    /** 子弹ID */
    public id: number = 0;
    /** 子弹消失方式：
0 指定距离内触碰消失，否则到达指定距离后消失#飞行距离（单位：像素）- - -直线触碰后消失
1 到达指定距离后消失#飞行距离（单位：像素）- - -直线贯穿的用这个
2 到达指定位置造成伤害- - -抛物线的用这个
- 无需配置参数，根据目标位置决定
3 指定时间后消失#消失时间（单位：毫秒）
4：追踪
 */
    public vanishType: number = 0;
    /** 消失参数 */
    public parameter: number = 0;
    /** 作用范围 */
    public rangeWay: string = "";
    /** 子弹速度 */
    public bulletExpression: string = "";
    /** 附加buff */
    public buffs: string = "";
    /** 子弹模型 */
    public bulletModel: string = "";
    /** 受击特效 */
    public hitModel: string = "";
    /** 技能音效 */
    public skillSound: string = "";
    /** 受击音效 */
    public hitSound: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_BulletEffect[] = [];
    static filePath: string = "gamecfg/2子弹表_BulletEffect";
    
    public static get list(): CFG_BulletEffect[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_BulletEffect {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_BulletEffect, this.filePath);
    }
}