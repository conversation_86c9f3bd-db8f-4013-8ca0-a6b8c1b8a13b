// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_Purgatory
{
    /** 炼狱阶数 */
    public id: number = 0;
    /** 关联场景ID */
    public sceneId: number = 0;
    /** 加成掉落 */
    public boostDrop: string = "";
    /** 加成Boss掉落 */
    public bossDrop: string = "";
    /** 普奖展示 */
    public rewardShow: string = "";
    /** 加奖展示 */
    public rewardPlusShow: string = "";
    /** 属性库ID */
    public attrStash: number = 0;
    /** 加成消耗 */
    public plusCost: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_Purgatory[] = [];
    static filePath: string = "gamecfg/L炼狱轮回_Purgatory";
    
    public static get list(): CFG_Purgatory[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_Purgatory {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_Purgatory, this.filePath);
    }
}