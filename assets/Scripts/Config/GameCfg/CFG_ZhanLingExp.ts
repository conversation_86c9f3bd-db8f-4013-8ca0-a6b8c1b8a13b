// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_ZhanLingExp
{
    /** 序号 */
    public id: number = 0;
    /** 活动类型ID */
    public activityId: number = 0;
    /** 等级 */
    public level: number = 0;
    /** 升至本级所需经验 */
    public needExp: number = 0;
    /** 免费奖励 */
    public freeReward: string = "";
    /** 高级奖励 */
    public premiumReward: string = "";
    /** 购买本等级消耗 */
    public buyLevelCost: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_ZhanLingExp[] = [];
    static filePath: string = "gamecfg/P1001经验类战令奖励_ZhanLingExp";
    
    public static get list(): CFG_ZhanLingExp[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_ZhanLingExp {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_ZhanLingExp, this.filePath);
    }
}