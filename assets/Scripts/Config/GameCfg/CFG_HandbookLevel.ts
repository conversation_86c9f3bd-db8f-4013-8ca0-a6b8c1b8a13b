// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_HandbookLevel
{
    /** 序号 */
    public id: number = 0;
    /** 图鉴品质 */
    public quality: number = 0;
    /** 提升方式 */
    public upType: number = 0;
    /** 图鉴等级 */
    public level: number = 0;
    /** 需要等级 */
    public needLevel: number = 0;
    /** 消耗道具数量 */
    public needCost: number = 0;
    /** 增长积分 */
    public point: number = 0;
    /** 提升属性 */
    public attrs: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_HandbookLevel[] = [];
    static filePath: string = "gamecfg/T图鉴等级表_HandbookLevel";
    
    public static get list(): CFG_HandbookLevel[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_HandbookLevel {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_HandbookLevel, this.filePath);
    }
}