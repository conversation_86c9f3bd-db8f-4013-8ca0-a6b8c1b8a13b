// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_EvilType
{
    /** 标识 */
    public identity: number = 0;
    /** 名称 */
    public name: string = "";
    /** 名称 */
    public nameLang: string = "";
    /** 品质 */
    public quality: number = 0;
    /** 系列 */
    public series: number = 0;

    getId(): number | string {
        return this.identity;
    }

    static _list: CFG_EvilType[] = [];
    static filePath: string = "gamecfg/Y妖傀类型_EvilType";
    
    public static get list(): CFG_EvilType[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(identity: number): CFG_EvilType {
        for (let it of this.list) {
            if (it.identity == identity) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_EvilType, this.filePath);
    }
}