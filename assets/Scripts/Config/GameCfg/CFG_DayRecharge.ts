// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_DayRecharge
{
    /** 序号 */
    public id: number = 0;
    /** 轮数 */
    public round: number = 0;
    /** 充值商品ID */
    public productId: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_DayRecharge[] = [];
    static filePath: string = "gamecfg/M每日充值_DayRecharge";
    
    public static get list(): CFG_DayRecharge[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_DayRecharge {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_DayRecharge, this.filePath);
    }
}