// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_DungeonMonster
{
    /** 序号 */
    public id: number = 0;
    /** 副本ID */
    public sceneId: number = 0;
    /** 场景区域ID */
    public zoneId: number = 0;
    /** 怪物刷新点 */
    public refreshId: number = 0;
    /** 怪物ID */
    public monsterId: number = 0;
    /** 刷怪点类型（0固定 1随机） */
    public refreshType: number = 0;
    /** 刷怪数量 */
    public num: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_DungeonMonster[] = [];
    static filePath: string = "gamecfg/M秘境副本刷怪表_DungeonMonster";
    
    public static get list(): CFG_DungeonMonster[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_DungeonMonster {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_DungeonMonster, this.filePath);
    }
}