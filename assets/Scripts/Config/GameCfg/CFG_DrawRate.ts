// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_DrawRate
{
    /** 序号 */
    public id: number = 0;
    /** 抽奖类型 */
    public drawType: number = 0;
    /** 奖励组 */
    public group: number = 0;
    /** 奖项ID */
    public drawId: number = 0;
    /** 数量上限 */
    public num: number = 0;
    /** 显示概率 */
    public showWeight: number = 0;
    /** 广播ID */
    public marqueeId: number = 0;
    /** 抽中是否特殊展示 */
    public needShow: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_DrawRate[] = [];
    static filePath: string = "gamecfg/C抽奖概率表_DrawRate";
    
    public static get list(): CFG_DrawRate[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_DrawRate {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_DrawRate, this.filePath);
    }
}