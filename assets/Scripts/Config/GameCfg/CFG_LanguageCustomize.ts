// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_LanguageCustomize
{
    /** 序号 */
    public id: number = 0;
    /** 语言ID */
    public langId: string = "";
    /** 简体中文 */
    public cn: string = "";
    /** 繁体中文 */
    public hk: string = "";
    /** 英语 */
    public en: string = "";
    /** 日语 */
    public jap: string = "";
    /** 韩语 */
    public kr: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_LanguageCustomize[] = [];
    static filePath: string = "gamecfg/YL语言表自定义填写_LanguageCustomize";
    
    public static get list(): CFG_LanguageCustomize[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_LanguageCustomize {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_LanguageCustomize, this.filePath);
    }
}