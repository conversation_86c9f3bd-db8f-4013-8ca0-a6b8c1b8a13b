// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_LoginGift
{
    /** 天数 */
    public id: number = 0;
    /** 奖励 */
    public reward: string = "";
    /** 是否大奖 */
    public bigGift: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_LoginGift[] = [];
    static filePath: string = "gamecfg/D登录豪礼表_LoginGift";
    
    public static get list(): CFG_LoginGift[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_LoginGift {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_LoginGift, this.filePath);
    }
}