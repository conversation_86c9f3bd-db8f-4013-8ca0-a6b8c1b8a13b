// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_SensitiveWords
{
    /** ID */
    public id: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_SensitiveWords[] = [];
    static filePath: string = "gamecfg/M敏感词库表统计_SensitiveWords";
    
    public static get list(): CFG_SensitiveWords[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_SensitiveWords {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_SensitiveWords, this.filePath);
    }
}