// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_FunctionOpen
{
    /** 功能ID */
    public id: number = 0;
    /** 功能名称 */
    public name: string = "";
    /** 开启条件 */
    public openCondition: string = "";
    /** 未开启描述 */
    public lockRemark: string = "";
    /** 未开启描述 */
    public lockRemarkLang: string = "";
    /** 未开启入口隐藏 */
    public lockHidden: number = 0;
    /** 入口路径 */
    public enterPath: string = "";
    /** 开启时是否提示 */
    public openShow: number = 0;
    /** 功能图标 */
    public icon: string = "";
    /** 开启描述 */
    public openDescribe: string = "";
    /** 开启描述 */
    public openDescribeLang: string = "";
    /** 建筑ID */
    public npcId: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_FunctionOpen[] = [];
    static filePath: string = "gamecfg/G功能开启表_FunctionOpen";
    
    public static get list(): CFG_FunctionOpen[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_FunctionOpen {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_FunctionOpen, this.filePath);
    }
}