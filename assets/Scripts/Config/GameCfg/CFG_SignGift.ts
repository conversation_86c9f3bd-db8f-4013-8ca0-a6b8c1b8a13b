// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_SignGift
{
    /** 天数 */
    public id: number = 0;
    /** 奖励 */
    public reward: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_SignGift[] = [];
    static filePath: string = "gamecfg/Q签到表_SignGift";
    
    public static get list(): CFG_SignGift[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_SignGift {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_SignGift, this.filePath);
    }
}