// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_Dungeon
{
    /** 秘境副本ID */
    public id: number = 0;
    /** 秘境副本名字 */
    public name: string = "";
    /** 秘境副本名字 */
    public nameLang: string = "";
    /** 秘境类型 */
    public type: number = 0;
    /** 大陆ID */
    public mainlineId: number = 0;
    /** 推荐战力 */
    public fighting: number = 0;
    /** 进入消耗 */
    public enterCost: string = "";
    /** 关联场景ID */
    public sceneId: number = 0;
    /** 展示奖励 */
    public rewardshow: string = "";
    /** 需要等级 */
    public needLevel: number = 0;
    /** 前置副本ID */
    public precopy: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_Dungeon[] = [];
    static filePath: string = "gamecfg/M秘境副本配置表_Dungeon";
    
    public static get list(): CFG_Dungeon[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_Dungeon {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_Dungeon, this.filePath);
    }
}