// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_Equip
{
    /** 装备ID */
    public equipId: number = 0;
    /** 装备大类 */
    public bigType: number = 0;
    /** 装备小类 */
    public type: number = 0;
    /** 装备名称 */
    public nameLang: string = "";
    /** 备注 */
    public beizhu: string = "";
    /** 装备图标 */
    public icon: string = "";
    /** 装备品质 */
    public quality: number = 0;
    /** 等级限制 */
    public level: number = 0;
    /** 装备基础属性 */
    public attrs: string = "";
    /** 附加属性库 */
    public attrRep: number = 0;
    /** 是否需要鉴定 */
    public appraise: number = 0;
    /** 基础评分 */
    public score: number = 0;
    /** 分解获得 */
    public smeltReward: string = "";
    /** 套装ID */
    public suitId: number = 0;

    getId(): number | string {
        return this.equipId;
    }

    static _list: CFG_Equip[] = [];
    static filePath: string = "gamecfg/Z装备表_Equip";
    
    public static get list(): CFG_Equip[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(equipId: number): CFG_Equip {
        for (let it of this.list) {
            if (it.equipId == equipId) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_Equip, this.filePath);
    }
}