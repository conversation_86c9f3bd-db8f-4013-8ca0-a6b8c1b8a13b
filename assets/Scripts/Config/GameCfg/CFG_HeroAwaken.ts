// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_HeroAwaken
{
    /** 序号 */
    public id: number = 0;
    /** 英雄品质 */
    public quality: number = 0;
    /** 觉醒等级 */
    public level: number = 0;
    /** 消耗材料 */
    public cost: string = "";
    /** 消耗本体数量 */
    public needBody: number = 0;
    /** 指定英雄碎片品质 */
    public otherQuality: number = 0;
    /** 数量 */
    public otherBody: number = 0;
    /** 属性加成 */
    public attr: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_HeroAwaken[] = [];
    static filePath: string = "gamecfg/Y英雄觉醒表_HeroAwaken";
    
    public static get list(): CFG_HeroAwaken[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_HeroAwaken {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_HeroAwaken, this.filePath);
    }
}