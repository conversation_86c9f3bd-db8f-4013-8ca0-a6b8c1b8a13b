// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_Harvest
{
    /** 收割资源ID */
    public id: number = 0;
    /** 资源名称 */
    public name: string = "";
    /** 资源名称 */
    public nameLang: string = "";
    /** 资源类型 */
    public type: number = 0;
    /** 刷新时间秒 */
    public refreshTime: number = 0;
    /** 需收割次数 */
    public needNum: number = 0;
    /** 收割产出 */
    public produce: string = "";
    /** 资源模型 */
    public model: string = "";
    /** 放大系数 */
    public scale: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_Harvest[] = [];
    static filePath: string = "gamecfg/S收割资源类型表_Harvest";
    
    public static get list(): CFG_Harvest[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_Harvest {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_Harvest, this.filePath);
    }
}