// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_ExchangeShop
{
    /** 序号 */
    public id: number = 0;
    /** 商店ID */
    public shopId: number = 0;
    /** 期数 */
    public round: number = 0;
    /** 商品ID */
    public productId: number = 0;
    /** 商品内容 */
    public product: string = "";
    /** 开启条件 */
    public condition: string = "";
    /** 限购类型 */
    public limitType: number = 0;
    /** 最大兑换次数 */
    public limit: number = 0;
    /** 兑换消耗 */
    public cost: string = "";
    /** 商店子ID */
    public shopSubId: number = 0;
    /** 免费次数 */
    public freeTimes: number = 0;
    /** 广告次数 */
    public advertising: number = 0;
    /** 开服x天消失 */
    public closeDay: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_ExchangeShop[] = [];
    static filePath: string = "gamecfg/D兑换商店_ExchangeShop";
    
    public static get list(): CFG_ExchangeShop[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_ExchangeShop {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_ExchangeShop, this.filePath);
    }
}