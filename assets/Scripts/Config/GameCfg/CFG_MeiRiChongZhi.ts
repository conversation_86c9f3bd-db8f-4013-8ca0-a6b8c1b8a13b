// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_MeiRiChongZhi
{
    /** 序号 */
    public id: number = 0;
    /** 活动ID */
    public activityId: number = 0;
    /** 奖励ID */
    public rewardId: number = 0;
    /** 充值数量 */
    public targetNum: string = "";
    /** 自选奖励 */
    public selectRewards: string = "";
    /** 固定奖励 */
    public rewards: string = "";
    /** 描述 */
    public descLang: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_MeiRiChongZhi[] = [];
    static filePath: string = "gamecfg/P1011每日充值_MeiRiChongZhi";
    
    public static get list(): CFG_MeiRiChongZhi[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_MeiRiChong<PERSON><PERSON> {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_MeiRiChongZhi, this.filePath);
    }
}