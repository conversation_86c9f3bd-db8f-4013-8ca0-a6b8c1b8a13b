// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_BattleAttr
{
    /** 属性ID */
    public id: number = 0;
    /** 属性名字 */
    public nameLang: string = "";
    /** 属性字段名 */
    public attrName: string = "";
    /** 运算时/xx */
    public type: number = 0;
    /** 属性类型 */
    public attrType: number = 0;
    /** 说明 */
    public commentLang: string = "";
    /** 属性图标 */
    public icon: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_BattleAttr[] = [];
    static filePath: string = "gamecfg/4战斗属性表_BattleAttr";
    
    public static get list(): CFG_BattleAttr[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_BattleAttr {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_BattleAttr, this.filePath);
    }
}