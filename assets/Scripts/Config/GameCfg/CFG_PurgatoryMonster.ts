// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_PurgatoryMonster
{
    /** 序号 */
    public id: number = 0;
    /** 难度 */
    public difficulty: number = 0;
    /** 场景区域ID */
    public zoneId: number = 0;
    /** 怪物刷新点 */
    public refreshId: number = 0;
    /** 怪物 */
    public monster: string = "";
    /** 刷怪点类型（0固定 1随机） */
    public refreshType: number = 0;
    /** 时间/秒 */
    public time: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_PurgatoryMonster[] = [];
    static filePath: string = "gamecfg/L炼狱轮回怪物表_PurgatoryMonster";
    
    public static get list(): CFG_PurgatoryMonster[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_PurgatoryMonster {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_PurgatoryMonster, this.filePath);
    }
}