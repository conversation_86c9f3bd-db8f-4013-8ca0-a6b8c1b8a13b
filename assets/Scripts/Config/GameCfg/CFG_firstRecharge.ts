// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_firstRecharge
{
    /** 序号 */
    public id: number = 0;
    /** 充值ID */
    public rechargeId: number = 0;
    /** 奖励天数 */
    public day: number = 0;
    /** 名字 */
    public nameLang: string = "";
    /** 奖励 */
    public reward: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_firstRecharge[] = [];
    static filePath: string = "gamecfg/S首充奖励_firstRecharge";
    
    public static get list(): CFG_firstRecharge[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_firstRecharge {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_firstRecharge, this.filePath);
    }
}