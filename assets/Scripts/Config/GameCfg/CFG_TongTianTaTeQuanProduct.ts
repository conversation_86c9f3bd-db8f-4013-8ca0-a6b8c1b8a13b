// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_TongTianTaTeQuanProduct
{
    /** 充值商品ID */
    public id: number = 0;
    /** 塔类型 */
    public towerType: number = 0;
    /** 奖励期数 */
    public round: number = 0;
    /** 购买商品需要VIP等级 */
    public vipLevel: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_TongTianTaTeQuanProduct[] = [];
    static filePath: string = "gamecfg/P1004特权系统通天塔充值档次_TongTianTaTeQuanProduct";
    
    public static get list(): CFG_TongTianTaTeQuanProduct[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_TongTianTaTeQuanProduct {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_TongTianTaTeQuanProduct, this.filePath);
    }
}