// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_QuickBuying
{
    /** 道具ID */
    public id: number = 0;
    /** 道具名字 */
    public nameLang: string = "";
    /** 每份数量 */
    public num: number = 0;
    /** 购买消耗 */
    public cost: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_QuickBuying[] = [];
    static filePath: string = "gamecfg/K快捷购买表_QuickBuying";
    
    public static get list(): CFG_QuickBuying[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_QuickBuying {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_QuickBuying, this.filePath);
    }
}