// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_AchievementPoints
{
    /** 奖励ID */
    public id: number = 0;
    /** 需要点数 */
    public needPoint: number = 0;
    /** 奖励 */
    public rewards: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_AchievementPoints[] = [];
    static filePath: string = "gamecfg/C成就点数奖励表_AchievementPoints";
    
    public static get list(): CFG_AchievementPoints[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_AchievementPoints {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_AchievementPoints, this.filePath);
    }
}