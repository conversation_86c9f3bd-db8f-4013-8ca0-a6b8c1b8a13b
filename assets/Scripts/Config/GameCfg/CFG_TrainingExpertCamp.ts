// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_TrainingExpertCamp
{
    /** 高级训练营等级 */
    public id: number = 0;
    /** 需要角色等级 */
    public level: number = 0;
    /** 加成类型 */
    public type: number = 0;
    /** 加成增量 */
    public num: number = 0;
    /** 加成详情 */
    public buff: number = 0;
    /** 消耗道具 */
    public costThing: string = "";
    /** 属性名称 */
    public attrNameLang: string = "";
    /** 属性描述 */
    public attrDescLang: string = "";
    /** 图标 */
    public icon: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_TrainingExpertCamp[] = [];
    static filePath: string = "gamecfg/X训练营高级属性加成表_TrainingExpertCamp";
    
    public static get list(): CFG_TrainingExpertCamp[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_TrainingExpertCamp {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_TrainingExpertCamp, this.filePath);
    }
}