// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_MainlineScene
{
    /** 主线ID */
    public id: number = 0;
    /** 场景名称 */
    public name: string = "";
    /** 场景名称 */
    public nameLang: string = "";
    /** 复活安全区ID */
    public reviveZone: number = 0;
    /** 关联场景 */
    public sceneId: number = 0;
    /** 最后BOSS怪物ID */
    public bossId: number = 0;
    /** 下一主线场景ID */
    public nextScene: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_MainlineScene[] = [];
    static filePath: string = "gamecfg/Z主线场景表_MainlineScene";
    
    public static get list(): CFG_MainlineScene[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_MainlineScene {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_MainlineScene, this.filePath);
    }
}