// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_ChallengeCopy
{
    /** 序号 */
    public id: number = 0;
    /** 副本类型 */
    public type: number = 0;
    /** 备注 */
    public beizhu: string = "";
    /** 1级目录 */
    public tab1: number = 0;
    /** 2级目录 */
    public tab2: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_ChallengeCopy[] = [];
    static filePath: string = "gamecfg/T挑战副本列表_ChallengeCopy";
    
    public static get list(): CFG_ChallengeCopy[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_ChallengeCopy {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_ChallengeCopy, this.filePath);
    }
}