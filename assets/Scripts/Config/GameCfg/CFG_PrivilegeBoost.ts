// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_PrivilegeBoost
{
    /** 特权id */
    public type: number = 0;
    /** 名称 */
    public name: string = "";
    /** 特权类型 */
    public privilege: string = "";

    getId(): number | string {
        return this.type;
    }

    static _list: CFG_PrivilegeBoost[] = [];
    static filePath: string = "gamecfg/VIP特权表_PrivilegeBoost";
    
    public static get list(): CFG_PrivilegeBoost[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(type: number): CFG_PrivilegeBoost {
        for (let it of this.list) {
            if (it.type == type) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_PrivilegeBoost, this.filePath);
    }
}