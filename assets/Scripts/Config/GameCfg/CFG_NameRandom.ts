// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_NameRandom
{
    /** 序号 */
    public id: number = 0;
    /** 性别 */
    public sex: number = 0;
    /** 类型 */
    public type: number = 0;
    /** 字库 */
    public name: string = "";
    /** 英文字库 */
    public en: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_NameRandom[] = [];
    static filePath: string = "gamecfg/M名字随机_NameRandom";
    
    public static get list(): CFG_NameRandom[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_NameRandom {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_NameRandom, this.filePath);
    }
}