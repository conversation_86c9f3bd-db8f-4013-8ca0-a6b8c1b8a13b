// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_QiRiMuBiaoTask
{
    /** 任务ID */
    public id: number = 0;
    /** 天数 */
    public day: number = 0;
    /** 任务描述 */
    public desc: string = "";
    /** 任务描述 */
    public descLang: string = "";
    /** 任务类型 */
    public taskType: string = "";
    /** 任务参数 */
    public taskParams: string = "";
    /** 任务完成目标数量 */
    public taskTargetNum: string = "";
    /** 完成积分 */
    public score: number = 0;
    /** 任务奖励 */
    public rewards: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_QiRiMuBiaoTask[] = [];
    static filePath: string = "gamecfg/P1007七日目标_QiRiMuBiaoTask";
    
    public static get list(): CFG_QiRiMuBiaoTask[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_QiRiMuBiaoTask {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_QiRiMuBiaoTask, this.filePath);
    }
}