// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_GmDefine
{
    /** 序号 */
    public idx: number = 0;
    /** 功能序号 */
    public funcIdx: number = 0;
    /** 功能名字 */
    public funcName: string = "";
    /** GM指令 */
    public gmName: string = "";
    /** 调用示例，注意参数之间有英文空格 */
    public example: string = "";
    /** 是否为无参命令 */
    public noArgs: number = 0;
    /** 预设参数 */
    public preArgs: string = "";
    /** 标题 */
    public title: string = "";

    getId(): number | string {
        return this.idx;
    }

    static _list: CFG_GmDefine[] = [];
    static filePath: string = "gamecfg/GGm指令说明_GmDefine";
    
    public static get list(): CFG_GmDefine[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(idx: number): CFG_GmDefine {
        for (let it of this.list) {
            if (it.idx == idx) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_GmDefine, this.filePath);
    }
}