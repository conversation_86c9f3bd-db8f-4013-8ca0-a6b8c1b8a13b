// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_Access
{
    /** 获取途径ID */
    public id: number = 0;
    /** 获取途径名称 */
    public nameLang: string = "";
    /** 前往类型 */
    public funcType: number = 0;
    /** 建筑ID */
    public npcId: number = 0;
    /** 界面枚举名字 */
    public enumName: string = "";
    /** 功能表id */
    public functionId: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_Access[] = [];
    static filePath: string = "gamecfg/H获取途径_Access";
    
    public static get list(): CFG_Access[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_Access {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_Access, this.filePath);
    }
}