// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_ZhiGouLiBao
{
    /** 礼包ID */
    public id: number = 0;
    /** 活动ID */
    public activityId: number = 0;
    /** 充值表ID */
    public chargeProductId: number = 0;
    /** 个性化奖励 */
    public diffReward: string = "";
    /** 限购类型 */
    public limitType: number = 0;
    /** 限购次数 */
    public limitNum: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_ZhiGouLiBao[] = [];
    static filePath: string = "gamecfg/P1009直购礼包_ZhiGouLiBao";
    
    public static get list(): CFG_ZhiGouLiBao[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_ZhiGouLiBao {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_ZhiGouLiBao, this.filePath);
    }
}