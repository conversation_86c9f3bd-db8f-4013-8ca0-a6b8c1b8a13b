// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_SceneNpc
{
    /** 序号 */
    public id: number = 0;
    /** 场景ID */
    public sceneId: number = 0;
    /** npcId */
    public npcId: number = 0;
    /** NPC名称 */
    public npcName: string = "";
    /** NPC名称 */
    public npcNameLang: string = "";
    /** 完成消耗 */
    public questCosts: string = "";
    /** 完成奖励 */
    public finishRewards: string = "";
    /** NPC模型状态处理 */
    public activeOperate: number = 0;
    /** 模型ID */
    public model: number = 0;
    /** 解锁点所在迷雾区域 */
    public unlockArea: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_SceneNpc[] = [];
    static filePath: string = "gamecfg/Z主线野外NPC表_SceneNpc";
    
    public static get list(): CFG_SceneNpc[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_SceneNpc {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_SceneNpc, this.filePath);
    }
}