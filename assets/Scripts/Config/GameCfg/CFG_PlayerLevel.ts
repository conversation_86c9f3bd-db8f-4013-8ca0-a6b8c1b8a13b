// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_PlayerLevel
{
    /** 等级 */
    public id: number = 0;
    /** 升到下一级需要的经验 */
    public exp: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_PlayerLevel[] = [];
    static filePath: string = "gamecfg/J角色等级表_PlayerLevel";
    
    public static get list(): CFG_PlayerLevel[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_PlayerLevel {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_PlayerLevel, this.filePath);
    }
}