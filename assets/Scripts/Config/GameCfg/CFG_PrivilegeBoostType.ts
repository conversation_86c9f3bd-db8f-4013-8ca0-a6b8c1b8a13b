// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_PrivilegeBoostType
{
    /** 特权类型 */
    public type: number = 0;
    /** 名称 */
    public nameLang: string = "";
    /** 加成途径 */
    public path: string = "";

    getId(): number | string {
        return this.type;
    }

    static _list: CFG_PrivilegeBoostType[] = [];
    static filePath: string = "gamecfg/VIP特权加成类型表_PrivilegeBoostType";
    
    public static get list(): CFG_PrivilegeBoostType[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(type: number): CFG_PrivilegeBoostType {
        for (let it of this.list) {
            if (it.type == type) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_PrivilegeBoostType, this.filePath);
    }
}