// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_TrainingGeneralCamp
{
    /** 训练等级 */
    public id: number = 0;
    /** 角色等级 */
    public level: number = 0;
    /** 属性字段名 */
    public attr: string = "";
    /** 消耗道具 */
    public costThing: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_TrainingGeneralCamp[] = [];
    static filePath: string = "gamecfg/X训练营普通属性加成表_TrainingGeneralCamp";
    
    public static get list(): CFG_TrainingGeneralCamp[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_TrainingGeneralCamp {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_TrainingGeneralCamp, this.filePath);
    }
}