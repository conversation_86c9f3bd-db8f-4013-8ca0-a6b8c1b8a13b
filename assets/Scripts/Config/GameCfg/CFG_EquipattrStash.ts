// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_EquipattrStash
{
    /** 序号 */
    public id: number = 0;
    /** 库id */
    public stashId: number = 0;
    /** 属性类型 */
    public type: number = 0;
    /** Max属性值 */
    public max: number = 0;
    /** 随机权重 */
    public weight: number = 0;
    /** 评分 */
    public score: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_EquipattrStash[] = [];
    static filePath: string = "gamecfg/Z装备附加属性库_EquipattrStash";
    
    public static get list(): CFG_EquipattrStash[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_EquipattrStash {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_EquipattrStash, this.filePath);
    }
}