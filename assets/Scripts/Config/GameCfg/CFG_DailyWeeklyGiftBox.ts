// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_DailyWeeklyGiftBox
{
    /** 编号 */
    public id: number = 0;
    /** 任务类型 */
    public type: number = 0;
    /** 奖励序号 */
    public rewardNum: number = 0;
    /** 活跃度 */
    public points: number = 0;
    /** 任务奖励 */
    public rewards: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_DailyWeeklyGiftBox[] = [];
    static filePath: string = "gamecfg/R日常周常活跃宝箱_DailyWeeklyGiftBox";
    
    public static get list(): CFG_DailyWeeklyGiftBox[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_DailyWeeklyGiftBox {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_DailyWeeklyGiftBox, this.filePath);
    }
}