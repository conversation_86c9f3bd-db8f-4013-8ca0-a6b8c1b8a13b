// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_QiRiMuBiaoStage
{
    /** 阶段ID */
    public id: number = 0;
    /** 阶段目标任务数 */
    public condition: number = 0;
    /** 任务奖励 */
    public reward: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_QiRiMuBiaoStage[] = [];
    static filePath: string = "gamecfg/P1007七日目标阶段奖励_QiRiMuBiaoStage";
    
    public static get list(): CFG_QiRiMuBiaoStage[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_QiRiMuBiaoStage {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_QiRiMuBiaoStage, this.filePath);
    }
}