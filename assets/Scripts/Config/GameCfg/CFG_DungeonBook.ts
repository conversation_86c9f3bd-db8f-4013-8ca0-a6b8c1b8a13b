// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_DungeonBook
{
    /** 书本ID */
    public id: number = 0;
    /** 技能ID */
    public skillId: number = 0;
    /** 权重 */
    public weight: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_DungeonBook[] = [];
    static filePath: string = "gamecfg/F副本工具书表_DungeonBook";
    
    public static get list(): CFG_DungeonBook[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_DungeonBook {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_DungeonBook, this.filePath);
    }
}