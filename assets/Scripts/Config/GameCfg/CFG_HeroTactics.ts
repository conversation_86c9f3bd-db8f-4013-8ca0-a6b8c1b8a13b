// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_HeroTactics
{
    /** 流派类型 */
    public id: number = 0;
    /** 流派图标 */
    public icon: string = "";
    /** 流派描述 */
    public describe: string = "";
    /** 流派描述 */
    public describeLang: string = "";
    /** 核心伙伴id */
    public heroIdentity: number = 0;
    /** 羁绊伙伴列表 */
    public heroList: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_HeroTactics[] = [];
    static filePath: string = "gamecfg/Y英雄流派表_HeroTactics";
    
    public static get list(): CFG_HeroTactics[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_HeroTactics {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_HeroTactics, this.filePath);
    }
}