// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_SysMainTemplate
{
    /** 系统ID（唯一） */
    public id: number = 0;
    /** 活动逻辑分类 */
    public logicType: number = 0;
    /** 跨服类型 */
    public crossType: number = 0;
    /** 当前所属模块名称 */
    public modeName: string = "";
    /** 入口图标 */
    public entryIcon: string = "";
    /** 按钮开的的界面名称 */
    public openModeName: string = "";
    /** 预制体模块 */
    public prefabName: string = "";
    /** 页面标题 */
    public viewTitle: string = "";
    /** 页签名字 */
    public tabName: string = "";
    /** 页签icon */
    public tabIcon: string = "";
    /** 入口位置 */
    public layout: number = 0;
    /** 入口排序(主) */
    public layoutSort: number = 0;
    /** 是否隐藏按钮 */
    public active: number = 0;
    /** 活动排序ID */
    public sortId: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_SysMainTemplate[] = [];
    static filePath: string = "gamecfg/F福利-类型配置表_SysMainTemplate";
    
    public static get list(): CFG_SysMainTemplate[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_SysMainTemplate {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_SysMainTemplate, this.filePath);
    }
}