// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_ChengZhangJiJin
{
    /** 序号 */
    public id: number = 0;
    /** 奖励期数 */
    public round: number = 0;
    /** 奖励ID */
    public rewardId: number = 0;
    /** 等级要求 */
    public level: number = 0;
    /** 奖励 */
    public reward: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_ChengZhangJiJin[] = [];
    static filePath: string = "gamecfg/P1006成长基金_ChengZhangJiJin";
    
    public static get list(): CFG_ChengZhangJiJin[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_ChengZhangJiJin {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_ChengZhangJiJin, this.filePath);
    }
}