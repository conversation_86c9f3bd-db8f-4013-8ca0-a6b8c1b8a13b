// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_VillageRank
{
    /** 序号 */
    public id: number = 0;
    /** 最高排名 */
    public rankMax: number = 0;
    /** 最低排名 */
    public rankMin: number = 0;
    /** 排行奖励 */
    public reward: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_VillageRank[] = [];
    static filePath: string = "gamecfg/X仙境排行奖励表_VillageRank";
    
    public static get list(): CFG_VillageRank[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_VillageRank {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_VillageRank, this.filePath);
    }
}