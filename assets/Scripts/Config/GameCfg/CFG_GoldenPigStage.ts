// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_GoldenPigStage
{
    /** 难度 */
    public id: number = 0;
    /** 通关奖励 */
    public reward: string = "";
    /** 怪物 */
    public monsters: string = "";
    /** 刷怪点类型（0固定 1随机） */
    public refreshType: number = 0;
    /** 关联场景ID */
    public sceneId: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_GoldenPigStage[] = [];
    static filePath: string = "gamecfg/H黄金猪挑战难度表_GoldenPigStage";
    
    public static get list(): CFG_GoldenPigStage[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_GoldenPigStage {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_GoldenPigStage, this.filePath);
    }
}