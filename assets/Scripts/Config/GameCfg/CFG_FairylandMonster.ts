// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_FairylandMonster
{
    /** 序号 */
    public id: number = 0;
    /** 难度 */
    public difficulty: number = 0;
    /** 波 */
    public wave: number = 0;
    /** 怪物刷新 */
    public monsters: string = "";
    /** 刷怪点类型（0固定 1随机） */
    public refreshType: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_FairylandMonster[] = [];
    static filePath: string = "gamecfg/X仙境守卫怪物表(村庄)_FairylandMonster";
    
    public static get list(): CFG_FairylandMonster[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_FairylandMonster {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_FairylandMonster, this.filePath);
    }
}