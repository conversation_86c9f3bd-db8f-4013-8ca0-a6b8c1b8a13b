// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_SceneTeleport
{
    /** 序号 */
    public id: number = 0;
    /** 主线场景ID */
    public mainlineId: number = 0;
    /** 传送点ID */
    public teleportId: number = 0;
    /** 传送点名字 */
    public teleportNameLang: string = "";
    /** 开启消耗 */
    public openCosts: string = "";
    /** 推荐战力 */
    public fighting: string = "";
    /** 归属大陆 */
    public belongLand: number = 0;
    /** 大陆名字 */
    public belongNameLang: string = "";
    /** 传送点分组 */
    public group: number = 0;
    /** 分组名字 */
    public groupNameLang: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_SceneTeleport[] = [];
    static filePath: string = "gamecfg/Z主线野外传送点表_SceneTeleport";
    
    public static get list(): CFG_SceneTeleport[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_SceneTeleport {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_SceneTeleport, this.filePath);
    }
}