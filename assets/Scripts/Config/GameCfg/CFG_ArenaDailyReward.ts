// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_ArenaDailyReward
{
    /** 序号 */
    public id: number = 0;
    /** 排名上 */
    public rankUp: number = 0;
    /** 排名下 */
    public rankDown: number = 0;
    /** 奖励 */
    public rewards: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_ArenaDailyReward[] = [];
    static filePath: string = "gamecfg/J竞技场每日奖励表_ArenaDailyReward";
    
    public static get list(): CFG_ArenaDailyReward[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_ArenaDailyReward {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_ArenaDailyReward, this.filePath);
    }
}