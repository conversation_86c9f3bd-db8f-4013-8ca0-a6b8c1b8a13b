// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_HandbookSuit
{
    /** 序号 */
    public id: number = 0;
    /** 加成属性 */
    public attrs: string = "";
    /** 备注 */
    public describe: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_HandbookSuit[] = [];
    static filePath: string = "gamecfg/T图鉴套装属性表_HandbookSuit";
    
    public static get list(): CFG_HandbookSuit[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_HandbookSuit {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_HandbookSuit, this.filePath);
    }
}