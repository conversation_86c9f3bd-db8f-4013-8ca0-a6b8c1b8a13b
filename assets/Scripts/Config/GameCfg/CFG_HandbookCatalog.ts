// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_HandbookCatalog
{
    /** 序号 */
    public id: number = 0;
    /** 图鉴类型 */
    public type: number = 0;
    /** 分组 */
    public group: number = 0;
    /** 组名 */
    public groupNameLang: string = "";
    /** 子类 */
    public subtype: number = 0;
    /** 子类名 */
    public subtypeName: string = "";
    /** 子类名 */
    public subtypeNameLang: string = "";
    /** 品质 */
    public quality: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_HandbookCatalog[] = [];
    static filePath: string = "gamecfg/T图鉴层级表_HandbookCatalog";
    
    public static get list(): CFG_HandbookCatalog[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_HandbookCatalog {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_HandbookCatalog, this.filePath);
    }
}