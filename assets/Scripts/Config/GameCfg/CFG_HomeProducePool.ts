// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_HomeProducePool
{
    /** 序号 */
    public id: number = 0;
    /** 建筑物ID */
    public buildingId: number = 0;
    /** 资源池等级 */
    public level: number = 0;
    /** 升级消耗 */
    public upCost: string = "";
    /** 最大容量 */
    public maxStore: string = "";
    /** 产出间隔秒 */
    public produceGap: number = 0;
    /** 产出的道具ID */
    public produceType: number = 0;
    /** 每个时间间隔产出的数量 */
    public produce: string = "";
    /** 界面文本 */
    public uiDescLang: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_HomeProducePool[] = [];
    static filePath: string = "gamecfg/J家园产出池等级表_HomeProducePool";
    
    public static get list(): CFG_HomeProducePool[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_HomeProducePool {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_HomeProducePool, this.filePath);
    }
}