// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_HeroRecruitSpecial
{
    /** 手动刷新次数 */
    public id: number = 0;
    /** 英雄池 */
    public heroPool: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_HeroRecruitSpecial[] = [];
    static filePath: string = "gamecfg/Y英雄特殊招募表_HeroRecruitSpecial";
    
    public static get list(): CFG_HeroRecruitSpecial[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_HeroRecruitSpecial {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_HeroRecruitSpecial, this.filePath);
    }
}