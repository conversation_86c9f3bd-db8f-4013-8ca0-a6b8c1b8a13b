// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_Monster
{
    /** 怪物ID */
    public id: number = 0;
    /** 怪物名称(程序字) */
    public name: string = "";
    /** 怪物名称(程序字) */
    public nameLang: string = "";
    /** 怪物模型 */
    public identity: number = 0;
    /** 战斗类型 */
    public hateType: number = 0;
    /** 怪物头像 */
    public icon: string = "";
    /** 怪物名称(艺术字) */
    public monsterName: string = "";
    /** 放大系数 */
    public scale: number = 0;
    /** 体积半径 */
    public volume: number = 0;
    /** 等级 */
    public level: number = 0;
    /** 类型 */
    public type: number = 0;
    /** 怪物属性 */
    public attribute: string = "";
    /** 血条数 */
    public hpPoints: number = 0;
    /** 最高扣血 */
    public maxHurt: number = 0;
    /** 刷新时间秒 */
    public refreshTime: number = 0;
    /** 寻敌范围 */
    public huntingRange: number = 0;
    /** 追击范围 */
    public chaseRange: number = 0;
    /** 脱战范围 */
    public outOfRange: number = 0;
    /** 脱战回血效率 */
    public revitalize: string = "";
    /** 击杀固定产出 */
    public produce: string = "";
    /** 怪物技能 */
    public skills: string = "";
    /** 霸体状态 */
    public superArmor: string = "";
    /** 怪物战力 */
    public fighting: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_Monster[] = [];
    static filePath: string = "gamecfg/G怪物表_Monster";
    
    public static get list(): CFG_Monster[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_Monster {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_Monster, this.filePath);
    }
}