// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_OutputPath
{
    /** id */
    public id: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_OutputPath[] = [];
    static filePath: string = "gamecfg/J奖励产出途径表_OutputPath";
    
    public static get list(): CFG_OutputPath[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_OutputPath {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_OutputPath, this.filePath);
    }
}