// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_DrawType
{
    /** 抽奖类型 */
    public id: number = 0;
    /** 免费次数 */
    public freeNum: number = 0;
    /** 广告次数 */
    public advNum: number = 0;
    /** 单抽消耗道具 */
    public oneCostItem: string = "";
    /** 单抽消耗仙玉 */
    public oneCostDiamond: string = "";
    /** 十抽仙玉折扣 */
    public tenDiscount: number = 0;
    /** 积分奖励 */
    public point: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_DrawType[] = [];
    static filePath: string = "gamecfg/C抽奖类型表_DrawType";
    
    public static get list(): CFG_DrawType[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_DrawType {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_DrawType, this.filePath);
    }
}