// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_Fairyland
{
    /** 仙境关卡Id */
    public id: number = 0;
    /** 通关奖励 */
    public reward: string = "";
    /** 神像血量 */
    public godHP: number = 0;
    /** 栅栏血量 */
    public fenceHP: number = 0;
    /** 栅栏消耗 */
    public fenceBuild: string = "";
    /** 随机奖励 */
    public randomReward: string = "";
    /** 展示奖励 */
    public showRewrad: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_Fairyland[] = [];
    static filePath: string = "gamecfg/X仙境守卫(村庄)_Fairyland";
    
    public static get list(): CFG_Fairyland[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_Fairyland {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_Fairyland, this.filePath);
    }
}