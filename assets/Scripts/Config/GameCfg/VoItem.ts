export default class VoItem {

    /** 道具ID */
    protected _id?: number;
    /** 数量 */
    itemNum?: number;

    set id(value: number) {
        this._id = value;
        this.init(this._id);
    }

    get id(): number {
        return this._id;
    }

    protected init(id: number) { };
}

export function parseResourceItem(data: string): VoItem[] {
    if (!data) return null;

    let ret: VoItem[] = [];
    let values = data.split("|");
    for (const value of values) {
        const content = value.split("#");
        const id = Number(content[0]);
        const count = Number(content[1]);
        let item: VoItem = new VoItem();
        item.id = id;
        item.itemNum = count;
        ret.push(item);
    }
    return ret;
}

export function parseRandItem(data: string): VoItem[] {
    if (!data) return null;

    let ret: VoItem[] = [];
    let values = data.split("|");
    for (const value of values) {
        const content = value.split("#");
        const id = Number(content[1]);
        const count = Number(content[2]);
        let item: VoItem = new VoItem();
        item.id = id;
        item.itemNum = count;
        ret.push(item);
    }
    return ret;
}

/** 解析固定奖励类型 */
export function parseFixItem(data: string): VoItem[] {
    if (!data) return null;

    let ret: VoItem[] = [];
    let values = data.split("|");
    for (const value of values) {
        const content = value.split("#");
        const id = Number(content[0]);
        const count = Number(content[1]);
        let item: VoItem = new VoItem();
        item.id = id;
        item.itemNum = count;
        ret.push(item);
    }
    return ret;
}
