// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_BattleType
{
    /** ID */
    public id: number = 0;
    /** 名字 */
    public name: string = "";
    /** 默认的地图文件 */
    public mapData: string = "";
    /** 战斗时长毫秒 */
    public battleTime: number = 0;
    /** 是否统计连杀数 */
    public sumKill: number = 0;
    /** 是否可以自动战斗 */
    public canAuto: number = 0;
    /** 是否弹结算界面 */
    public showSettle: number = 0;
    /** 每天获得免费门票 */
    public freeTicket: string = "";
    /** 默认场景ID */
    public defaultScene: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_BattleType[] = [];
    static filePath: string = "gamecfg/Z战斗类型表_BattleType";
    
    public static get list(): CFG_BattleType[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_BattleType {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_BattleType, this.filePath);
    }
}