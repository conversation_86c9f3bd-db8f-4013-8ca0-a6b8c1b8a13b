// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_ZhanLingGoal
{
    /** 任务ID */
    public id: number = 0;
    /** 活动类型ID */
    public activityId: number = 0;
    /** 排序编号 */
    public sortIndex: number = 0;
    /** 任务类型 */
    public taskType: string = "";
    /** 任务参数 */
    public taskParams: string = "";
    /** 任务完成目标数量 */
    public taskTargetNum: string = "";
    /** 免费奖励 */
    public freeReward: string = "";
    /** 高级奖励 */
    public premiumReward: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_ZhanLingGoal[] = [];
    static filePath: string = "gamecfg/P1002目标类战令奖励_ZhanLingGoal";
    
    public static get list(): CFG_ZhanLingGoal[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_ZhanLingGoal {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_ZhanLingGoal, this.filePath);
    }
}