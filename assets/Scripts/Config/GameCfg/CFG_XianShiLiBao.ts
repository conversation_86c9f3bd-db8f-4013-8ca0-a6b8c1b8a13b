// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_XianShiLiBao
{
    /** 礼包ID */
    public id: number = 0;
    /** 礼包类型 */
    public type: number = 0;
    /** 充值表ID */
    public chargeProductId: number = 0;
    /** Vip等级 */
    public vipLevel: number = 0;
    /** 触发条件 */
    public condition: string = "";
    /** 个性化奖励 */
    public diffReward: string = "";
    /** 有效期/分钟 */
    public countdown: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_XianShiLiBao[] = [];
    static filePath: string = "gamecfg/P1005限时礼包_XianShiLiBao";
    
    public static get list(): CFG_XianShiLiBao[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_XianShiLiBao {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_XianShiLiBao, this.filePath);
    }
}