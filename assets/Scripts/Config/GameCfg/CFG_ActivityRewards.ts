// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_ActivityRewards
{
    /** 任务ID */
    public id: number = 0;
    /** 活动ID */
    public templateId: number = 0;
    /** 显示顺序 */
    public index: number = 0;
    /** 任务类型 */
    public taskType: string = "";
    /** 任务参数 */
    public taskParams: string = "";
    /** 任务完成目标数量 */
    public taskTargetNum: string = "";
    /** 自选奖励 */
    public selectRewards: string = "";
    /** 固定奖励 */
    public rewards: string = "";
    /** 描述 */
    public desc: string = "";
    /** 描述 */
    public descLang: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_ActivityRewards[] = [];
    static filePath: string = "gamecfg/H活动-奖励配置表_ActivityRewards";
    
    public static get list(): CFG_ActivityRewards[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_ActivityRewards {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_ActivityRewards, this.filePath);
    }
}