// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_ComboAttr
{
    /** 连击次数 */
    public num: number = 0;
    /** buffId */
    public buffId: number = 0;
    /** 断连时间/毫秒 */
    public disconnect: string = "";

    getId(): number | string {
        return this.num;
    }

    static _list: CFG_ComboAttr[] = [];
    static filePath: string = "gamecfg/L连击Buff_ComboAttr";
    
    public static get list(): CFG_ComboAttr[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(num: number): CFG_ComboAttr {
        for (let it of this.list) {
            if (it.num == num) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_ComboAttr, this.filePath);
    }
}