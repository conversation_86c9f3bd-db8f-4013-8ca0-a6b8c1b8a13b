// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_PurgatorySeason
{
    /** 赛季 */
    public id: number = 0;
    /** 推荐英雄 */
    public suggest: string = "";
    /** 提升道具id */
    public drop: number = 0;
    /** 品质加成 */
    public Quality: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_PurgatorySeason[] = [];
    static filePath: string = "gamecfg/L炼狱阵容推荐_PurgatorySeason";
    
    public static get list(): CFG_PurgatorySeason[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_PurgatorySeason {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_PurgatorySeason, this.filePath);
    }
}