// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_MainlineTreasureBox
{
    /** 序号 */
    public idx: number = 0;
    /** 宝箱类型 */
    public type: number = 0;
    /** 类型参数 */
    public typeArg: number = 0;
    /** 奖励预览 */
    public rewardShow: string = "";

    getId(): number | string {
        return this.idx;
    }

    static _list: CFG_MainlineTreasureBox[] = [];
    static filePath: string = "gamecfg/C场景宝箱配置表_MainlineTreasureBox";
    
    public static get list(): CFG_MainlineTreasureBox[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(idx: number): CFG_MainlineTreasureBox {
        for (let it of this.list) {
            if (it.idx == idx) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_MainlineTreasureBox, this.filePath);
    }
}