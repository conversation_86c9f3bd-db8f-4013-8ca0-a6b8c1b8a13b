// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_HandbookIdentity
{
    /** 图鉴ID */
    public id: number = 0;
    /** 图鉴品质 */
    public quality: number = 0;
    /** 升级方式 */
    public upType: number = 0;
    /** 归属层级 */
    public catalog: number = 0;
    /** 套装ID */
    public suit: number = 0;
    /** 图鉴icon */
    public icon: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_HandbookIdentity[] = [];
    static filePath: string = "gamecfg/T图鉴列表配置表_HandbookIdentity";
    
    public static get list(): CFG_HandbookIdentity[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_HandbookIdentity {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_HandbookIdentity, this.filePath);
    }
}