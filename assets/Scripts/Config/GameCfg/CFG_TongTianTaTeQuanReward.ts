// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_TongTianTaTeQuanReward
{
    /** 序号 */
    public id: number = 0;
    /** 塔类型 */
    public towerType: number = 0;
    /** 奖励期数 */
    public round: number = 0;
    /** 奖励等级 */
    public rewardLv: number = 0;
    /** 所需达到层数 */
    public layer: number = 0;
    /** 普通挑战奖励 */
    public normalReward: string = "";
    /** 高级挑战奖励 */
    public highReward: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_TongTianTaTeQuanReward[] = [];
    static filePath: string = "gamecfg/P1004特权系统通天塔层数奖励_TongTianTaTeQuanReward";
    
    public static get list(): CFG_TongTianTaTeQuanReward[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_TongTianTaTeQuanReward {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_TongTianTaTeQuanReward, this.filePath);
    }
}