// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_RechargeProduct
{
    /** 充值商品ID */
    public id: number = 0;
    /** 商品名称 */
    public name: string = "";
    /** 商品名称 */
    public nameLang: string = "";
    /** 商品逻辑类型 */
    public productType: number = 0;
    /** 商品充值参数 */
    public productParam: string = "";
    /** 金额单位分 */
    public money: string = "";
    /** 商品编码 */
    public productCode: string = "";
    /** 充值奖励 */
    public rewards: string = "";
    /** 首充额外奖励 */
    public firstRewards: string = "";
    /** 贵族经验奖励 */
    public noble: string = "";
    /** 价格文本 */
    public priceTxtLang: string = "";
    /** 返利 */
    public rebate: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_RechargeProduct[] = [];
    static filePath: string = "gamecfg/C充值商品表_RechargeProduct";
    
    public static get list(): CFG_RechargeProduct[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_RechargeProduct {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_RechargeProduct, this.filePath);
    }
}