// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_SceneStage
{
    /** 序号 */
    public id: number = 0;
    /** 场景ID */
    public sceneId: number = 0;
    /** 难度阶段 */
    public stage: number = 0;
    /** 首通奖励 */
    public firstRewards: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_SceneStage[] = [];
    static filePath: string = "gamecfg/C场景难度阶段表_SceneStage";
    
    public static get list(): CFG_SceneStage[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_SceneStage {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_SceneStage, this.filePath);
    }
}