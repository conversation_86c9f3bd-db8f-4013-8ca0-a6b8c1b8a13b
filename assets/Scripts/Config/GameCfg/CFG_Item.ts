// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_Item
{
    /** 道具ID */
    public id: number = 0;
    /** 道具名称 */
    public nameLang: string = "";
    /** 道具逻辑类型 */
    public kind: number = 0;
    /** 道具类型子类 */
    public subType: number = 0;
    /** 逻辑类型参数 */
    public kindParam: number = 0;
    /** 品质 */
    public quality: number = 0;
    /** 使用类型 */
    public useType: number = 0;
    /** 使用数据 */
    public useData: string = "";
    /** 道具图标 */
    public icon: string = "";
    /** 排序 */
    public sort: number = 0;
    /** 道具描述 */
    public describe: string = "";
    /** 道具描述 */
    public describeLang: string = "";
    /** 获取途径描述 */
    public access: string = "";
    /** 角标 */
    public tab: string = "";
    /** 掉落光柱 */
    public beam: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_Item[] = [];
    static filePath: string = "gamecfg/1道具表_Item";
    
    public static get list(): CFG_Item[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_Item {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_Item, this.filePath);
    }
}