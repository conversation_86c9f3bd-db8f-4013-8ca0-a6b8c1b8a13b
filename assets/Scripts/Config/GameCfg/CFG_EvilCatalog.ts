// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_EvilCatalog
{
    /** 序号 */
    public id: number = 0;
    /** 标识 */
    public identity: number = 0;
    /** 阶级 */
    public level: number = 0;
    /** 消耗本体数量 */
    public costBody: number = 0;
    /** 属性加成 */
    public attr: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_EvilCatalog[] = [];
    static filePath: string = "gamecfg/Y妖录_EvilCatalog";
    
    public static get list(): CFG_EvilCatalog[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_EvilCatalog {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_EvilCatalog, this.filePath);
    }
}