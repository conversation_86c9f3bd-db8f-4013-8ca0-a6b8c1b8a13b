// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_ItemRandomPack
{
    /** 序号 */
    public id: number = 0;
    /** 掉落组ID */
    public group: number = 0;
    /** 掉落奖励(概率#道具id#数量|) */
    public reward: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_ItemRandomPack[] = [];
    static filePath: string = "gamecfg/D掉落表_ItemRandomPack";
    
    public static get list(): CFG_ItemRandomPack[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_ItemRandomPack {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_ItemRandomPack, this.filePath);
    }
}