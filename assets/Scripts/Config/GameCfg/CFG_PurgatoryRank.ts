// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_PurgatoryRank
{
    /** 序号 */
    public id: number = 0;
    /** 最高排名 */
    public rankMax: number = 0;
    /** 最低排名 */
    public rankMin: number = 0;
    /** 排行奖励 */
    public reward: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_PurgatoryRank[] = [];
    static filePath: string = "gamecfg/L炼狱排行奖励_PurgatoryRank";
    
    public static get list(): CFG_PurgatoryRank[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_PurgatoryRank {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_PurgatoryRank, this.filePath);
    }
}