// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_HeroPrizedraw
{
    /** 品质 */
    public quality: number = 0;
    /** 抽取权重 */
    public weight: number = 0;
    /** 碎片 */
    public fragment: string = "";
    /** 积分 */
    public points: string = "";
    /** 抽奖多少次后必定中 */
    public gottaNum: number = 0;
    /** 提升阶段变化数量 */
    public upStageNum: number = 0;

    getId(): number | string {
        return this.quality;
    }

    static _list: CFG_HeroPrizedraw[] = [];
    static filePath: string = "gamecfg/Y英雄抽取表_HeroPrizedraw";
    
    public static get list(): CFG_HeroPrizedraw[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(quality: number): CFG_HeroPrizedraw {
        for (let it of this.list) {
            if (it.quality == quality) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_HeroPrizedraw, this.filePath);
    }
}