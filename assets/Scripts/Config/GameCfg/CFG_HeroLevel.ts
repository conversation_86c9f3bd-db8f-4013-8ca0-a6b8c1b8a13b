// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_HeroLevel
{
    /** 序号 */
    public id: number = 0;
    /** 英雄标识 */
    public heroIdentity: number = 0;
    /** 等级 */
    public level: number = 0;
    /** 下一级序号 */
    public nextId: number = 0;
    /** 消耗本体数量 */
    public costBody: number = 0;
    /** 除本体外的消耗 */
    public costMaterial: string = "";
    /** 是不是突破升级类型 */
    public breakLevel: number = 0;
    /** 技能 */
    public skills: string = "";
    /** 全局属性 */
    public globalAttribute: string = "";
    /** 升级属性 */
    public levelAttribute: string = "";
    /** 阶段描述 */
    public phaseDesc: string = "";
    /** 阶段描述 */
    public phaseDescLang: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_HeroLevel[] = [];
    static filePath: string = "gamecfg/Y英雄等级表_HeroLevel";
    
    public static get list(): CFG_HeroLevel[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_HeroLevel {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_HeroLevel, this.filePath);
    }
}