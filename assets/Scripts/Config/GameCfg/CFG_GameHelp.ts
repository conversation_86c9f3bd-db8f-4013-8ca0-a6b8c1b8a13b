// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_GameHelp
{
    /** 属性ID */
    public id: number = 0;
    /** 键名 */
    public keyCol: string = "";
    /** 帮助说明 */
    public describe: string = "";
    /** 帮助说明 */
    public describeLang: string = "";
    /** 备注 */
    public title: string = "";
    /** 备注 */
    public titleLang: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_GameHelp[] = [];
    static filePath: string = "gamecfg/B帮助说明_GameHelp";
    
    public static get list(): CFG_GameHelp[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_GameHelp {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_GameHelp, this.filePath);
    }
}