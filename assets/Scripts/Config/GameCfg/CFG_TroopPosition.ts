// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_TroopPosition
{
    /** 站位数量 */
    public id: number = 0;
    /** 角色等级 */
    public level: number = 0;
    /** 开服天数 */
    public openDay: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_TroopPosition[] = [];
    static filePath: string = "gamecfg/D队伍站位解锁表_TroopPosition";
    
    public static get list(): CFG_TroopPosition[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_TroopPosition {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_TroopPosition, this.filePath);
    }
}