// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_NpcModel
{
    /** npc模型ID */
    public id: number = 0;
    /** npc名字 */
    public name: string = "";
    /** npc名字 */
    public nameLang: string = "";
    /** 形象模型 */
    public model: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_NpcModel[] = [];
    static filePath: string = "gamecfg/NNpc模型表_NpcModel";
    
    public static get list(): CFG_NpcModel[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_NpcModel {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_NpcModel, this.filePath);
    }
}