// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_PurgatoryAttr
{
    /** 序号 */
    public id: number = 0;
    /** 属性库ID */
    public stashId: number = 0;
    /** 属性 */
    public attr: string = "";
    /** 权重 */
    public weight: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_PurgatoryAttr[] = [];
    static filePath: string = "gamecfg/L炼狱属性库_PurgatoryAttr";
    
    public static get list(): CFG_PurgatoryAttr[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_PurgatoryAttr {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_PurgatoryAttr, this.filePath);
    }
}