// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_EquipSuitAttr
{
    /** 序号 */
    public id: number = 0;
    /** 套装ID */
    public suitId: number = 0;
    /** 套装数量 */
    public num: number = 0;
    /** 装备套装属性 */
    public attrs: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_EquipSuitAttr[] = [];
    static filePath: string = "gamecfg/Z装备套装属性表_EquipSuitAttr";
    
    public static get list(): CFG_EquipSuitAttr[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_EquipSuitAttr {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_EquipSuitAttr, this.filePath);
    }
}