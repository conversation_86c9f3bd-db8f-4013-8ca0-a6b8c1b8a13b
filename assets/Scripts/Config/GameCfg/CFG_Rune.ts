// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_Rune
{
    /** 真言ID */
    public id: number = 0;
    /** 真言穿戴部位 */
    public part: number = 0;
    /** 名称 */
    public nameLang: string = "";
    /** 作为强化素材时的经验 */
    public materialExp: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_Rune[] = [];
    static filePath: string = "gamecfg/Z真言表_Rune";
    
    public static get list(): CFG_Rune[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_Rune {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_Rune, this.filePath);
    }
}