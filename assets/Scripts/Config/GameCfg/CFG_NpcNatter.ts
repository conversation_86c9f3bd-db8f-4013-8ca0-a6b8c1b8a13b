// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_NpcNatter
{
    /** 序号 */
    public id: number = 0;
    /** npcId */
    public npcId: number = 0;
    /** 闲聊内容 */
    public words: string = "";
    /** 闲聊内容 */
    public wordsLang: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_NpcNatter[] = [];
    static filePath: string = "gamecfg/NNpc闲聊表_NpcNatter";
    
    public static get list(): CFG_NpcNatter[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_NpcNatter {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_NpcNatter, this.filePath);
    }
}