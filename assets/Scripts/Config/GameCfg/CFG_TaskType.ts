// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_TaskType
{
    /** 序号 */
    public id: number = 0;
    /** 任务类型 */
    public type: string = "";
    /** 任务描述 */
    public describeLang: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_TaskType[] = [];
    static filePath: string = "gamecfg/R任务类型说明表_TaskType";
    
    public static get list(): CFG_TaskType[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_TaskType {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_TaskType, this.filePath);
    }
}