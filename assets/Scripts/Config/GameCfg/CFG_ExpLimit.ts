// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_ExpLimit
{
    /** 序号 */
    public id: number = 0;
    /** 最小限制 */
    public limitMin: number = 0;
    /** 最大限制 */
    public limitMax: number = 0;
    /** 经验加成 */
    public expAdd: number = 0;
    /** 名字 */
    public name: string = "";
    /** 名字 */
    public nameLang: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_ExpLimit[] = [];
    static filePath: string = "gamecfg/J角色经验获取限制_ExpLimit";
    
    public static get list(): CFG_ExpLimit[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_ExpLimit {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_ExpLimit, this.filePath);
    }
}