// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_PurgatoryRefine
{
    /** 洗练次数 */
    public times: number = 0;
    /** 通关消耗 */
    public passCost: string = "";
    /** 消耗 */
    public cost: string = "";

    getId(): number | string {
        return this.times;
    }

    static _list: CFG_PurgatoryRefine[] = [];
    static filePath: string = "gamecfg/L炼狱洗练消耗_PurgatoryRefine";
    
    public static get list(): CFG_PurgatoryRefine[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(times: number): CFG_PurgatoryRefine {
        for (let it of this.list) {
            if (it.times == times) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_PurgatoryRefine, this.filePath);
    }
}