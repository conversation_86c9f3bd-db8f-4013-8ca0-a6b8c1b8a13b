// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_ZhangLingNormal
{
    /** 战令ID */
    public id: number = 0;
    /** 对应领取奖励ID */
    public rechargeId: number = 0;
    /**  ZhanLingExpID */
    public expZLID: number = 0;
    /** 框的背景 */
    public itemBg: string = "";
    /** 奖励框的背景 */
    public itemlistBg: string = "";
    /** 是否开启1开启2隐藏 */
    public active: number = 0;
    /** 打开视图的名称 */
    public openModeView: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_ZhangLingNormal[] = [];
    static filePath: string = "gamecfg/Z普通战令表_ZhangLingNormal";
    
    public static get list(): CFG_ZhangLingNormal[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_ZhangLingNormal {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_ZhangLingNormal, this.filePath);
    }
}