// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_Tower
{
    /** 序号 */
    public id: number = 0;
    /** 塔类型 */
    public type: number = 0;
    /** 层数 */
    public layer: number = 0;
    /** 通关奖励 */
    public rewards: string = "";
    /** 怪物 */
    public monster: string = "";
    /** 刷怪点类型（0固定 1随机） */
    public refreshType: number = 0;
    /** 展示模型 */
    public modelShow: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_Tower[] = [];
    static filePath: string = "gamecfg/T通天塔_Tower";
    
    public static get list(): CFG_Tower[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_Tower {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_Tower, this.filePath);
    }
}