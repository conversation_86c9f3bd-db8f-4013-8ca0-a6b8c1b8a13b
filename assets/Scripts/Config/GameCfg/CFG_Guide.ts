// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_Guide
{
    /** 序号 */
    public id: number = 0;
    /** 引导组ID */
    public group: number = 0;
    /** 步骤 */
    public subId: number = 0;
    /** 触发指引条件 */
    public task_id: string = "";
    /** 引导类型 */
    public type: number = 0;
    /** 目标参数 */
    public targetType: string = "";
    /** 目标ID */
    public targetId: string = "";
    /** 控件名字 */
    public targetName: string = "";
    /** 控件所在UI */
    public targetUI: string = "";
    /** 动画所在路径 */
    public moviePath: string = "";
    /** 单步触发条件 */
    public condition: number = 0;
    /** 条件参数 */
    public conditionParam: string = "";
    /** 对话ID */
    public dialogue: number = 0;
    /** 手指方向 */
    public fingerDiction: number = 0;
    /** 指引文本框方向 */
    public diction: string = "";
    /** 气泡文本 */
    public bubbleTxt: string = "";
    /** 气泡文本 */
    public bubbleTxtLang: string = "";
    /** 指引文本 */
    public guideTxt: string = "";
    /** 指引文本 */
    public guideTxtLang: string = "";
    /** 是否聚焦 */
    public focus: number = 0;
    /** 是否强指引 */
    public force: number = 0;
    /** 断链处理方法 */
    public repeat: string = "";
    /** 备注 */
    public beizhu: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_Guide[] = [];
    static filePath: string = "gamecfg/X引导配置表_Guide";
    
    public static get list(): CFG_Guide[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_Guide {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_Guide, this.filePath);
    }
}