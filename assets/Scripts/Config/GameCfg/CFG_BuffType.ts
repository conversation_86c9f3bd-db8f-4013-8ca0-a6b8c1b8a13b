// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_BuffType
{
    /** 序号 */
    public id: number = 0;
    /** 名字 */
    public name: string = "";
    /** 枚举名 */
    public enumName: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_BuffType[] = [];
    static filePath: string = "gamecfg/BBuff类型说明表_BuffType";
    
    public static get list(): CFG_BuffType[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_BuffType {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_BuffType, this.filePath);
    }
}