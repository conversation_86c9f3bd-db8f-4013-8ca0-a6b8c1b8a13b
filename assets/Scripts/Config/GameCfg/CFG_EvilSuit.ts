// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_EvilSuit
{
    /** 序号 */
    public id: number = 0;
    /** 套装Id */
    public suitId: number = 0;
    /** 套装数量 */
    public suitNum: number = 0;
    /** 装备套装属性 */
    public attrs: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_EvilSuit[] = [];
    static filePath: string = "gamecfg/Y妖傀套装_EvilSuit";
    
    public static get list(): CFG_EvilSuit[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_EvilSuit {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_EvilSuit, this.filePath);
    }
}