// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_HomeStoreHouse
{
    /** 等级 */
    public id: number = 0;
    /** 肉容量 */
    public meat: string = "";
    /** 木容量 */
    public wood: string = "";
    /** 矿容量 */
    public mine: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_HomeStoreHouse[] = [];
    static filePath: string = "gamecfg/J家园仓库容量表_HomeStoreHouse";
    
    public static get list(): CFG_HomeStoreHouse[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_HomeStoreHouse {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_HomeStoreHouse, this.filePath);
    }
}