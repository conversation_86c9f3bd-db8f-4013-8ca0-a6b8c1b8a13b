// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_PrivilegeNormal
{
    /** 特权ID */
    public id: number = 0;
    /** 充值商品表ID */
    public rechargeId: number = 0;
    /** 持续天数 */
    public days: number = 0;
    /** 是否开启 */
    public active: number = 0;
    /** 特权描述 */
    public describe: string = "";
    /** 特权描述 */
    public describeLang: string = "";
    /** 充值奖励 */
    public rechargeRewards: string = "";
    /** 每日奖励 */
    public dailyRewards: string = "";
    /** 充值自选奖励[废弃] */
    public chooseRewards: string = "";
    /** 框的背景 */
    public itemBg: string = "";
    /** 奖励框的背景 */
    public itemlistBg: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_PrivilegeNormal[] = [];
    static filePath: string = "gamecfg/P普通特权表_PrivilegeNormal";
    
    public static get list(): CFG_PrivilegeNormal[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_PrivilegeNormal {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_PrivilegeNormal, this.filePath);
    }
}