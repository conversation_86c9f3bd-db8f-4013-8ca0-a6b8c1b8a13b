// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_HeroType
{
    /** 标识 */
    public id: number = 0;
    /** 名称 */
    public name: string = "";
    /** 名称 */
    public nameLang: string = "";
    /** 种族类型 */
    public element: number = 0;
    /** 职业类型 */
    public career: string = "";
    /** 品质 */
    public quality: number = 0;
    /** 英雄头像 */
    public head: string = "";
    /** 立绘资源 */
    public inset: number = 0;
    /** 头像资源 */
    public portrait: string = "";
    /** 模型资源id */
    public modelId: number = 0;
    /** 放大系数 */
    public scale: number = 0;
    /** 体积半径 */
    public volume: number = 0;
    /** 界面展示立绘缩放 */
    public imageScale: number = 0;
    /** 界面展示立绘高度偏移 */
    public imageOffH: number = 0;
    /** 界面展示立绘左右偏移 */
    public imageOffR: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_HeroType[] = [];
    static filePath: string = "gamecfg/Y英雄类型表_HeroType";
    
    public static get list(): CFG_HeroType[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_HeroType {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_HeroType, this.filePath);
    }
}