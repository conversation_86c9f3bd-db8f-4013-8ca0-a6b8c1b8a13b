// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_BattleConst
{
    /** 属性ID */
    public id: number = 0;
    /** 键名 */
    public keyCol: string = "";
    /** 值类型 */
    public typeCol: string = "";
    /** 值 */
    public valueCol: string = "";

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_BattleConst[] = [];
    static filePath: string = "gamecfg/3常量表_BattleConst";
    
    public static get list(): CFG_BattleConst[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_BattleConst {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_BattleConst, this.filePath);
    }
}