// !!! 此文件为自动生成，手动修改无法保存
import * as CFGUtils from "../CFGUtils";

export default class CFG_GiftCode
{
    /** 兑换码礼包ID */
    public id: number = 0;

    getId(): number | string {
        return this.id;
    }

    static _list: CFG_GiftCode[] = [];
    static filePath: string = "gamecfg/D兑换码礼包表_GiftCode";
    
    public static get list(): CFG_GiftCode[] {
        if (this._list == null || this._list.length == 0) {
            this.reload();
        }
        return this._list;
    }


    static get(id: number): CFG_GiftCode {
        for (let it of this.list) {
            if (it.id == id) {
                return it;
            }
        }
        return null;
    }

    static reload(): void {
        this._list = CFGUtils.reloadCfg(CFG_GiftCode, this.filePath);
    }
}