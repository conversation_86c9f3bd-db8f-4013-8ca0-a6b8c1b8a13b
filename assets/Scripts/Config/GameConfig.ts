export class GameConfig {

    public biuAppId: string = "21c7e1d8-2bb2-438a-a357-31fd5a1eca19";

    //发布外网请将此两项设置为0
    /**本地测试环境设为 1 */
    public test: string = "1";
    /**调试环境设为 1 */
    public debug: string = "1";

    /** 游戏帧率 */
    public readonly frameRate: number = 30;
    /** 游戏帧时间 */
    public readonly frameTime: number = 0.03333333333333333;

    /**
     * 游戏设计分辨率
     */
    public readonly initWidth: number = 768;

    public readonly initHeight: number = 1366;

    /** 渠道 1.外网mobile  2.外网微信*/
    public channelId: number = 2;

    /** 用户id */
    public openId: string = "AAA";

    /**服务器地址 */
    public serverUrl: string = "ws://192.168.1.5:10004"; // 昱源

    /**服务器Id */
    public serverID: number = 4;
    public serverName: string = "";
    public serverStatus: number = 0;

    /** 远程资源CDN地址 */
    public remoteUrl = "";

    /** 远程资源bundle版本号 */
    public resourceMd5 = "";

    /** 远程资源bundle */
    public resourceBundle = "res";

    /** API 调用的地址 */
    public apiUrl = "";

    /**游戏的版本号 */
    public gameVersion = 0;
    /** socket 前置 */
    public socketPre = ""

    public online: boolean = true;

    public openGm: boolean = false;
    constructor() { }

    /** 获取远程Url */
    getRemoteUrl(): string {
        return this.remoteUrl;
    }

    /** 获取参数 */
    getOptions(): any {
        if (this.isTest || !this.resourceMd5 || this.resourceMd5.length == 0) {
            return null;
        }
        return { "version": this.resourceMd5 };
    }

    /** 上报错误地址 */
    getErrorReportUrl(): string {
        return this.apiUrl + "/api/clientLog";
    }

    /** 37API地址 */
    get37SdkUrl(): string {
        return this.apiUrl + "/api/37wan/checkLogin";
    }

    /** 服务器列表 */
    getServerListUrl(regionId: any): string {
        return this.apiUrl + "/api/serverInRegion?regionId=" + regionId;
    }

    /** 获取当前区的服务器数据 */
    getRegionListURL(openId: string = "AAA", channel: number = 1, gameVersion: number = 0): string {
        return this.apiUrl + "/api/serversList?channelId=" + channel + "&openId=" + openId + "&versionId=" + gameVersion; //请求大区路径（channelId和openId日后改成配置）
    }

    /**测试环境 */
    get isTest(): boolean {
        return this.test === "1";
    }

    /**Debug环境 */
    get isDebug(): boolean {
        return this.debug === "1";
    }

    /**
     * 初始化参数
     */
    mergeParams(gameParams: Record<string, any>) {
        for (let key in gameParams) {
            if (this[key] !== undefined) {
                this[key] = gameParams[key];
            }
        }
    }
}

//导出全局唯一配置.
export const AppConfig = new GameConfig();


