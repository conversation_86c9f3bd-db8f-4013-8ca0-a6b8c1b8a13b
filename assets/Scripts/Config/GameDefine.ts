import * as cc from "cc";

/**
 * 资源名字
 */
export let ResourcesName: Object = {
    1: "金币",
    2: "钻石",
    3: "角色经验",
    4: "木材",
    5: "肉",
    6: "矿石",
};

/**
 * npc 类型
 */
export enum MapNpcType {
    /** 回归传送 */
    TransferDoor_Return = 10,
    /** 酒馆 */
    Pub = 100,
    /** 家园-伐木场 */
    HomeLumberyard = 200,
    /** 家园-矿场 */
    HomePsychicPulse = 201,
    /** 家园-金币场 */
    HomeMintShop = 202,
    /** 家园-仓库 */
    HomeStoreHouse = 300,
}

/**
 * 战斗属性枚举
 * (枚举名参照属性表定义)
 */
export enum BattleAttr {
    hp = 201,//当前血量
    maxHp = 202,//生命
    atk = 203,//攻击
    def = 204,//防御
    hpRate = 205,//血量加成
    atkRate = 206,//攻击加成
    defRate = 207,//防御加成
    dmgRate = 208,//伤害加成
    hit = 209,//命中
    dodge = 210,//闪避
    crit = 211,//暴击
    critDmgRate = 212,//暴击伤害
    vampireRate = 213,//吸血
    reduceDmg = 214,//减伤固定值
    reduceDmgRate = 215,//伤害减免
    cdSpdRate = 216,//冷却时间
    moveSpd = 217,//移动速度
    moveSpdRate = 218,//移动速度加成
    atkSpd = 219,//攻击速度
    atkSpdRate = 220,//攻击速度加成
    dmgbl = 221,//技能伤害加成
    dmg = 222,//增伤固定值
    llmaxRate = 223,//人族生命加成
    lldmgRate = 224,//人族伤害加成
    mjmaxRate = 225,//灵族生命加成
    mjdmgRate = 226,//灵族伤害加成
    zlmaxRate = 227,//仙族生命加成
    zldmgRate = 228,//仙族伤害加成
    woodDmg = 229,//伐木伤害
    mineDmg = 230,//挖矿伤害
    llmax = 231,//人族伙伴生命固定值
    lldmg = 232,//人族伙伴伤害固定值
    mjmax = 233,//灵族伙伴生命固定值
    mjdmg = 234,//灵族伙伴伤害固定值
    zlmax = 235,//仙族伙伴生命固定值
    zldmg = 236,//仙族伙伴伤害固定值
    battleRange = 237,//进战范围
    llpvedmg = 238,//人族伙伴对怪物伤害加成
    mjpvedmg = 239,//灵族伙伴对怪物伤害加成
    zlpvedmg = 240,//仙族伙伴对怪物伤害加成
    pvedmg = 241,//对怪物伤害加成
    fovRange = 242,//视野范围
    reducecrit = 243,//暴击抵抗
}

export enum CharacterType {
    /** 无 */
    None = 0,
    /** 英雄 */
    Hero = 1,
    /** 怪物 */
    Monster = 2,
    /** NPC */
    NPC = 3,
}

/** 碰撞分组 */
export enum GameLayer {
    /** 无 */
    defalut = 6,
    /** 英雄 */
    player = 10,
    /** 怪物 */
    monster = 11,
    /** 子弹 */
    bullet = 12,
    /** 墙 */
    wall = 13,
}

/**
 * 设置单位方向 为 0-7， 0是下，1是左下，2是左，3是左上，4是上，5是右上，6是右，7是右下
 *
 *        4
 *      3   5
 *    2   *   6
 *      1   7
 *        0
 *
 */
export enum MoveDir {
    down = 0,
    left_down = 1,
    left = 2,
    left_up = 3,
    up = 4,
    right_up = 5,
    right = 6,
    right_down = 7,
}

/** unit类型 */
export enum UnitType {
    /** 无 */
    None,
    Hero,
    Npc,
    Monster,
    Tree,
    Building,
}

/**
 * 动作名
 */
export enum UnitAnim {
    /**
     * 待机
     */
    idle = "idle",
    /**
     * 行走
     */
    walk = "walk",
    /**
     * 攻击
     */
    attack = "attack",
    /**
     * 死亡
     */
    dead = "die",

    /**
     * 技能1
     */
    skill01 = "skill01",

    /**
     * 技能2
     */
    skill02 = "skill02",

    /**
     * 砍树
     */
    cuttrees = "cuttrees",

    /**
     * 挖矿
     */
    mine = "mine",
}

/** 地下城类型  */
export enum DungeonType {
    /** 世界秘境 地图NPC进入*/
    World = 2,
    /** 日常秘境 界面进入 */
    Daily = 1,
}

export class UIColor {
    static GREEN: cc.Color = cc.color(15, 255, 47, 255);
    static RED: cc.Color = cc.color(255, 90, 91, 255);
    static WHITE: cc.Color = cc.Color.WHITE;
}

/** 回城按钮CD时间 */
export const TOWN_PORTAL_COOL_DOWN = 3;
/** 追击走路的容错 比例*/
export const CHASE_WALK_RATE = 1;
/** 万数值 */
export const WAN_NUMBER = 10000;
/** 万分比比例 */
export const WAN_RATE = 0.0001;

