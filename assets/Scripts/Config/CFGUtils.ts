import AppManager from "../Game/Managers/AppManager";

export function reloadCfg(cfgClass: { new(): any }, filePath): any {
    let dataList = AppManager.instance().ResManager.readConfigFile(filePath, true);
    if (dataList == null) {
        console.error("Not defind filePath:" + filePath);
        return;
    }

    if (Array.isArray(dataList)) {
        let list = [];
        for (let obj of dataList) {
            let s = new cfgClass();
            for (let key in s) {
                if (typeof s[key] == "function") {
                    continue;
                }
                s[key] = obj[key];
            }
            list.push(s);
        }
        return list;
    } else {
        let s = new cfgClass();
        for (let key in s) {
            if (typeof s[key] == "function") {
                continue;
            }
            s[key] = dataList[key];
        }

        return s;
    }
}

export type reward = {
    id: number;
    count: number;
}

export type randItem = {
    rand: number;
    id: number;
    count: number;
}

export type monster = {
    /** 刷怪点 */
    pos: number;
    /** 怪物id */
    id: number;
    /** 怪物数量 */
    num: number;
}

/**
 * 属性加成
 */
export type AttributeAdd = {
    /** 属性id */
    id: number,
    /** 加成比例(万分比) */
    addPc: number
}