export enum ViewId {

    /////////////////////////////////////////////////////////资源已调整目录未打包图集的UI页面/////////////////////////////////////////////////////////////////////////////////////
    /** gm  */
    GM = "ui/gm/GM",
    /** 主界面 */
    MAINUI = "ui/mainUI/mainUI",
    /** 离线提示  */
    OffLineUI = "ui/offline/offlineUI",
    /** UI加载等待界面 */
    UIWaiting = "ui/offline/UIWaiting",
    /** 地图加载页 */
    MAP_LOADING = "ui/map/MapLoading_zong",
    /** 恭喜获得 */
    REWARD = "ui/reward/rewardView",
    /** 礼包页 */
    GIFT = "ui/bag/giftView",
    /** 背包 */
    BAG_VIEW = "ui/bag/bagUI",
    /** 背包礼包打开 */
    BAG_GIFT_OPEN = "ui/bag/BagGiftOpen",
    /** 礼包——道具选择页 */
    GIFT_SELECT = "ui/bag/BagGiftSelect",
    /** 挑战入口 */
    CHALLENGE_ENTER = "ui/challenge/challenge",
    /** 试炼副本入口 */
    TOWER_DUP_ENTER = "ui/challenge/towerEnter",
    /** 塔信息界面 */
    TOWER_INFO = "ui/challenge/towerInfo",
    /** 通天塔倒计时 */
    TOWER_CD_TIME_VIEW = "ui/challenge/towerLayer",
    /** 副本入口界面 */
    DAILY_DUP_ENTER = "ui/dailyDup/dailyDupEnter",
    /** 副本层数 */
    DAILY_DUP_LAYER = "ui/dailyDup/dailyDupLayer",
    /** 副本援军 */
    DAILY_DUP_AID = "ui/dailyDup/dailyDupAid",
    /** 日常周常任务界面 */
    DAILYTASK_VIEW = "ui/dailyweekly/DailyUiTaskView",
    /** 邮箱界面 */
    EMAIL_VIEW = "ui/email/emailUI",
    /** 邮件详情界面 */
    EMAILINFO_VIEW = "ui/email/emailDetail",
    /** 兑换商店主界面 */
    EXCHANGE_MAIN = "ui/exchangeshop/exchangeUiMainView",
    /** 兑换界面 */
    EXCHANGE = "ui/exchangeshop/exchangeUiView",
    /**兑换商店提示页面 */
    MsgBox = "ui/exchangeshop/msgBox",
    /** 功能开启界面 */
    MODULE_FUN_OPEN = "ui/moduleFunOpen/moduleFunOpen",
    /** 英雄复活 */
    GameOver_Revive_VIEW = "ui/gameOver/gameOverRevive",
    /** 胜利 */
    GAME_WIN = "ui/gameOver/gameWin",
    /** 战斗结束 */
    GameOver_VIEW = "ui/gameOver/gameOver",
    /** 黄金洞窟副本 */
    GOLDEN_CAVE = "ui/goldenCave/GoldenCaveView",
    /** 黄金洞窟副本战斗ui界面 */
    GOLDEN_CAVE_BATTLE_UI = "ui/goldenCave/GoldenCaveBattleUI",
    /** 英雄上阵 */
    HeroArray_VIEW = "ui/heroEmbattle/heroArrayUI",
    /** 英雄技能详情 */
    HERO_SKILL_VIEW = "ui/hero/skillInfo",
    /** 英雄羁绊界面 */
    BOND_VIEW = "ui/hero/bondView",
    /** 伐木场、矿场、钱庄 */
    HOME_RESOURCE_VIEW = "ui/homeResource/homeResourceUI",


    /** 酒馆界面 */
    PUB_VIEW = "ui/pub/pubUI",
    /**酒馆帮助详情页面 */
    HELP_VIEW = "ui/pub/helpTips",
    /** 酒馆刷新 */
    PubFlash_VIEW = "ui/pub/flushUI",
    /** 酒馆刷新提示 */
    PubFlashTips_VIEW = "ui/pub/flushTipsUI",
    /** 神坛界面 */
    TRAINING_VIEW = "ui/training/trainingView",
    /** 炼妖页面 */
    REFINE = "ui/refine/refineView",
    /**炼妖-选择妖魂页面 */
    REFINE_SELECT = "ui/refine/refineSelectView",
    /**山海妖录页面 */
    REFINE_BOOK = "ui/refine/refineBookView",
    /**山海妖录获得详情页面 */
    REFINE_BOOK_INFO = "ui/refine/refineBookInfoView",
    /** 世界地图 */
    WorldMap_VIew = "ui/worldMap/worldMapUI",
    /** 目标血条 */
    TARGET_HP_BAR = "ui/TargetHpBar",
    /** 宝箱详情页面 */
    TreasureBoxInfo = "ui/treasureBox/TreasureBoxInfo",
    /** 闪白 */
    WHITE_FLASH_VIEW = "ui/whiteFlash/whiteFlash",
    /** 公告 */
    GONG_GAO = "ui/login/gonggao",

    /////////////////////////////////////////////////////////资源已经替换成图集的UI放下面/////////////////////////////////////////////////////////////////////////////////////
    /** 适龄提醒界面 */
    AGE_VIEW = "ui/login/ageTips",
    /** 忠告 */
    ADVISE = "ui/login/advise",
    /** 适龄提示 */
    AGE_TIPS = "ui/login/ageTips",
    /** 加载中 */
    LOADING = "ui/login/loading",
    /** 登录 */
    LOGIN = "ui/login/login",
    /** 选择服务器 */
    SELECT_SERVER = "ui/login/selectServer",

    /** 传送阵界面 */
    TRANSFER_VIEW = "ui/transfer/transfer",
    /**获得新英雄界面 */
    HERO_GAIN_VIEW = "ui/heroGain/heroGain",
    /**剧情对话 */
    PLOT_VIEW = "ui/guide/plot",
    /** 引导提示 */
    GUIDE_TIPS = "ui/guide/guideTips",
    /** 引导黑色遮罩  */
    GUIDE_MASK = "ui/guide/guideMask",
    /** 珍宝阁界面 */
    ZHENBAOGE_VIEW = "ui/zhenbaoge/ZbgUiMain",
    /** 英雄页面 */
    HEROMANAGE_VIEW = "ui/hero/heroManageUI",
    /** 英雄详情信息页面 */
    HERO_Detail_VIEW = "ui/hero/heroDetailUI",
    /** 援军进入界面 */
    DUP_AID_ENTER_VIEW = "ui/duplicate/dupAidEnter",
    /** 神像装备界面 */
    EQUIPMENT_VIEW = "ui/equipment/equipmentView",
    /**装备信息详情页面 */
    EQUIPMENT_INFO_VIEW = "ui/equipment/equipmentInfoView",
    /**装备批量分解页面 */
    EQUIPMENT_DESTROY_VIEW = "ui/equipment/equipmentDestroyView",
    /**背包分解装备页面 */
    EQUIPMENT_INFO_BAG_VIEW = "ui/equipment/equipmentInfoBagView",
    /** 首充界面 */
    FIRST_RECHARGE_VIEW = "ui/firstrecharge/FirstRechargeUiMainView",
    /** 设置界面 */
    SETTING_VIEW = "ui/setting/settingUI",
    /** 修改姓名界面 */
    CHANGENAME_VIEW = "ui/setting/changeNameUI",
    /**兑换码页面 */
    CDKEY_BOX_VIEW = "ui/setting/cdkeyBoxUI",
    /**限时礼包页面 */
    TIME_LIMIT_PACK_VIEW = "ui/timeLimitPack/timeLimitPackView",
    /**wx群页面 */
    WX_QUN_VIEW = "ui/sdkUI/wxQunView",
    /**调查问卷页面*/
    SURVEY_VIEW = "ui/sdkUI/surveyView",
    /**登录豪礼页面*/
    Login_REWARD_VIEW = "ui/loginSign/loginRewardView",
    /**提示界面 */
    MsgBoxEx = "ui/offline/MsgBoxEx",
    /**修行主界面 */
    EnCampMainView = "ui/enCamp/enCampTime",
    /**修行获得奖励 */
    EnCampAwardView = "ui/enCamp/campAward",
    /**高级修行展示 */
    EnCampAdvInfoView = "ui/enCamp/campAdvInfo",
    /**快速购买提示框 */
    QuickBuyMsgBox = "ui/tips/QuickBuyMsgBox",

    /** 排行榜主面板  */
    RANK_MAIN_VIEW = "ui/rank/rankMain",
    /** 排行榜奖励  */
    RANK_AWARD_VIEW = "ui/rank/rankAward",
    /**活动页面-七日目标 */
    ActivityUiMain = "ui/sevenDayGoal/ActivityUiMain",

    /** 杀怪上限  */
    LimitExpMain = "ui/limitExp/limitMain",
    /**英雄流派界面 */

    HeroTacticsPanel = "ui/tactics/tacticsView",

    PrivilegeNormalView = "ui/privilegeNormal/privilegeNormalMain",
    OpenServerRankView = "ui/openServerRank/openServerView",
    OpenServerRankAwardView = "ui/openServerRank/openServerRankAward",
    ActivieMainView = "ui/active/ActivieMainView", 
    RechargeDiscountView = "ui/rechargeDiscount/rechargeDiscountView",
    AccumulateRechargeView = "ui/accumulateRecharge/accumulateRechargeView",
    OpenServerGoalView = "ui/openServerGoal/openServerGoalView",
    UpgradeView = "ui/upgrade/upgradeView",
    SignView = "ui/loginSign/SignView",
    VipMainView = "ui/vip/VipMainView",
    /** 首充68界面 */
    FIRST_RECHARGE_68_VIEW = "ui/firstrecharge/FirstRecharge68UiMainView",
    /** 首充技能预览界面 */
    SKILL_SHOW_VIEW = "ui/firstrecharge/SkillShowView",



    /**福利界面 */
    
    HWelfarePanel = "ui/welfare/WelfareMainView",
    //特权
    PrivilegeView = "ui/welfare/privilege/PrivilegeView",
    PrivilegeItemView = "ui/welfare/privilege/PrivilegeItem",

    //战令
    WarOrderView = "ui/welfare/warOrder/WarOrderView",
    WarOrderItemView = "ui/welfare/warOrder/WarOrderItem",
    /**活跃战令**/
    HuoYueZLView = "ui/welfare/warOrder/HuoYueZLView",
    HuoYueZLInfo = "ui/welfare/warOrder/HuoYueZLInfo",
    

    
    /** 英雄突破觉醒战力提升面板 */
    HREO_UPPROWER_VIEW = "ui/hero/heroUpAttr",
    /** 觉醒道具选择面板 */
    WAKEN_ITEMSELECT_VIEW = "ui/hero/wakenItemSelect",
    /** 道具tips */
    ITEM_TIPS_VIEW = "ui/bag/tip",
    /** 获取途径tips */
    GET_TIPS_VIEW = "ui/bag/getTips",

    /** 聊天界面 */
    CHAT_VIEW = "ui/chat/chatMainView",

    /** 地图加载前 */
    LOADING_HEIMU = "ui/map/LoadingHeimu1",

    /** 游戏开场动画 */
    GAME_OPEN = "ui/map/open/GameOpen",

    /** 图鉴 */
    HANDBOOK_VIEW = "ui/handBook/HandBookView",
    /** 图鉴奖励 */
    HANDBOOK_REWARD = "ui/handBook/HandBookRewardView",
    /** 图鉴升级 */
    HANDBOOK_UPLEVEL = "ui/handBook/HandBookUpLevel",
    /** 图鉴总属性 */
    HANDBOOK_ATTR = "ui/handBook/HandBookAttr",

    /**竞技场主界面 */
    ARENAMAIN_VIEW = "ui/Arena/ArenaMain",
    /**竞技场奖励界面 */
    ARENA_REWARD_VIEW = "ui/Arena/ArenaAward",
    /**竞技场阵容界面 */
    ARENA_ARRAY_VIEW = "ui/Arena/arenaArrayUI",
    /**竞技场战报界面 */
    ARENA_WAR_VIEW = "ui/Arena/ArenaWarReport",
    /**竞技场副本场景界面 */
    ARENA_SCENE_VIEW = "ui/Arena/ArenaSceneLayer",
}

export enum ViewZIndex {
    MAX_ZINDEX = 65535,
}
