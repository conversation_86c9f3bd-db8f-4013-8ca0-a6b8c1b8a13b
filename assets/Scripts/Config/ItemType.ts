import CFG_Item from "./GameCfg/CFG_Item";
import ItemCache from "./GameCfgExt/ItemCache";

/**
 * 道具类型
 */
export class ItemType {


    /** 1 金币 */
    static readonly Gold = 1;
    /** 2 钻石 */
    static readonly Diamond = 2;
    /** 3 经验 */
    static readonly Exp = 3;
    /** 4 木材 */
    static readonly Wood = 4;
    /** 5 肉类 */
    static readonly Meat = 5;
    /** 6 矿石 */
    static readonly Ore = 6;
    /** 7 VIP经验 */
    static readonly VIP = 7;
    /** 8 招募积分 */
    static readonly Recruit = 8;
    /** 9 仙境积分 */
    static readonly FAIRYLAND_SCORE = 9;
    /** 10 炼狱积分 */
    static readonly HELL_SCORE = 10;
    /** 80 通行证积分 */
    static readonly GATE_PASS_SCORE = 80;
    /** 100 英雄 */
    static readonly HERO = 100;
    /** 200 道具 */
    static readonly ITEM = 200;
    /** 300 装备 */
    static readonly EQUIP = 300;
    /** 400 妖 */
    static readonly GENIE = 400;
    /** 500 自动开箱盒子 */
    static readonly AUTO_OPEN_BOX = 500;
    /** 600 炼妖加速卡 */
    static readonly REFINE_SPEED_CARD = 600;

    /**是否为资源 */
    static isRes(type: number): boolean {
        return type > 0 && type <= ItemType.Ore;
    }

    /**是否为属性资源 */
    static isAttRes(type: number): boolean {
        return type >= ItemType.Recruit && type < ItemType.HERO;
    }

    /**
     * 通过identity获取道具类型
     * @param identity 
     * @returns 
     */
    static getType(identity: number): number {
        let itemCfg: CFG_Item = ItemCache.getItem(identity);
        if (!itemCfg) {
            return null;
        }
        return itemCfg.kind;
    }

    /**
     * 通过identity判断道具类型
     * @param identity 
     * @param itemType 
     * @returns 
     */
    static isType(identity: number, itemType: number) {
        let kind = this.getType(identity);
        if (kind == null) {
            return false;
        }
        return kind == itemType;
    }
}

export class ItemUseType{
    /** 普通礼包[固定，随机，随机+固定，等级]  */
    public static randomGift: number = 1;
    /** 自选礼包  */
    public static choiceGift: number = 2; 
}

