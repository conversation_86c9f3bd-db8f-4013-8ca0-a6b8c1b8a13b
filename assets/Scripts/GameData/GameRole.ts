import Singleton from "../Framework/Utils/Singleton";
import GameConn from "./GameConn";
import { AppConfig } from "../Config/GameConfig";
import { RoleInfo } from "./Data/RoleInfo";
import  Logger  from "../Framework/Logger";
import { EventDispatcher, LoginEvent } from "../GameEvent/Events";
import pb from "../Proto/pb.js";

export default class GameRole extends Singleton {
    private _roleInfo: RoleInfo = new RoleInfo();

    public constructor() {
        super();
        this._registerProtocol();
    }

    public registerProtocolHandler() {
        let registerMap = [
            {
                protoId: pb.LoginResponse.Proto.ID,
                handler: this._onRoleLogined,
            }
        ]

        registerMap.forEach((item) => {
            GameConn.instance().socket.registerProtocol(
                item.protoId,
                this,
                item.handler,
            )
        });
    }

    public async loginToGameServer() {
        GameConn.instance().connectGameServer(AppConfig.serverUrl);
        // Logger.trace("loginResp", loginResp);
        // this._onRoleLoginSuccess(loginResp);
    }

    private _onRoleLoginSuccess(loginResp: pb.LoginResponse) {
        this._roleInfo.token = "";
        this._roleInfo.name = loginResp.nick;
        this._roleInfo.id = loginResp.pid as number;
        this._roleInfo.level = loginResp.level;
        this._roleInfo.vipLevel = loginResp.vipLevel;
        this._roleInfo.vipExp = loginResp.vipExp as number;
        this._roleInfo.openDay = loginResp.openDay;
        this._roleInfo.createRoleTime = loginResp.createTime as number;

        EventDispatcher.dispatch(LoginEvent.LOGIN_SUCCESS, null);
    }

    private _onRoleLogined(msg: pb.MessageWrapper) {
        console.log("onRoleLogined", msg);
        const loginResp = pb.LoginResponse.decode(msg.data);
        if (loginResp.status === pb.LoginStatus.FORBID) {
            EventDispatcher.dispatch(LoginEvent.LOGIN_BLOCKED, {status: loginResp.status});
            GameConn.instance().dispose();
            return;
        } else if (loginResp.status === pb.LoginStatus.MAINTAIN || loginResp.status === pb.LoginStatus.VIOLATION) {
            EventDispatcher.dispatch(LoginEvent.LOGIN_BLOCKED, {status: loginResp.status});
            GameConn.instance().dispose();
            return;
        }

        this._onRoleLoginSuccess(loginResp);
    }

    private _registerProtocol() {
    }
}

