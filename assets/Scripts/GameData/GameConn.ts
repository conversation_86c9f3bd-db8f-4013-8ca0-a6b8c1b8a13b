import Socket from "../Framework/Network/ws";
import WSWrapper from "../Framework/Network/WSWrapper";
import Player from "./Player";
import {AppConfig} from "../Config/GameConfig"

import {
    LoginRequest,
    LoginResponse,
} from "../Proto/pb.js";

import Singleton from "../Framework/Utils/Singleton";

export default class GameConn extends Singleton {
    private _socket: WSWrapper | null = null;

    public constructor() {
        super();
        this._socket = new WSWrapper(new Socket({
            name: "gameServer",
        }));
    }

    public connectGameServer(wsUrl: string) {
        this._socket.start(wsUrl);
        const loginReq = new LoginRequest({
            openId: Player.instance().getOpenId(),
            serverId: AppConfig.serverID,
            deviceType: "",
            deviceId: "1",
            channelId: AppConfig.channelId,
        });
        return this._socket.send(
            LoginRequest.Proto.ID, 
            LoginRequest.encode(loginReq).finish(),
        );
    }

    public dispose() {
        this._socket.stop();
    }

    public get socket() {
        return this._socket;
    }
}