/* 战斗类型枚举 */
export enum BattleType {
    /** 1 主线 */
    MainLine = 1,
    /** 2 试炼之塔(试炼) */
    Tower = 2,
    /** 3 人族之塔(试炼) */
    TowerOfStrength = 3,
    /** 4 灵族之塔(试炼) */
    TowerOfAgility = 4,
    /** 5 仙族之塔(试炼) */
    TowerOfWisdom = 5,
    /** 6 黄金洞窟 */
    GoldenPig = 6,
    /** 7 时空裂缝 */
    Rift = 7,
    /** 8 村庄保卫 */
    VillageDefense = 8,
    /** 9 日常秘境 */
    Dungeon = 9,
    /** 10 竞技场(进攻阵容) */
    ArenaAtt = 10,
    /** 11 竞技场(防守阵容) */
    ArenaDef = 11,
}

/** 挑战副本常量定义  */
export enum TowerType {
    /** 通天塔 */
    SkyTower = 1,
    /** 力量塔 人 */
    HumanTower = 2,
    /** 敏捷塔 灵(敏捷) */
    SpriteTower = 3,
    /** 智力塔 仙(智力) */
    FairyTower = 4
}