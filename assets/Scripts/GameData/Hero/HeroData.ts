import { SkillLv } from "../../Config/GameCfgExt/struct/SkillLv";

export default class HeroData {

    heroIdentity: number = 0;//英雄标识
    heroName: string = "";//英雄姓名
    heroQuality: number = 0;//英雄品质
    heroType: number = 0;//英雄类型
    heroImag: string = "";//英雄图片
    heroCareer: number = 0;//职业类型
    heroInsert: number = 0;//英雄立绘
    heroHead: string = "";//英雄头像

    heroStatus: number = 0; // 0未激活 1 待命   2参战
    heroLevel: number = 0;//英雄等级
    heroSkill: SkillLv[] = [];//英雄技能
    heroMaxHp: number = 0;//基础血量
    heroAtk: number = 0;//基础攻击
    costBody: number = 0;//消耗本体数量    
    fragment: number = 0; // 碎片数量
    fighting: number = 0; // 战力
    awakenLevel: number = 0; //觉醒等级

    /**当前血量 */
    heroHp: number = 0;


    private _panelAttr: any = {};
    /**  英雄面板属性 */
    public get panelAttr(): any {
        return this._panelAttr;
    }
    public set panelAttr(attr: any) {
        this._panelAttr = {};
        for (let key in attr) {
            this._panelAttr[key] = attr[key];
        }
    }

    /**
     * 获取属性值
     * @param type  属性名.  BattleAttr枚举名 
     * @param defaultValue 
     * @returns 
     */
    public getAttr(type: string, defaultValue: number = 0): number {
        return this._panelAttr[type] || defaultValue;
    }
    /**设置英雄状态 */
    public setHeroStatus(status: number) {
        this.heroStatus = status;
    }
    /**获取英雄状态 */
    public getHeroStatus(): number {
        return this.heroStatus;
    }
}
