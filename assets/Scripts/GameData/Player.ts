/**
 * 玩家信息
 * 这个主要处理 SDK 和玩家的全局信息
 */

import { createError, FrameErrorCode } from "../Framework/Error";
import { request } from "../Framework/Network/http";
import Logger from "../Framework/Logger"
import { getItemJson, removeItem, setItemJson } from "../Framework/Storage/storage";

import * as biu from "@paygo-biu/minigame-sdk"
import { EventDispatcher, LoginEvent } from "../GameEvent/Events";
import { AppConfig } from "../Config/GameConfig";
import Singleton from "../Framework/Utils/Singleton";

interface PlayerInfo {
    serverId: number;
    nick: string;
    level: number;
    head: string;
}

interface Region {
    id: number;
    name: string;
}

interface GameServer {
    id: number;
    name: string;
    ip: string;
    port: number;
    status: number;
    socketType: string;
}

interface SessionStorage {
    openId: string;
    sessionId: string;
    expires: number;
}

interface ServerListResponse {
    title: string;
    content: string;
    players: PlayerInfo[];
    regions: Region[];
    servers: GameServer[];
}

interface LoginResponse {
    data: {
        openId: string;
        sessionId: string;
        expires: number;
        serverList: ServerListResponse;
    }
}

const PlayerKey = "www.playerss";

export default class Player extends Singleton {
    private _openId: string;
    private _channel: number = 0;
    private _gameVersion: number = 0;
    private _sessionId: string | null = null;
    private _isLogin: boolean = false;
    private _isReLogin: boolean = false;

    // server list
    private _serverList: ServerListResponse | null = null;

    public setChannelAndVersin(channel: number, gameVersion: number) {
        this._channel = channel;
        this._gameVersion = gameVersion;
    }

    public get isLogin(): boolean {
        return this._isLogin;
    }

    public getOpenId(): string {
        return this._openId;
    }
    
    public logout() {
        this._openId = "";
        this._channel = 0;
        this._gameVersion = 0;
        this._sessionId = null;
        this._serverList = null;
        this._isLogin = false;
        removeItem(PlayerKey);
    }

    public get isReLogin(): boolean {
        return this._isReLogin;
    }

    public async sdkLogin() {
        try {
            // 先尝试获取已存在的 session
            const session = this._getSession();
            if (session && session.expires > Date.now()) {
                // session 未过期，直接使用
                this._openId = session.openId;
                this.requestServerList().then(() => {
                    Logger.log("server list updated");
                });
                this._isLogin = true;
                console.log('dispatch server list updated');
                EventDispatcher.dispatch(LoginEvent.SERVER_LIST_UPDATED, null);
                return;
            }

            this._clearSession();
            // session 不存在或已过期，需要重新登录
            await this._fullLogin();
        } catch (error) {
            throw createError(`sdk login error: ${error.message}`, FrameErrorCode.NETWORK_ERROR, error);
        }
    }

    public getServers(): GameServer[] {
        return this._serverList?.servers || [];
    }

    public getPlayers(): PlayerInfo[] {
        return this._serverList?.players || [];
    }

    public getRegionList(): Region[] {
        return this._serverList?.regions || [];
    }

    private async _fullLogin() {
        try {
            const biuLoginResult = await biu.login();
            const response = await request({
                url: `${AppConfig.apiUrl}/api/v1/login`,
                method: "POST",
                body: {
                    authToken: biuLoginResult.authCode,
                    channel: biu.getChannelName(),
                    ChannelNo: this._channel,
                    gameVersion: 0,
                },
            });
            console.log('login response', response);
            if (response.status !== 200) {
                const bodyValue = String(response.body);
                throw createError(`sdk login error, status: ${response.status}`, FrameErrorCode.HTTP_ERROR, new Error(bodyValue));
            }

            const loginResult = JSON.parse(response.body);
            const loginData = loginResult.data as LoginResponse["data"];
            this._openId = loginData.openId;
            const sessionId = loginData.sessionId;
            setItemJson(PlayerKey, {
                openId: this._openId,
                sessionId: sessionId,
                expires: loginData.expires,
            });
            console.log('login data', loginData);
            this._serverList = loginData.serverList;
            console.log('dispatch server list updated');
            this._isLogin = true;
            EventDispatcher.dispatch(LoginEvent.SERVER_LIST_UPDATED, null);
        } catch (error) {
            throw createError(`sdk login error, status: ${error.status}`, FrameErrorCode.NETWORK_ERROR, error);
        }
    }

    public async requestServerList() {
        const response = await request({
            url: `${AppConfig.apiUrl}/api/v1/player/serverList`,
            query: {
                gameVersion: this._gameVersion,
            },
            headers: {
               "X-WWW-SS" : this._sessionId,
            },
            method: "GET",
        });
        if (response.status !== 200) {
            const bodyValue = String(response.body);
            throw createError(`sdk login error, status: ${response.status}`, FrameErrorCode.HTTP_ERROR, new Error(bodyValue));
        }

        const loginData = JSON.parse(response.body).data;
        this._serverList = loginData.serverList;
        EventDispatcher.dispatch(LoginEvent.SERVER_LIST_UPDATED, null);
    }


    private _getSession(): SessionStorage | null {
        const session = getItemJson(PlayerKey);
        if (session) {
            return session;
        }
        return null;
    }
    
    private _clearSession() {
        removeItem(PlayerKey);
    }
}