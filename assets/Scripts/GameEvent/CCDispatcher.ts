import * as cc from "cc";
import { EventDataMap, EventDispatcherImpl , EventDispatcher} from "./Events";

class CCEventDispatcher implements EventDispatcherImpl {
    private _eventTarget = new cc.EventTarget();

    once<K extends keyof EventDataMap>(eventName: K, callback: (data: EventDataMap[K]) => void, thisArgs?: any): void {
        this._eventTarget.once(eventName, callback, thisArgs);
    }
    off<K extends keyof EventDataMap>(eventName: K, callback: (data: EventDataMap[K]) => void): void {
        this._eventTarget.off(eventName, callback);
    }
    targetOff(target: any): void {
        this._eventTarget.targetOff(target);
    }
    emit<K extends keyof EventDataMap>(eventName: K, data: EventDataMap[K]): void {
        this._eventTarget.emit(eventName, data);
    }
    removeAllListeners(eventName: string): void {
        this._eventTarget.removeAll(eventName);
    }
    on<K extends keyof EventDataMap>(eventName: K, callback: (data: EventDataMap[K]) => void, thisArgs?: any): void {
        this._eventTarget.on(eventName, callback, thisArgs);
    }
}

EventDispatcher.setImpl(new CCEventDispatcher());
