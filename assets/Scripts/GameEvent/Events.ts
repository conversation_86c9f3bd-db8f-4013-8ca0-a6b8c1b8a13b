import BaseView from "../Game/Components/BaseView";
import { EventBus } from "../Framework/EventBus/EventBus";
import pb from "../Proto/pb";
import { EffectEventData } from "./EffectEvent";

export const SocketEvent = {
    SOCKET_CONNECTED: "SOCKET_CONNECTED",
    SOCKET_DISCONNECTED: "SOCKET_DISCONNECTED",
    SOCKET_CONNECTING: "SOCKET_CONNECTING",
    SOCKET_RECONNECTING: "SOCKET_RECONNECTING",
    SOCKET_CLOSED: "SOCKET_CLOSED",
} as const;

// Game event constants
export const GameEvent = {
    // Game Control
    GAME_INIT_COMPLETE: "GAME_INIT_COMPLETE",

    // UI Related
    VIEW_SHOW: "VIEW_SHOW",
    VIEW_CLOSE: "VIEW_CLOSE",
    OPEN_VIEW_MAIN_UI_SHOW: "OPEN_VIEW_MAIN_UI_SHOW",
    WAITING_SHOW: "WAITING_SHOW",
    WAITING_HIDE: "WAITING_HIDE",

    // Control
    JOYSTICK_TOUCH: "JOYSTICK_TOUCH",
    SHOW_JOYSTICK: "SHOW_JOYSTICK",
    HIDE_JOYSTICK: "HIDE_JOYSTICK",

    // Actor
    ACTOR_BUFF_CHANGE: "ACTOR_BUFF_CHANGE",
    REMOVE_UNIT: "REMOVE_UNIT",

    // Map/Zone Related
    MapData_Update_Zone: "MapData_Update_Zone",
    ENTER_AREA_SAFE: "ENTER_AREA_SAFE",
    ENTER_AREA_OUTSIDE: "ENTER_AREA_OUTSIDE",

    // Hero/Player Related
    HERO_REBORN_SAFE: "HERO_REBORN_SAFE",
    HERO_REBORN_SITU: "HERO_REBORN_SITU",
    HEROLIST_UPDATE: "HEROLIST_UPDATE",

    // Unit/Combat Related
    UNIT_DIE: "UNIT_DIE",
    BOSS_BE_CHALLENGE: "BOSS_BE_CHALLENGE",
    BOSS_BE_RANGSKILL: "BOSS_BE_RANGSKILL",

    // Game Object Related
    CREATE_TREASURE_BOX: "CREATE_TREASURE_BOX",
    RECEIVE_TREASURE_BOX_SUCCESS: "RECEIVE_TREASURE_BOX_SUCCESS",
    ADD_UNIT: "ADD_UNIT",
    ADD_TASK_TARGET_TAB: "ADD_TASK_TARGET_TAB",
    ADD_TASK_TARGET_GUIDE_ARROW: "ADD_TASK_TARGET_GUIDE_ARROW",

    // Game Events
    UNLOCK_VILLAGE: "UNLOCK_VILLAGE",
    ENTER_WORLD_COMP: "ENTER_WORLD_COMP",
    OPEN_GAME_END_CREATROLE: "OPEN_GAME_END_CREATROLE",
} as const;

export const LoginEvent = {
    CHECK_WX_PRIVACY: "CHECK_WX_PRIVACY",
    SERVER_LIST_UPDATED: "SERVER_LIST_UPDATED",
    USER_SELECT_SERVER: "USER_SELECT_SERVER",
    OPEN_PRIVACY_POLICY_URL_VIEW: "OPEN_PRIVACY_POLICY_URL_VIEW",
    OPEN_NOTICE_FINISH: "OPEN_NOTICE_FINISH",

    PRE_LOADING_PROGRESS: "PRE_LOADING_PROGRESS",
    PRE_LOAD_COMPLETE: "PRE_LOAD_COMPLETE",

    LOGIN_SUCCESS: "LOGIN_SUCCESS",
    LOGIN_BLOCKED: "LOGIN_BLOCKED",

} as const;

export const MapEvent = {
    LOAD_MAP_PROGRESS: "LOAD_MAP_PROGRESS",

    /** 加载地图成功 */
    LOAD_MAP_COMPLETE: "LOAD_MAP_COMPLETE",

    /** 加载初始化成功 */
    Map_INIT_COMPLETE: "Map_INIT_COMPLETE",

    /** 开始加载地图 */
    START_MAP_LOAD: "MAP_START_LOAD",

    /** 地图被激活 */
    MAP_ACTIVE: "MAP_ACTIVE",

    /** 移除加载地图页面  */
    MAP_REMOVE_LOAD: "MAP_REMOVE_LOAD",

    /** 进入世界成功，可以把loading页移除  */
    ENTER_WORLD_SUCCESS: "ENTER_WORLD_SUCCESS",
} as const;

export const TipEvent = {
    ONCE_POWER_TIP_FINISH: "ONCE_POWER_TIP_FINISH",
    ONCE_COMMON_TIP_FINISH: "ONCE_COMMON_TIP_FINISH",
}

export type EventDataMap = iEventDataMap & EffectEventData;

// Event data mapping - define what data each event carries
interface iEventDataMap {
    // Socket Events
    [SocketEvent.SOCKET_CONNECTED]: {
        socketName: string;
    };
    [SocketEvent.SOCKET_DISCONNECTED]: {
        socketName: string;
    };
    [SocketEvent.SOCKET_CONNECTING]: {
        socketName: string;
    };
    [SocketEvent.SOCKET_RECONNECTING]: {
        socketName: string;
    };
    [SocketEvent.SOCKET_CLOSED]: {
        socketName: string,
    };


    // UI Events
    [GameEvent.VIEW_SHOW]: BaseView;
    [GameEvent.VIEW_CLOSE]: BaseView; // view URI
    [GameEvent.OPEN_VIEW_MAIN_UI_SHOW]: boolean;
    [GameEvent.WAITING_SHOW]: null;
    [GameEvent.WAITING_HIDE]: null;

    // JoyStick
    [GameEvent.HIDE_JOYSTICK]: null;
    [GameEvent.JOYSTICK_TOUCH]: null;
    [GameEvent.SHOW_JOYSTICK]: null;
    [GameEvent.HIDE_JOYSTICK]: null;

    // Actors
    [GameEvent.ACTOR_BUFF_CHANGE]: null;
    [GameEvent.REMOVE_UNIT]: null;

    // Map/Zone Related
    [GameEvent.MapData_Update_Zone]: null;
    [GameEvent.ENTER_AREA_SAFE]: null;
    [GameEvent.ENTER_AREA_OUTSIDE]: null;

    // Hero/Player Related
    [GameEvent.HERO_REBORN_SAFE]: null;
    [GameEvent.HERO_REBORN_SITU]: null;

    // Unit/Combat Related
    [GameEvent.UNIT_DIE]: any; // IUnit interface
    [GameEvent.BOSS_BE_CHALLENGE]: number; // boss ID
    [GameEvent.BOSS_BE_RANGSKILL]: null;

    // Game Object Related
    [GameEvent.CREATE_TREASURE_BOX]: number; // box type
    [GameEvent.RECEIVE_TREASURE_BOX_SUCCESS]: null;
    [GameEvent.ADD_UNIT]: null;
    [GameEvent.ADD_TASK_TARGET_TAB]: null;
    [GameEvent.ADD_TASK_TARGET_GUIDE_ARROW]: null;

    // Game Events  
    [GameEvent.UNLOCK_VILLAGE]: null;
    [GameEvent.ENTER_WORLD_COMP]: null;
    [GameEvent.OPEN_GAME_END_CREATROLE]: boolean;

    // Login Events
    [LoginEvent.CHECK_WX_PRIVACY]: boolean;
    [LoginEvent.SERVER_LIST_UPDATED]: null;
    [LoginEvent.USER_SELECT_SERVER]: number;
    [LoginEvent.OPEN_PRIVACY_POLICY_URL_VIEW]: boolean;
    [LoginEvent.OPEN_NOTICE_FINISH]: null;
    [LoginEvent.PRE_LOADING_PROGRESS]: number;
    [LoginEvent.PRE_LOAD_COMPLETE]: null;
    [LoginEvent.LOGIN_SUCCESS]: null;
    [LoginEvent.LOGIN_BLOCKED]: { status: pb.LoginStatus };

    // Map Events
    [MapEvent.LOAD_MAP_PROGRESS]: {
        percent: number;
        totalCount: number;
    };
    [MapEvent.LOAD_MAP_COMPLETE]: {
        mapId: number,
    };
    [MapEvent.Map_INIT_COMPLETE]: null;
    [MapEvent.START_MAP_LOAD]: null;
    [MapEvent.MAP_ACTIVE]: null;
    [MapEvent.MAP_REMOVE_LOAD]: null;
    [MapEvent.ENTER_WORLD_SUCCESS]: null;

    // Tip Events
    [TipEvent.ONCE_POWER_TIP_FINISH]: null;
    [TipEvent.ONCE_COMMON_TIP_FINISH]: null;

    // Player events
    "player.serverListUpdated": null;
    "player.login": {
        userId: string;
        username: string;
        level: number;
    };
    "player.logout": {
        userId: string;
        reason?: string;
    };
}

// Event class base type
export abstract class GameEventBase<T = any> {
    abstract readonly eventName: keyof EventDataMap;
    abstract readonly data: T;
}

// Helper function to create event instances with proper typing
export function createEvent<K extends keyof EventDataMap>(
    eventName: K,
    data: EventDataMap[K]
): GameEventBase<EventDataMap[K]> {
    return {
        eventName,
        data,
    } as GameEventBase<EventDataMap[K]>;
}

export interface EventDispatcherImpl {
    on<K extends keyof EventDataMap>(eventName: K, callback: (data: EventDataMap[K]) => void, thisArgs?: any): void;
    once<K extends keyof EventDataMap>(eventName: K, callback: (data: EventDataMap[K]) => void, thisArgs?: any): void;
    off<K extends keyof EventDataMap>(eventName: K, callback: (data: EventDataMap[K]) => void): void;
    targetOff(target: any): void;
    emit<K extends keyof EventDataMap>(eventName: K, data: EventDataMap[K]): void;
    removeAllListeners(eventName: string): void;
}

// Type-safe event dispatcher
export class EventDispatcher {
    static _eventTarget: EventDispatcherImpl | null = null;

    static setImpl(impl: EventDispatcherImpl) {
        this._eventTarget = impl;
    }

    static dispatch<K extends keyof EventDataMap>(eventName: K, data: EventDataMap[K]): void {
        this._eventTarget.emit(eventName, data);
    }

    static on<K extends keyof EventDataMap>(
        eventName: K,
        callback: (data: EventDataMap[K]) => void,
        thisArgs?: any
    ): void {
        this._eventTarget.on(eventName, callback, thisArgs);
    }

    static once<K extends keyof EventDataMap>(
        eventName: K,
        callback: (data: EventDataMap[K]) => void,
        thisArgs?: any
    ): void {
        this._eventTarget.once(eventName, callback, thisArgs);
    }

    static off<K extends keyof EventDataMap>(
        eventName: K,
        callback: (data: EventDataMap[K]) => void
    ): void {
        this._eventTarget.off(eventName, callback);
    }

    static targetOff(target: any): void {
        this._eventTarget.targetOff(target);
    }

    static getEventBusWrapper(): EventBus {
        const wrapper: EventBus = {
            on: (event: string, listener: (...args: any[]) => void) => {
                this._eventTarget!.on(event as keyof EventDataMap, listener);
                return wrapper;
            },
            once: (event: string, listener: (...args: any[]) => void) => {
                this._eventTarget!.once(event as keyof EventDataMap, listener);
                return wrapper;
            },
            off: (event: string, listener: (...args: any[]) => void) => {
                this._eventTarget!.off(event as keyof EventDataMap, listener);
                return wrapper;
            },
            emit: (event: string, data: any) => {
                this._eventTarget!.emit(event as keyof EventDataMap, data);
                return true;
            },
            removeAllListeners: (event: string) => {
                this._eventTarget!.removeAllListeners(event);
                return wrapper;
            },
        };
        return wrapper;
    }
}
