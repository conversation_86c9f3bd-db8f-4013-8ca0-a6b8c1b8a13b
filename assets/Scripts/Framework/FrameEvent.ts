import pb from "../Proto/pb.js";

export enum FrameEvent {
    // Socket 连接中
    SOCKET_CONNECTING = "frame.socket.connecting",
    // Socket 连接成功
    SOCKET_CONNECTED = "frame.socket.connected",
    // Socket 断开连接
    SOCKET_DISCONNECTED = "frame.socket.disconnected",
    // Socket 重连中
    SOCKET_RECONNECTING = "frame.socket.reconnecting",
    // Socket 连接失败
    SOCKET_FAILED = "frame.socket.failed",
    // Socket 连接关闭
    SOCKET_CLOSED = "frame.socket.closed",
    // Socket 收到消息
    SOCKET_ONMESSAGE = "frame.socket.onmessage",
}


export interface SocketControlEvent {
    socketName: string;
}

export interface SocketMessageEvent {
    socketName: string;
    message: pb.MessageWrapper;
}

export type SocketEvent = SocketControlEvent | SocketMessageEvent;