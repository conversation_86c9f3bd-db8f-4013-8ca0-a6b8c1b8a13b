import * as cc from 'cc';

export enum LogType {
    /** 网络层日志 */
    Net = 1,
    /** 数据结构层日志 */
    Model = 2,
    /** 业务逻辑层日志 */
    Business = 4,
    /** 视图层日志 */
    View = 8,
    /** 配置日志 */
    Config = 16,
    /** 标准日志 */
    Trace = 32,
}

var names = {
    "1": "网络日志",
    "2": "数据日志",
    "4": "业务日志",
    "8": "视图日志",
    "16": "配置日志",
    "32": "标准日志",
}

export class ReportParams {

    /** 玩家id */
    playerId?: string;
    /** 玩家名 */
    name?: string;
    /** 玩家渠道 */
    channelId?: string;
}


/** 
 * 日志管理 
 * @example
oops.cc.log.trace("默认标准日志");
oops.cc.log.logConfig("灰色配置日志");
oops.cc.log.logNet("橙色网络日志");
oops.cc.log.logModel("紫色数据日志");
oops.cc.log.logBusiness("蓝色业务日志");
oops.cc.log.logView("绿色视图日志");
 */
export default class L {
    private static tags: number = 0;

    private static _enable: boolean = true;

    /**是否启用上报 */
    public static report: boolean = true;

    /** 上报链接 */
    public static reportUrl: string = "";

    public static reportParams = new ReportParams();

    private static init(): void {
        this.tags =
            LogType.Net |
            LogType.Model |
            LogType.Business |
            LogType.View |
            LogType.Config |
            LogType.Trace;

        this.addErrorEventListener();
    }

    /** 
     * 设置显示的日志类型，默认值为不显示任何类型日志
     * @example
        oops.cc.log.setTags(LogType.View|LogType.Business)
     */
    static setTags(tag: LogType) {
        // if (tag) {
        this.tags = tag;
        // }
    }

    /**
     * 启用/关闭日志
     */
    static set enable(value: boolean) {
        this._enable = value;
        if (value) {
            this.tags =
                LogType.Net |
                LogType.Model |
                LogType.Business |
                LogType.View |
                LogType.Config |
                LogType.Trace;
        } else {
            this.tags = 0;
        }
    }

    static get enable(): boolean {
        return this._enable;
    }

    /**
     * 记录开始计时
     * @param describe  标题描述
     * @example
        oops.cc.log.start();
        ...
        省略N行代码
        ...
        oops.cc.log.end();
     */
    static start(describe: string = "Time"): void {
        console.time(describe);
    }

    /**
     * 打印范围内时间消耗
     * @param describe  标题描述
     * @example
        oops.cc.log.start();
        ...
        省略N行代码
        ...
        oops.cc.log.end();
     */
    static end(describe: string = "Time"): void {
        console.timeEnd(describe);
    }

    /**
     * 打印表格
     * @param msg       日志消息
     * @param describe  标题描述
     * @example
        var object:any = {uid:1000, name:"oops"};
        oops.cc.log.table(object);
     */
    static table(msg: any, describe?: string) {
        if (!this.isOpen(LogType.Trace)) {
            return;
        }
        console.table(msg);
    }


    /**
     * 错误日志
     * @param args 
     * @returns 
     */
    static error(...args) {
        //将报错的位置打印出来.
        args.push(L.stack(3), L.stack(4), L.stack(5),)

        if (this.report) {
            this.reportError(...args);
        }

        if (!this.enable) return;

        const color: string = "color:#FF0000;"
        var backLog = console.error//cc.log || cc.log;
        backLog.call(null, "%c%s%s", color, this.getDateString(), args);
    }


    /**
     * 输出日志
     * @param args 
     * @returns 
     */
    static log(...args) {
        L.trace.apply(L, args);
    }

    /**
     * 打印标准日志
     * @param msg       日志消息
     */
    static trace(...args) {
        // 标记没有打开，不打印该日志
        if (!this.isOpen(LogType.Trace)) {
            return;

        }
        const color: string = "color:#000000;"
        var backLog = console.log//cc.log || cc.log;
        backLog.call(null, "%c%s%s", color, this.getDateString(), args);
    }

    /**
     * 打印网络层日志
     * @param msg       日志消息
     * @param describe  标题描述
     */
    static logNet(msg: any, describe?: string, type?: string, stackNum?: number) {
        this.orange(LogType.Net, msg, describe, type, stackNum);
    }


    /**
     * 打印数据层日志
     * @param msg       日志消息
     * @param describe  标题描述
     */
    static logModel(msg: any, describe?: string, type?: string, stackNum?: number) {
        this.violet(LogType.Model, msg, describe, type, stackNum);
    }

    /**
     * 打印业务层日志
     * @param msg       日志消息
     * @param describe  标题描述
     */
    static logBusiness(msg: any, describe?: string, type?: string, stackNum?: number) {
        this.blue(LogType.Business, msg, describe, type, stackNum);
    }

    /**
     * 打印视图日志
     * @param msg       日志消息
     * @param describe  标题描述
     */
    static logView(msg: any, describe?: string, type?: string, stackNum?: number) {
        this.green(LogType.View, msg, describe, type, stackNum);
    }

    /** 打印配置日志 */
    static logConfig(msg: any, describe?: string, type?: string, stackNum?: number) {
        this.gray(LogType.Config, msg, describe, type, stackNum);
    }

    // 橙色
    private static orange(tag: LogType, msg: any, describe?: string, type?: string, stackNum?: number) {
        this.print(tag, msg, "color:#ee7700;", describe, type, stackNum)
    }

    // 紫色
    private static violet(tag: LogType, msg: any, describe?: string, type?: string, stackNum?: number) {
        this.print(tag, msg, "color:Violet;", describe, type, stackNum)
    }

    // 蓝色
    private static blue(tag: LogType, msg: any, describe?: string, type?: string, stackNum?: number) {
        this.print(tag, msg, "color:#3a5fcd;", describe, type, stackNum)
    }

    // 绿色
    private static green(tag: LogType, msg: any, describe?: string, type?: string, stackNum?: number) {
        this.print(tag, msg, "color:green;", describe, type, stackNum)
    }

    // 灰色
    private static gray(tag: LogType, msg: any, describe?: string, type?: string, stackNum?: number) {
        this.print(tag, msg, "color:gray;", describe, type, stackNum)
    }

    public static isOpen(tag: LogType): boolean {
        return (this.tags & tag) != 0;
    }

    /**
     * 输出日志
     * @param tag       日志类型
     * @param msg       日志内容
     * @param color     日志文本颜色
     * @param describe  日志标题描述
     */
    private static print(tag: LogType, msg: any, color: string, describe?: string, type?: string, stackNum?: number) {
        // 标记没有打开，不打印该日志
        if (!this.isOpen(tag)) {
            return;

        }
        if (!stackNum) stackNum = 5;
        var backLog = console.log;//cc.log || cc.log;
        var typeStr = type || names[tag];
        if (describe) {
            backLog.call(null, "%c%s%s%s:%s%o", color, this.getDateString(), '[' + typeStr + ']', this.stack(stackNum), describe, msg);
        }
        else {
            backLog.call(null, "%c%s%s%s:%o", color, this.getDateString(), '[' + typeStr + ']', this.stack(stackNum), msg);
        }
    }

    public static zz_log(...args) {
        console.log(args);
    }

    public static stack(start: number = 3, end?: number): string {
        if (!end || isNaN(end) || end < start) {
            end = start;
        }
        let message = ""
        for (; start <= end; start++) {
            message += this._stack(start);
            if (start < end) message += "\n"
        }
        return message;
    }

    public static _stack(index: number): string {
        var e = new Error();
        var lines = e.stack!.split("\n");
        var result: Array<any> = [];
        lines.forEach((line) => {
            line = line.substring(7);
            var lineBreak = line.split(" ");
            if (lineBreak.length < 2) {
                result.push(lineBreak[0]);
            }
            else {
                result.push({ [lineBreak[0]]: lineBreak[1] });
            }
        });

        var list: string[] = [];
        var splitList: Array<string> = [];
        if (index < result.length - 1) {
            var value: string;
            for (var a in result[index]) {
                var splitList = a.split(".");

                if (splitList.length == 2) {
                    list = splitList.concat();
                }
                else {
                    value = result[index][a];
                    if (!value || !value.lastIndexOf) continue;
                    var start = value!.lastIndexOf("/");
                    var end = value!.lastIndexOf(".");
                    if (start > -1 && end > -1) {
                        var r = value!.substring(start + 1, end);
                        list.push(r);
                    }
                    else {
                        list.push(value);
                    }
                }
            }
        }

        if (list.length == 1) {
            return "[" + list[0] + ".ts]";
        }
        else if (list.length == 2) {
            return "[" + list[0] + ".ts->" + list[1] + "]";
        }
        return "";
    }

    /**
     * 获取时间 [16:01:01:790]
     * @returns 
     */
    public static getDateString(): string {
        let d = new Date();
        let str = d.getHours().toString();
        let timeStr = "";
        timeStr += (str.length == 1 ? "0" + str : str) + ":";
        str = d.getMinutes().toString();
        timeStr += (str.length == 1 ? "0" + str : str) + ":";
        str = d.getSeconds().toString();
        timeStr += (str.length == 1 ? "0" + str : str) + ":";
        str = d.getMilliseconds().toString();
        if (str.length == 1) str = "00" + str;
        if (str.length == 2) str = "0" + str;
        timeStr += str;

        timeStr = "[" + timeStr + "]";
        return timeStr;
    }

    static reportError(...args) {
        let exception = new ErrorException();
        exception.errorObj = {
            "message": args,
            "stack": L.stack(3, 8)
        }
        exception.message = args;
        this._reportError(exception);
    }

    private static _reportError(exception: ErrorException) {
        if (window["exception"] != JSON.stringify(exception)) {
            window["exception"] = JSON.stringify(exception);
            console.log("ErrorReport", exception);
            this.requestReport(this.reportUrl, exception);
        }
    }

    private static requestReport(url: string, exception?: ErrorException) {
        let timer;
        let xhr = cc.loader.getXMLHttpRequest();

        let dataStr: string = url;

        dataStr += "?";
        dataStr += "level=error";
        dataStr += "&title=报错";
        dataStr += "&playerId=" + this.reportParams.playerId;
        dataStr += "&name=" + this.reportParams.name;

        let content = "";
        try {
            content = JSON.stringify(exception);
        } catch (error) {
            content = exception ? exception.message : "";
        }
        dataStr += "&content=" + encodeURI(content);

        if (exception && exception.errorObj) {
            let data = "";
            if (exception.errorObj.message) {
                data += "message:" + exception.errorObj.message;
            }
            if (exception.errorObj.stack) {
                data.length > 0 && (data += ",");
                data += "stack:" + exception.errorObj.stack;
            }
            //自定义参数.
            data.length > 0 && (data += ",");
            data += "channelId:" + this.reportParams.channelId;

            data = "{" + data + "}";
            dataStr += "&data=" + encodeURI(data);
        }
        let self = this;
        xhr.onreadystatechange = () => {
            if (xhr.readyState === 4 && xhr.status >= 200 && xhr.status < 300) {
                clearTimeout(timer);
                xhr.onreadystatechange = null;
                xhr = null;
            } else {
                // 请求失败，每N秒后重新发送请求
                if (xhr.readyState === 4)
                    if (!timer) {
                        timer = setTimeout(() => {
                            clearTimeout(timer);
                            self.requestReport(url, exception);
                        }, 5000);
                    }
            }
        }
        xhr.timeout = 5000;
        xhr.open("GET", dataStr);
        xhr.send();
    }

    static addErrorEventListener() {
        if (cc.sys.isNative) {
            window["__errorHandler"] = function (errorMessage, file, line, message) {
                let exception = new ErrorException();
                exception.errorMessage = errorMessage;
                exception.file = file;
                exception.line = line;
                exception.message = message;
                L._reportError(exception);
            };
        } else if (cc.sys.isBrowser) {
            window["onerror"] = function (message, url, line, column, errorObj) {
                let exception = new ErrorException();
                exception.message = message;
                exception.url = url;
                exception.line = line;
                exception.column = column;
                exception.errorObj = errorObj;
                L._reportError(exception);
            };
        }
    }
}

class ErrorException {

    errorMessage?: any;

    file?: any;

    line?: any;

    message?: any;

    url?: any;

    errorObj?: any;

    column?: any;
}

// @ts-ignore
L.init();