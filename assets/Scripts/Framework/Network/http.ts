export type RequestOptions = {
    url: string;
    query?: Record<string, string[] | string | number>;
    method: 'GET' | 'POST' | 'PUT' | 'DELETE';
    headers?: Record<string, string>;
    body?: string | object | ArrayBuffer;
    timeout?: number;
    autoParseJson?: boolean;

    // 以下不一定生效，依赖平台能力
    enableKeepAlive?: boolean;
}

export interface RequestReply {
    status: number;
    headers: string;
    body: any;
}

export function request(options: RequestOptions): Promise<RequestReply> {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        let fullUrl = options.url;
        if (options.query) {
            fullUrl += '?';
            for (const key in options.query) {
                const value = options.query[key];
                if (Array.isArray(value)) {
                    // Handle array values by joining them with commas
                    for (const v of value) {
                        fullUrl += `${key}=${v}&`;
                    }
                } else {
                    const value1 = encodeURIComponent(String(value));
                    // Handle string and number values by converting to string
                    fullUrl += `${key}=${value1}&`;
                }
            }
            fullUrl = fullUrl.slice(0, -1);
        }
        xhr.open(options.method, fullUrl, true);
        if (options.headers) {
          for (const key in options.headers) {
            if (options.headers.hasOwnProperty(key)) {
              xhr.setRequestHeader(key, options.headers[key]);
            }
          }
        }
        let body = options.body;
        console.log('body', body, typeof body);
        if (typeof body === 'object') {
            body = JSON.stringify(body);
        } 

        if (body) {
          xhr.send(body);
        }
        if (options.timeout > 0) {
          xhr.timeout = options.timeout;
        }
        xhr.onerror = (e) => {
            reject(new Error("HTTP_ERROR " + e));
        }
        xhr.onreadystatechange = () => {
          if (xhr.readyState === 4) {
            let body = xhr.response;
            if (options.autoParseJson) {
                if (xhr.responseType === 'json') {
                    try {
                        body = JSON.parse(body);
                    // eslint-disable-next-line @typescript-eslint/no-unused-vars
                    } catch (e) {
                        reject(new Error("JSON_PARSE_ERROR " + e.message));
                    }
                }
            }
            resolve({
                status: xhr.status,
                headers: xhr.getAllResponseHeaders(),
                body: body
            });
          } 
      }});   
}