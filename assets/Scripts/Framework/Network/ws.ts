import * as cc from "cc";
import { FrameEvent } from "../FrameEvent";
import Logger from "../Logger/index";
import pb from "../../Proto/pb.js";
console.log(pb)
console.log('xxxxxxx', pb.MessageWrapper)

//Socket回调数据结构
export interface SocketCallBackModel {
    target: any;
    callbackFunc: () => void;
}

//Socket监听数据结构
export interface SocketProtocolModel {
    Protocol: number;
    obj: any;
    Callback: (obj: object) => void;
}

export interface WebSocketConfig {
    name: string;

    // connect timeout, default to 15s
    connectTimeout?: number;

    // default to 30s
    heartbeatPeriod?: number; 

    // max loss time, default to 2 minutes
    maxLossTime?: number;

    // reconnect period base, default to 1s
    reconnectPeriodTime?: number;

    // max reconnect retry time, default to 1m
    maxReconnectPeriod?: number

    // Max reconnect times, default to 3, 0 means never
    maxReconnectTimes?: number;
}

const defaultWebSocketConfig = {
    name: '',
    wsUrl: '',
    
    connectTimeout: 15,
    heartbeatPeriod: 30,
    maxLossTime: 120,
    reconnectPeriodTime: 1,
    maxReconnectPeriod: 60
}

export enum WSSocketState {
    INIT = 0,
    CONNECTING = 1,
    RECONNECTING = 2,
    CONNECTED = 3,
    DISCONNECTED = 4,
    FAILED = 5,
    CLOSED = 6,
}

let socketId = 0;

function nextSocketId(): number {
    let v = socketId;
    socketId++;
    return v;
}

export interface SocketEventHandler {
    onStateChange: (state: WSSocketState) => void;
    onMessage: (msg: pb.MessageWrapper) => void;
}

/**
 * 
 * CONNECTING：值为0，表示正在连接。
 * OPEN：值为1，表示连接成功，可以通信了。
 * CLOSING：值为2，表示连接正在关闭。
 * CLOSED：值为3，表示连接已经关闭，或者打开连接失败
 */
export default class Socket {

    /** 当前的webSocket的对象 */
    private _objSocket: WebSocket = null;
    private _cfg: WebSocketConfig = defaultWebSocketConfig;
    private _state: WSSocketState = WSSocketState.INIT;
    public get state() {
        return this._state;
    }
    // 客户端是否关闭
    private _isUserClosed: boolean = false;

    // Reconnect的时间管理
    private _lastReconnectTime = 0;
    private _reconnectTimes = 0;
    private _wsUrl: string | null = null

    private _socketId = 0;
    private _name: string;
    get name() {
        return this._name;
    }

    /** 重连定时器 */
    protected _reconnectTimeOut: any = null;
    /** 连接超时定时器 */
    protected _connectTimeOut: any = null;

    /** 连接时间 */
    private _connectTime: number = 0;
    private _eventHandler?: SocketEventHandler;

    /** 当前的Socket ID */
    get sockId(): number {
        return this._socketId;
    }

    constructor(cfg: WebSocketConfig, eventHandler?: SocketEventHandler) {
        if (!cfg.name) {
            throw new Error("Socket name is required");
        }
        this._name = cfg.name;
        this._cfg = {
            ...defaultWebSocketConfig,
            ...cfg,
        };
        if (eventHandler) {
            this._eventHandler = eventHandler;
        }
    }

    public setEventHandler(eventHandler: SocketEventHandler) {
        this._eventHandler = eventHandler;
    }

    /**
     WebSocket启动
     */
    public start(wsUrl: string): void {
        this._logging("Socket start");
        this._wsUrl = wsUrl;
        this._connect();
    }

    /**
     WebSocket停止
     @param forceStop 是否强制停止，如果为真不会自动重连
     */
    public stop(forceStop: boolean): void {
        this._isUserClosed = true;
        this._logging("Socket stop", forceStop);
        this._disconnectCurrent();
        this.setState(WSSocketState.CLOSED);
    }

    /** 发送消息 */
    public send(data: Uint8Array): boolean {
        if (this._objSocket == null) {
            this._logging("SOCKET 未连接");
            return false;
        }
        
        if (this._objSocket.readyState !== WebSocket.OPEN) {
            this._logging("SOCKET 未处于OPEN状态，当前状态:", this._objSocket.readyState);
            return false;
        }
        
        this._logging("Sending data:", data.constructor.name, "length:", data.length);
        this._objSocket.send(data);
        return true;
    }

    /**
     * 手动重连
     */
    public reconnect() {
        this._disconnectCurrent();
        this._connect();
    }

    public getSocketState(): number {
        return this._objSocket.readyState;
    }

    private setState(state: WSSocketState) {
        this._state = state;
        this._eventHandler?.onStateChange(state);
    }

    private scheduleReconnect() {
        if (this._reconnectTimes >= this._cfg.maxReconnectTimes) {
            this._logging("重连次数超过最大重连次数");
            this.setState(WSSocketState.FAILED);
            return;
        }
        const nextReconnectTime = this.nextReconnectTime();
        this._reconnectTimeOut = setTimeout(() => {
            this._reconnectTimeOut = null;
            this._reconnect();
        }, nextReconnectTime);
        this._eventHandler?.onStateChange(WSSocketState.RECONNECTING);
    }

    /**
     WebSocket重新连接操作
     */
    private _reconnect() {
        this._logging("Socket _reconnnect", Date.now());
        if (this._isUserClosed) {
            this._logging("Socket closed, ignore reconnect")
            return;
        }

        //如果不是自动重连不处理
        if (this._cfg.reconnectPeriodTime < 0) {
            this._logging("设置了不重连");
            return;
        }

        if (this._state === WSSocketState.CONNECTING) {
            this._logging("重连中稍后再重连");
            return;
        }

        if (this._reconnectTimes >= this._cfg.maxReconnectTimes) {
            this._logging("重连次数超过最大重连次数");
            return;
        }

        this._reconnectTimes++;
        this._connect();
    }

    /**
     WebSocket连接操作
     */
    private _connect(): void {
        //Url = "wss://www.xiugoux1.cn/game?ip=**********&port=10001";
        const wsurl = this._wsUrl;

        if (this._objSocket != null) {
            this._logging("socket already connected");
            return;
        }

        let connectTime: number = new Date().getTime();
        // Url += "?" + connectTime;
        this._logging("socket try connect[Addr:" + wsurl + "]");

        this._connectTimeOut = setTimeout(() => {
            if (this._objSocket.readyState !== WebSocket.OPEN) {
                this._onConnectTimeout();
            }
        }, this._cfg.connectTimeout * 1000);

        try {
            this._objSocket = new WebSocket(wsurl);

            let newSocketId = nextSocketId();
            this._socketId = newSocketId;
            // this._objSocket = new WebSocket( Url );
            this._logging("_connect socketId 准备重连! :" + newSocketId);

            this.setState(WSSocketState.CONNECTING);

            this._objSocket.binaryType = "arraybuffer";
            this._objSocket["socketId"] = "socketId" + newSocketId;
            this._objSocket.onopen = (e) => {
                this._onOpen(e, newSocketId);
            }
            this._objSocket.onclose = (e) => {
                this._onClose(e, newSocketId);
            }
            this._objSocket.onmessage = (e) => {
                this._onMessage(e, newSocketId);
            }

            this._objSocket.onerror = (e) => {
                this._onError(e, newSocketId);
            }
            this._connectTime = connectTime;


            this._logging("_connect socketId 尝试重连:" + socketId);
        } catch (error) {
            this._logging("connect error", error);
        }
    };

    private _onConnectTimeout() {
        this._logging("connect timeout");
        this._disconnectCurrent();
        this.scheduleReconnect();
    }


    /**
     * 
     * 连接成功后的回调函数
     * 如果要指定多个回调函数，可以使用addEventListener方法。
     */
    private _onOpen(e: Event, socketId: number): void {
        this._logging("onOpen socketId:" + socketId, socketId, this._socketId);
        if (socketId !== this._socketId) {
            this._logging("socketId mismatch, ignore");
            return;
        }

        if (this._objSocket.readyState == WebSocket.OPEN) {  //表示己经连接成功
            this.setState(WSSocketState.CONNECTED);
            this.resetReconnectState();
            if (this._connectTimeOut) {
                clearTimeout(this._connectTimeOut);
                this._connectTimeOut = null;
            }
        }
    }

    /**
     指定报错时的回调函数。
     如果要指定多个回调函数，可以使用addEventListener方法。
     */
    public _onError(e: Event, socketId: number): void {
        this._logging("_onError socketId:" + this._socketId);
        if (socketId !== this._socketId) {
            this._logging("socketId mismatch, ignore");
            return;
        }

        this._disconnectCurrent()
        this._logging("_onError socketId:" + this._socketId);
        this.scheduleReconnect();
    }

    private _disconnectCurrent() {
        let sock = this._objSocket;
        this._clearWebSocket();
        if (sock) {
            sock.close();
        }
        this.resetReconnectState();
        this.resetConnectState();
    }

    private resetConnectState() {
        this._connectTime = 0;
        this._state = WSSocketState.DISCONNECTED;
    }

    private _clearWebSocket() {
        this._objSocket.onclose = null;
        this._objSocket.onerror = null;
        this._objSocket.onmessage = null;
        this._objSocket = null;
    }

    /**
        指定连接关闭后的回调函数.
        在ios下，你断开wifi，看触不触发onClose事件！
     */
    protected _onClose(e: CloseEvent, socketId: number): void {
        this._logging("_onClose socketId:" + socketId);
        if (socketId !== this._socketId) {
            this._logging("socketId mismatch, ignore");
            return;
        }

        if (this._objSocket) this._objSocket.onclose = null;
        this.setState(WSSocketState.DISCONNECTED);
    }

    /**
     指定收到服务器数据后的回调函数。
     如果要指定多个回调函数，可以使用addEventListener方法。
     */
    private _onMessage(e: MessageEvent, socketId: number): void {
        let message: Uint8Array = new Uint8Array(e.data);
        try {
            let wrapper = pb.MessageWrapper.decode(message);
            this._eventHandler?.onMessage(wrapper);
        } catch (error) {
            this._logging("decode message error", error);
        }
    }

    private _logging(...args): void {
        Logger.logNet(args);
    }

    protected _clearReconnectTimeout() {
        this._logging("Socket _clearReconnectTimeout");
        if (this._reconnectTimeOut) {
            clearTimeout(this._reconnectTimeOut);
            this._reconnectTimeOut = null;
        }
    }

    private resetReconnectState() {
        this._reconnectTimes = 0;
        this._lastReconnectTime = 0;
    }

    private nextReconnectTime(): number {
        let reconnectDelay = this._cfg.reconnectPeriodTime * Math.pow(2, this._reconnectTimes);
        if (reconnectDelay > this._cfg.maxReconnectPeriod) {
            reconnectDelay = this._cfg.maxReconnectPeriod;
        }
        return reconnectDelay*1000;
    }
};
