import Socket, { WSSocketState } from "./ws";
import pb from "../../Proto/pb.js";
import Logger from "../Logger";
import { SocketEvent, EventDispatcher } from "../../GameEvent/Events";

export interface RequestOptions {
    timeout?: number;
}

const defaultRqstOpts = {
    timeout: 10000,
}


function mergeOpts(opts ?: RequestOptions) {
    if (!opts) {
        return {...defaultRqstOpts};
    }
    return {
        ...defaultRqstOpts,
        ...opts,
    }
}

export default class WSWrapper {
    private _messageId: number = Math.floor(Math.random() * 1024)+1024;
    private _socket: Socket;
    private _reader: Reader = new Reader();
    private _writer: Writer;

    constructor(socket: Socket) {
        this._socket = socket;
        this._writer = new Writer(socket);
        const name = socket.name;
        this._socket.setEventHandler({
            onStateChange: (state: WSSocketState) => {
                Logger.logNet(`Socket ${name} state changed to: ${state}`);
                switch (state) {
                    case WSSocketState.CONNECTED:
                        EventDispatcher.dispatch(SocketEvent.SOCKET_CONNECTED, {
                            socketName: this._socket.name,
                        });
                        this._writer.onConnected();
                        break;
                    case WSSocketState.CONNECTING:
                        EventDispatcher.dispatch(SocketEvent.SOCKET_CONNECTING, {
                            socketName: this._socket.name,
                        });
                        break;
                    case WSSocketState.RECONNECTING:
                        EventDispatcher.dispatch(SocketEvent.SOCKET_RECONNECTING, {
                            socketName: this._socket.name,
                        });
                        break;
                    case WSSocketState.DISCONNECTED:
                        EventDispatcher.dispatch(SocketEvent.SOCKET_CLOSED, {
                            socketName: this._socket.name,
                        });
                        break;
                    case WSSocketState.CLOSED:
                        EventDispatcher.dispatch(SocketEvent.SOCKET_CLOSED, {
                            socketName: this._socket.name,
                        });
                        break;
                }
            },
            onMessage: (msg: pb.MessageWrapper) => {
                this._reader.onMessage(msg);
            },
        });
    }

    public start(wsUrl: string) {
        this._socket.start(wsUrl);
    }

    public stop() {
        this._socket.stop(true);
    }

    public registerProtocol(protocol: number, obj: any, callback: (obj: object) => void) {
        this._reader.addEventListener(obj, callback, protocol);
    }

    public unregisterProtocol(protocol: number, obj: any, callback: (obj: object) => void) {
        this._reader.removeEventListener(obj, callback, protocol);
    }

    public send(protocol: number, data: any) {
        let msg = new pb.MessageWrapper({
            id: this.nextMessageId(),
            protoId: protocol,
            data: data,
        });
        this._writer.send(msg);
    }

    public request<Reply>(protocol: number, data: Uint8Array, opts?: RequestOptions): Promise<Reply> {
        opts = mergeOpts(opts);
        return new Promise((resolve, reject) => {
            let replyId = this._reader.createReplyChan((reply: any) => {
                // Clear timeout if it exists
                if (timeoutId) {
                    clearTimeout(timeoutId);
                }
                resolve(reply as Reply);
            });
            let msg = new pb.MessageWrapper({
                id: this.nextMessageId(),
                protoId: protocol,
                data: data,
                toReplyId: replyId,
            });
            this._writer.send(msg);
            
            // Add timeout handler
            let timeoutId: NodeJS.Timeout | null = null;
            if (opts.timeout && opts.timeout > 0) {
                timeoutId = setTimeout(() => {
                    this._reader.removeReplyChan(replyId);
                    reject(new Error(`Request timeout after ${opts.timeout}ms`));
                }, opts.timeout);
            }
        })
    }

    private nextMessageId() {
        this._messageId++;
        return this._messageId;
    }
}

export interface SocketProtocolModel {
    Protocol: number;
    obj: any;
    callback: (obj: object, ...args: any[]) => void;
}

function initNextReplyId() {
    return Math.floor(Math.random() * 1000000)+1024;
}

class Reader {
    private socketListenMap: { [Protocol: string]: (SocketProtocolModel[]) } = {}; //监听的列表
    private replyChanMap: { [replyId: number]: (obj: object) => void } = {}; //回复的通道
    private _nextReplyId: number =initNextReplyId();

    /**
     * 
     * 增加监听事件
     */
    public addEventListener(p_obj: any, p_Callback: (obj: object) => void, p_Protocol: number): boolean {
        let Model: SocketProtocolModel = { Protocol: p_Protocol, obj: p_obj, callback: p_Callback };
        Logger.logNet("addEventListener: protoId", `${p_Protocol}`);
        if (!this.socketListenMap[p_Protocol]) { //如果本个协议没有监听过的话需要创建一个新的数组
            this.socketListenMap[p_Protocol] = [];
        }
        if (this.socketListenMap[p_Protocol].length > 0) {//如果本个协议有正在监听的情况下需要判断是否有重复方法
            for (const info of this.socketListenMap[p_Protocol]) {
                if (p_Protocol == info.Protocol && p_obj == info.obj && p_Callback == info.callback) {
                    //存在重复监听的情况
                    Logger.logNet("addEventListener: duplicate listener for protoId", `${p_Protocol}`);
                    // this._logging("存在重复监听");
                    return false;
                }
            }
        }
        this.socketListenMap[p_Protocol].push(Model); //加入到监听对象中
        return true;
    }

    /**
     * 
     * 删除监听事件
     */
    public removeEventListener(obj: any, callback?: (obj: object) => void, protocol?: number): boolean {
        if (!callback || !protocol) { //针对整个组件删除事件
            for (const protocol in this.socketListenMap) {
                for (var i: number = 0; i < this.socketListenMap[protocol].length;) {
                    if (obj == this.socketListenMap[protocol][i].obj) {
                        this.socketListenMap[protocol].splice(i, 1);  //删除某一个元素
                        //cc.log( "this.SocketListenMap[Protocol] length:"+this.SocketListenMap[Protocol].length );
                        if (this.socketListenMap[protocol].length === 0) { //如果本个词典是空的直接delete
                            delete this.socketListenMap[protocol];
                            break;
                        }
                    } else {
                        i++;
                    }
                }
            }
        }
        else {  //某个监听事件删除
            if (this.socketListenMap[protocol]) {  //判断是否有监听本个协议
                let index: number = 0;
                for (const info of this.socketListenMap[protocol]) {
                    if (protocol == info.Protocol && obj == info.obj && callback == info.callback) {
                        this.socketListenMap[protocol].splice(index, 1);// 删除某一个元素
                        if (this.socketListenMap[protocol].length === 0) {//如果本个词典是空的直接delete
                            delete this.socketListenMap[protocol];
                        }
                        return true;
                    }
                    index++;
                }
            }
        }
        return false;
    }

    public onMessage(msg: pb.MessageWrapper) {
        if (msg.replyId) {
            Logger.logNet("onMessage: replyId", `${msg.replyId}`);
            if (this.replyChanMap[msg.replyId]) {
                let cb = this.replyChanMap[msg.replyId];
                (msg);
                delete this.replyChanMap[msg.replyId];
            } else {
                Logger.error("replyChanMap not found replyId", msg.replyId);
            }
            return;
        }
        
        if (this.socketListenMap[msg.protoId]) {
            Logger.logNet("onMessage: dispatch proto for protoId", `${msg.protoId}`);
            for (const info of this.socketListenMap[msg.protoId]) {
                let sender = {};

                sender["data"] = msg.data;
                sender["msgId"] = msg.protoId;
                info.callback.call(info.obj, sender);
            }
        } else {
            Logger.logNet("onMessage: no listener for protoId", `${msg.protoId}`);
        }
        return true;
    }

    public createReplyChan(callback: (obj: any) => void) {
        let toReplyId = this.nextReplyId();
        this.replyChanMap[toReplyId] = callback;
        return toReplyId;
    }

    public removeReplyChan(replyId: number) {
        if (this.replyChanMap[replyId]) {
            delete this.replyChanMap[replyId];
        }
    }

    private nextReplyId() {
        this._nextReplyId++;
        return this._nextReplyId;
    }

    destroy() {
        this.socketListenMap = {};
    }
}

const MAX_QUEUE_SIZE = 16;

class Writer {

    private _objSocket: Socket;
    protected _Queue: Uint8Array[] = [];

    constructor(socket: Socket) {
        this._objSocket = socket;
    }


    send(msg: pb.MessageWrapper) {
        let sendData = pb.MessageWrapper.encode(msg).finish();
        
        if (this._objSocket.state != WSSocketState.CONNECTED) {
            this.pushQueue(sendData);
            return false;
        }
        
        // TODO: 心跳包不打印日志
        // if( pbId != pb.HeartBeatRequest.Proto.ID ){
        // }
        Logger.logNet("发送协议:" + msg.protoId + ",t=" + Date.now());

        return this.send_with_try(sendData);
    }

    private pushQueue(buf: Uint8Array) {
        if (this._Queue.length >= MAX_QUEUE_SIZE) {
            throw new Error("Queue is full");
        }
        Logger.logNet("pushQueue", `${this._Queue.length}`);
        this._Queue.push(buf);
    }

    private send_with_try(buf: Uint8Array) {
        try {
            if (this._objSocket.state === WSSocketState.CONNECTED) {
                Logger.logNet("send_with_try: one message");
                this._objSocket.send(buf);
            } else {
                Logger.logNet(" websocket readyState is error, can not send msg! ReadyState is " + this._objSocket.state);
            }
        }
        catch (error) {
            Logger.logNet("send_with_try error", error);
            return false;
        }
    }

    /**
     * 连接成功 清空队列缓存
     */
    onConnected() {
        if (this._Queue.length > 0) { //之前队列的数据未能发送 ，重新连接后在一起发送
            for (const message of this._Queue) {
                this.send_with_try(message);
            }
            this._Queue = [];
        }
    }

    destroy() {
        this._Queue.length = 0;
    }
}