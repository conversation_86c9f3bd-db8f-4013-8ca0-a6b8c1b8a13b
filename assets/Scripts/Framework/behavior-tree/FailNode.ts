import { BTContext } from "./BTContext";
import { DecoratorNode } from "./DecoratorNode";
import { NodeBase } from "./NodeBase";
import { NodeState } from "./NodeState";
import { NodeType } from "./NodeType";

/** 子节点执行完毕后, 必定返回失败 */
export class FailNode extends DecoratorNode {
    constructor(child: NodeBase) {
        super(NodeType.Fail, child);
    }

    execute(context: BTContext): NodeState {
        const result = this.child.execute(context);
        //if (result === NodeState.Success) {
        //    return NodeState.Success;
        //}
        return NodeState.Failure;
    }
}