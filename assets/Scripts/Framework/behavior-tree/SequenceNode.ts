import { BTContext } from "./BTContext";
import { CombineNode } from "./CombineNode";
import { NodeBase } from "./NodeBase";
import { NodeState } from "./NodeState";
import { NodeType } from "./NodeType";

/** 依次执行子节点, 遇到执行失败的则退出并返回失败, 全部执行成功则返回成功  */
export class SequenceNode extends CombineNode {
    constructor(children: NodeBase[]) {
        super(NodeType.Sequence, children);
    }

    execute(context: BTContext): NodeState {
        for (let i = 0, n = this.children.length; i < n; i++) {
            const child = this.children[i];
            const result = child.execute(context);
            if (result === NodeState.Failure) {
                return NodeState.Failure;
            } else if (result === NodeState.Running) {
                return NodeState.Running;
            }
        }
        return NodeState.Success
    }
}