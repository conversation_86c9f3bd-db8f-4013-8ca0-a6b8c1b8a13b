import { CombineNode } from "./CombineNode";
import { NodeBase } from "./NodeBase";
import { NodeState } from "./NodeState";
import { NodeType } from "./NodeType";

/** 依次执行子节点, 遇到执行失败的则退出并返回失败, 全部执行成功则返回成功  */
export class LockedSequenceNode extends CombineNode {
    public currIdx = 0;
    public ignoreFailure = false;

    constructor(children: NodeBase[], ignoreFailture = false) {
        super(NodeType.LockedSequence, children);
        this.ignoreFailure = ignoreFailture;
    }

    onEnter(dt: number) : void {
        this.currIdx = 0;
        this.children[this.currIdx].onEnter(dt);
        this.state = NodeState.Running;
    }
    
    onUpdate(dt: number) : void {
        if(this.state !== NodeState.Running) return ;
        if(this.currIdx < 0 || this.currIdx >= this.children.length) {
            // 越界了, 不应该发生, 直接认为是失败了
            this.state = NodeState.Failure;
            return;
        }

        this.children[this.currIdx].onUpdate(dt);
        // context.executor.updateBTNode(this.children[this.currIdx], context);
        let state = this.children[this.currIdx].state;
        if(state == NodeState.Running) return;

        if(state === NodeState.Failure && !this.ignoreFailure) {
            this.state = NodeState.Failure;
            return;
        }
        if(state === NodeState.Success && this.currIdx == this.children.length-1) {
            this.state = NodeState.Success;
            return ;
        }
        this.children[++this.currIdx].onEnter(dt);
        // context.executor.onEnterBTNode(this.children[++this.currIdx], context);
    }
}