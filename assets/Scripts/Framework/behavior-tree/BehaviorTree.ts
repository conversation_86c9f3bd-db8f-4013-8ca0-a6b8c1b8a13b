import { BTContext } from "./BTContext";
import { NodeBase } from "./NodeBase";

/**
 * 行为树
 */
export class BehaviorTree {

    /** 行为树根节点 */
    root: NodeBase;

    /** 行为树环境类 */
    context: BTContext;

    constructor() {
    }

    onUpdate() {
        this.root && this.root.execute(this.context);
    }
}

























// /** 移动到目标位置后 返回成功 */
// export class WalkToPosNode extends NodeBase {
//     public speed: number;
//     public targetPos: cc.Vec2;
//     constructor(speed: number, pos: cc.Vec2) {
//         super(NodeType.WalkToPos);
//         this.speed = speed;
//         this.targetPos = pos;
//     }
// }

// export class WalkToRandomPosNode extends NodeBase {
//     public speed: number;
//     public size: cc.Size;
//     constructor(speed: number, size: cc.Size) {
//         super(NodeType.WalkToRandomPos);
//         this.speed = speed;
//         this.size = size;
//     }
// }

// export class WalkToTargetNode extends NodeBase {
//     public speed: number;
//     constructor(speed: number) {
//         super(NodeType.WalkToTarget);
//         this.speed = speed;
//     }
// }

// export class MonitorNode extends NodeBase {
//     constructor() {
//         super(NodeType.Monitor);
//     }
// }

// export class AttackNode extends NodeBase {
//     constructor() {
//         super(NodeType.Attack);
//     }
// }

// export class EnoughAttrNode extends NodeBase {
//     public com: { prototype: any };
//     public attr: string;
//     public value: number;
//     constructor(com: { prototype: any }, attr: string, value: number) {
//         super(NodeType.EnoughAttr);
//         this.com = com;
//         this.attr = attr;
//         this.value = value;
//     }
// }

// export class WillBeAttackedNode extends NodeBase {
//     constructor() {
//         super(NodeType.WillBeAttacked);
//     }
// }

// export class AvoidNode extends NodeBase {
//     public speed: number;
//     constructor(speed: number) {
//         super(NodeType.Avoid);
//         this.speed = speed;
//     }
// }

// export class RunAwayNode extends NodeBase {
//     constructor() {
//         super(NodeType.RunAway);
//     }
// }

// export class InAttackingNode extends NodeBase {
//     constructor() {
//         super(NodeType.InAttacking);
//     }
// }

// class NodeHandler {
//     onEnter: (node: NodeBase, dt: number) => void;
//     onUpdate: (node: NodeBase, dt: number) => void;
// }

// export const NodeHandlers: NodeHandler[] = [];

// /** Sequence node */
// NodeHandlers[NodeType.Sequence] = {
//     onEnter(node: SequenceNode, context: ExecuteContext): void {
//         node.currIdx = 0;
//         context.executor.onEnterBTNode(node.children[node.currIdx], context);
//         node.state = NodeState.Executing;
//     },
//     onUpdate(node: SequenceNode, context: ExecuteContext): void {

//         if (node.currIdx < 0 || node.currIdx >= node.children.length) {
//             // 越界了, 不应该发生, 直接认为是失败了
//             node.state = NodeState.Fail;
//             return;
//         }

//         // 检查前置条件是否满足
//         for (let i = 0; i < node.currIdx; i++) {
//             context.executor.updateBTNode(node.children[i], context);
//             if (node.children[i].state !== NodeState.Success) return;
//         }

//         context.executor.updateBTNode(node.children[node.currIdx], context);
//         let state = node.children[node.currIdx].state;
//         if (state == NodeState.Executing) return;

//         if (state === NodeState.Fail && !node.ignoreFailure) {
//             node.state = NodeState.Fail;
//             return;
//         }
//         if (state === NodeState.Success && node.currIdx == node.children.length - 1) {
//             node.state = NodeState.Success;
//             return;
//         }
//         context.executor.onEnterBTNode(node.children[++node.currIdx], context);
//     }
// };

// /** LockedSequence node */
// NodeHandlers[NodeType.LockedSequence] = {
//     onEnter(node: LockedSequenceNode, context: ExecuteContext): void {
//         node.currIdx = 0;
//         context.executor.onEnterBTNode(node.children[node.currIdx], context);
//         node.state = NodeState.Executing;
//     },
//     onUpdate(node: LockedSequenceNode, context: ExecuteContext): void {
//         if (node.state !== NodeState.Executing) return;
//         if (node.currIdx < 0 || node.currIdx >= node.children.length) {
//             // 越界了, 不应该发生, 直接认为是失败了
//             node.state = NodeState.Fail;
//             return;
//         }

//         context.executor.updateBTNode(node.children[node.currIdx], context);
//         let state = node.children[node.currIdx].state;
//         if (state == NodeState.Executing) return;

//         if (state === NodeState.Fail && !node.ignoreFailure) {
//             node.state = NodeState.Fail;
//             return;
//         }
//         if (state === NodeState.Success && node.currIdx == node.children.length - 1) {
//             node.state = NodeState.Success;
//             return;
//         }
//         context.executor.onEnterBTNode(node.children[++node.currIdx], context);
//     }
// };

// /** Selector node */
// NodeHandlers[NodeType.Selector] = {
//     onEnter(node: SelectorNode, context: ExecuteContext): void {
//         node.currIdx = 0;
//         context.executor.onEnterBTNode(node.children[node.currIdx], context);
//         node.state = NodeState.Executing;
//     },
//     onUpdate(node: SelectorNode, context: ExecuteContext): void {
//         if (node.state !== NodeState.Executing) return;
//         if (node.currIdx < 0 || node.currIdx >= node.children.length) {
//             // 越界了, 认为是失败了
//             node.state = NodeState.Fail;
//             return;
//         }

//         context.executor.updateBTNode(node.children[node.currIdx], context);
//         let state = node.children[node.currIdx].state;
//         if (state == NodeState.Executing) return;

//         // 执行到最后一个都失败了, 那边selector失败了
//         if (state === NodeState.Fail && node.currIdx == node.children.length - 1) {
//             node.state = NodeState.Fail;
//             return;
//         }
//         if (state == NodeState.Success) {
//             node.state = NodeState.Success;
//             return;
//         }
//         context.executor.onEnterBTNode(node.children[++node.currIdx], context);
//     }
// };

// /** Selector node */
// NodeHandlers[NodeType.RandomSelector] = {
//     onEnter(node: RandomSelectorNode, context: ExecuteContext): void {
//         // 根据权重随机获取idx
//         let totalWeight = 0;
//         for (const weight of node.weights) {
//             totalWeight += weight;
//         }
//         let randomWeight = Math.random() * totalWeight;
//         for (let i = 0; i < node.weights.length; i++) {
//             randomWeight -= node.weights[i];
//             if (randomWeight <= 0) {
//                 node.currIdx = i;
//                 break;
//             }
//         }
//         context.executor.onEnterBTNode(node.children[node.currIdx], context);
//         node.state = NodeState.Executing;
//     },
//     onUpdate(node: RandomSelectorNode, context: ExecuteContext): void {
//         if (node.state !== NodeState.Executing) return;
//         let n = node.children[node.currIdx];
//         context.executor.updateBTNode(n, context);
//         node.state = n.state;
//     }
// };

// /** Parallel node */
// NodeHandlers[NodeType.Parallel] = {
//     onEnter(node: ParallelNode, context: ExecuteContext): void {
//         for (const n of node.children) {
//             context.executor.onEnterBTNode(n, context);
//         }
//         node.state = NodeState.Executing;
//     },
//     onUpdate(node: ParallelNode, context: ExecuteContext): void {
//         if (node.state !== NodeState.Executing) return;
//         let end = true;
//         for (const child of node.children) {
//             context.executor.updateBTNode(child, context);
//             if (child.state === NodeState.Executing) {
//                 end = false;
//                 continue;
//             }

//             if (child.state == NodeState.Fail) {
//                 node.state = NodeState.Fail;
//                 return;
//             }
//         }
//         if (end) {
//             node.state = NodeState.Success;
//         }
//     }
// };

// /** Inverter node */
// NodeHandlers[NodeType.Inverter] = {
//     onEnter(node: InverterNode, context: ExecuteContext): void {
//         context.executor.onEnterBTNode(node.child, context);
//         node.state = NodeState.Executing;
//     },
//     onUpdate(node: InverterNode, context: ExecuteContext): void {
//         context.executor.updateBTNode(node.child, context);
//         if (node.child.state === NodeState.Executing) return;
//         if (node.child.state == NodeState.Success) node.state = NodeState.Fail;
//         if (node.child.state == NodeState.Fail) node.state = NodeState.Success;
//     }
// };

// /** Success node */
// NodeHandlers[NodeType.Success] = {
//     onEnter(node: SuccessNode, context: ExecuteContext): void {
//         context.executor.onEnterBTNode(node.child, context);
//         node.state = NodeState.Executing;
//     },
//     onUpdate(node: SuccessNode, context: ExecuteContext): void {
//         if (node.state !== NodeState.Executing) return;
//         context.executor.updateBTNode(node.child, context);
//         if (node.child.state === NodeState.Executing) return;
//         node.state = NodeState.Success;
//     }
// };

// /** Fail node */
// NodeHandlers[NodeType.Fail] = {
//     onEnter(node: FailNode, context: ExecuteContext): void {
//         context.executor.onEnterBTNode(node.child, context);
//         node.state = NodeState.Executing;
//     },
//     onUpdate(node: FailNode, context: ExecuteContext): void {
//         if (node.state !== NodeState.Executing) return;
//         context.executor.updateBTNode(node.child, context);
//         if (node.child.state === NodeState.Executing) return;
//         node.state = NodeState.Fail;
//     }
// };

// /** Repeater node */
// NodeHandlers[NodeType.Repeater] = {
//     onEnter(node: RepeaterNode, context: ExecuteContext): void {
//         node.currRepeatCount = 0;
//         context.executor.onEnterBTNode(node.child, context);
//         node.state = NodeState.Executing;
//     },
//     onUpdate(node: RepeaterNode, context: ExecuteContext): void {
//         if (node.state !== NodeState.Executing) return;
//         context.executor.updateBTNode(node.child, context);
//         if (node.child.state === NodeState.Executing) return;
//         if (!node.mustSuccess || node.child.state == NodeState.Success) node.currRepeatCount++;
//         if (node.currRepeatCount >= node.repeatCount) {
//             node.state = NodeState.Success;
//             return;
//         }
//         context.executor.onEnterBTNode(node.child, context);
//     }
// };


// /** RetryTillSuccess node */
// NodeHandlers[NodeType.RetryTillSuccess] = {
//     onEnter(node: RetryTillSuccess, context: ExecuteContext): void {
//         node.countDown = node.timeout;
//         context.executor.onEnterBTNode(node.child, context);
//         node.state = NodeState.Executing;
//     },

//     onUpdate(node: RetryTillSuccess, context: ExecuteContext): void {
//         if (node.state !== NodeState.Executing) return;
//         node.countDown -= context.dt;

//         context.executor.updateBTNode(node.child, context);
//         if (node.child.state === NodeState.Executing) return;

//         if (node.child.state == NodeState.Success) {
//             node.state = NodeState.Success;
//             return;
//         }

//         if (node.countDown > 0) {
//             context.executor.onEnterBTNode(node.child, context);
//             return;
//         }
//         node.state = NodeState.Fail;
//     }
// };


// /** Wait node */
// NodeHandlers[NodeType.Wait] = {
//     onEnter(node: WaitNode, context: ExecuteContext): void {
//         node.countDown = node.waitSeconds;
//         node.state = NodeState.Executing;
//     },
//     onUpdate(node: WaitNode, context: ExecuteContext): void {
//         if (node.state !== NodeState.Executing) return;
//         node.countDown -= context.dt;
//         if (node.countDown <= 0) {
//             node.state = NodeState.Success;
//         }
//     }
// };

// /** Wait node */
// NodeHandlers[NodeType.WalkToPos] = {
//     onEnter(node: WalkToPosNode, context: ExecuteContext): void {
//         let comTrans = context.world.getComponent(context.entity, ComTransform);
//         let comMovable = context.world.getComponent(context.entity, ComMovable);
//         comMovable.pointIdx = 0;
//         comMovable.points.length = 0;
//         comMovable.points.push(cc.v2(comTrans.x, comTrans.y), node.targetPos);
//         comMovable.speed = node.speed;
//         comMovable.speedDirty = true;
//         comMovable.keepDir = false;
//         node.state = NodeState.Executing;
//         comMovable.running = false;
//     },
//     onUpdate(node: WalkToPosNode, context: ExecuteContext): void {
//         if (node.state !== NodeState.Executing) return;
//         let comMovable = context.world.getComponent(context.entity, ComMovable);
//         if (comMovable.points.length == 0 || comMovable.pointIdx < 0 || comMovable.pointIdx >= comMovable.points.length) {
//             node.state = BT.NodeState.Success;
//         }
//     }
// };

// /** WalkToRandomPos node */
// NodeHandlers[NodeType.WalkToRandomPos] = {
//     onEnter(node: WalkToRandomPosNode, context: ExecuteContext): void {
//         let comTrans = context.world.getComponent(context.entity, ComTransform);
//         let comMovable = context.world.getComponent(context.entity, ComMovable);
//         comMovable.pointIdx = 0;
//         comMovable.points.length = 0;
//         let targetX = node.size.width * Math.random() - node.size.width / 2;
//         let targetY = node.size.height * Math.random() - node.size.height / 2 - 150;
//         comMovable.points.push(cc.v2(comTrans.x, comTrans.y), cc.v2(targetX, targetY));
//         comMovable.speed = node.speed;
//         comMovable.speedDirty = true;
//         comMovable.keepDir = false;
//         node.state = NodeState.Executing;
//         comMovable.running = false;
//     },
//     onUpdate(node: WalkToPosNode, context: ExecuteContext): void {
//         if (node.state !== NodeState.Executing) return;
//         let comMovable = context.world.getComponent(context.entity, ComMovable);
//         if (comMovable.points.length == 0 || comMovable.pointIdx < 0 || comMovable.pointIdx >= comMovable.points.length) {
//             node.state = BT.NodeState.Success;
//         }
//     }
// };

// /** WalkToTarget node */
// NodeHandlers[NodeType.WalkToTarget] = {
//     onEnter(node: WalkToTargetNode, context: ExecuteContext): void {
//         let comTrans = context.world.getComponent(context.entity, ComTransform);
//         let comMovable = context.world.getComponent(context.entity, ComMovable);
//         let comMonitor = context.world.getComponent(context.entity, ComMonitor);
//         if (comMonitor.others.length <= 0) return;
//         let target = context.world.getComponent(comMonitor.others[0], ComTransform);
//         let xOffdet = Math.sign(comTrans.x - target.x) * 80;
//         comMovable.pointIdx = 0;
//         comMovable.points.length = 0;
//         comMovable.points.push(cc.v2(comTrans.x, comTrans.y), cc.v2(target.x + xOffdet, target.y));
//         comMovable.speed = node.speed;
//         comMovable.speedDirty = true;
//         comMovable.keepDir = false;
//         node.state = NodeState.Executing;
//         comMovable.running = false;

//     },
//     onUpdate(node: WalkToTargetNode, context: ExecuteContext): void {
//         if (node.state !== NodeState.Executing) return;
//         let comTrans = context.world.getComponent(context.entity, ComTransform);
//         let comMonitor = context.world.getComponent(context.entity, ComMonitor);
//         let comMovable = context.world.getComponent(context.entity, ComMovable);
//         if (comMonitor.others.length <= 0) {
//             node.state = BT.NodeState.Fail;
//             return;
//         }
//         if (comMovable.points.length == 0 || comMovable.pointIdx < 0 || comMovable.pointIdx >= comMovable.points.length) {
//             let target = context.world.getComponent(comMonitor.others[0], ComTransform);

//             comTrans.dir.x = Math.abs(comTrans.dir.x) * -Math.sign(comTrans.x - target.x)
//             node.state = BT.NodeState.Success;
//             return;
//         }
//         if (comMonitor.others.length <= 0) {
//             node.state = BT.NodeState.Fail;
//             return;
//         }
//         let target = context.world.getComponent(comMonitor.others[0], ComTransform);
//         let xOffdet = Math.sign(comTrans.x - target.x) * 80;
//         comMovable.points[1].x = target.x + xOffdet;
//         comMovable.points[1].y = target.y;
//     }
// };

// /** Monitor node */
// NodeHandlers[NodeType.Monitor] = {
//     onEnter(node: MonitorNode, context: ExecuteContext): void {
//         let comMonitor = context.world.getComponent(context.entity, ComMonitor);
//         if (!comMonitor) return;
//         node.state = NodeState.Executing;
//     },
//     onUpdate(node: MonitorNode, context: ExecuteContext): void {
//         let comMonitor = context.world.getComponent(context.entity, ComMonitor);
//         node.state = comMonitor.others.length > 0 ? BT.NodeState.Success : BT.NodeState.Fail;
//     }
// };

// /** Monitor node */
// NodeHandlers[NodeType.Attack] = {
//     onEnter(node: AttackNode, context: ExecuteContext): void {
//         node.state = NodeState.Executing;

//         let comCocosNode = context.world.getComponent(context.entity, ComCocosNode);
//         if (!comCocosNode.loaded) return;

//         comCocosNode.events.push(new (EventAttack));
//         let comAttackable = context.world.getComponent(context.entity, ComAttackable);
//         comAttackable.countDown = comAttackable.duration;
//         comAttackable.dirty = true;
//         comAttackable.debugInfo = null;


//         comAttackable.willHurtFrame = 0.8;
//         comAttackable.willHurtFrameCompleted = false;

//         comAttackable.hurtFrame = 0.5;
//         comAttackable.hurtFrameCompleted = false;


//     },
//     onUpdate(node: AttackNode, context: ExecuteContext): void {
//         if (node.state !== NodeState.Executing) return;
//         let comAttackable = context.world.getComponent(context.entity, ComAttackable);

//         if (comAttackable.countDown <= 0) {
//             node.state = NodeState.Success;
//         }
//     }
// };

// /** EnoughAttr node */
// NodeHandlers[NodeType.EnoughAttr] = {
//     onEnter(node: EnoughAttrNode, context: ExecuteContext): void {
//         let com = context.world.getComponent(context.entity, node.com);
//         if (!com) return;
//         node.state = NodeState.Executing;
//     },
//     onUpdate(node: EnoughAttrNode, context: ExecuteContext): void {
//         let com = context.world.getComponent(context.entity, node.com);
//         if (!com) return;
//         node.state = com[node.attr] >= node.value ? NodeState.Success : NodeState.Fail;
//     }
// };

// /** WillBeAttacked node */
// NodeHandlers[NodeType.WillBeAttacked] = {
//     onEnter(node: WillBeAttackedNode, context: ExecuteContext): void {
//         let comBeAttacked = context.world.getComponent(context.entity, ComBeAttacked);

//         if (!comBeAttacked) return;
//         node.state = NodeState.Executing;
//     },
//     onUpdate(node: WillBeAttackedNode, context: ExecuteContext): void {
//         let comBeAttacked = context.world.getComponent(context.entity, ComBeAttacked);
//         let comMonitor = context.world.getComponent(context.entity, ComMonitor);
//         let monitor = comMonitor.others && comMonitor.others.indexOf(comBeAttacked.attacker) !== -1;
//         if (!comBeAttacked) return;

//         node.state = (monitor && comBeAttacked.attacker !== -1 && Math.random() > 0.8) ? NodeState.Success : NodeState.Fail;
//     }
// };

// /** Avoid node */
// NodeHandlers[NodeType.Avoid] = {
//     onEnter(node: AvoidNode, context: ExecuteContext): void {
//         let comTrans = context.world.getComponent(context.entity, ComTransform);
//         let comMovable = context.world.getComponent(context.entity, ComMovable);
//         comMovable.pointIdx = 0;
//         comMovable.points.length = 0;

//         let selfPoint = cc.v2(comTrans.x, comTrans.y);
//         comMovable.points.push(selfPoint, selfPoint.sub(cc.v2(50 * Math.sign(comTrans.dir.x), 0)));
//         comMovable.speed = node.speed;
//         comMovable.speedDirty = true;
//         comMovable.running = false;
//         comMovable.keepDir = true;


//         node.state = NodeState.Executing;
//     },
//     onUpdate(node: AvoidNode, context: ExecuteContext): void {
//         if (node.state !== NodeState.Executing) return;
//         let comMovable = context.world.getComponent(context.entity, ComMovable);
//         if (!comMovable) {
//             node.state = BT.NodeState.Fail;
//             return;
//         }

//         if (comMovable.points.length == 0 || comMovable.pointIdx < 0 || comMovable.pointIdx >= comMovable.points.length) {
//             node.state = BT.NodeState.Success;
//         }
//     }
// };

// /** NotInAttack node */
// NodeHandlers[NodeType.InAttacking] = {
//     onEnter(node: InAttackingNode, context: ExecuteContext): void {
//         let comAttackable = context.world.getComponent(context.entity, ComAttackable);
//         if (!comAttackable) return;
//         node.state = NodeState.Executing;
//     },
//     onUpdate(node: InAttackingNode, context: ExecuteContext): void {
//         let comAttackable = context.world.getComponent(context.entity, ComAttackable);
//         if (!comAttackable) return;

//         node.state = (comAttackable.countDown > 0 && comAttackable.countDown < comAttackable.duration - 0.2) ? NodeState.Success : NodeState.Fail;
//     }
// };


