
import { BTContext } from "./BTContext";
import { DecoratorNode } from "./DecoratorNode";
import { NodeBase } from "./NodeBase";
import { NodeState } from "./NodeState";
import { NodeType } from "./NodeType";

/** 返回子节点执行结果的取反值 */
export class InverterNode extends DecoratorNode {
    constructor(child: NodeBase) {
        super(NodeType.Inverter, child);
    }

    execute(context: BTContext): NodeState {
        const result = this.child.execute(context);
        if (result === NodeState.Running) {
            return NodeState.Running;
        } else if (result === NodeState.Failure) {
            return NodeState.Success;
        } else if (result === NodeState.Success) {
            return NodeState.Failure;
        }
    }
}