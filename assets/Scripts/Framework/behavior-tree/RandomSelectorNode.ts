import { BTContext } from "./BTContext";
import { CombineNode } from "./CombineNode";
import { NodeBase } from "./NodeBase";
import { NodeState } from "./NodeState";
import { NodeType } from "./NodeType";

/** 根据权重随机选择执行某个子节点 */
export class RandomSelectorNode extends CombineNode {
    public weights: number[];       // 权重
    constructor(children: NodeBase[], weights?: number[]) {
        super(NodeType.RandomSelector, children);
        this.weights = weights ? weights : new Array(children.length).fill(1);
    }

    execute(context: BTContext): NodeState {
        // 根据权重随机获取idx
        let totalWeight = 0;
        let randomIndex = 0;
        for (const weight of this.weights) {
            totalWeight += weight;
        }
        let randomWeight = Math.random() * totalWeight;
        for (let i = 0; i < this.weights.length; i++) {
            randomWeight -= this.weights[i];
            if (randomWeight <= 0) {
                randomIndex = i;
                break;
            }
        }
        const selectedNode = this.children[randomIndex];
        return selectedNode.execute(context);
    }
}