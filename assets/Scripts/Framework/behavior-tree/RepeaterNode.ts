import { BTContext } from "./BTContext";
import { DecoratorNode } from "./DecoratorNode";
import { NodeBase } from "./NodeBase";
import { NodeState } from "./NodeState";
import { NodeType } from "./NodeType";

/** 子节点执行重复repeatCount次后返回成功 */
export class RepeaterNode extends DecoratorNode {
    public repeatCount = 1;
    public currentCount = 0;
    constructor(child: NodeBase, repeatCount: number) {
        super(NodeType.Repeater, child);
        this.repeatCount = repeatCount;
        this.currentCount = 0;
    }

    execute(context: BTContext): NodeState {
        if (this.currentCount < this.repeatCount) {
            const result = this.child.execute(context);
            if (result === NodeState.Success) {
                // 子节点成功执行，增加计数
                this.currentCount++;
                return NodeState.Running; // 继续执行
            } else if (result === NodeState.Running) {
                return NodeState.Running;
            } else {
                // 子节点失败
                return NodeState.Failure;
            }
        } else {
            // 达到重复次数
            return NodeState.Success;
        }
    }
}