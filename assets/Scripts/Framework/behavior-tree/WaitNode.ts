import { BTContext } from "./BTContext";
import { NodeBase } from "./NodeBase";
import { NodeState } from "./NodeState";
import { NodeType } from "./NodeType";

/** 等待结点 */
export class WaitNode extends NodeBase {

    public duration: number;

    public elapsedTime: number;

    /**
     * 等待多少秒的节点
     * @param duration 单位秒
     */
    constructor(duration: number) {
        super(NodeType.Wait);
        this.duration = duration;
        this.elapsedTime = 0;
    }

    execute(context: BTContext): NodeState {
        this.elapsedTime += context.dt;
        if (this.elapsedTime >= this.duration) {
            this.elapsedTime = 0;
            return NodeState.Success;
        }
        return NodeState.Running;
    }
}