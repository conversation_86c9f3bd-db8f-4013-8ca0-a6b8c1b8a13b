/** 节点类型 */
export enum NodeType {
    // 组合节点
    LockedSequence,             // 锁状态顺序节点
    Sequence,                   // 顺序节点
    Selector,                   // 选择节点
    RandomSelector,             // 随机选择节点
    Parallel,                   // 并行节点

    // 修饰节点
    Inverter,                   // 逆变节点
    Success,                    // 成功节点
    Fail,                       // 失败节点
    Repeater,                   // 重复节点
    RetryTillSuccess,           // 重复直到成功

    // 叶子结点
    Wait,                       // 等待
    Action,
    // WalkToPos,
    // WalkToRandomPos,            // 
    // WalkToTarget,
    // Monitor,                     // 监视
    // Attack,

    // EnoughAttr,                 // 足够的属性
    // WillBeAttacked,             // 即将收到攻击
    // Avoid,                      // 闪避
    // RunAway,                    // 逃跑          
    // InAttacking

}