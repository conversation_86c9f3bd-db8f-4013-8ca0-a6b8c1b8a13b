import { BTContext } from "./BTContext";
import { CombineNode } from "./CombineNode";
import { NodeBase } from "./NodeBase";
import { NodeState } from "./NodeState";
import { NodeType } from "./NodeType";

/** 并行执行所有子节点, 全部执行完毕后返回 */
export class ParallelNode extends CombineNode {
    public ignoreFailture = true;
    constructor(children: NodeBase[], ignoreFailture: boolean) {
        super(NodeType.Parallel, children);
        this.ignoreFailture = ignoreFailture;
    }

    execute(context: BTContext): NodeState {
        let successCount = 0;
        let runningCount = 0;
        for (const child of this.children) {
            const result = child.execute(context);
            if (result === NodeState.Success) {
                successCount++;
            } else if (result === NodeState.Running) {
                runningCount++;
            }
        }
        if (runningCount > 0) {
            return NodeState.Running;
        } else if (successCount > 0) {
            return NodeState.Success;
        } else {
            return NodeState.Failure;
        }
    }
}
