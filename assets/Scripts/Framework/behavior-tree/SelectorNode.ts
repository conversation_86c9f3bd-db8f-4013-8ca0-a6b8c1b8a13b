import { BTContext } from "./BTContext";
import { CombineNode } from "./CombineNode";
import { NodeBase } from "./NodeBase";
import { NodeState } from "./NodeState";
import { NodeType } from "./NodeType";

/** 依次执行子节点, 遇到执行成功的则退出并返回成功, 全部执行失败则返回失败 */
export class SelectorNode extends CombineNode {
    public currIdx: number = -1;

    constructor(children: NodeBase[],) {
        super(NodeType.Selector, children);
    }

    execute(context: BTContext): NodeState {
        for (const child of this.children) {
            const result = child.execute(context);
            if (result === NodeState.Success) {
                return NodeState.Success;
            } else if (result === NodeState.Running) {
                return NodeState.Running;
            }
        }
        return NodeState.Failure;
    }
}