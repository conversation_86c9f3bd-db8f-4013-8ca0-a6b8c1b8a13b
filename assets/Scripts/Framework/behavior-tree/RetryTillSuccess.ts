import { BTContext } from "./BTContext";
import { DecoratorNode } from "./DecoratorNode";
import { NodeBase } from "./NodeBase";
import { NodeState } from "./NodeState";
import { NodeType } from "./NodeType";

/** 子节点重复执行直到返回成功 */
export class RetryTillSuccess extends DecoratorNode {
    timeout: number;    // 超时时间
    countDown: number;  // 剩余时间
    constructor(child: NodeBase, timeout: number) {
        super(NodeType.RetryTillSuccess, child);
        this.timeout = timeout;
    }

    execute(context: BTContext): NodeState {
        this.countDown -= context.dt;
        if(this.countDown <= 0) {
            return NodeState.Failure;
        }
        let result = this.child.execute(context);
        if(result === NodeState.Running) {
            return NodeState.Running;
        }
        if(result === NodeState.Failure) {
            return;
        }
        return NodeState.Success;
    }
}