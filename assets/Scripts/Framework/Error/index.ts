/**
 * Custom Error class with error code and chaining support
 */
export class CustomError extends Error {
    public readonly code: number;
    public readonly cause?: Error;
    public readonly timestamp: number;

    constructor(message: string, code: number, cause?: Error) {
        super(message);
        
        // Ensure proper prototype chain for instanceof checks
        Object.setPrototypeOf(this, CustomError.prototype);
        
        this.name = 'CustomError';
        this.code = code;
        this.cause = cause;
        this.timestamp = Date.now();
    }

    /**
     * Get the root cause of the error chain
     */
    public getRootCause(): Error {
        let currentError: Error | undefined = this;
        while (currentError instanceof CustomError && currentError.cause) {
            currentError = currentError.cause;
        }
        return currentError || this;
    }

    /**
     * Get the full error chain as an array
     */
    public getErrorChain(): Error[] {
        const chain: Error[] = [this];
        let currentError: Error | undefined = this;
        
        while (currentError instanceof CustomError && currentError.cause) {
            chain.push(currentError.cause);
            currentError = currentError.cause;
        }
        
        return chain;
    }

    /**
     * Check if this error or any error in the chain has a specific code
     */
    public hasCode(code: number): boolean {
        return this.getErrorChain().some(error => 
            error instanceof CustomError && error.code === code
        );
    }

    /**
     * Get the first error in the chain with a specific code
     */
    public getErrorByCode(code: number): CustomError | null {
        for (const error of this.getErrorChain()) {
            if (error instanceof CustomError && error.code === code) {
                return error;
            }
        }
        return null;
    }

    /**
     * Convert error to a plain object for serialization
     */
    public toJSON(): object {
        return {
            name: this.name,
            message: this.message,
            code: this.code,
            timestamp: this.timestamp,
            stack: this.stack,
            cause: this.cause ? (this.cause instanceof CustomError ? this.cause.toJSON() : {
                name: this.cause.name,
                message: this.cause.message,
                stack: this.cause.stack
            }) : undefined
        };
    }

    /**
     * Create a new error with additional context
     */
    public wrap(message: string, code?: number): CustomError {
        return new CustomError(
            message,
            code || this.code,
            this
        );
    }
}

/**
 * Helper function to create errors with predefined codes
 */
export function createError(message: string, code: number, cause?: Error): CustomError {
    return new CustomError(message, code, cause);
}

/**
 * Helper function to check if an error is a CustomError
 */
export function isCustomError(error: any): error is CustomError {
    return error instanceof CustomError;
}

/**
 * Helper function to safely get error code
 */
export function getErrorCode(error: Error): number {
    if (isCustomError(error)) {
        return error.code;
    }
    return -1;
}

export enum FrameErrorCode {
    UNKNOWN_ERROR = -1,
    OK = 0,
    JSON_PARSE_ERROR = 1,
    HTTP_ERROR = 2,
    NETWORK_ERROR = 3,
    TIMEOUT_ERROR = 4,
    INVALID_PARAMETER = 5,
    UNAUTHORIZED = 6,
}
