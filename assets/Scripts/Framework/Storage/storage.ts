import * as cc from "cc";

export function setItem(key: string, value: string) {
    cc.sys.localStorage.setItem(key, value);
}

export function getItem(key: string) {
    return cc.sys.localStorage.getItem(key);
}

export function removeItem(key: string) {
    cc.sys.localStorage.removeItem(key);
}

export function clear() {
    cc.sys.localStorage.clear();
}

export function getItemJson(key: string) {
    const value = getItem(key);
    if (value) {
        return JSON.parse(value);
    }
    return null;
}

export function setItemJson(key: string, value: any) {
    setItem(key, JSON.stringify(value));
}
