import * as cc from "cc";

import { _decorator, Component } from "cc";

import BaseView, { BaseViewStatus } from "../Components/BaseView";
import { ViewId, ViewZIndex } from "../../Config/ViewConst";
import { LoadUtils } from "../Utils/LoadUtils";
import { ViewUtil } from "../Utils/ViewUtils";
import LayerManagerComponent from "./LayerManagerComponent";
import UIButton from "../Components/UIButton";
import { SoundId, SoundType } from "../Module/Sound/SoundManagers";
import SoundManager from "./SoundManager";
import { GameEvent } from "../../GameEvent/Events";
import EventManagerComponent from "./EventManagerComponent";
import { AppConfig } from "../../Config/GameConfig";
import  Logger  from '../../Framework/Logger';
import Singleton from "../../Framework/Utils/Singleton";

const { ccclass, property } = _decorator;

@ccclass("ViewManager")
export default class ViewManager extends Singleton {

    private _views: { [viewId: string]: BaseView } = {};
    private _OneScreenView: ViewId[] = [];
    private _globalComp: cc.Component;

    public get views(): { [viewId: string]: BaseView } {
        return this._views;
    }

    public init() {
    }

    public dispose(): void {
        this._views = {};
    }


    public setGlobalComp(globalComp: cc.Component) {
        this._globalComp = globalComp;
    }

    public showBack(ui_url: ViewId, viewParam = {}, single = false) {
        const layerCom: cc.Node = LayerManagerComponent.instance().getBackLayer();
        this.show(ui_url, viewParam, single, layerCom);
    }

    /**
     * 显示view
     * single = true 只显示顶层面板，隐藏背后面板
     */
    public show(ui_url: ViewId, viewParam = {}, single = false, parent: cc.Node = null, showMask: boolean = true, isShowAni = false, fullScreen = false) {
        Logger.trace("show view" + ui_url);
        console.log("打开面板:" + ui_url);
        
        const layerCom: cc.Node = parent ? parent : LayerManagerComponent.instance().getLayer();
        if (!layerCom) {
            cc.error("scene not init layermanager , add super.onLoad() in scene!");
            return;
        }

        let oldView = this._views[ui_url];
        if (oldView) {
            Logger.trace("panel is open" + ui_url);
            return;
        }

        if (single) {
            for (let k in this._views) {
                if (!this._views[k] || !this._views[k].node) {
                    console.warn("no node baseview", k);
                    continue;
                }
                this._views[k].node.active = false;
            }
        }

        let panelParams = viewParam
        let onCompleteUI2 = (panel2: cc.Node) => {
            this.hideLoading();

            Logger.logView("on load ui complete" + ui_url);
            let bv = panel2.getComponent(BaseView);
            bv.viewData = panelParams;
            bv.url = ui_url;
            bv.isSingle = single;
            bv.loadStatus = BaseViewStatus.Loaded;
            bv.closeCB = () => {
                this.closeByUri(ui_url);
            }

            this.addView(bv);
            //显示黑色半透底
            showMask && this.showMask();
            layerCom.addChild(panel2);
            console.log("layer com", layerCom.name);
            EventManagerComponent.instance().dispatch(GameEvent.VIEW_SHOW, bv);
            if (isShowAni) bv['showAnimation'] = true;
            bv['fullScreen'] = fullScreen;
            EventManagerComponent.instance().dispatch(GameEvent.OPEN_VIEW_MAIN_UI_SHOW, !fullScreen);
        };


        let cache = LoadUtils.get(ui_url, cc.Prefab) as cc.Prefab;
        if (!cache) {
            let base = new BaseView();// 提前占位,避免重复打开，加载完成后会把这个移除
            base.url = ui_url;
            base.loadStatus = BaseViewStatus.Loading;
            this.showLoading();
            this.addView(base);
        }

        this.playSoundEffect(SoundId.OPENUI, SoundType.COMMON);
        LoadUtils.loadPrefabNode(ui_url, onCompleteUI2);

        // for (let k in this._views) {
        //     if (!parent && single) {
        //         this._OneScreenView.push(k as ViewId)
        //     }
        // }
        // for (let k in this._OneScreenView) {
        //     if (this._OneScreenView[k] != ui_url) {
        //         this.closeByUri(this._OneScreenView[k])
        //     }
        // }
    }

    public showDialog(ui_url: ViewId, param = {}) {
        this.show(ui_url, param, false, null, true, true)
    }

    /** 按URL名字保存baseView引用  */
    addView(baseView: BaseView) {
        Logger.logView("addView", baseView.url);
        this._views[baseView.url] = baseView;
    }

    /** 按URL名字获取baseView引用  */
    hasView(viewId: string): any {
        if (this._views[viewId] && this._views[viewId].loadStatus == BaseViewStatus.Loaded) {
            return this._views[viewId];
        }
        return null;
    }

    isOpened(url: ViewId): boolean {
        if (this._views[url] != null && this._views[url].loadStatus == BaseViewStatus.Loaded) {
            return true;
        }
        return false
    }

    /** 按URL名字关闭一个界面  */
    closeByUri(uri) {
        let baseView: BaseView = this._views[uri];
        if (baseView) {
            let isSingle = baseView.isSingle;
            baseView.closePanel();
            baseView = null;
            delete this._views[uri];
            EventManagerComponent.instance().dispatch(GameEvent.VIEW_CLOSE, uri);
            if (isSingle) {
                for (let k in this._views) {
                    this._views[k].node && (this._views[k].node.active = true);
                }
            }
            Logger.logView("closeByUri", uri);
            // for (let k in this._OneScreenView) {
            //     if (this._OneScreenView[k] != uri && isSingle) {
            //         this.show(this._OneScreenView[k])
            //     }
            //     this._OneScreenView.pop();
            // }
            let hasfullScreen = false;
            for (let k in this._views) {
                if (this._views[k]['fullScreen']) {
                    hasfullScreen = true;
                    break;
                }
            }
            if (hasfullScreen) {
                EventManagerComponent.instance().dispatch(GameEvent.OPEN_VIEW_MAIN_UI_SHOW, true);
            }
        }
        this.showMask();
    }

    /** 按BaseView  */
    close(view: BaseView) {
        let ui_url: string = view.url;
        this.closeByUri(ui_url);
    }

    getTopView(): BaseView {
        return null;
    }

    closeAll(url: string[] = null) {
        if (url == null) url = []
        for (let key in this._views) {
            if (url.indexOf(key) < 0)
                this.close(this._views[key]);
        }
    }


    private hideLoading() {
        EventManagerComponent.instance().dispatch(GameEvent.WAITING_HIDE, null);
    }

    showMask() {
        const layerCom: cc.Node = LayerManagerComponent.instance().getBackLayer();
        if (!layerCom || !layerCom.isValid) return;
        let temMask = layerCom.getChildByName("_mainMask");
        if (temMask == null) {
            temMask = new cc.Node("_mainMask");
            temMask.getComponent(cc.UITransform).setAnchorPoint(
                0.5, 0.5,
            )
            temMask.addComponent(cc.BlockInputEvents);

            let uiButton: UIButton = temMask.addComponent(UIButton);
            uiButton.touchSoundEnable = false;
            uiButton.scaleMax = 1;
            uiButton.scaleMin = 1;
            uiButton.setOnClick((event: cc.EventTouch) => {
                this.onClickMask(event);
            }, this);

            temMask.getComponent(cc.UITransform).setContentSize(
                AppConfig.initWidth * 10, AppConfig.initHeight * 2.5);

            LoadUtils.loadPrefab('ui/UIMask', () => {
                const newNode: cc.Node = ViewUtil.createPrefabNode('ui/UIMask');
                if (newNode != null) {
                    temMask.addChild(newNode);
                }
            });
            Logger.trace(temMask);
            layerCom.addChild(temMask);
            temMask.setSiblingIndex(ViewZIndex.MAX_ZINDEX);
        }

        temMask.active = false;

        // 判断黑底是否显示
        let tempv: BaseView = null;
        for (let k in this._views) {
            tempv = this._views[k];
            if (tempv.showMask) {
                temMask.active = true;
                break;
            }
        }

        if (Object.keys(this._views).length <= 0) {
            temMask.active = false;
        }
    }

    showLoading() {
        EventManagerComponent.instance().dispatch(GameEvent.WAITING_SHOW, null);
    }

    private onViewClose(v: BaseView) {
        this.closeByUri(v.url);
    }

    private onClickMask(event: cc.EventTouch) {
        let tempv: BaseView = null;
        for (let k in this._views) {
            tempv = this._views[k];
            if (tempv.showMask) {
                tempv.onClickMatte(event);
            }
        }
    }

    private playSoundEffect(soundId: string, soundType: SoundType) {
        this._globalComp.scheduleOnce(() => {
            const soundManager: SoundManager = SoundManager.instance();
            soundManager.playEffect(soundId, soundType);
        }, 100);
    }
}