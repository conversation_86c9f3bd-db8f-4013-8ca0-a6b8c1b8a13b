import * as cc from "cc";
import { _decorator } from "cc";
import Logger from "../../Framework/Logger";
import ViewManager from "./ViewManagerComponent";

const { ccclass, property } = _decorator;

@ccclass("GlobalComponent")
export default class GlobalComponent extends cc.Component {
    protected onLoad(): void {
        cc.director.addPersistRootNode(this.node);
        const viewManager = ViewManager.instance();
        viewManager.setGlobalComp(this);
    }
}
