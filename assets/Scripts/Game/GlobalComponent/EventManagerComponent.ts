import { _decorator, Component } from "cc";

const { ccclass, property } = _decorator;
/**
 * 这里注册 cocos 的事件实现，不能去除
 */
import * as CCDispatcher from "../../GameEvent/CCDispatcher";
import { EventDataMap, EventDispatcher } from "../../GameEvent/Events";
import Logger from "../../Framework/Logger";
import Singleton from "../../Framework/Utils/Singleton";

@ccclass
export default class EventManagerComponent extends Singleton {

    public dispatch<K extends keyof EventDataMap>(eventName: K, data: EventDataMap[K]): void {
        Logger.logView("dispatch", eventName as string);
        EventDispatcher.dispatch(eventName, data);
    }

    public addEventListener<K extends keyof EventDataMap>(eventName: K, callback: (data: EventDataMap[K]) => void, thisArgs?: any): void {
        EventDispatcher.on(eventName, callback, thisArgs);
    }

    public removeEventListener<K extends keyof EventDataMap>(eventName: K, callback: (data: EventDataMap[K]) => void, thisArgs?: any): void {
        EventDispatcher.off(eventName, callback);
    }

    public targetOff(target: any): void {
        EventDispatcher.targetOff(target);
    }
}