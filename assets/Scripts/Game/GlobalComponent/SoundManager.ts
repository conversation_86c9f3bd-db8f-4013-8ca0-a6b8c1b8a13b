import { _decorator, Component } from "cc";
import Singleton from "../../Framework/Utils/Singleton";

const { ccclass, property } = _decorator;

/**音效ID */
export enum SoundId {
    /**世界地图 */
    WORLD_MAP = "bg/city",
    /**副本场景 */
    BATTLE_COPY = "bg/fight",
    /**点击音效 */
    CLICK = "common/click2",
    /**技能音效 */
    SKILL = "skill/",
    /**npc音效 */
    NPC = "npc/",
    /**砍树音效 */
    CHOP = "common/Chop_wood",
    /**挖矿音效 */
    MINING = "common/mining",
    /**弹出界面音效 */
    OPENUI = "common/openUI",
    /**关闭界面音效 */
    CLOSEUI = "common/closeUI",
    /**传送音效 */
    CS = "common/chuansong",
    /**获得英雄音效 */
    HUODE_HERO = "common/huodekapai",
    /**提交物品和获得资源音效 */
    HUODE_ZY = "common/huodeziyuan",
    /**驱散迷雾音效 */
    MIWU = "common/qusanmiwu",
    /**完成任务音效 */
    TASK = "common/task",
    /**点亮技能点音效 */
    DIANLIANG = "common/dianliang",
    /**升级/领取宝箱音效 */
    LEVEL_UP = "common/levelUP",
    /**灯切换音效1 */
    XUANZEKA1 = "common/choose3",
    /**灯切换音效2 */
    XUANZEKA2 = "common/choose2",
    /**获得卡/功能开启音效 */
    HUODE_KA = "common/huodekapian",
    /**获得道具音效 */
    HUODE_ITEM = "common/huodejaingli",
    /**副本通关音效 */
    VICTOR = "common/victor",
    /**建造成功 */
    JZBUILD = "common/jianzaochenggong",
    /**副本失败音效 */
    FAIL = "common/fail",
    /**脚步 */
    JIAOBU = "common/jiaobu",
    /**卡牌开门 */
    KAIMEN = "common/kaimen",
    /**卡牌关门 */
    GUANMEN = "common/guanmen",
    /**游戏开场 */
    GAMEOPEN = "common/open",
}

/**音效类型 */
export enum SoundType {
    /**通用音效 */
    COMMON,
    /**攻击音效 */
    ATTACK,
    /**技能音效 */
    SKILL,
    /**npc音效 */
    NPC,
    /**点击音效 */
    CLICK,
}


@ccclass("SoundComponent")
export default class SoundManager extends Singleton {

    /**音效开关 */
    private _effectOn: boolean;
    /**背景音乐开关 */
    private _bgOn: boolean;
    /**音效音量 */
    private _effectVolume: number;
    /**背景音乐音量 */
    private _bgVolume: number;
    /**当前背景音乐 */
    private _currBgName: string;
    /**上一个背景音乐 */
    private _lastBgName: string;

    /**音乐淡出间隔时间 */
    private readonly MusicFadeInterval: number = 1;

    /**最小音量 */
    private readonly MinMusicVolume: number = 0;
    /**最大音量 */
    private readonly MaxMusicVolume: number = 0.5;

    private _clickTime: number = 0;

    onLoad(): void {
        
    }

    playEffect(name: string, type: SoundType, loop: boolean = false): void {
    }

    playClickEffect() {
        //音效按钮有效点击时间
        let time: number = new Date().getTime();
        if ((time - this._clickTime) < 300) return;
        this._clickTime = time;
        this.playEffect(SoundId.CLICK, SoundType.CLICK);
    }
}
