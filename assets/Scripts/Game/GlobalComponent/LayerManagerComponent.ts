import * as cc from "cc";

import SceneBase from "../Components/SceneBase";

import { _decorator, Component } from "cc";
import Singleton from "../../Framework/Utils/Singleton";

const { ccclass, property } = _decorator;

@ccclass("LayerManagerComponent")
export default class LayerManagerComponent extends Singleton {

    windowLayer: cc.Node = null;

    netLayer: cc.Node = null;

    tipsLayer: cc.Node = null;

    windowBackLayer: cc.Node = null;

    setGameScene(scene: SceneBase): void {
        this.init(scene);
    }

    /**
     * 初始化
     */
    private init(scene: SceneBase): void {
        this.windowLayer = scene.uiLayer;
        this.netLayer = scene.networkLayer;
        this.tipsLayer = scene.tipLayer;
    }

    dispose(): void {
        this.windowLayer = null;
        this.netLayer = null;
        this.tipsLayer = null;
        this.windowBackLayer = null;
    }

    /**
     * 获取层级GComponent节点
     * @param layer
     */
    public getLayer(layer: string = ""): cc.Node {
        return this.windowLayer;
    }

    getBackLayer(): cc.Node {
        return this.windowBackLayer;
    }

    getNetLayer(): cc.Node {
        return this.netLayer;
    }

    getTipsLayer(): cc.Node {
        return this.tipsLayer;
    }
}