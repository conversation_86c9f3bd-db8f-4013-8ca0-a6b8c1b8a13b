/**加载状态*/
import * as cc from 'cc';
import  Logger  from '../../Framework/Logger';

export enum LoaderState {
    None,//无加载
    Doing,//加载中
    Done//已加载
}
/**
 * 资源数据结构
 */
export class BaseAssetInfo {
    private _count = 0;//当前资源引用计数
    private _path: string;//资源路径

    constructor(path: string) {
        this._path = path;
    }

    public get path(): string {
        return this._path;
    }

    public get count(): number {
        return this._count;
    }
    public resetCount(): void {
        this._count = 0;
    }
    /**
     * 计数++
     */
    public addRef() {
        this._count++;
    }
    /**
     * 计数--
     */
    public decRef(num: number = 1) {
        this._count = this._count - num;
        if (this._count < 0) {
            console.error("资源引用计数减多了")
        }
        if (this._count <= 0) {
            this.destroy();
        }
    }
    /**销毁此资源 */
    protected destroy() {

    }
}

/**
 * Bundle 的引用计数管理
 */
export class ABAssetInfo extends BaseAssetInfo {
    private _abAsset: cc.AssetManager.Bundle;

    constructor(path: string, ab: cc.AssetManager.Bundle) {
        super(path);
        this._abAsset = ab;
    }

    public get abAsset(): cc.AssetManager.Bundle {
        return this._abAsset;
    }

    protected destroy() {
        LoadUtils.removeAb(this.path);
    }
}

export class AssetInfo extends BaseAssetInfo {
    private _asset: cc.Asset;
    private _abInfo: ABAssetInfo;
    private _resState: number;
    constructor(path: string) {
        super(path);
        this._resState = LoaderState.None;
    }
    public initAsset(asset: cc.Asset, abInfo: ABAssetInfo): void {
        asset.addRef();
        this._asset = asset;
        this._abInfo = abInfo;
        this._abInfo.addRef();
    }
    public set resState(val: number) {
        this._resState = val;
    }
    public get resState(): number {
        return this._resState
    }
    public get asset(): cc.Asset {
        return this._asset;
    }
    protected destroy() {
        if (this._asset) this._asset.decRef();
        if (this._abInfo) this._abInfo.decRef();
    }
}


export type AbAssetComplete = (err: Error | null, bundle: ABAssetInfo | null) => void;
export type AbAssetProgress = (percent: number) => void;

export type AssetProgress = (percent: number) => void;
export type AssetComplete = (assets: cc.Asset | cc.Asset[] | null | any) => void;
export type PrefabComplete = (assets: cc.Node | cc.Node[]) => void;
export type AbDoneComplete = (error: Error | null, resPath: string, bundle: ABAssetInfo | null) => void;
export class LoadUtils {

    /** 默认Bundle. 因为都是远程加载.所以这里在正式服是 http://www.xxx.com/res  */
    public static remoteBundle = "res";
    /** 远程地址 */
    public static remoteUrl = "";

    public static remoteOption = null;

    protected static _abInfoMap: { [key: string]: ABAssetInfo } = cc.js.createMap();
    protected static _assetInfoMap: { [key: string]: AssetInfo } = cc.js.createMap();

    /** 加载Prefab并创建 */
    public static loadPrefabNode(path: string, onComplete: PrefabComplete, onProgress?: AssetProgress) {
        this.loadPrefab(path, (prefab: cc.Prefab) => {
            if (prefab) {
                onComplete(cc.instantiate(prefab));
            } else {
                onComplete(null!);
            }
        }, onProgress)
    }

    /** 加载Prefab不创建 */
    public static loadPrefab(path: string, onComplete: AssetComplete, onProgress?: AssetProgress) {
        Logger.trace("加载Prefab", path);
        this.load(path, cc.Prefab, onComplete, onProgress);
    }

    /** 加载多个prefab */
    public static loadPrefabArray(path: string[], onComplete: AssetComplete, onProgress?: AssetProgress) {
        let self = this;
        let loadFunc = (path: string) => {
            Logger.trace("加载Prefab", path);
            return new Promise((resolve, reject) => {
                self.loadPrefab(path, (asset: cc.Asset) => {
                    resolve(asset);
                })
            });
        }

        let funcArr = [];
        for (let i = 0; i < path.length; i++) {
            funcArr.push(loadFunc(path[i]));
        }

        Promise.all(funcArr)
            .then((value: cc.Asset[]) => {
                onComplete && onComplete(value);
            })
            .catch(() => {
                console.error("加载错误");
            })
    }

    /** 获取一个资源 */
    static get<T extends cc.Asset>(path: string, type?: typeof cc.Asset | null, bundleName?: string): T | null {
        if (bundleName == null) bundleName = this.remoteBundle;
        var bundle: cc.AssetManager.Bundle = cc.assetManager.getBundle(bundleName)!;
        return bundle ? bundle.get(path, type) as T : null;
    }

    static preload(path: string, type: typeof cc.Asset, onComplete: AssetComplete, onProgress?: AssetProgress) {
        let bundleName = this.remoteBundle;
        let bundle: cc.AssetManager.Bundle = cc.assetManager.getBundle(bundleName);
        if (bundle) {
            bundle.preload(path, type, onProgress, onComplete);
        } else {
            cc.assetManager.loadBundle(this.remoteUrl + this.remoteBundle, this.remoteOption, (error, bundle: cc.AssetManager.Bundle) => {
                if (error) {
                    console.error("preload error!", error);
                } else {
                    bundle.preload(path, type, onProgress, onComplete);
                }
            })
        }
    }

    public static load(path: string, type: typeof cc.Asset, onComplete: AssetComplete, onProgress?: AssetProgress): void {
        Logger.logBusiness("加载资源", path);
        let assetInfo = this._assetInfoMap[path];
        if (!assetInfo) {
            assetInfo = new AssetInfo(path);
            this._assetInfoMap[path] = assetInfo;
        }
        assetInfo.addRef();
        if (assetInfo.resState == LoaderState.Done) {
            onComplete(assetInfo.asset);
        } else if (assetInfo.resState == LoaderState.None) {
            let abBase = 0.5;
            let self = this;
            this.checkLoadAb(path, (error: Error | null, resPath: string, abInfo: ABAssetInfo | null) => {
                if (error) {
                    //assetInfo.decRef();
                    console.error(`加载ab包失败---->${path}`);
                    onComplete(abInfo);
                } else {
                    let progressCall = (finished: number, total: number) => {
                        if (onProgress) onProgress(abBase + finished / total);
                    }
                    let completeCall = (error, asset: cc.Asset) => {
                        if (error == null && asset) {
                            assetInfo = self._assetInfoMap[path];
                            if (assetInfo) {
                                assetInfo.resState = LoaderState.Done;
                                if (!assetInfo.asset) assetInfo.initAsset(asset, abInfo);
                                onComplete(assetInfo.asset);
                            } else {
                                asset.decRef();
                                console.log("取消已加载回调")
                            }
                        } else {
                            console.error(`加载资源失败---->${path}`, error);
                            onComplete(null);

                        }
                    }
                    if (type == cc.SceneAsset) {
                        abInfo.abAsset.loadScene(resPath, type, progressCall, completeCall);
                    } else {
                        abInfo.abAsset.load(resPath, type, progressCall, completeCall);

                    }
                }
            }, (percent: number) => {
                onProgress && onProgress(percent * abBase);
            });
        }
    }

    private static checkLoadAb(pathName: string, onComplete: AbDoneComplete, onprogress?: AbAssetProgress) {
        let abName = this.remoteBundle;
        let path = pathName;
        // abName = this.remoteUrl + this.remoteBundle;
        let options = this.remoteOption;

        this.loadAb(abName, options, (error: Error | null, abInfo: ABAssetInfo | null) => {
            if (error) {
                onComplete(error, path, abInfo);
            } else {
                onComplete(error, path, abInfo);
            }
        }, onprogress)
    }

    //加载Ab包
    private static loadAb(abName: string, options: any, onComplete: AbAssetComplete, onProgress?: AbAssetProgress) {
        let abInfo = this._abInfoMap[abName];
        if (abInfo) {
            onProgress && onProgress(1);
            onComplete(null, abInfo);
        } else {
            if (abName == "resources") {
                abInfo = new ABAssetInfo(abName, cc.resources);
                this._abInfoMap[abName] = abInfo;
                onProgress && onProgress(1);
                onComplete(null, abInfo);
                return;
            } else {
                //已经加载到assetManager中了.
                let abAsset = cc.assetManager.getBundle(abName);
                if (abAsset) {
                    let oldInfo = this._abInfoMap[abName];
                    if (!oldInfo) {
                        oldInfo = new ABAssetInfo(abName, abAsset);
                        this._abInfoMap[abName] = oldInfo;
                    }
                    onProgress && onProgress(1);
                    onComplete(null, oldInfo);
                    return;
                }
                if (!options) {
                    options = {};
                }
                if (onProgress) {
                    options.onFileProgress = (loaded: number, total: number) => {
                        onProgress(loaded / total);
                    }
                } else {
                    options.onFileProgress = null;
                }
                let self = this;
                cc.assetManager.loadBundle(this.remoteUrl + abName, options, (err: Error | null, abAsset: cc.AssetManager.Bundle) => {
                    if (err == null) {
                        let oldInfo = this._abInfoMap[abName];
                        if (!oldInfo) {
                            oldInfo = new ABAssetInfo(abName, abAsset);
                            self._abInfoMap[abName] = oldInfo;
                        }
                        onComplete(null, oldInfo);
                    } else {
                        onComplete(err, null);
                    }
                });
            }
        }
    }
    /**
     * 移除AB包
     * @param abName 
     */
    public static removeAb(abName: string) {
        let asset = this._abInfoMap[abName];
        if (asset) {
            delete this._abInfoMap[abName];
            if (abName != "resources") cc.assetManager.removeBundle(asset.abAsset);
        }
    }

    /** 释放一次资源引用. 将次数-1 */
    public static releaseAsset(path: string, count: number = 1) {
        let assetInfo = this._assetInfoMap[path];
        console.log("releaseAsset", assetInfo, path);
        if (assetInfo) {
            assetInfo.decRef(count);
            if (assetInfo.count <= 0) {
                delete this._assetInfoMap[path];
                assetInfo = null;
            }
        }
    }

    public static get assetInfoMap(): { [key: string]: AssetInfo } {
        return this._assetInfoMap;
    }
}