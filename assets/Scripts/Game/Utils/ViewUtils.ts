import * as cc from "cc";
import { LoadUtils } from "./LoadUtils";


/** 预制件创建的节点上附加的Path全局属性 */
const PATH_PROPERTY = "$$Path";
/** 预制件创建的节点上附加的Bundle全局属性 */
const BUNDLE_PROPERTY = "$$Bundle";


/** 显示对象工具 */
export class ViewUtil {
    /**
     * 把Node当前的节点树结构根据Node命名转成一个js对象,重名的组件会覆盖，
     * Node的name不应该包含空格键，否则将跳过
     * @param parent 被遍历的Node组件
     * @param obj    绑定的js对象 (可选)
     */
    static nodeTreeInfoLite(parent: cc.Node, obj?: Map<string, cc.Node>): Map<string, cc.Node> | null {
        let map: Map<string, cc.Node> = obj || new Map();
        let items = parent.children;
        for (let i = 0; i < items.length; i++) {
            let _node = items[i];
            if (_node.name.indexOf(" ") < 0) {
                map.set(_node.name, _node);
            }
            ViewUtil.nodeTreeInfoLite(_node, map);
        }
        return map;
    }

    static fadeTo(node: cc.Node, fromOpacity: number, toOpacity: number, duration: number) {
        const uiCom = node.getComponent(cc.UIOpacity);
        if (uiCom) {
            uiCom.opacity = fromOpacity;
            return cc.tween(uiCom).to(duration, { opacity: toOpacity })
        }
        throw new Error("node must have a UIOpacity component");
    }

    /**
     * 正则搜索节点名字,符合条件的节点将会返回
     * @param reg     正则表达式
     * @param parent  要搜索的父节点
     * @param nodes   返回的数组（可选）
     */
    static findNodes(reg: RegExp, parent: cc.Node, nodes?: Array<cc.Node>): Array<cc.Node> {
        let ns: Array<cc.Node> = nodes || [];
        let items = parent.children;
        for (let i = 0; i < items.length; i++) {
            let _name: string = items[i].name;
            if (reg.test(_name)) {
                ns.push(items[i]);
            }
            ViewUtil.findNodes(reg, items[i], ns);
        }
        return ns;
    };

    // /**
    //  * 节点之间坐标互转
    //  * @param a         A节点
    //  * @param b         B节点
    //  * @param aPos      A节点空间中的相对位置
    //  */
    // static calculateASpaceToBSpacePos(a: cc.Node, b: cc.Node, aPos: cc.Vec3): cc.Vec3 {
    //     var world: cc.Vec3 = a.getComponent(UITransform)!.convertToWorldSpaceAR(aPos);
    //     var space: cc.Vec3 = b.getComponent(UITransform)!.convertToNodeSpaceAR(world);
    //     return space;
    // }

    // /**
    //  * 屏幕转空间坐标
    //  * @param event 触摸事件
    //  * @param space 转到此节点的坐标空间
    //  */
    // static calculateScreenPosToSpacePos(event: cc.Event.EventTouch, space: cc.Node): cc.Vec3 {
    //     let uil = event.getLocation();
    //     let worldPos: cc.Vec3 = cc.v3(uil.x, uil.y);
    //     let mapPos: cc.Vec3 = space.getComponent(UITransform)!.convertToNodeSpaceAR(worldPos);
    //     return mapPos;
    // }

    /**
     * 显示对象等比缩放
     * @param targetWidth       目标宽
     * @param targetHeight      目标高
     * @param defaultWidth      默认宽
     * @param defaultHeight     默认高
     */
    static uniformScale(targetWidth: number, targetHeight: number, defaultWidth: number, defaultHeight: number) {
        var widthRatio = defaultWidth / targetWidth;
        var heightRatio = defaultHeight / targetHeight;
        var ratio;
        widthRatio < heightRatio ? ratio = widthRatio : ratio = heightRatio;
        var size = new cc.Size(Math.floor(targetWidth * ratio), Math.floor(targetHeight * ratio));
        return size;
    }

    /**
     * 从资源缓存中找到预制资源名并创建一个显示对象
     * @param path 资源路径
     */
    static createPrefabNode(path: string, bundle?: string): cc.Node {
        var p: cc.Prefab = LoadUtils.get(path, cc.Prefab);
        if (p) {
            p.addRef();

            //添加自定义属性
            let node = cc.instantiate(p);
            node[PATH_PROPERTY] = path;
            node[BUNDLE_PROPERTY] = bundle;
            return node;
        }
        return null;
    }

    /**
     * 删除一个资源.
     * @param path 
     * @param bundle 
     */
    static removePrefabNode(path: string | cc.Node, bundle?: string) {
        if (path instanceof cc.Node) {
            //这两个不能调整顺序. 懂得都懂
            bundle = path[BUNDLE_PROPERTY];
            path = path[PATH_PROPERTY];
        }
        if (typeof path === "string") {
            LoadUtils.releaseAsset(path);
            // let prefab: cc.Prefab = LoadUtils.get(path, cc.Prefab) as cc.Prefab;
            // if (prefab) {
            //     prefab.decRef();
            //     prefab.refCount <= 0 && console.debug("清除资源", path, bundle);
            // }
        }
    }

    /**
     * 销毁节点以及节点下所有组件.
     * @param node 
     */
    static destroyAllComponent(node: cc.Node) {
        let components = node.getComponents(cc.Component);
        for (let i = 0; i < components.length; i++) {
            components[i].destroy();
        }
        let children = node.children;
        for (let i = 0; i < children.length; i++) {
            this.destroyAllComponent(children[i]);
        }
    }

    /** 销毁节点 */
    static destroyNode(node: cc.Node) {
        if (node && node.isValid) {
            // this.destroyAllComponent(node);
            node.removeFromParent();
            //移除一个引用可能会释放资源. 请确保资源不再被使用
            this.removePrefabNode(node);
            node.destroy();
        }
    }


    // /**
    //  * 加载预制并创建预制节点
    //  * @param path 资源路径
    //  */
    // static createPrefabNodeAsync(path: string, bundleName?: string): Promise<cc.Node> {
    //     return new Promise(async (resolve, reject) => {
    //         oops.res.load(bundleName, path, cc.Prefab, (err: Error | null, content: cc.Prefab) => {
    //             if (err) {
    //                 console.error(`名为【${path}】的资源加载失败`);
    //                 return;
    //             }

    //             var node = this.createPrefabNode(path, bundleName);
    //             resolve(node);
    //         });
    //     });
    // }

    // /**
    //  * 加载预制节点
    //  * @param path          资源路径
    //  * @param callback      资源加载完成回调
    //  */
    // static loadPrefabNode(path: string, callback: Function, bundleName?: string) {
    //     oops.res.load(bundleName, path, cc.Prefab, (err: Error | null, content: cc.Prefab) => {
    //         if (err) {
    //             console.error(`名为【${path}】的资源加载失败`);
    //             return;
    //         }

    //         var node = this.createPrefabNode(path, bundleName);
    //         callback(node);
    //     });
    // }

    /**
     * 添加节点动画
     * @param path              资源路径
     * @param node              目标节点
     * @param onlyOne           是否唯一
     * @param isDefaultClip     是否播放默认动画剪辑
     */
    static addNodeAnimation(path: string, node: cc.Node, onlyOne: boolean = true, isDefaultClip: boolean = false) {
        if (!node || !node.isValid) {
            return;
        }

        var anim = node.getComponent(cc.Animation);
        if (anim == null) {
            anim = node.addComponent(cc.Animation);
        }

        var clip = LoadUtils.get(path, cc.AnimationClip) as cc.AnimationClip;
        if (!clip) {
            return;
        }

        if (onlyOne && anim.getState(clip.name) && anim.getState(clip.name).isPlaying) {
            return;
        }

        if (isDefaultClip) {
            anim.defaultClip = clip;
            anim.play();
            return;
        }

        // 播放完成后恢复播放默认动画
        anim.once(cc.Animation.EventType.FINISHED, () => {
            if (anim!.defaultClip) {
                anim!.play();
            }
        }, this);

        if (anim.getState(clip.name)) {
            anim.play(clip.name);
            return
        }
        anim.addClip(clip, clip!.name);
        // anim.createState(clip, clip!.name);
        anim.play(clip!.name);
    }
}