import * as cc from "cc";

export default class RandomUtils {

    public static seed: number = 5;

    /**种子随机数*/
    public static seedRandom(seed) {
        this.seed = (seed * 9301 + 49297) % 233280;
        return this.seed / 233280.0;
    }

    public static rnd(seed) {
        seed = (seed * 9301 + 49297) % 233280;
        return seed / (233280.0);
    };

    public static rand() {
        let date = new Date();
        let milliont = date.getTime();
        return RandomUtils.rnd(milliont);
    };

    /**
     * 获取一个区间的随机数 (from, end)
     * @param {number} from 最小值
     * @param {number} end 最大值
     * @returns {number}
     */
    public static random(from: number, end: number): number {
        const min: number = Math.min(from, end);
        const max: number = Math.max(from, end);
        const range: number = max - min;
        return min + Math.random() * range;
    }

    /**
     * 获取一个区间的随机数 (from, end)
     * @param {number} from 最小值
     * @param {number} end 最大值
     * @returns {number}
     */
    public static randomInt(from: number, end: number): number {
        from -= 1;
        let ret = this.random(from, end);
        return Math.ceil(ret);
    }

    /**
     * 获取数组中随机一个单元
     * @param arr 数组数据源
     */
    public static randomArray<T>(arr: Array<T>): T {
        const index: number = this.random(0, arr.length) | 0;
        return arr[index];
    }

    /**
     * 随机打乱一个数据
     * @param array 
     */
    public static shuffleArray(array) {
        for (var i = array.length - 1; i > 0; i--) {
            var j = Math.floor(Math.random() * (i + 1));
            var temp = array[i];
            array[i] = array[j];
            array[j] = temp;
        }
    }



    public static getResult(arr) {
        var leng = 0;
        for (var i = 0; i < arr.length; i++) {
            leng += arr[i];
        }
        for (var i = 0; i < arr.length; i++) {
            var random = Math.random() * leng;
            if (random < arr[i]) {
                return i;
            }
            else {
                leng -= arr[i];
            }
        }
    }


    public static getAroundPosition(center: cc.Vec3, minDis, maxDis): cc.Vec3 {
        let angle = this.random(0, 360);
        let radius = this.random(minDis, maxDis);

        var x = Math.floor(center.x + radius * Math.cos((angle * Math.PI) / 180));
        var y = Math.floor(center.y + radius * Math.sin((angle * Math.PI) / 180));

        return new cc.Vec3(x, y, center.z);
    }

}