import * as cc from "cc";

export default class ColorUtils {
    /** 绿 */
    public static GREEN: cc.Color = cc.color(48, 232, 149, 255);
    /** 蓝 */
    public static BLUE: cc.Color = cc.color(41, 195, 250, 255);
    /** 紫 */
    public static PURPLE: cc.Color = cc.color(200, 24, 253, 255);
    /** 橙 */
    public static ORANGE: cc.Color = cc.color(247, 210, 90, 255);
    /** 红 */
    public static RED: cc.Color = cc.color(254, 57, 17, 255);

    /** 绿 */
    public static GREEN_STRING: string = "#30e895";
    /** 蓝 */
    public static BLUE_STRING: string = "#29c3fa";
    /** 紫 */
    public static PURPLE_STRING: string = "#c818fd";
    /** 橙 */
    public static ORANGE_STRING: string = "#f7d25a";
    /** 红 */
    public static RED_STRING: string = "#fe3911";


    /**
     * 根据品质获得颜色
     * @param quality 品质
     * @returns 颜色值
     */
    public static getColorByQuality(quality: number): cc.Color {
        switch (quality) {
            case 1:
                return ColorUtils.GREEN;
            case 2:
                return ColorUtils.BLUE;
            case 3:
                return ColorUtils.PURPLE;
            case 4:
                return ColorUtils.ORANGE;
            case 5:
                return ColorUtils.RED;
        }
    }

    public static getColor(quality: number): string {
        switch (quality) {
            case 1:
                return ColorUtils.GREEN_STRING;
            case 2:
                return ColorUtils.BLUE_STRING;
            case 3:
                return ColorUtils.PURPLE_STRING;
            case 4:
                return ColorUtils.ORANGE_STRING;
            case 5:
                return ColorUtils.RED_STRING;
        }
    }
}