export class Utils {

    private static _gid: number = 1;

    /**
     * 获取游戏唯一GID
     * @returns 
     */
    static getGID() {
        return Utils._gid++;
    }

    /**
     * 切割字典的数组 
     * ------sample------ </br>
     * src="10021_2,10031_1_3,1000_4_3"      splics=",_" </br>
     * -------return------ </br>
     * [["10021","2"],["10031", "1", "3"], ["1000","4","3"]];
     */
    static split(src: string, rule: string): any {
        var remainStr: string;
        var tmpStr: string;
        var tmpArr: Array<any>;
        var result: any;

        if (rule.length > 0) {
            tmpStr = rule.charAt(0);
            remainStr = rule.slice(1);
            tmpArr = src.split(tmpStr);

            result = [];
            for (let i: number = 0, len: number = tmpArr.length; i < len; i++) {
                let s = tmpArr[i];
                (result as Array<any>).push(this.split(s, remainStr));
            }
        } else {
            result = src;
        }
        return result;
    }
}