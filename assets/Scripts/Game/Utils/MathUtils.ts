import * as cc from "cc";

export class MathUtils {

    static precisionRevise(num: number) {
        return Math.round(num * 1000000) / 1000000;
    }

    static radiansToDegrees(radians: number) {
        return MathUtils.precisionRevise(radians * 57.29577951);
    }
    static degreesToRadians(degrees: number) {
        return MathUtils.precisionRevise(degrees * 0.01745329252);
    }

    /**
     * 计算角度  
     * @param startPoint 
     * @param endPoint 
     * @returns 返回角度
     */
    static computeDegrees(startPoint: cc.Vec2 | cc.Vec3, endPoint: cc.Vec2 | cc.Vec3): number {
        let dx = endPoint.x - startPoint.x;
        let dy = endPoint.y - startPoint.y;
        let angle = Math.atan2(dy, dx);
        let d = MathUtils.radiansToDegrees(angle);
        return d;
    }

    /**
     *  获取src面对dest区域的扇形坐标
     * @param src 起点
     * @param dest 目标
     * @param radius 总角度
     * @param num  分区数量
     * @param distance 距离
     * @returns 
     */
    static getFanShapedPoint(src: cc.Vec2, dest: cc.Vec2, radius: number, num: number, distance: number): cc.Vec2[] {
        if (!src || !dest) return null;

        let n = 360 / (radius / num);
        const theta = (2 * Math.PI) / n;
        // Logger.trace("theta", theta / (Math.PI / 180));

        let dx = dest.x - src.x;
        let dy = dest.y - src.y;
        let moveAgle = Math.atan2(dy, dx);
        // Logger.trace("moveAgle", moveAgle / (Math.PI / 180));
        let startAgle = moveAgle - theta / num;
        // Logger.trace("startAgle", startAgle / (Math.PI / 180));

        let result: cc.Vec2[] = [];
        for (let i = 0; i < num; i++) {
            let nx: number = src.x + Math.cos(startAgle) * distance;
            let ny: number = src.y + Math.sin(startAgle) * distance;

            result.push(new cc.Vec2(nx, ny));
            startAgle += theta;
        }
        return result;
    }

    /**
     * 根据两点计算方向
     * @param startPoint 起点
     * @param endPoint 终点
     * @param drtNum 方向的数量，默认是8
     */
    static computeDirection(startPoint: cc.Vec2, endPoint: cc.Vec2, drtNum: number = 8): number {

        let angle_deg = MathUtils.computeDegrees(startPoint, endPoint);
        if (angle_deg < 0) {
            angle_deg += 360;
        }

        let part = 360 / drtNum;

        let begin = 0 - part / 2;

        let ret = 0;

        for (let i = 0; i < drtNum; ++i) {

            let min = begin + part * i;
            let max = min + part;

            if (min < 0) {
                min = 360 + min;
                if (angle_deg >= min || angle_deg < max) {
                    ret = i;
                    break;
                }
            } else {
                if (angle_deg >= min && angle_deg < max) {
                    ret = i;
                    break;
                }
            }
        }

        ret = ret + drtNum / 4;
        ret = ret % drtNum;

        return ret;
    }


    static clockwise(a: cc.Vec2, b: cc.Vec2, c: cc.Vec2): number {
        let area: number = this.areaDouble(a, b, c);
        if (area == 0) {
            // 同一条直线上
            return 0;
        } else if (area > 0) {
            // 顺时针
            return 1;
        } else {
            // 逆时针
            return -1;
        }
    }

    static areaDouble(a: cc.Vec2, b: cc.Vec2, c: cc.Vec2): number {
        let CA = c.subtract(a);
        let BA = b.subtract(a);
        return CA.cross(BA);
    }

    /**获取两个点直接的距离 */
    static getDistance(pointA: cc.Vec2, pointB: cc.Vec2) {
        return cc.Vec2.distance(pointA, pointB);
    }

    private static pointA: cc.Vec2 = new cc.Vec2();
    private static pointB: cc.Vec2 = new cc.Vec2();
    /**获取两个点直接的距离 */
    static getDistancePosition(ax: number, ay: number, bx: number, by: number) {
        this.pointA.x = ax;
        this.pointA.y = ay;
        this.pointB.x = bx;
        this.pointB.y = by;
        return this.getDistance(this.pointA, this.pointB);
    }

    /**
     * 获取方向
     * @param dx 
     * @param dy 
     * @param moveAngle 
     * 方向值范围为 0-7，方向值设定如下，0是下，1是左下，2是左，3是左上，4是上，5是右上，6是右，7是右下
     *
     *        4
     *      3   5
     *    2   *   6
     *      1   7
     *        0
     */
    static getDirection(dx: number, dy: number, moveAngle: number = NaN): number {
        if (isNaN(moveAngle)) moveAngle = Math.atan2(dy, dx);
        var dire: number = Math.round((-moveAngle + Math.PI) / (Math.PI / 4));
        var direction: number = 8 - dire;
        //策划要求不用显示 (4 上方向) (0 方向)  因为只在4，0时不会切换左右方向
        if (direction == 4 || direction == 0) {
            direction = dy >= 0 ? direction + 1 : direction - 1;
        }
        return direction;
    }

    /**
    * 通过角度获得圆上某点坐标
    * @param angle
    * @param radius
    * @param center
    */
    public static getPosInCircle(angle, radius, center): cc.Vec2 {
        //求圆上某角度的点的坐标
        var x = Math.floor(center.x + radius * Math.cos((angle * Math.PI) / 180)); //Math.floor不加上的话 4，当angle=90时x不对
        var y = Math.floor(center.y + radius * Math.sin((angle * Math.PI) / 180));
        return cc.v2(x, y);
    }


    public static getAngle(origin: cc.Vec3, target: cc.Vec3) {
        let direction = target.subtract(origin);
        const radians = Math.atan2(direction.y, direction.x);
        return cc.misc.radiansToDegrees(radians);
    }

    public static segmentsIntr(a, b, c, d) {

        /** 1 解线性方程组, 求线段交点. **/
        // 如果分母为0 则平行或共线, 不相交
        var denominator = (b.y - a.y) * (d.x - c.x) - (a.x - b.x) * (c.y - d.y);
        if (denominator == 0) {
            return false;
        }

        // 线段所在直线的交点坐标 (x , y)
        var x = ((b.x - a.x) * (d.x - c.x) * (c.y - a.y)
            + (b.y - a.y) * (d.x - c.x) * a.x
            - (d.y - c.y) * (b.x - a.x) * c.x) / denominator;
        var y = -((b.y - a.y) * (d.y - c.y) * (c.x - a.x)
            + (b.x - a.x) * (d.y - c.y) * a.y
            - (d.x - c.x) * (b.y - a.y) * c.y) / denominator;

        /** 2 判断交点是否在两条线段上 **/
        if (
            // 交点在线段1上  
            (x - a.x) * (x - b.x) <= 0 && (y - a.y) * (y - b.y) <= 0
            // 且交点也在线段2上  
            && (x - c.x) * (x - d.x) <= 0 && (y - c.y) * (y - d.y) <= 0
        ) {

            // 返回交点p  
            return {
                x: x,
                y: y
            }
        }
        //否则不相交  
        return false

    }

    //求三角形的面积
    public static CalculateTriangleArea(a: cc.Vec3, b: cc.Vec3, c: cc.Vec3): number {
        // 向量叉乘x1y2-x2y1的大小的一半
        let res = 0;
        if ((a.x == b.x && a.y == b.y) || (a.x == c.x && a.y == c.y) || (b.x == c.x && b.y == c.y)) return res;
        let ba = new cc.Vec3(a.x - b.x, a.y - b.y);
        let bc = new cc.Vec3(c.x - b.x, c.y - b.y);
        res = Math.abs(ba.x * bc.y - bc.x * ba.y) * 0.5;
        return res;
    }

    // 计算椭圆上的角度（相对于椭圆中心）  
    public static calculateEllipseAngle(desiredAngle, radiusX, radiusY): number {
        // const rawDegrees = cc.misc.radiansToDegrees(desiredAngle);
        const x = radiusX * Math.cos(desiredAngle);
        const y = radiusX * Math.sin(desiredAngle);
        const angle = Math.atan2(y / radiusY, x / radiusX);
        const degrees = cc.misc.radiansToDegrees(angle);
        return degrees > 0 ? degrees : 360 + degrees;
    }
}