import * as cc from "cc";
import { ResFile } from "../../Config/CoreConst";

type ResJson = {
    groups: Array<{ keys: string; name: string }>;
    resources: Array<{ name: string; type: string; url: string }>;
};

const fileType: { [type: string]: typeof cc.Asset } = {
    image: cc.SpriteFrame,
    bin: cc.BufferAsset,
    json: cc.JsonAsset,
};

export class ResourceGroupUtils {
    /**
     * 获取资源组中所有资源url
     * @param groupName 资源组
     * @param resJson 资源配置JSON数据
     */
    public static getGroupUrls(groupName: string, resJson: ResJson): Array<ResFile> {
        const resFiles: Array<ResFile> = [];
        const groups: Array<{ keys: string; name: string }> = resJson.groups;
        const resources: Array<{ name: string; type: string; url: string }> = resJson.resources;
        let group: { keys: string; name: string };
        for (let i = 0, len = groups.length; i < len; i++) {
            if (groups[i].name === groupName) {
                group = groups[i];
                break;
            }
        }
        if (group == undefined) {
            return resFiles;
        }
        const keys: Array<string> = group.keys.split(",");
        const keyLen: number = keys.length;
        for (let i = 0, len = resources.length; i < len; i++) {
            if (keys.indexOf(resources[i].name) !== -1) {
                resFiles.push({
                    url: resources[i].url,
                    type: ResourceGroupUtils.getType(resources[i].type),
                });
                if (resFiles.length >= keyLen) {
                    break;
                }
            }
        }
        return resFiles;
    }

    /**
     * 获取资源类型
     * @param type 类型字符串
     */
    public static getType(type: string): typeof cc.Asset {
        return fileType[type];
    }

    /**
     * 移除资源配置数据中的资源拓展名
     * @param resJson 资源配置JSON数据
     */
    public static removeExtname(resJson: ResJson): void {
        let res: { name: string; type: string; url: string };
        const resources: Array<{ name: string; type: string; url: string }> = resJson.resources;
        for (let i = 0, len = resources.length; i < len; i++) {
            res = resources[i];
            res.url = res.url.split(".")[0];
        }
    }
} 