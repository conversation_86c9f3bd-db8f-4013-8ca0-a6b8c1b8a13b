/**
//测试代码:
        let obj1:Object = {"a":"c", "b":2, "c":3, "d":1};
        let obj2:Object = {"a":"b", "b":1, "c":2, "d":3};
        let obj3:Object = {"a":"A", "b":2, "c":4, "d":4};
        let obj4:Object = {"a":"a", "b":1, "c":2, "d":1};

        let arr:Array<any> = [];
        arr.push(obj1);
        arr.push(obj2);
        arr.push(obj3);
        arr.push(obj4);

        ArraySort.sort(arr, ArraySort.NUMERIC|ArraySort.DESCENDING);
        MyLog.log("sort");
        for(let i:number=0; i<arr.length; i++)
        {
            let obj:Object = arr[i];
            MyLog.log(obj["a"], obj["b"], obj["c"], obj["d"] )
        }


        ArraySort.sortOn(arr, ["a"], [ArraySort.CASEINSENSITIVE]);
        MyLog.log("sort 1");
        for(let i:number=0; i<arr.length; i++)
        {
            let obj:Object = arr[i];
            MyLog.log(obj["a"], obj["b"], obj["c"], obj["d"] )
        }

        ArraySort.sortOn(arr, ["b", "c", "d"], [ArraySort.NUMERIC, ArraySort.NUMERIC|ArraySort.DESCENDING, ArraySort.NUMERIC]);
        MyLog.log("sort 2");
        for(let i:number=0; i<arr.length; i++)
        {
            let obj:Object = arr[i];
            MyLog.log(obj["a"], obj["b"], obj["c"], obj["d"] )
        }

        arr = ["1", "11", "2", "5", "4", "66", "10"];
        ArraySort.sort(arr, ArraySort.NUMERIC|ArraySort.DESCENDING);
        MyLog.log("sort 3");
        for(let i:number=0; i<arr.length; i++)
        {
            let obj:Object = arr[i];
            //MyLog.log(obj["a"], obj["b"], obj["c"], obj["d"] )
            MyLog.log(obj)
        }

        arr = [{"d":100}, "11", "2", {"b":0}, "4", "66", "10"];
        ArraySort.sort(arr, ArraySort.NUMERIC|ArraySort.DESCENDING);
        MyLog.log("sort 3");
        for(let i:number=0; i<arr.length; i++)
        {
            let obj:Object = arr[i];
            //MyLog.log(obj["a"], obj["b"], obj["c"], obj["d"] )
            MyLog.log(obj)
        }
 */

/**
 * 同AS3中Array.sort, Array.sortOn方法.
 */
export class ArraySort {

    /**指定 Array 类排序方法为不区分大小写的排序。 */
    public static readonly CASEINSENSITIVE: number = 1;
    /**指定 Array 类排序方法为降序排序。 */
    public static readonly DESCENDING: number = 2;
    /**指定 Array 类排序方法的唯一排序要求。 */
    public static readonly UNIQUESORT: number = 4;
    /** 指定排序返回的数组包含数组索引。*/
    public static readonly RETURNINDEXEDARRAY: number = 8;
    /**指定 Array 类排序方法为数值（而不是字符串）排序。 */
    public static readonly NUMERIC: number = 16;


    /**
     * 排序
     * @param arr 需要排序的数组 
     * @param option 排序规则
     */
    public static sort(arr: Array<any>, option: number): void {
        arr.sort(function (a: any, b: any): number {
            return ArraySort._sort(a, b, option);
        })
    }

    private static _sort(a: any, b: any, option: number): number {
        switch (option) {
            case ArraySort.CASEINSENSITIVE:
                return ArraySort.sortChar(a, b, true);
            case ArraySort.CASEINSENSITIVE | ArraySort.DESCENDING:
                return ArraySort.sortChar(a, b, false);
            case ArraySort.NUMERIC:
                return ArraySort.sortNumber(a, b, true);
            case ArraySort.NUMERIC | ArraySort.DESCENDING:
                return ArraySort.sortNumber(a, b, false);
            case ArraySort.DESCENDING:
                return ArraySort.sortNumber(a, b, false);
        }
        return 0;
    }

    public static sortOn(arr: Array<any>, names: Array<string> | string, options: Array<number> | number): void {
        if (typeof names == 'string') {
            names = [names] as Array<string>;
        }

        if (typeof options == 'number') {
            options = [options] as Array<number>;
        }

        arr.sort(function (a: any, b: any): number {
            let len: number = names.length;
            for (let i: number = 0; i < len; i++) {
                let ret: number = ArraySort._sortOn(a, b, names[i], options[i]);
                if (ret != 0) return ret;
            }
            return 0;
        })
    }

    private static _sortOn(a: any, b: any, name: string, option: number): number {
        let aValue: any = a[name];
        let bValue: any = b[name];
        return ArraySort._sort(aValue, bValue, option);
    }

    private static sortChar(a: any, b: any, ascending: boolean): number {
        let aValue: number = String(a).toLowerCase().charCodeAt(0);
        let bValue: number = String(b).toLowerCase().charCodeAt(0);
        if (aValue > bValue) {
            return ascending ? 1 : -1;
        } else if (aValue < bValue) {
            return ascending ? -1 : 1;
        }
        return 0;
    }

    private static sortNumber(a: any, b: any, ascending: boolean): number {
        let aValue: number = Number(a);
        let bValue: number = Number(b);
        if (aValue > bValue) {
            return ascending ? 1 : -1;
        } else if (aValue < bValue) {
            return ascending ? -1 : 1;
        }
        return 0;
    }
}