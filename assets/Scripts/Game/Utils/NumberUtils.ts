import * as cc from "cc";

import  Logger  from "../../Framework/Logger";

type ScheduleFn = () => void;

/**
 * 数字工具 created by sky
 */
export default class NumberUtils {
    private node: cc.Label;
    private num: number;
    private addNum: number;

    private nums = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
    private addNums = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
    private step: number = 1;
    private timeCd: number = 0.05;

    private schedulFn: ScheduleFn[] = [null, null, null, null, null, null, null, null, null, null];

    //阶数
    private static add: number = 1;
    /** 时间间隔(秒) */
    private static timeCd: number = 0.05;


    /**
     * 滚动数字(目前仅支持到8位,有变化再加)
     */
    public static rollNumber(node: cc.Label, num: number, addNum: number) {
        if (node == null || num == null || addNum == null) {
            return;
        }

        let instance = new NumberUtils();
        instance.node = node;
        instance.num = num;
        instance.addNum = addNum;
        instance.step = 1;

        let n = num;
        let idx = 0;
        while (n > 0) {
            instance.nums[idx] = n % 10;
            n = Math.floor(n / 10);
            idx++;
        }

        n = addNum;
        idx = 0;
        while (n > 0) {
            instance.addNums[idx] = n % 10;
            n = Math.floor(n / 10);
            idx++;
        }

        instance.node.schedule(instance.numberX.bind(instance, 0), instance.timeCd);
        instance.numberX(0);
    }

    private numberX(x: number) {
        this.addNums[x] -= this.step;
        if (this.addNums[x] >= 0) {
            this.nums[x] += this.step;
        }

        if (this.nums[x] >= 10) {
            this.nums[x + 1] += 1;
            if (this.nums[x + 1] >= 10) {
                this.nums[x + 1] = 0;
            }
            this.nums[x] = 0;
            if (this.schedulFn[x + 1] == null) {
                this.schedulFn[x + 1] = this.numberX.bind(this, x + 1);
                this.node.schedule(this.schedulFn[x + 1], this.timeCd);
            }
        }

        if (this.addNums[x] < 0) {
            if (this.schedulFn[x] != null) {
                this.node.unschedule(this.schedulFn[x]);
                this.schedulFn[x] = null;
            }

            if (this.needToAddForward(x)) {
                if (this.schedulFn[x + 1] == null) {
                    this.schedulFn[x + 1] = this.numberX.bind(this, x + 1);
                    this.node.scheduleOnce(this.schedulFn[x + 1], this.timeCd);
                }
            }
        }

        this.node.string = this.autoCalc();
    }

    /**
     * 自动补位
     * @param num 偏移长度
     * @returns 补位值
     */
    private autoCalc(): string {
        // Logger.trace("NumberUtils.num100000000 : ", NumberUtils.num100000000);
        // Logger.trace("NumberUtils.num10000000 : ", NumberUtils.num10000000);
        // Logger.trace("NumberUtils.num1000000 : ", NumberUtils.num1000000);
        // Logger.trace("NumberUtils.num100000 : ", NumberUtils.num100000);
        // Logger.trace("NumberUtils.num10000 : ", NumberUtils.num10000);
        // Logger.trace("NumberUtils.num1000 : ", NumberUtils.num1000);
        // Logger.trace("NumberUtils.num100 : ", NumberUtils.num100);
        // Logger.trace("NumberUtils.num10 : ", NumberUtils.num10);
        // Logger.trace("NumberUtils.num1 : ", NumberUtils.num1);
        for (let i = this.nums.length - 1   ; i >= 0; i--) {
            if (this.nums[i] > 0) {
                return this.nums.slice(0, i + 1).map(num => num % 10).join("");
            }
        }
        return "0";
    }

    /**
     * 是否需要继续+
     * @param digit 位数
     */
    private needToAddForward(idx: number) {
        if (idx == null || idx <= 0) {
            return false;
        }

        for (let i = idx+1; i < this.addNums.length; i++) {
            if (this.addNums[i] > 0) {
                return true;
            }
        }
        return false;
    }

}

/**
 * 大写数值转换
 * @param value 转换数值
 */
function bigNumberTransform(value: number): string {
    const newValue: string[] = ['', '', ''];
    let fr: number = 10000,
        num: number = 3,
        text1: string = '',
        text2: string = '',
        fm: number = 1;
    if (value == null || isNaN(value)) {
        return '';
    }
    if (value < 0) {
        //负数
        value = Math.abs(value);
        text2 = '-';
    }
    while (value / fr >= 1) {
        fr *= 10;
        num += 1;
    }
    if (num <= 4) {
        //千~万位数
        newValue[0] = value.toString();
        newValue[1] = '';
    } else if (num <= 8) {
        //十万~千万位数
        text1 = '万';
        fm = text1 === '万' ? 10000 : 10000000;
        if (value % fm === 0) {
            newValue[0] = parseInt((value / fm).toString()) + '';
        } else {
            //保留两位小数且不四舍五入
            // newValue[0] = (value / fm).toString().match(/^\d+(?:\.\d{0,2})?/)[0];
            newValue[0] = (Math.floor((value / fm) * 100) / 100).toString();
        }
        newValue[1] = text1;
    } else {
        text1 = '亿';
        text1 = (num - 8) / 4 > 1 ? '万亿' : text1;
        text1 = (num - 8) / 7 > 1 ? '千万亿' : text1;
        text1 = (num - 8) / 10 > 1 ? '亿亿' : text1;
        fm = 1;
        switch (text1) {
            case '亿':
                fm = 100000000;
                break;
            case '千亿':
                fm = 100000000000;
                break;
            case '万亿':
                fm = 1000000000000;
                break;
            case '千万亿':
                fm = 1000000000000000;
                break;
            default:
                fm = 1000000000000000000;
                break;
        }
        if (value % fm === 0) {
            newValue[0] = parseInt((value / fm).toString()) + '';
        } else {
            // newValue[0] = (value / fm).toString().match(/^\d+(?:\.\d{0,2})?/)[0];
            newValue[0] = (Math.floor((value / fm) * 100) / 100).toString();
        }
        newValue[1] = text1;
    }
    newValue[0] = text2 ? text2 + newValue[0] : newValue[0];
    return (newValue[0] + newValue[1]);
}
