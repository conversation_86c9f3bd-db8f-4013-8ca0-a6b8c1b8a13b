import * as cc from "cc";
import { Timer as T } from "../Utils/laya/Timer";

export default class Timer {
    static _timer: T = new T(false);

    static update(dt: number) {
        this._timer._update();
    }

    static loop(delay: number, caller: any, method: Function, args = null, coverBefore = true) {
        this._timer.loop(delay, caller, method, args, coverBefore);
    }

    static frameLoop(framePeriod: number, caller: any, method: Function, args = null, coverBefore = true) {
        this._timer.frameLoop(framePeriod, caller, method, args, coverBefore);
    }

    static once(delay: number, caller: any, method: Function, args = null) {
        this._timer.once(delay, caller, method, args);
    }

    static clear(caller: any, method: Function) {
        this._timer.clear(caller, method);
    }

}