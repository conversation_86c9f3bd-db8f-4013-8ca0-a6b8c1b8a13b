import * as zip from "@zip.js/zip.js";

/**
 * zip解压库
 */

export default class ZipUtil {

    // private static _wx:any = window['wx'];

    // //下载zip
    // public static downloadZip(url:string, targetObj: any = null, successCallback:Function = null, failCallback:Function = null):any{

    //     return this._wx.downloadFile({
    //         'url':url,
    //         // 'filePath':fileName,
    //         'success':(res) => {
    //             if (res.statusCode === 404) {
    //                 if(failCallback && targetObj){
    //                     failCallback.call(targetObj, new Error("download file 404: " + url));
    //                 }
    //                 //cc.log("download file 404: " + url);
    //                 return;
    //             }

    //             if(successCallback && targetObj){
    //                 successCallback.call(targetObj, res);
    //             }
    //             //cc.log(res);

    //             // ZipUtil.unZip(res['tempFilePath'], 'zip');
    //         },
    //         'fail':function(err){
    //             if(failCallback && targetObj){
    //                 failCallback.call(targetObj, err);
    //             }
    //             //cc.log(err);
    //         }
    //     });
    // }

    // //解压zip
    // public static unZip(fromPath:string, toPath:string, targetObj: any = null, successCallback:Function = null, failCallback:Function = null):void{
    //     let fileMgr = this._wx.getFileSystemManager();

    //     //cc.log("unZip targetPath " + this._wx.env.USER_DATA_PATH + '/' + toPath);
    //     //移除目录
    //     // fileMgr.rmdirSync(this._wx.env.USER_DATA_PATH + '/' + toPath);
    //     //解压
    //     fileMgr.unzip({
    //         'zipFilePath': fromPath,
    //         'targetPath': this._wx.env.USER_DATA_PATH + '/' + toPath,
    //         'success':(res) => {
    //             //cc.log(res);

    //             if(successCallback && targetObj){
    //                 successCallback.call(targetObj, res);
    //             }
    //         },
    //         'fail':(err) => {
    //             //cc.log(err);

    //             if(failCallback && targetObj){
    //                 failCallback.call(targetObj, err)
    //             }
    //         }
    //     });
    // }

    // //读取zip解压目录下的文件
    // //demo //cc.log(ZipUtil.readFileFromZipDir('tb_config/tb_config_achieve.json'));
    // public static readFileFromZipDir(dirName:string, filePath:string, type:string = 'text'):any{
    //     let fileMgr = this._wx.getFileSystemManager();
    //     let tmpFilePath = this._wx.env.USER_DATA_PATH + '/' + dirName + '/' + filePath;
    //     if(type == 'text'){
    //         let jsonStr: string = "";
    //         try {
    //             jsonStr = fileMgr.readFileSync(tmpFilePath, 'utf-8');
    //         } catch (error) {
    //             let errorStr: string = "";
    //             if (error != null) {
    //                 if (error["length"] != null) {
    //                     errorStr = error;
    //                 }
    //                 else {
    //                     errorStr = JSON.stringify(error);
    //                 }
    //             }
    //             let param:any = {nick:PlayerInfo.getInstance().NickName,PID:PlayerInfo.getInstance().PlayerId,
    //                             SID:ServerInfo.getInstance().ServerId,error:errorStr,fileName:dirName + "/" + filePath,
    //                             err:"读取文件catch"};
    //             //cc.log(JSON.stringify(param));
    // IGame.PlatformTo("10401",{Content:StringUtil.base64(JSON.stringify(param))}); 
    //         }

    //         return jsonStr;
    //     }

    //     return null;
    // }

    /** 读取zip二进制数据 */
    // public static readZipArrayBuffer(fromPath: string, targetObj: object, successCallback: (data: ArrayBuffer) => void, failCallback: (error: Error) => void) {
    //     FileSystemManager.getInstance().readFileBinary(fromPath,
    //         (binaryData) => {
    //             //读取成功
    //             if (targetObj != null && successCallback != null) {
    //                 successCallback.call(targetObj, binaryData);
    //             }
    //         },
    //         () => {
    //             //读取失败
    //             if (targetObj != null && failCallback != null) {
    //                 failCallback.call(targetObj);
    //             }
    //         }
    //     )

    // }

    /** 解压zip到内存 */
    public static async unZipToMemory(data: ArrayBuffer, parentDir: string = "") {
        let zipStream = new zip.Uint8ArrayReader(new Uint8Array(data));
        let zipFile = new zip.ZipReader(zipStream);
        let timeLog: number = new Date().getTime();
        const entries = await zipFile.getEntries();
        const allPromises = entries.filter((entry) => {
            if (entry.directory) {
                return false;
            }
            if (entry.filename.indexOf(".DS_Store") != -1) {
                return false;
            }
            return true;
        }).map(async (entry) => {
            const name = entry.filename;
            let writer = new zip.Uint8ArrayWriter();
            let data = await entry.getData(writer);
            return {
                name: name,
                data: data,
            };
        });

        return Promise.allSettled(allPromises).then((results) => {
            const cfgs : Record<string, Uint8Array> = {};
            let errorStr: string = "adm-zip getDataAsync 失败 ";
            let haveError: boolean = false;
            results.forEach((result) => {
                if (result.status === "fulfilled") {
                    const {name, data} = result.value;
                    // Handle successful result
                    cfgs[parentDir + name] = data;
                } else {
                    // Handle error
                    haveError = true;
                    const error = result.reason;
                    try {
                        errorStr += error;
                    } catch (error) {
                        errorStr += error;
                    }
                }
            });
            if (haveError) {
                throw new Error(errorStr);
            }
            return cfgs;
        });
    };

    /** 1字节二进制数组 通过4个字节来转成数字 */
    private static unit8ArrayFourByteToNumber(uint8Array: Uint8Array, start: number): number {
        let number: number = 0;
        number += uint8Array[start + 0];
        number += uint8Array[start + 1] << 8;
        number += uint8Array[start + 2] << 16;
        number += uint8Array[start + 3] << 24;
        return number;
    }
}
