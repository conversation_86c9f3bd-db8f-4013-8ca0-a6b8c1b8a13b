import * as cc from 'cc';

import { AppConfig } from "../../Config/GameConfig";
import * as Store  from "../../Framework/Storage";

export class CommonUtils {
    /**
     * 获取性能等级
     * -Android
     * 设备性能等级，取值为：
     * -2 或 0（该设备无法运行小游戏）
     * -1（性能未知）
     * >=1（设备性能值，该值越高，设备性能越好，目前最高不到50)
     * -IOS
     * 微信不支持IO性能等级
     * iPhone 5s 及以下
     * 设定为超低端机 benchmarkLevel = 5
     * iPhone 6 ～ iPhone SE
     * 设定为超低端机 benchmarkLevel = 15
     * iPhone 7 ~ iPhone X
     * 设定为中端机 benchmarkLevel = 25
     * iPhone XS 及以上
     * 设定为高端机 benchmarkLevel = 40
     * -H5或其他
     * -1（性能未知）
     */
    public static getBenchmarkLevel(): number {
        //@ts-ignore
        if (window.wx) {
            //@ts-ignore
            const sys = window.wx.getSystemInfoSync();
            const isIOS = sys.system.indexOf("iOS") >= 0;
            if (isIOS) {
                const model = sys.model;
                // iPhone 5s 及以下
                const ultraLowPhoneType = ["iPhone1,1", "iPhone1,2", "iPhone2,1", "iPhone3,1", "iPhone3,3", "iPhone4,1", "iPhone5,1", "iPhone5,2", "iPhone5,3", "iPhone5,4", "iPhone6,1", "iPhone6,2"];
                // iPhone 6 ~ iPhone SE
                const lowPhoneType = ["iPhone6,2", "iPhone7,1", "iPhone7,2", "iPhone8,1", "iPhone8,2", "iPhone8,4"];
                // iPhone 7 ~ iPhone X
                const middlePhoneType = ["iPhone9,1", "iPhone9,2", "iPhone9,3", "iPhone9,4", "iPhone10,1", "iPhone10,2", "iPhone10,3", "iPhone10,4", "iPhone10,5", "iPhone10,6"];
                // iPhone XS 及以上
                const highPhoneType = ["iPhone11,2", "iPhone11,4", "iPhone11,6", "iPhone11,8", "iPhone12,1", "iPhone12,3", "iPhone12,5", "iPhone12,8"];
                for (let i = 0; i < ultraLowPhoneType.length; i++) {
                    if (model.indexOf(ultraLowPhoneType[i]) >= 0) return 5;
                }
                for (let i = 0; i < lowPhoneType.length; i++) {
                    if (model.indexOf(lowPhoneType[i]) >= 0) return 10;
                }
                for (let i = 0; i < middlePhoneType.length; i++) {
                    if (model.indexOf(middlePhoneType[i]) >= 0) return 20;
                }
                for (let i = 0; i < highPhoneType.length; i++) {
                    if (model.indexOf(highPhoneType[i]) >= 0) return 30;
                }
                return -1;
            } else {
                return sys.benchmarkLevel;
            }
        } else {
            return 50;
        }
    }

    /**根据name查找返回第一个找到的节点 */
    public static seekNodeByName(root: cc.Node, name: string): cc.Node {
        if (!root) {
            return null;
        }
        if (root.name == name) {
            return root;
        }
        let arrayRootChildren = root.children;
        for (let subWidget of arrayRootChildren) {
            let child = subWidget as cc.Node;
            if (child) {
                let res = this.seekNodeByName(child, name);
                if (res != null) {
                    return res;
                }
            }
        }
        return null;
    }

    /** 获取node世界坐标包围盒 */
    static getRectToWorld(node: cc.Node) {
        let rect = node.getComponent(cc.UITransform).getBoundingBoxToWorld();

        // let width = node.getContentSize().width;
        // let height = node.getContentSize().height;

        // let rect = cc.rect(
        //     -node.anchorX * width,
        //     -node.anchorY * height,
        //     width,
        //     height);

        // var wMatrix = new cc.mat4();
        // node.getWorldMatrix(wMatrix);
        // rect.transformMat4(rect, wMatrix);
        return rect;
    }

    /**根据name查找返回所有节点 */
    public static seekNodeArrByName(root: cc.Node, name: string): cc.Node[] {
        if (!root) {
            return [];
        }

        if (root.name == name) {
            return [root];
        }

        let nodeArr: cc.Node[] = [];
        let arrayRootChildren = root.children;
        for (let subWidget of arrayRootChildren) {
            let child = subWidget as cc.Node;
            if (child) {
                let res = this.seekNodeArrByName(child, name);
                if (res && res.length > 0) {
                    // nodeArr = nodeArr.concat(res);
                    nodeArr.push.apply(nodeArr, res);
                }
            }
        }
        return nodeArr;
    }

    /**设置节点下的所有精灵灰化
     *@node   传入的节点
     *@flag   是否灰化
     */
    public static setNodeSpriteGray(node: cc.Node, flag: boolean, force: boolean = false): void {
        if (!node) return;
        if (!node.active && !force) return;
        if (node["isGray"] == flag) return;
        let childrens: cc.Node[] = node.children;
        let components: cc.Sprite[] = node.getComponents(cc.Sprite);
        for (let j = 0; j < components.length; j++) {
            if (components[j].node.activeInHierarchy || force) {
                if (!cc.Material["getBuiltinMaterial"]) continue;
                let tempM = flag ? cc.Material["getBuiltinMaterial"]("2d-gray-sprite") : cc.Material["getBuiltinMaterial"]("2d-sprite");
                components[j].setMaterialInstance(
                    tempM, 0
                )
            }
        }
        for (let i = 0; i < childrens.length; i++) {
            let components: cc.Sprite[] = childrens[i].getComponents(cc.Sprite);
            for (let k = 0; k < components.length; k++) {
                if (!components[k].node.active && !force) continue;
                if (!cc.Material["getBuiltinMaterial"]) continue;
                let tempM = flag ? cc.Material["getBuiltinMaterial"]("2d-gray-sprite") : cc.Material["getBuiltinMaterial"]("2d-sprite");
                components[k].setMaterialInstance(
                    tempM, 0
                )
            }

            let labels: cc.Label[] = childrens[i].getComponents(cc.Label);
            for (let k = 0; k < labels.length; k++) {
                if (!cc.isValid(labels[k]) && !force) continue;
                if (flag == true) {
                    if (labels[k]["NC"] == undefined) {
                        let grayColor: cc.Color = labels[k].color.clone();
                        let num: number = (grayColor.r + grayColor.g + grayColor.b) / 3;
                        grayColor = cc.color(num, num, num);
                        labels[k]["NC"] = labels[k].color;
                        labels[k].color = grayColor;
                    }
                } else {
                    if (labels[k]["NC"] != undefined) {
                        labels[k].color = labels[k]["NC"];
                        labels[k]["NC"] = undefined;
                    }
                }
            }

            let LabelOutline: cc.LabelOutline[] = childrens[i].getComponents(cc.LabelOutline);
            for (let k = 0; k < LabelOutline.length; k++) {
                if (!cc.isValid(LabelOutline[k]) && !force) continue;
                if (flag == true) {
                    if (LabelOutline[k]["OC"] == undefined) {
                        let grayColor: cc.Color = LabelOutline[k].color.clone();
                        let num: number = (grayColor.r + grayColor.g + grayColor.b) / 3;
                        grayColor = cc.color(num, num, num);
                        LabelOutline[k]["OC"] = LabelOutline[k].color;
                        LabelOutline[k].color = grayColor;
                    }
                } else {
                    if (LabelOutline[k]["OC"] != undefined) {
                        LabelOutline[k].color = LabelOutline[k]["OC"];
                        LabelOutline[k]["OC"] = undefined;
                    }
                }
            }

            if (childrens[i].children.length > 0) {
                CommonUtils.setNodeSpriteGray(childrens[i], flag);
            }
        }
        node["isGray"] = flag;
    }


    static getRecursiveName(node: cc.Node): string {
        if (node.parent == null) return "root";
        return CommonUtils.getRecursiveName(node.parent) + "->" + node.name;
    }
}
