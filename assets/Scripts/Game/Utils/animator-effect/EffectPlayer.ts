/*
 * @Author: dgflash
 * @Date: 2022-08-19 15:36:08
 * @LastEditors: dgflash
 * @LastEditTime: 2023-03-01 18:28:55
 */
import * as cc from "cc";
import { EffectEvent } from './EffectEvent';
import { EventDispatcher } from "../../../GameEvent/Events";

const { ccclass, property } = cc._decorator;

/** 动画播放完释放特效 - Animation、ParticleSystem */
export class EffectPlayer extends cc.Component {
    /** 动画最大播放时间 */
    private maxDuration: number = 0;

    /**
    * 循环播放次数
    */
    loopCount: number;

    /**
     * 资源名
     */
    path: string;

    /**
     * Bundle名
     */
    bundle?: string;

    protected onEnable() {
        // console.log("onEnable", this.path, this.uuid);

        this.replay();
    }

    protected onDestroy(): void {
        // console.log("onDestroy", this.path, this.uuid);

        this.loopCount = 0;
        // this.onRecovery();
    }

    /**
     * 重新播放
     */
    private replay() {

        this.loopCount -= 1;

        // SPINE动画
        let spine = this.getComponent(cc.sp.Skeleton);
        if (spine) {
            // 播放第一个动画
            let json = (spine.skeletonData.skeletonJson as any).animations;
            for (var name in json) {
                spine.setCompleteListener(this.onNextPlay.bind(this));
                spine.setAnimation(0, name, false);
                break;
            }
        }
        else {
            // COCOS动画
            let anims: cc.Animation[] = this.node.getComponentsInChildren(cc.Animation);
            if (anims.length > 0) {
                anims.forEach(animator => {
                    let aniName = animator.defaultClip?.name;
                    if (aniName) {
                        let aniState = animator.getState(aniName);
                        if (aniState) {
                            let duration = aniState.duration;
                            // console.log("EffectPlayer>>" + aniName + ">>" + Date.now() + ">>" + duration, this.uuid);
                            this.maxDuration = duration > this.maxDuration ? duration : this.maxDuration;
                        }
                    }
                    // console.log("播放状态", aniName, this.uuid);


                    //if(!animator.playOnLoad) {
                    //animator?.setCurrentTime(0);
                    //animator?.play();
                    // }
                });
                this.scheduleOnce(this.onNextPlay.bind(this), this.maxDuration);
            }
            // // 粒子动画
            // else if (cc.ParticleSystem) {
            //     let particles: cc.ParticleSystem[] = this.node.getComponentsInChildren(cc.ParticleSystem);
            //     particles.forEach(particle => {
            //         particle.resetSystem();

            //         let duration: number = particle.duration;
            //         this.maxDuration = duration > this.maxDuration ? duration : this.maxDuration;
            //     });
            //     this.scheduleOnce(this.onNextPlay.bind(this), this.maxDuration);
            // }
        }
    }

    /**
     * 播放下一次
     */
    private onNextPlay() {
        if (this.loopCount > 0) {
            this.replay();
        } else {
            this.onRecovery();
        }
    }

    /**
     * 回收
     */
    private onRecovery() {
        // console.log("EffectPlayer>>onRecovery", this.path, ">>" + Date.now(), this.uuid);
        if (this.node.parent) {
            EventDispatcher.dispatch(EffectEvent.Put, this.node);
        }
    }
}
