import * as cc from "cc";

import { LoadUtils } from '../LoadUtils';
import { ViewUtil } from '../ViewUtils';
import { EffectEvent } from './EffectEvent';
import { EffectPlayer } from './EffectPlayer';
import EventManagerComponent from "../../GlobalComponent/EventManagerComponent";
import Logger from "../../../Framework/Logger"

/** 效果数据 */
class EffectData extends cc.Component {
    /** 资源路径 */
    path: string = null!;
    /** 资源对应Bundle */
    bundle?: string;
}

/** 特效参数 */
export interface IEffectParams {
    /** 初始位置 */
    pos?: cc.Vec3,

    /** 角度 */
    angle?: number,

    /** 翻转 */
    scaleX?: number,

    scaleY?: number;

    /** 循环次数 */
    loopCount?: number;
}

/** 动画特效对象池管理器 */
export class EffectSingleCase {
    private static _instance: EffectSingleCase;
    static get instance(): EffectSingleCase {
        if (this._instance == null) {
            this._instance = new EffectSingleCase();
        }
        return this._instance;
    }


    /** 全局动画播放速度 */
    private _speed: number = 1;
    get speed(): number {
        return this._speed;
    }
    set speed(value: number) {
        this._speed = value;
        // this.effects_use.forEach((value: Boolean, key: cc.Node) => {
        //     this.setSpeed(key);
        // });
    }

    /** 不同类型的对象池集合 */
    // private effects: Map<string, cc.NodePool> = new Map();
    // private effects_use: Map<cc.Node, boolean> = new Map();

    constructor() {
        EventManagerComponent.instance().addEventListener(
            EffectEvent.Put, 
            this.onPut, this);
    }

    private onPut(args: any) {
        this.put(args as cc.Node);
    }

    /** 加载资源并现实特效 */
    loadAndShow(name: string, parent?: cc.Node, params?: IEffectParams, bundleName?: string): Promise<cc.Node> {
        let self = this;
        return new Promise(async (resolve, reject) => {
            // var np = this.effects.get(name);
            Logger.logNet(`名为【${name}】的特效资源开始加载`);
            if (!LoadUtils.get(name, cc.Prefab)) {
                LoadUtils.loadPrefab(bundleName + name, () => {
                    // oops.res.load(bundleName, name, cc.Prefab, (err: Error | null, prefab: cc.Prefab) => {
                    //     if (err) {
                    //         console.error(`名为【${name}】的特效资源加载失败`);
                    //         return;
                    //     }
                    Logger.logNet(`名为【${name}】的特效资源加载完成`);

                    var node = self.show(name, parent, params, bundleName);
                    resolve(node);
                });
            }
            else {
                var node = self.show(name, parent, params, bundleName);
                resolve(node);
            }
        });
    }

    /** 
     * 显示预制对象 
     * @param name    预制对象名称
     * @param parent  父节点
     * @param pos     位置
     */
    show(name: string, parent?: cc.Node, params?: IEffectParams, bundleName?: string): cc.Node {
        // var np = this.effects.get(name);
        // if (np == null) {
        //     np = new cc.NodePool();
        //     this.effects.set(name, np);
        // }
        //父对象没有了.
        if (!parent || !parent.isValid) {
            return null;
        }

        var node: cc.Node;
        // if (np.size() == 0) {
        node = ViewUtil.createPrefabNode(bundleName + name, bundleName);
        let data = node.addComponent(EffectData);
        data.path = name;
        data.bundle = bundleName;

        if (params && params.loopCount > 0) {
            node.addComponent(EffectPlayer);
        }
        // }
        // else {
        //     node = np.get()!;
        // }
        let comp = node.getComponent(EffectPlayer);
        if (comp && params && params.loopCount > 0) {
            comp.path = name;
            comp.loopCount = params.loopCount;
        }
        this.setSpeed(node);

        if (params) {
            if (params.pos) {
                node.setPosition(params.pos.x, params.pos.y, params.pos.z);
            }
            if (params.angle && !isNaN(params.angle)) {
                node.angle = params.angle;
            }

            const v = node.getScale();

            if (params.scaleX && !isNaN(params.scaleX)) {
                v.x = params.scaleX;
            }
            if (params.scaleY && !isNaN(params.scaleY)) {
                v.y = params.scaleY;
            }

            node.setScale(v);
        }

        if (parent) node.parent = parent;

        // this.resetNode(node);
        // 记录缓冲池中放出的节点
        // this.effects_use.set(node, true);

        return node;
    }

    /**
     * 回收对象
     * @param name  预制对象名称
     * @param node  节点
     */
    put(node: cc.Node) {
        if (!node) return;

        //停止粒子动画.
        let particles = node.getComponentsInChildren(cc.ParticleSystem2D);
        particles && particles.forEach(particle => {
            particle && particle.stopSystem();
        });

        let data: EffectData = node.getComponent(EffectData);
        if (data) {
            node.getComponent(EffectData).destroy();
            ViewUtil.removePrefabNode(data.path, data.bundle);
        }
        node.getComponent(EffectPlayer).destroy();
        node.removeFromParent();
        node.destroy();

        // var name = node.getComponent(EffectData)!.type;
        // var np = this.effects.get(name);
        // if (np == null) {
        //     np = new cc.NodePool();
        //     this.effects.set(name, np);
        // }
        // // 回收使用的节点
        // this.effects_use.delete(node);

        // // 回到到池中
        // np.put(node);

        //将资源从cocos内部移除
        // oops.res.release(name);
    }

    /**
     * 清除对象池数据
     * @param name  参数为空时，清除所有对象池数据;指定名时，清楚指定数据
     */
    clear(name?: string | string[]) {
        // if (name) {
        //     if (typeof name === "string") {
        //         name = [name];
        //     }
        //     name.forEach(n => {
        //         var np = this.effects.get(n)!;
        //         np?.clear();
        //     })
        // }
        // else {
        //     this.effects.forEach(np => {
        //         np.clear();
        //     });
        //     this.effects.clear();
        // }
    }

    private resetNode(node: cc.Node) {
        let animArr: cc.Animation[] = node.getComponentsInChildren(cc.Animation);
        animArr && animArr.forEach(animator => {
            const defaultName = animator.defaultClip.name;
            const sts = animator.getState(defaultName);
            sts.time = 0;
            sts.sample();
            try {
                animator && animator.play();
            } catch (error) {
                console.error("播放出错", defaultName);
            }
        });
        let particles: cc.ParticleSystem2D[] = node.getComponentsInChildren(cc.ParticleSystem2D);
        particles && particles.forEach(particle => {
            particle && particle.stopSystem();
            particle && particle.resetSystem();
        });
    }

    /** 设置动画速度 */
    private setSpeed(node: cc.Node) {
        // // SPINE动画
        // let spine = node.getComponent(sp.Skeleton);
        // if (spine) {
        //     spine.timeScale = this.speed;
        // }
        // else {
        // COCOS动画
        // let anims: cc.Animation[] = node.getComponentsInChildren(cc.Animation);
        // if (anims.length > 0) {
        //     anims.forEach(animator => {
        //         let aniName = animator.defaultClip?.name;
        //         if (aniName) {
        //             let aniState = animator.getAnimationState(aniName);
        //             if (aniState) {
        //                 aniState.speed = this.speed;
        //             }
        //         }
        //     });
        // }
        // 粒子动画
        // else if (cc.ParticleSystem) {
        //     let particles: cc.ParticleSystem[] = node.getComponentsInChildren(cc.ParticleSystem);
        //     particles.forEach(particle => {
        //         particle.speed = this.speed;
        //     });
        // }
        // }
    }
}