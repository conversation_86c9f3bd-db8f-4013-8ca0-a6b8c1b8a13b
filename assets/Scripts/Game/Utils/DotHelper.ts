import { request } from "../../Framework/Network/http";
import { AppConfig } from "../../Config/GameConfig";

export interface DotInfo {
    channelId: number;

    openId?: string;
    apiBaseUrl?: string;

    [key: string]: any;
}

const excludeKeys = ["apiBaseUrl", "channelId", "openId"];

export async function requestDot(dotInfo: DotInfo) {
    const apiBaseUrl = dotInfo.apiBaseUrl || AppConfig.apiUrl;

    const query = {
        channelId: dotInfo.channelId,
        openId: dotInfo.openId || "",
    };
    for (let key in dotInfo) {
        if (excludeKeys.findIndex(k => k === key) !== -1) continue;
        query[key] = dotInfo[key];
    }

    const reply = await request({
        url: `${apiBaseUrl}/api/v1/loginDot`,
        method: "GET",
        query: query,
    });

    if (reply.status !== 200) {
        // TODO: 打点失败，需要重试
        return;
    }

    return reply.body;
}