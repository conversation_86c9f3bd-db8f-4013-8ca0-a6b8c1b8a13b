
export class Guid {
    private value: string;

    static readonly validator: RegExp = new RegExp("^[a-z0-9]{8}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{12}$", "i");

    constructor(guid: any) {
        if (!guid) throw new TypeError("Invalid argument; `value` has no value.");

        this.value = Guid.EMPTY;

        if (guid && guid instanceof Guid) {
            this.value = guid.toString();
        } else if (
            guid &&
            Object.prototype.toString.call(guid) === "[object String]" &&
            Guid.isGuid(guid)
        ) {
            this.value = guid;
        }
    }

    public equals(other: any): boolean {
        return Guid.isGuid(other) && this.value == other;
    }

    public isEmpty(): boolean {
        return this.value === Guid.EMPTY;
    }

    public toString(): string {
        return this.value;
    }

    public toJSON(): string {
        return this.value;
    }

    static gen(count: number): string {
        let out: string = "";
        for (let i = 0; i < count; i++) {
            out += (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
        }
        return out;
    }

    public static EMPTY: string = "00000000-0000-0000-0000-000000000000";

    public static isGuid(value: any): boolean {
        return value && (value instanceof Guid || Guid.validator.test(value.toString()));
    }

    public static create(): Guid {
        return new Guid([
            Guid.gen(2),
            Guid.gen(1),
            Guid.gen(1),
            Guid.gen(1),
            Guid.gen(3)
        ].join("-"));
    }

    public static raw(): string {
        return [
            Guid.gen(2),
            Guid.gen(1),
            Guid.gen(1),
            Guid.gen(1),
            Guid.gen(3)
        ].join("-");
    }

    private static Gid: number = 1000000;
    /** 获取一个新的Gid */
    public static get gid(): number {
        return ++this.Gid;
    }
}

