import * as cc from 'cc';

/**
 * 递归转储节点架构信息
 * @param node 要转储的节点
 * @param depth 当前深度（用于缩进）
 * @param prefix 前缀字符串（用于树形显示）
 */
export function debugDumpNodeArch(node: cc.Node, depth: number = 0, prefix: string = ""): void {
    if (!node) {
        console.log(`${prefix}[NULL NODE]`);
        return;
    }

    const indent = "  ".repeat(depth);
    const isLast = prefix.endsWith("└─");
    const childPrefix = isLast ? "   " : "│  ";
    
    // 获取节点基本信息
    const nodeInfo = {
        name: node.name || "[unnamed]",
        active: node.active,
        position: `(${node.position.x.toFixed(2)}, ${node.position.y.toFixed(2)}, ${node.position.z.toFixed(2)})`,
        scale: `(${node.scale.x.toFixed(2)}, ${node.scale.y.toFixed(2)}, ${node.scale.z.toFixed(2)})`,
        rotation: `(${node.eulerAngles.x.toFixed(2)}, ${node.eulerAngles.y.toFixed(2)}, ${node.eulerAngles.z.toFixed(2)})`,
        layer: node.layer,
        uuid: node.uuid.slice(0, 8) + "...",
        childCount: node.children.length
    };

    // 获取组件信息
    const components = node.getComponents(cc.Component);
    const componentNames = components.map(comp => comp.constructor.name);

    // 输出节点信息
    console.log(`${prefix}${nodeInfo.name} ${nodeInfo.active ? '✓' : '✗'}`);
    console.log(`${indent}${childPrefix}├─ UUID: ${nodeInfo.uuid}`);
    console.log(`${indent}${childPrefix}├─ Position: ${nodeInfo.position}`);
    console.log(`${indent}${childPrefix}├─ Scale: ${nodeInfo.scale}`);
    console.log(`${indent}${childPrefix}├─ Rotation: ${nodeInfo.rotation}`);
    console.log(`${indent}${childPrefix}├─ Layer: ${nodeInfo.layer}`);
    console.log(`${indent}${childPrefix}├─ Children: ${nodeInfo.childCount}`);
    
    if (componentNames.length > 0) {
        console.log(`${indent}${childPrefix}├─ Components: [${componentNames.join(', ')}]`);
    }

    // 如果有UI相关组件，输出额外信息
    const uiTransform = node.getComponent(cc.UITransform);
    if (uiTransform) {
        console.log(`${indent}${childPrefix}├─ Content Size: (${uiTransform.contentSize.width.toFixed(2)}, ${uiTransform.contentSize.height.toFixed(2)})`);
        console.log(`${indent}${childPrefix}├─ Anchor: (${uiTransform.anchorX.toFixed(2)}, ${uiTransform.anchorY.toFixed(2)})`);
    }

    const widget = node.getComponent(cc.Widget);
    if (widget && widget.enabled) {
        const alignFlags = [];
        if (widget.isAlignTop) alignFlags.push("TOP");
        if (widget.isAlignBottom) alignFlags.push("BOTTOM");
        if (widget.isAlignLeft) alignFlags.push("LEFT");
        if (widget.isAlignRight) alignFlags.push("RIGHT");
        if (widget.isAlignHorizontalCenter) alignFlags.push("H_CENTER");
        if (widget.isAlignVerticalCenter) alignFlags.push("V_CENTER");
        
        if (alignFlags.length > 0) {
            console.log(`${indent}${childPrefix}├─ Widget: [${alignFlags.join(', ')}]`);
        }
    }

    // 递归处理子节点
    const children = node.children;
    for (let i = 0; i < children.length; i++) {
        const child = children[i];
        const isLastChild = i === children.length - 1;
        const newPrefix = `${indent}${childPrefix}${isLastChild ? '└─ ' : '├─ '}`;
        
        debugDumpNodeArch(child, depth + 1, newPrefix);
    }
}

/**
 * 简化版本的节点架构转储（只显示名称和层级）
 * @param node 要转储的节点
 * @param depth 当前深度
 */
export function debugDumpNodeArchSimple(node: cc.Node, depth: number = 0): void {
    if (!node) {
        console.log("  ".repeat(depth) + "[NULL NODE]");
        return;
    }

    const indent = "  ".repeat(depth);
    const activeStatus = node.active ? "✓" : "✗";
    const childrenCount = node.children.length > 0 ? ` (${node.children.length} children)` : "";
    
    console.log(`${indent}${node.name || "[unnamed]"} ${activeStatus}${childrenCount}`);
    
    // 递归处理子节点
    for (const child of node.children) {
        debugDumpNodeArchSimple(child, depth + 1);
    }
}

/**
 * 转储场景中的所有根节点架构
 */
export function debugDumpSceneArch(): void {
    console.log("=== Scene Architecture ===");
    const scene = cc.director.getScene();
    if (!scene) {
        console.log("No active scene found");
        return;
    }

    console.log(`Scene: ${scene.name}`);
    debugDumpNodeArch(scene);
}

/**
 * 查找并转储指定名称的节点架构
 * @param nodeName 节点名称
 * @param searchRoot 搜索根节点（默认为场景根节点）
 */
export function debugDumpNodeByName(nodeName: string, searchRoot?: cc.Node): void {
    const root = searchRoot || cc.director.getScene();
    if (!root) {
        console.log("No search root available");
        return;
    }

    const foundNode = cc.find(nodeName, root);
    if (foundNode) {
        console.log(`=== Node Architecture for "${nodeName}" ===`);
        debugDumpNodeArch(foundNode);
    } else {
        console.log(`Node "${nodeName}" not found`);
    }
}