import * as cc from "cc";
import Env from "../../Env";

export class UIUtils {
    /** 根据节点名字, 递归寻找子节点 */
    static find(name: string, referenceNode: cc.Node): cc.Node {
        if (!referenceNode) {
            const scene = cc.director.getScene();
            if (!scene) {
                if (Env.debug) {
                    console.error("连场景都没有");
                }
                return null;
            } else if (Env.debug && !scene.isValid) {
                console.error("场景不可用");
                return null;
            }
            referenceNode = scene;
        } else if (Env.debug && !referenceNode.isValid) {
            console.error("节点不可用");
            return null;
        }

        for (const child of referenceNode.children) {
            if (name === child.name) {
                return child;
            } else {
                const ret = UIUtils.find(name, child);
                if (!!ret) return ret;
            }
        }

        return null;
    }

    /** 根据节点名字, 递归寻找子节点 */
    static findAll(name: string, referenceNode: cc.Node): cc.Node[] {
        let ret: cc.Node[] = [];

        if (!referenceNode) {
            const scene = cc.director.getScene();
            if (!scene) {
                if (Env.debug) {
                    console.error("连场景都没有");
                }
                return [];
            } else if (Env.debug && !scene.isValid) {
                console.error("场景不可用");
                return [];
            }
            referenceNode = scene;
        } else if (Env.debug && !referenceNode.isValid) {
            console.error("节点不可用");
            return [];
        }

        for (const child of referenceNode.children) {
            if (name === child.name) {
                ret.push(child);
            } else {
                ret.push(...UIUtils.findAll(name, child));
            }
        }
        return ret;
    }

    /** 获取一个节点下面全部的子节点，用字典存起来 */
    static getUINodeDic(node:cc.Node):cc.Node[]
    {
        let uiNodeDic:cc.Node[] = [];
        this.loadNodeInfo(node,"",uiNodeDic)
        return uiNodeDic;
    }

    private static loadNodeInfo(node:cc.Node,path:string,dic:cc.Node[])
    {
        for(var i = 0;i < node.children.length;i++)
        {
            dic[path + node.children[i].name] = node.children[i];
            this.loadNodeInfo(node.children[i],path + node.children[i].name + "/",dic);
        }
    }
}
