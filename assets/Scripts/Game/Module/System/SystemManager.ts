import * as cc from "cc";
import Timer from "../../Utils/Timer";
import { AppConfig } from "../../../Config/GameConfig";

const {ccclass, property} = cc._decorator;

@ccclass
export default class SystemManager extends cc.Component {

    protected onLoad(): void {
        cc.game.frameRate = AppConfig.frameRate + 1;
        cc.director.addPersistRootNode(this.node);
    }

    protected update(dt: number): void {
        Timer.update(dt);
    }
}