/**
 * 实现SDK的文件管理系统的接口
 */

//Stats 对象
export class FileStatsData{
    public isDirectory:boolean = false;
    public isFile:boolean = false;
}

export abstract class FileSystemBase {

    /** （游戏中没用到，废弃）
     * 下载文件 (*可能保存到用户目录或者临时缓存目录，如果不是临时目录需要自行清除)
     * @param url      下载的url路径
     * @param fileType 文件后缀名 如zip,jpg,json等
     * @param succFunc 
     * @param failFunc 
     */
    public abstract downloadFile(url:string, succFunc?:(temFilePath:string)=>void, failFunc?:Function);
    
    /** （游戏中没用到，废弃）
     * 解压文件
     * @param fromPath 需要解压的文件地址（绝对地址）
     * @param toPath   解压到目录地址 （相对地址，在可写入目录下）
     * @param succFunc
     * @param failFunc 
     */
    public abstract unZip(fromPath:string, toPath:string, succFunc?:Function, failFunc?:Function):void;

    /**
     * 读取目录
     * @param dirPath 目录绝对地址
     * @param succFunc 
     * @param failFunc 
     */
    public abstract readDir(dirPath:string, succFunc: (files:string[])=>void, failFunc?:Function):void;
    
    /**
     * 删除文件
     * @param filePath 文件的绝对路径
     * @param succFunc 
     * @param failFunc 
     */
    public abstract unLink(filePath:string, succFunc?:Function, failFunc?:Function):void;

    /**
     * 获取文件 Stats 对象
     * @param path 目录或文件的绝对路径
     * @param succFunc 
     * @param failFunc 
     */
    public abstract fileStats(path:string, succFunc?:(stats:FileStatsData)=>void, failFunc?:Function):void;
    
    /**
     * 读取文本 （游戏中没用到，废弃）
     * @param filePath 文件的绝对路径
     * @param succFunc 
     * @param failFunc 
     */
    public abstract readFileText(filePath:string, succFunc?: (text:string) => void, failFunc?:Function):void;
    
    /**
     * 读取文件二进制
     * @param filePath 文件的绝对路径
     * @param succFunc 
     * @param failFunc 
     */
    public abstract readFileBinary(filePath:string, succFunc?: (binaryData:any) => void, failFunc?:Function):void;
    
    /**
     * 写入二进制文件
     * 
     */
    public abstract writeFileBinary(filePath:string, binaryData:any, succFunc?:Function, failFunc?:Function):void;

    /**
     * 获取可写入目录
     * 统一目录后面以/结尾
     */
    public abstract getWritablePath():string;

    /**
     * 获取远程资源的地址
     * 读取cocoscreator构建面板发布设置的地址
     */
    public abstract getRemoteServerRoot():string;

    /**
     * 是否解压import josn到内存
     * 如果import目录是打包到包内可以设为false
     */
    public isUnZipJsonMemoryMode():boolean{
        return true;
    }
}
