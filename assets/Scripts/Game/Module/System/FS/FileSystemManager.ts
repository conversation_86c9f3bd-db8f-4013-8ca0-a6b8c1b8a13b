import { FileSystemBase } from "./FileSystemBase";

/**
 * 文件系统管理类
 */

export default class FileSystemManager{

    private static _mInstance:FileSystemBase = null;

    private static _inited:boolean = false; //已经初始化过;

    //如果返回的instante为空，说明该平台不支持文件管理
    public static getInstance() :FileSystemBase{
       if(!this._mInstance && !this._inited){
           this.init();
       }
      
       return this._mInstance;
   }

   public static init():void{
        //实例化sdk
        this._inited = true;
   }
}
