

export default class TimeManager {
    // 服务端时间(ms)
    // private _serverTime: number;
    // 服务端和客户端时间差(ms)
    private _diffTimeMs: number = 0;
    // 是否同步过时间
    private _isSyncTime: boolean = false;

    private _gameStartTime: number;

    public initGameStartTime() {
        this._gameStartTime = Date.now();
    }

    public GetGameStartTime() {
        return this._gameStartTime;
    }
    /**
     * 初始化服务器时间(ms)
     * @param time 获取同步的服务器时间
     */
    public initSeverTime(time: number): void {
        // this._serverTime = time;
        this._diffTimeMs = time - this.localTimeMillisecond;
        // console.warn("Sync server Time = " + time);
        // console.warn("Sync local Time = " + this.localTimeMillisecond);
        // Logger.trace("Sync server time diff = " + this._diffTimeMs);
        this._isSyncTime = true;
    }

    /**
     * 更新时间
     * @param dt 服务器时间(s)
     */
    // public onUpdate(dt: number): void {
    //     if (!this._isSyncTime) return;
    //     this._serverTime = this.localTime + this._diffTime;
    // }

    /**
     * 获取服务器时间(s)
     */
    public get serverTime(): number {
        return Math.floor((this.localTimeMillisecond + this._diffTimeMs) / 1000);
    }

    /**
    * 获取服务器时间(ms)
    */
    public get serverTimeMs(): number {
        return this.localTimeMillisecond + this._diffTimeMs;
    }

    /**
     * 获取本地时间(s)
     */
    public get localTime(): number {
        const time: number = (Date.now() / 1000) | 0;
        return time;
    }

    /**
     * 获取本地时间(s)
     */
    public get localTimeMillisecond(): number {
        return Date.now();
    }

    // syncServerTime(){
    // }

    /** 明天的0点 */
    public get tomorrowZeroSecond(): number {
        let date: Date = new Date(this.serverTimeMs);
        date.setHours(0, 0, 0, 0);
        return date.getTime() + 86400000;
    }
}
