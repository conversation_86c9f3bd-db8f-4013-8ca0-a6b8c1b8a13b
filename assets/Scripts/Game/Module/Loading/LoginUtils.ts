import * as cc from "cc";
import * as Store from "../../../Framework/Storage";
import { CommonUtils } from "../../Utils/CommonUtils";
import Env from "../../../Env";
import Logger from "../../../Framework/Logger";
import { AppConfig } from "../../../Config/GameConfig";

export class LoginUtils {
    /**
    * 获取对应字段的信息
    * @param key 字段
    */
    public static getQueryString(key: string) {
        const reg = new RegExp("(^|&)" + key + "=([^&]*)(&|$)", "i");
        const url = decodeURI(window.location.search);
        var match = url.substr(1).match(reg);
        if (match != null) {
            return unescape(match[2]);
        }
        return null
    }

    /**
     * 随机打乱一个数据
     * @param array 
     */
    public static shuffleArray(array) {
        for (var i = array.length - 1; i > 0; i--) {
            var j = Math.floor(Math.random() * (i + 1));
            var temp = array[i];
            array[i] = array[j];
            array[j] = temp;
        }
    }

    /** 根据节点名字, 递归寻找子节点 */
    static find(name: string, referenceNode: cc.Node): cc.Node {
        if (!referenceNode) {
            const scene = cc.director.getScene();
            if (!scene) {
                if (Env.debug) {
                    Logger.error("连场景都没有");
                }
                return null;
            } else if (Env.debug && !scene.isValid) {
                Logger.error("场景不可用");
                return null;
            }
            referenceNode = scene;
        } else if (Env.debug && !referenceNode.isValid) {
            Logger.error("节点不可用");
            return null;
        }

        for (const child of referenceNode.children) {
            if (name === child.name) {
                return child;
            } else {
                const ret = LoginUtils.find(name, child);
                if (!!ret) return ret;
            }
        }

        return null;
    }

    static getClientId(): string {
        if (AppConfig.online) {
            return window["gameParams"]["openId"];
        }
        let result = Store.getItem("Guid");
        let localId = result ? result : "";

        if (!localId) {
            localId = "xg01" + Date.now();//Guid.raw();
            Store.setItem("Guid", localId);
        }
        return localId + "";
    }

}