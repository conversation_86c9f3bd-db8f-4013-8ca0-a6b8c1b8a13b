import * as cc from "cc";

import { ResFile } from "../../../../Config/CoreConst";
import { LoadUtils } from "../../../Utils/LoadUtils";
import { ResourceGroupUtils } from "../../../Utils/ResourceGroupUtils";

type ResCache = {
    [url: string]: { count: number; cacheTime: number; isClear: boolean };
};

export default class ResManager {
    // 加载资源缓存
    private _resCache: ResCache;

    /** 浏览器用的游戏配置 */
    private _chromeCfgs: object = {};
    /** 微信用的游戏配置 */
    private _wxCfgs: object = {};

    public init(): void {
        this._resCache = {};
    }

    /**
     * 每帧更新
     * @param dt 更新时间(s)
     */
    public onUpdate(dt: number): void { }

    /**
     * 获取资源
     * @param url 资源地址
     * @param type 文件类型
     */
    public getRes(url: string, type?: typeof cc.Asset): any {
        // return cc.resources.get(url, type);
        return LoadUtils.get(url, type);
    }

    /**
     * 销毁缓存资源
     * @param url 销毁资源
     */
    public clearRes(url: string | Array<string>): void {
        this.removeUseRes(url);
    }

    /**
     * 添加ui包体
     * @param pkgName 包名
     */
    public addUiPackage(pkgName: string): void {
        // const pkg: fgui.UIPackage = fgui.UIPackage.getByName(pkgName);
        // if (!pkg) {
        //     const pkgUrl: string = App.PathManager.getPkgPath(pkgName);
        //     fgui.UIPackage.addPackage(pkgUrl);
        // }
    }

    /**
     * 删除ui包体
     * @param pkgName 包名
     */
    public removeUiPackage(pkgName: string): void {
        // const pkg: fgui.UIPackage = fgui.UIPackage.getByName(pkgName);
        // if (pkg) {
        //     fgui.UIPackage.removePackage(pkgName);
        // }
    }

    /**
     * 添加资源组使用
     * @param groupName 资源组名
     * @param isTrust 是否托管
     * @param resJson 资源配置JSON数据
     */
    public addGroupUse(groupName: string, isTrust: boolean, resJson: any): void {
        if (!isTrust) return;
        const resFile: Array<ResFile> = ResourceGroupUtils.getGroupUrls(groupName, resJson);
        const urls: Array<string> = [];
        for (let i = 0, len = resFile.length; i < len; i++) {
            urls.push(resFile[i].url);
        }
        this.addUseRes(urls);
    }

    /**
     * 移除资源组使用
     * @param groupName 资源组名
     * @param isTrust 是否托管
     * @param resJson 资源配置JSON数据
     */
    public removeGroupUse(groupName: string, isTrust: boolean, resJson: any): void {
        if (!isTrust) return;
        const resFile: Array<ResFile> = ResourceGroupUtils.getGroupUrls(groupName, resJson);
        const urls: Array<string> = [];
        for (let i = 0, len = resFile.length; i < len; i++) {
            urls.push(resFile[i].url);
        }
        this.removeUseRes(urls);
    }

    /**
     * 添加资源引用次数
     * @param url 资源路径，可是数组类型
     * @param isClear 是否能清理默认可清理资源
     */
    public addUseRes(url: string | Array<string>, isClear: boolean = true): void {
        let urls: Array<string>;
        if (typeof url === "string") {
            urls = [url];
        } else {
            urls = url;
        }

        let resName: string;
        let res: { count: number; cacheTime: number; isClear: boolean };
        for (let i = 0, len = urls.length; i < len; i++) {
            resName = urls[i];
            res = this._resCache[resName];
            if (!res) {
                res = {} as any;
                res.count = 1;
                res.cacheTime = 0;
                res.isClear = isClear;
                this._resCache[resName] = res;
            } else {
                res.count += 1;
                res.cacheTime = 0;
                res.isClear = isClear;
            }
        }
    }

    /**
     * 删除资源引用次数
     * @param url 资源路径，可是数组类型
     */
    public removeUseRes(url: string | Array<string>): void {
        let urls: Array<string>;
        if (typeof url === "string") {
            urls = [url];
        } else {
            urls = url;
        }

        let resName: string;
        let res: { count: number; cacheTime: number; isClear: boolean };
        for (let i = 0, len = urls.length; i < len; i++) {
            resName = urls[i];
            res = this._resCache[resName];
            if (res && res.count > 0) {
                res.count -= 1;
            }
        }
    }

    /** 清理配置数据 */
    public clearCfg() {
        this._chromeCfgs = {};
    }

    // /**
    //  * 移除资源名
    //  */
    // private release(resUrl: string): void {
    //     // 移除资源之前移除fgui包
    //     if (resUrl.indexOf("ui") !== -1) {
    //         const pkgName: string = resUrl.split("/")[1];
    //         this.removeUiPackage(pkgName);
    //     }
    //     cc.resources.release(resUrl);
    // }

    // public readConfigFile(filePath: string){
    //     let obj: cc.JsonAsset = this.getRes(filePath, cc.JsonAsset);
    //     if(obj == null){
    //         console.error(filePath + ' not found');
    //     }
    //     return obj.json;
    // }

    /**
     * 读取配置文件
     */
    public readConfigFile(fileName: string, release: boolean = false) {
        let json = this._readConfigFile(fileName, release);
        // let body = null;
        // if (json != null && json["Body"] != null) {
        //     body = json["Body"];
        // }
        // return body;
        return json;
    }

    /** 读取配置文件 */
    private _readConfigFile(fileName: string, release: boolean = true): Record<string, any> | null {
        let filePath = fileName;
        let obj: cc.JsonAsset = this.getRes(filePath, cc.JsonAsset);
        if (obj == null || obj.json == null) {
            let rFileName: string = fileName + ".json";
            let jsonStr: string = this._chromeCfgs[rFileName];
            if (jsonStr == null) {
                return null;
            }
            if (jsonStr.length > 0) {
                let json = null;
                try {
                    json = JSON.parse(jsonStr);
                    //释放占用的内存.
                    release && (delete this._chromeCfgs[rFileName]);
                } catch (error) { }
                return json;
            }
            return null;
        }
        return obj.json;
    }

    /** 浏览器用的游戏配置 */
    public setChromeCfg(cfgs: object) {
        this._chromeCfgs = { ...cfgs, ...this._chromeCfgs };
    }

    /** 微信用的游戏配置 */
    public setWxCfg(cfgs: object) {
        this._wxCfgs = cfgs;
    }

    /** 遍历配置获取md5表 */
    // public getCfgMd5s(finish: (md5s: object, md5sToOne: string) => void, target: object) {
    //     let md5s: object = this._getCfgMd5sInFile();
    //     let md5ToOne: string = this._getCfgMd5ToOne(md5s);

    //     if (finish != null && target != null) {
    //         finish.call(target, md5s, md5ToOne);
    //     }

    //     // this._getCfgMd5sByCalculation(finish, target);
    // }

    /** md5表转一个值 */
    // private _getCfgMd5ToOne(md5s: object): string {
    //     let array = [];
    //     for (let i in md5s) {
    //         array.push(i);
    //     }
    //     array.sort((a: string, b: string) => {
    //         return a.toUpperCase().localeCompare(b.toUpperCase())
    //     });

    //     let md5: string = "";
    //     let md5l = "";
    //     if (md5s != null) {
    //         let md5Str: string = "";
    //         for (let i = 0; i < array.length; i++) {
    //             md5Str += md5s[array[i]];
    //             //md5l += array[i] + " " + md5s[array[i]] + "\n";
    //         }
    //         //Logger.trace(md5l);
    //         md5 = SparkMD5.hash(md5Str);
    //     }

    //     return md5;
    // }

    /** 读取配置文件内写有的MD5值返回 */
    // private _getCfgMd5sInFile(): object {
    //     // let timeLog: number = new Date().getTime();

    //     let cfgs = this._chromeCfgs;

    //     let md5s: object = {};
    //     for (let i in cfgs) {
    //         let cfgStr: string = cfgs[i];
    //         if (cfgStr.length > 0) {
    //             try {
    //                 let cfg: object = JSON.parse(cfgStr);
    //                 if (cfg != null && cfg["MD5"] != null) {
    //                     md5s[i] = cfg["MD5"];
    //                 }
    //             } catch (error) {

    //             }
    //         }
    //     }

    //     // Logger.trace("md5表从文件直接获取 耗时:" + TimerUtil.getTimeFormat("S", new Date().getTime() - timeLog));
    //     return md5s;
    // }
}
