import * as cc from "cc";

import { AppConfig } from "../../../Config/GameConfig";
import Logger from "../../../Framework/Logger";
import EventManagerComponent from "../../GlobalComponent/EventManagerComponent";
import { LoadInfo, Loader } from "./Loader";
import { LoginEvent } from "../../../GameEvent/Events";

/**
 * 预加载器
 */
export class PreLoader {

    private percent: number = 0;

    private static _intance: PreLoader;

    public static get instance(): PreLoader {
        return PreLoader._intance || (PreLoader._intance = new PreLoader);
    }

    private loader: Loader;
    constructor() {
    }

    /**
     * 启动预加载
     */
    start() {
        this.loader = new Loader();
        let remoteUrl = AppConfig.remoteUrl + AppConfig.resourceBundle;
        let options = AppConfig.getOptions();

        //进入游戏前的预加载配置  
        let bundles: LoadInfo[] = [
            //加载配置表.
            {
                "bundleName": remoteUrl, options: options, "res": [
                    //配置
                    // { "url": "config/gamecfg", "type": cc.BufferAsset },
                    // { "url": "config/map", "type": cc.BufferAsset },
                    //界面通用
                    // { "url": "ui/common", "dir": true, "loadType": true },
                    //主界面
                    // { "url": "ui/mainUI/mainUI", "type": cc.Prefab },
                    //战斗相关
                    // { "url": "ui/fight", "dir": true, "loadType": true },
                    //李逍遥
                    // { "url": "mapThing/model/1020001", "dir": true, "loadType": true },
                    //主game
                    { "url": "game", "type": cc.SceneAsset },
                    //主场景img
                    // { "url": "map/img_2000001", "dir": true, "loadType": true },
                    //主场景tmx
                    // { "url": "map/tmx_2000001/2000001", "type": cc.TiledMapAsset },
                    //转场动画1
                    // { "url": "ui/map/MapLoading_zong", "type": cc.Prefab },
                    //转场动画2
                    // { "url": "ui/map/LoadingHeimu1", "type": cc.Prefab },
                ]
            },
        ]
        
        // TODO
        // if (!App.UserController.userInfo.isFirstPlayGame) {
        //     let urlRes: any[]  = [
        //         //开场1特效
        //         { "url": "ui/map/open/open1", "type": cc.Prefab },
        //         //开场2特效
        //         { "url": "ui/map/open/open2", "type": cc.Prefab },
        //     ]
        //     for (let url of urlRes) {
        //         bundles[0].res.push(url);
        //     }
        // }

        Logger.zz_log("zhangzhen_PreLoader_begin:" + Date.now());
        this.loader.load(bundles, this.onProgress, this.onComplete, this);
    }

    private onProgress(value: number) {
        this.percent = value;
        const eventManager = EventManagerComponent.instance();
        eventManager.dispatch(LoginEvent.PRE_LOADING_PROGRESS, value);
    }

    private onComplete() {
        Logger.zz_log("zhangzhen_PreLoader_onComplete:" + Date.now());
        const eventManager = EventManagerComponent.instance();
        eventManager.dispatch(LoginEvent.PRE_LOAD_COMPLETE, null);
    }

    /**
     * 获取进度值
     * @returns 
     */
    getPercent(): number {
        return this.percent;
    }

    /**
     * 进入游戏 加载页面才能启动游戏
     */
    enter(callback: Function) {
        cc.director.loadScene("game", () => {
            callback && callback();
            callback = null;
        })
    }
}