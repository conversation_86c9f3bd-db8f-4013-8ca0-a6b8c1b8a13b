import * as cc from "cc";

import { AppConfig } from "../../../../Config/GameConfig";
import { ViewId } from "../../../../Config/ViewConst";
import * as Store from "../../../../Framework/Storage";
import Logger from "../../../../Framework/Logger";
import { LoadUtils } from "../../../Utils/LoadUtils";
import SdkManger, { TimingType } from "../../../SDK/SDKManager";
import SceneBase from "../../../Components/SceneBase";
import UIButton from "../../../Components/UIButton";
import { Guid } from "../../../Utils/Guid";
import { UIUtils } from "../../../Utils/UIUtils";
import { LoginEvent } from "../../../../GameEvent/Events";

import { PreLoader } from "../PreLoader";
import EventManagerComponent from "../../../GlobalComponent/EventManagerComponent";
import ViewManager from "../../../GlobalComponent/ViewManagerComponent";
import SDKManagerComponent from "../../../GlobalComponent/SDKManagerComponent";

const { ccclass, property } = cc._decorator;

/**
 * 加载游戏组件
 */
@ccclass
export default class Loading extends SceneBase {

    readonly startNodeShowTime = 1.5;

    @property(UIButton)
    enterGame: UIButton = null;

    @property(UIButton)
    ageBtn: UIButton = null;

    @property(UIButton)
    btnRandomName: UIButton = null;

    @property(UIButton)
    noticeBtn: UIButton = null;

    @property(cc.EditBox)
    loginNameEditBox: cc.EditBox = null;

    @property(UIButton)
    btnSelectServer: UIButton = null;

    @property(UIButton)
    btnCheck: UIButton = null;

    /** 界面节点  */
    @property(cc.Node)
    viewNode: cc.Node = null;

    @property(cc.Node)
    conBottom: cc.Node = null;

    @property(cc.Node)
    camera: cc.Node = null;

    @property(cc.Label)
    serverLbFlag: cc.Label = null;

    @property(cc.Sprite)
    serverImFlag: cc.Sprite = null;

    timer = null;

    @property(cc.Node)
    loginNode: cc.Node = null;

    private initRegions: boolean = false; //是否返回大区列表
    private initProgressComp: boolean = false; //进度条是否加载完毕

    /**微信SDK协议是否同意 */
    private isSDKAgree: boolean = false;

    //适配大小 添加层 侦听添加“别急”图标
    onLoad() {

        super.onLoad();
        const eventManager = EventManagerComponent.instance();
        this.init();

        Logger.enable = AppConfig.isDebug;
        Logger.reportUrl = AppConfig.getErrorReportUrl();
        LoadUtils.remoteUrl = AppConfig.remoteUrl;
        LoadUtils.remoteBundle = AppConfig.resourceBundle;
        // App.UserController.loginInfo.init(AppConfig.isTest);

        cc.director.addPersistRootNode(this.viewNode);
        cc.view.setOrientation(cc.macro.ORIENTATION_PORTRAIT);
        cc.view.enableAutoFullScreen(false);
        this.initControllers();

        this.registerBtnEvents();
        this.registerEvents();

        const sdkManager = SDKManagerComponent.instance();
        sdkManager.initGameSdk().then(async () => {
            // 监听角色登录成功
            this.setUIActiveState(sdkManager);
            await sdkManager.sdkLogin(AppConfig.channelId, AppConfig.gameVersion);
            await sdkManager.requirePrivacyAuthorize();
        });

    }

    private setUIActiveState(sdkManager: SDKManagerComponent) {
        if (AppConfig.isTest) {
            this.btnRandomName && (this.btnRandomName.node.active = true);
        } else if (SdkManger.isWeChatRunTime() || SdkManger.isAndroidRunTime()) {
            this.btnRandomName && (this.btnRandomName.node.active = false);
            this.loginNameEditBox && (this.loginNameEditBox.node.active = false);
        }

        // if (AppConfig.online) {
        //     this.loginNameEditBox && (this.loginNameEditBox.node.active = false);
        //     this.btnRandomName && (this.btnRandomName.node.active = false);
        // }

        //记录用户是否勾选已阅读协议
        // const isAgree: boolean = Store.getItem("Agree");
        // this.btnCheck.checked = isAgree ?? false;
        this.btnCheck && (this.btnCheck.node.active = false);
        let lblReaded = UIUtils.find("lblReaded", this.node);
        lblReaded && (lblReaded.active = false);

        this.loginNode.active = false;
        if (sdkManager.getSdkBase().isDirectlyEnter()) {
            this.loginNode.active = false;
        }
        else {
            this.loginNode.active = true;
        }
        console.log(this.loginNode.active);
    }

    private registerBtnEvents(): void {
        console.log("registerBtnEvents", ViewManager, SDKManagerComponent);
        const viewManager = ViewManager.instance();

        if (this.btnRandomName) {
            this.btnRandomName.setOnClick(() => {
                const localId = Guid.raw();
                if (AppConfig.openId == "AAA") {
                    Store.setItem("Guid", localId);
                    AppConfig.openId = localId;
                }

                this.loginNameEditBox.string = localId;
            }, this);
        }

        this.ageBtn && this.ageBtn.setOnClick(() => {
            viewManager.show(ViewId.AGE_TIPS, {}, false);
        }, this);

        this.noticeBtn && this.noticeBtn.setOnClick(() => {
            viewManager.show(ViewId.GONG_GAO, {}, false);
        }, this);

        //点击进入游戏
        console.log("enterGame", this.enterGame);
        this.enterGame.setOnClick(this.onStartGame, this);

        this.btnSelectServer && this.btnSelectServer.setOnClick(() => {
            viewManager.show(ViewId.SELECT_SERVER);
        }, this);
    }

    private registerEvents(): void {
        const eventManager = EventManagerComponent.instance();
        // App.EventManager.addEventListener(SocketEvent.LOGIN_SUCCESS, this.onLoginSuccess); //登录成功
        eventManager.addEventListener(LoginEvent.USER_SELECT_SERVER, this.onSelectServerClosedHandler, this);
        eventManager.addEventListener(LoginEvent.CHECK_WX_PRIVACY, this.onCheckSDKPrivacy, this);
        eventManager.addEventListener(LoginEvent.OPEN_PRIVACY_POLICY_URL_VIEW, this.onOpenPrivacyContract, this);
        eventManager.addEventListener(LoginEvent.SERVER_LIST_UPDATED, this.onResponseServerList, this);
    }

    /**检查微信SDK隐私协议是否同意 */
    private onCheckSDKPrivacy(isArgee: boolean): void {
        this.isSDKAgree = isArgee;
    }

    /**请求服务器列表 */
    private requestServerList(): void {
        const player = SDKManagerComponent.instance().getPlayer();
        if (!player.isLogin) {
            return;
        }

        player.requestServerList();
    }

    /**
     * 响应服务器列表
     * @param responseText 
     */
    private onResponseServerList() {
        console.log('onResponseServerList');
        LoadUtils.remoteOption = AppConfig.getOptions();

        this.initRegions = true;
        this.updateShowServer();

        // this.showNoticeView();

        const sdkManager = SDKManagerComponent.instance();
        if (sdkManager.getSdkBase().isDirectlyEnter() && !sdkManager.getPlayer().isReLogin) {
            this.loginNode.active = false;
            if (AppConfig.serverStatus == 0) {
                this.loginNode.active = true;
                return;
            }
            this.onStartGame()
        }
        else {
            this.loginNode.active = true;
        }

        LoadUtils.load(ViewId.LOADING, cc.Prefab, () => {
            this.onLoading();
        });
    }

    /**开始游戏 */
    private onStartGame() {
        Logger.logView("onStartGame");
        // if (!this.btnCheck.checked) {
        //     App.showTip("请先阅读用户协议和隐私协议");
        //     return;
        // }
        if (!(SdkManger.isWeChatRunTime() || SdkManger.isAndroidRunTime())) {

            if (!AppConfig.online) {
                if (this.loginNameEditBox.string == "") {
                    // App.showTip(App.getLanCustom("language50"));
                    return;
                }
                AppConfig.openId = this.loginNameEditBox.string;
                Store.setItem("Guid", this.loginNameEditBox.string);
            }

        } else {
            //微信这里不校验隐私权限，后面再获取头像等隐私信息的时候，低版本可能拿不到数据，要做校验
            // if (!this.isSDKAgree) {
            //     App.SdkManger.getSdkBase().requirePrivacyAuthorize();//弹出隐私框
            //     return;
            // }
        }

        // Store.setItemJson("Agree", this.btnCheck.checked);
        this.enterGame.setGrayEffect(true);
        this.enterGame.disabled = true;

        this.showWaiting(false); //隐藏别急
        SdkManger.requestDot(AppConfig.channelId, AppConfig.openId, TimingType.START_GAME);
        Store.setItem(`${AppConfig.openId}:selServerId`, AppConfig.serverID.toString());
        // this.camera.parent = this.viewNode;
        // Logger.zz_log("zhangzhen_onStartGame:" + Date.now());


        Logger.logView("show Loading");
        console.log("show Loading, view component", ViewManager, SDKManagerComponent);
        const viewManager = ViewManager.instance();
        viewManager.show(ViewId.LOADING, {}, false, this.viewNode);
        Logger.logView("show Loading done");
    }

    /** 初始化一些控制器 **/
    private initControllers(): void {
    }

    /**跳转至隐私协议链接页面 */
    private onOpenPrivacyContract(): void {
        const sdkManager = SDKManagerComponent.instance();
        sdkManager.getSdkBase().openPrivacyContract();
    }

    protected onDestroy(): void {
        Logger.trace("Loading Scene. onDestroy");
        super.onDestroy();
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }

    start() {
    }

    /** 连接分服socket失败 */
    onSocketError() {
        // App.showTip(App.getLanCustom("language51"));
        // this.hideWaiting();
        // this.setBtnFree();
    }

    setBtnFree() {
        this.hideWaiting();

        // this.enterTime = 0;
        this.enterGame.setGrayEffect(false);
        this.enterGame.disabled = false;
    }

    //关闭了选服界面
    private onSelectServerClosedHandler(selServerId: number): void {
        this.updateShowServer(selServerId);
    }

    private updateShowServer(selId: number = -1): void {
        const player = SDKManagerComponent.instance().getPlayer();
        console.log('player', player.isLogin);
        if (!player.isLogin) {
            return;
        }

        //上次登录的服务器
        let selServerId = selId;
        let servers = player.getServers();
        console.log('servers', servers);
        if (selId == -1) {
            let plays: any[] = player.getPlayers();
            if (plays && plays.length > 0)
                selServerId = plays[0].serverId;
            if (isNaN(selServerId) || selServerId < 0) {
                selServerId = servers[0].id;
            }
        }

        // AppConfig.openId = this.loginNameEditBox.string;
        this.loginNameEditBox.string = player.getOpenId();

        let curSelServ;
        for (var index = 0; index < servers.length; index++) {
            let obj = servers[index];
            if (selServerId == obj.id) {
                curSelServ = obj;
                break;
            }
        }

        console.log('curSelServ', curSelServ);

        //如果不在推荐服, 在当前区服查找
        if (!curSelServ) {
            let curRegionServerList = player.getRegionList();
            if (curRegionServerList)
                for (var index = 0; index < curRegionServerList.length; index++) {
                    let obj: any = curRegionServerList[index];
                    if (selServerId == obj.id) {
                        curSelServ = obj;
                        break;
                    }
                }
        }

        if (servers && servers.length > 0) {
            if (!curSelServ) {
                curSelServ = servers[0];
            }

            //let pattern = /^(\d{1,3}\.){3}\d{1,3}$/;
            //使用IP地址的使用 ws.  域名的使用wss
            if (curSelServ.socketType == "wss") {
                //Url = "wss://www.xiugoux1.cn/game?ip=**********&port=10001";
                AppConfig.serverUrl = "wss://" + AppConfig.socketPre + "ip=" + curSelServ.ip + "&port=" + curSelServ.port;
            } else {
                AppConfig.serverUrl = "ws://" + curSelServ.ip + ":" + curSelServ.port;
            }
            console.log('curSelServ', curSelServ);
            AppConfig.serverID = parseInt(curSelServ.id);
            AppConfig.serverName = curSelServ.name;
            AppConfig.serverStatus = curSelServ.status;
            // AppConfig.serverUrl  = "wss://www.xiugoux1.cn/game?ip=**********&port=10001"
            // AppConfig.serverID = 1;
            // Logger.trace(selServerId, curSelServ, curSelServ.ip, AppConfig.serverUrl, AppConfig.serverID);
            const ldlServerId = this.conBottom.getChildByPath("imgServerBg/ldlServerId");
            const ldlServerName = this.conBottom.getChildByPath("imgServerBg/ldlServerName");

            ldlServerId.getComponent(cc.Label).string = "S-" + curSelServ.id;
            ldlServerName.getComponent(cc.Label).string = curSelServ.name;

            let imgs = ["img_weihu", "img_xinfu", "img_baoman", "img_xinfu"];
            cc.assetManager.loadBundle("Game", (err, bundle) => {
                bundle.load("Scene/login/login", cc.SpriteAtlas, null, (error, data: cc.SpriteAtlas) => {
                    this.serverImFlag.spriteFrame = data.getSpriteFrame(imgs[curSelServ.status]);
                });
            });

            let texts = ["维护", "新服", "爆满", "新服"];
            this.serverLbFlag.string = texts[curSelServ.status];
            Logger.logView("updateShowServer done");
        }
    }

    /**
     * 打开公告页面
     */
    private showNoticeView() {
        console.info("showNoticeView");

        this.enterGame.node.active = true;
        this.conBottom.active = true;

        const eventManager = EventManagerComponent.instance();
        eventManager.addEventListener(LoginEvent.OPEN_NOTICE_FINISH, this.openNoticeFinish, this);

        const viewManager = ViewManager.instance();
        viewManager.show(ViewId.GONG_GAO, {}, false);
    }

    /**
     * 启动预加载
     */
    private openNoticeFinish() {
        console.info("openNoticeFinish");

        const eventManager = EventManagerComponent.instance();
        eventManager.removeEventListener(LoginEvent.OPEN_NOTICE_FINISH, this.openNoticeFinish, this);
    }

    private onLoading() {
        PreLoader.instance.start();
    }

    //预加载两个资源成功 显示登录按钮，以及公告界面
    showLoginBtn() {
    }

    /* 检查是否显示登录按钮 */
    private checkShowLoginBtn(): void {
        if (this.initRegions && this.initProgressComp) {
            this.showLoginBtn();
        }
    }

    hideWaiting() {

    }

    showWaiting(value: boolean) {

    }
}

/** 打印缓存中所有资源信息 */
window["dump"] = function () {
    cc.assetManager.assets.forEach((value: cc.Asset, key: string) => {
        Logger.trace(cc.assetManager.assets.get(key));
    })
    Logger.trace(`当前资源总数:${cc.assetManager.assets.count}`);
}

