import * as cc from "cc";

import { AppConfig } from "../../../../Config/GameConfig";
import  Logger  from "../../../../Framework/Logger";
import SdkManger, { TimingType } from "../../../SDK/SDKManager";
// import QuadtreeManager from "../../../manager/quadtree/QuadtreeManager";
import BaseView from "../../../Components/BaseView";
// import { pb } from "../../../net/proto/gameProto";
import { LoadInfo, Loader } from "../Loader";
import { LoginEvent, MapEvent } from "../../../../GameEvent/Events";
import { LoginUtils } from "../LoginUtils";
import { PreLoader } from "../PreLoader";

import EventManagerComponent from "../../../GlobalComponent/EventManagerComponent";

const { ccclass } = cc._decorator;

@ccclass
export default class LoadingView extends BaseView {
    private isView: boolean = false;

    private progress: cc.ProgressBar;
    private desc: cc.Label;
    private loader: Loader;

    onLoad() {
        Logger.logView("LoadingView onLoad");
        this.initUI();
    }

    protected start(): void {
        const eventManager = EventManagerComponent.instance();
        eventManager.addEventListener(LoginEvent.PRE_LOADING_PROGRESS, this.onProgress, this);
        eventManager.addEventListener(LoginEvent.PRE_LOAD_COMPLETE, this.onPreLoadComplete, this);
        eventManager.addEventListener(MapEvent.LOAD_MAP_COMPLETE, this.onParseMapCompHandler, this);

        let percent: number = PreLoader.instance.getPercent();
        this.onProgress(percent, true);
        if (percent >= 1) this.onPreLoadComplete();
    }

    protected update(dt: number): void {
        // this.updateLblProgres() 
    }

    private updateLblProgres(): void {
        this.desc.string = Math.ceil(this.progress.progress * 100) + "%"
    }

    private initUI(): void {
        this.progress = LoginUtils.find("pb", this.node).getComponent(cc.ProgressBar);
        this.desc = LoginUtils.find("lblProgress2", this.node).getComponent(cc.Label);
    }

    /** 加载进度发生改变  */
    private onProgress(percent: number, value?: boolean): void {
        if (!this.isView && value) {
            this.isView = true;
        }
        if (this.isView) {
            percent = percent * 0.8;
            this.progress.progress = this.progress.progress < percent ? percent : this.progress.progress;
        }
    }

    /** 强行设置进度  */
    private froceSetProgress(val: number): void {
        cc.Tween.stopAllByTarget(this.progress);
        this.progress.progress = val / 100;
    }

    /** 预加载资源完成  */
    private onPreLoadComplete() {
        Logger.logView("LoadingView onPreLoadComplete");
        this.scheduleOnce(this.enterGameWorld, 0.1);
        const eventManager = EventManagerComponent.instance();
        eventManager.addEventListener(MapEvent.Map_INIT_COMPLETE, this.onInitComplete, this);
        eventManager.addEventListener(MapEvent.ENTER_WORLD_SUCCESS, this.onEnterWorldSuccessHandler, this);
        eventManager.addEventListener(MapEvent.LOAD_MAP_PROGRESS, this.onLoadMainLandProHandler, this);
        cc.Tween.stopAllByTarget(this.progress);
        cc.tween(this.progress).to(0.2, { "progress": 0.84 }).start();
    }

    /** 解析地图完成  */
    private onParseMapCompHandler(): void {
        this.froceSetProgress(99);
        //cc.Tween.stopAllByTarget(this.progress);
        //cc.tween(this.progress).to(1, { "progress": 0.99 }).start();
    }

    /** 地图加载完毕，等待加载主角完成  */
    private onInitComplete() {
        this.froceSetProgress(99);
        //cc.Tween.stopAllByTarget(this.progress);
        //cc.tween(this.progress).to(0.5, { "progress": 0.99 }).start();
    }

    /** 地图加载完毕，移除加载页  */
    private onEnterWorldSuccessHandler(): void {
        this.froceSetProgress(100);
        //加大加载并发数量.
        // cc.assetManager.downloader.maxConcurrency = 10;
        // cc.assetManager.downloader.maxRequestsPerFrame = 6;

        const eventManager = EventManagerComponent.instance();
        eventManager.removeEventListener(MapEvent.Map_INIT_COMPLETE, this.onInitComplete, this);
        eventManager.removeEventListener(MapEvent.ENTER_WORLD_SUCCESS, this.onEnterWorldSuccessHandler, this);
        eventManager.removeEventListener(MapEvent.LOAD_MAP_PROGRESS, this.onLoadMainLandProHandler, this);

        let UINode = this.node.parent;
        cc.director.addPersistRootNode(UINode);
        UINode.destroy();
        SdkManger.requestDot(AppConfig.channelId, AppConfig.openId, TimingType.IN_GAME);

        this.scheduleOnce(() => {
            // TODO:
            // let req = pb.PlayerMoreInfoRequest.fromObject({});
            // App.Socket.send(pb.PlayerMoreInfoRequest.Proto.ID, pb.PlayerMoreInfoRequest.encode(req).finish());
        }, 1000);

        this.loadRes();
        // QuadtreeManager.instance.froceUpdate(App.UserController.isUnlockVillage());
    }

    private loadRes() {
        this.loader = new Loader();
        let remoteUrl = AppConfig.remoteUrl + AppConfig.resourceBundle;
        let options = AppConfig.getOptions();
        //进入游戏前的预加载配置  
        let bundles: LoadInfo[] = [
            //加载配置表.
            {
                "bundleName": remoteUrl, options: options, "res": [
                    //主场景img
                    { "url": "map/img_2000001", "dir": true, "loadType": true },
                ]
            },
        ]
        // TODO: 
        // this.loader.load(bundles, null, null, this);
    }

    /**加载当前地图的进度  */
    private onLoadMainLandProHandler(data: { percent: number, totalCount: number }): void {
        const { percent } = data;
        if (this.isView) {
            const progress = percent > 1 ? 0.99 : (0.84 + percent * 0.1);
            this.progress.progress = progress;
        }
    }

    protected onDestroy(): void {
        Logger.trace("LoadingView onDestroy", Date.now());
        const eventManager = EventManagerComponent.instance();
        eventManager.removeEventListener(LoginEvent.PRE_LOADING_PROGRESS, this.onProgress, this);
        eventManager.removeEventListener(LoginEvent.PRE_LOAD_COMPLETE, this.onPreLoadComplete, this);
        eventManager.removeEventListener(MapEvent.LOAD_MAP_COMPLETE, this.onParseMapCompHandler, this);
    }

    private enterGameWorld() {
        Logger.logView("enterGameWorld");
        PreLoader.instance.enter(() => {
            //卸载部分内容
            Logger.logView("enterGameWorld done");
        });
    }
}
