import * as cc from "cc";
import { LoginEvent } from "../../../../GameEvent/Events";
import EventManagerComponent from "../../../GlobalComponent/EventManagerComponent";

const { ccclass, property } = cc._decorator;

@ccclass
export default class AgreeClickCtrl extends cc.Component {
    //点击用户协议
    clickUser(): void {
        // cc.sys.openURL("http://www.baidu.com");
        const eventManager = EventManagerComponent.instance();
        eventManager.dispatch(LoginEvent.OPEN_PRIVACY_POLICY_URL_VIEW, true);
    }

    //点击隐私协议
    handlerPrivacy(): void {
        // cc.sys.openURL("http://cn.chinadaily.com.cn/");
        const eventManager = EventManagerComponent.instance();
        eventManager.dispatch(LoginEvent.OPEN_PRIVACY_POLICY_URL_VIEW, false);
    }

}
