import * as cc from "cc";


/**
 * 加载的信息
 */
export class LoadInfo {
    /**Bundle 名 */
    bundleName: string;
    /** 对应加载的资源列表 */
    res?: LoadRes[];
    /** 参数选项 */
    options?: any;
}

/**
 * 加载的资源
 */
export class LoadRes {
    /**资源路径 */
    url: string;
    /**资源类型 */
    type?: typeof cc.Asset;
    /**完成回调 */
    complete?: Function;
    /**调用对象 */
    caller?: any;
    /**是否为文件夹 */
    dir?: boolean;
    /** false.预加载  true:加载 */
    loadType?: boolean = false;
}

export enum LoadType {
    /** 预加载 */
    preload,
    /** 只加载, 不增加引用计数 */
    onlyLoad,
    /** 加载后常驻内存. 引用计数+1 */
    permanent,
}

/**
 * 加载器.
 */
export class Loader {

    private _arr: LoadInfo[];

    private _progress: Function;

    private _complete: Function;

    private _caller: any;

    private _current: LoadInfo;

    private _bundleIndex: number = 0;

    private _resIndex: number = 0;

    private readonly defaultBundleName = "resources";

    load(arr: LoadInfo[], progress?: Function, complete?: Function, caller?: any) {
        this._arr = arr;
        this._bundleIndex = 0;
        this._progress = progress;
        this._complete = complete;
        this._caller = caller;

        if (this._arr && this._arr.length > 0) {
            this.loadNext();
        }
    }

    private loadNext() {
        // console.log("loadNext", Date.now(), this._bundleIndex);

        if (this._bundleIndex < this._arr.length) {
            this._current = this._arr[this._bundleIndex];
            // console.log("loadNext", Date.now(), this._bundleIndex, this._current);

            if (this._current.bundleName && this._current.bundleName.length > 0) {
                let bundle = cc.assetManager.getBundle(this._current.bundleName);
                if (!bundle) {
                    cc.assetManager.loadBundle(this._current.bundleName, this._current.options, (err: Error, bundle: cc.AssetManager.Bundle) => {
                        if (err) {
                            console.error("loadBundleComplete error!", err);
                        } else {
                            this.loadBundleComplete(bundle);
                        }
                    });
                } else {
                    // console.log("exist", this._current.bundleName);
                    this.loadBundleComplete(bundle);
                }
            } else {
                // console.log("not exist", "default");
                this.loadBundleComplete(null)
            }
        } else {
            this._current = null;
            this._progressHandler(1, 1);
            this._complete && this._complete.apply(this._caller);
        }
    }

    private loadBundleComplete(bundle: cc.AssetManager.Bundle) {
        console.log("loadBundleComplete", Date.now(), bundle.name);
        const v=  cc.assetManager.getBundle(bundle.name)
        console.log('try get agagin', v.name);
        this._resIndex = 0;
        // this._bundle = bundle;

        if (this._current.res && this._current.res.length > 0) {
            this.loadNextRes(bundle);
        } else {
            this._progressHandler(1, 1);
            this._bundleIndex++;
            this.loadNext();
        }
    }

    private loadNextRes(bundle: cc.AssetManager.Bundle) {
        if (this._resIndex < this._current.res.length) {
            let res: LoadRes = this._current.res[this._resIndex];
            // console.log(">>loadNextRes", Date.now(), this._resIndex, res, bundle);
            this._load(res, bundle);
        } else {
            this._bundleIndex++;
            this.loadNext();
        }
    }

    private _progressHandler(finish: number, total: number) {
        let resPercent: number = 0;
        if (this._current && this._current.res && this._current.res.length > 0) {
            resPercent = (this._resIndex + (finish / total)) / this._current.res.length;
        }

        let percent: number = (this._bundleIndex + resPercent) / this._arr.length;
        // console.log("progressHandler",
        //     "res:" + (this._current ? this._current.res.toString() : null),
        //     "_bundleIndex:" + this._bundleIndex,
        //     "_arr:" + this._arr.length,
        //     "resPercent:" + resPercent,
        //     "finish:" + finish,
        //     "total:" + total,
        //     "percent:" + percent,
        // );

        // console.log("finish:" + finish, "total:" + total, "percent:" + percent)
        this._progress && this._progress.apply(this._caller, [percent]);
    }

    private _load(res: LoadRes, bundle?: cc.AssetManager.Bundle) {
        let funcCaller: any;
        let loadFunc: Function;
        if (res.type == cc.SceneAsset) {
            funcCaller = cc.director;
            loadFunc = cc.director.preloadScene;
        } else {
            if (!bundle) {
                bundle = cc.assetManager.getBundle(this.defaultBundleName)
            }
            funcCaller = bundle;
            if (res.dir) {
                loadFunc = res.loadType ? bundle.loadDir : bundle.preloadDir;
            } else {
                loadFunc = res.loadType ? bundle.load : bundle.preload;
            }
        }
        // console.log("_load", Date.now(), res, loadFunc, bundle);
        loadFunc.apply(funcCaller, [
            res.url,
            // res.type,
            (finish: number, total: number, item: cc.AssetManager.RequestItem) => {
                this._progressHandler(finish, total);
            },
            (error: Error, asset?: cc.Asset) => {
                console.log("load complete", Date.now(), res.url);
                if (error) cc.error(bundle, res, error);
                //引用计数+1, 避免删掉
                // if(asset && asset instanceof cc.Asset) {
                //     asset.addRef();
                // }
                // console.log("load complete", Date.now(), res);
                res.complete && res.complete.apply(res.caller);

                this._resIndex++;
                this.loadNextRes(bundle);
            }
        ]);
    }
}