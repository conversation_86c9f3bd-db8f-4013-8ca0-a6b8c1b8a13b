import * as cc from "cc";

import Logger from "../../../Framework/Logger";
import { TileMapData } from "../Map/tiled/TileMapData";
// import { HeroManager } from "../Managers/Fight/HeroManager";

const { ccclass, property } = cc._decorator;

export const NORMAL_ZOOM_RATIO = 0.85;
export const HEIGHT_ZOOM_RATIO = 0.75;

export enum MoveType {
    Immediately,
    QuickTween,
    Tween
}

/**
 * 摄像机控制类
 */
@ccclass
export default class CameraController extends cc.Component {
    public static _instance: CameraController = null;
    public static get instance(): CameraController {    
        if (!this._instance) {   
            throw new Error("CameraController instance is not initialized");
        }
        return this._instance;
    }

    public static set instance(newInst: CameraController) {
        if (this._instance) {
            throw new Error("CameraController instance is already initialized");
        }
        this._instance = newInst;
        Logger.log("CameraController onLoad " + newInst.node.name);
    }

    @property(cc.Camera)
    public camera: cc.Camera = null;

    private currentZoomRatio = 1;

    private target: cc.Node = null;

    private targetPos: cc.Vec3 = cc.v3(0, 0, 1000);

    /** 偏移高度 用于计算中心点 */
    private offsetHeight: number = 80;

    private moveType: MoveType = MoveType.Tween;

    private moveCmpCallBack: Function = null;

    private isMoveing: boolean = false;

    private isLockMove: boolean = false;

    private targetList: cc.Node[] | cc.Vec2[] = []
    private mapData: TileMapData | null = null;

    public set isLock(lock: boolean) {
        this.isLockMove = lock;
    }

    public get isLock() {
        return this.isLockMove;
    }

    public get isMove() {
        return this.isMoveing;
    }

    onLoad() {
        CameraController.instance = this;
    }

    public init(offsetHeight: number = 80): void {
        this.offsetHeight = offsetHeight;
        this.currentZoomRatio = NORMAL_ZOOM_RATIO;
    }

    public setCurrentMap(mapData: TileMapData): void {
        this.mapData = mapData;
    }

    public moveCameraTo(pos: cc.Vec2) {
        const v = this.camera.node.position.clone();
        v.x = pos.x;
        v.y = pos.y;
        this.camera.node.setPosition(v);
    }

    update(dt) {
        if (this.isLockMove) return;
        this.followTarget(dt);
        this.updateZoomRatio(dt);
    }

    /**
     * 获得摄像机坐标
     * @returns
     */
    public getCameraPosition(position: cc.Vec3 = null): cc.Vec3 {
        if (this.camera == null) {
            return cc.Vec3.ZERO;
        }
        if (position == null) {
            position = new cc.Vec3();
        }
        //对摄像机位置向上取整后返回
        position.x = Math.ceil(this.camera.node.position.x);
        position.y = Math.ceil(this.camera.node.position.y);
        position.z = 0;
        return position;
    }

    /**Camera中心坐标 */
    public getCenterPosition(position: cc.Vec3 = null): cc.Vec3 {
        if (this.camera == null) {
            return cc.Vec3.ZERO;
        }
        if (position == null) {
            position = new cc.Vec3();
        }
        const winSize = cc.view.getDesignResolutionSize();
        position.x = Math.ceil(this.camera.node.position.x + winSize.width / 2);
        position.y = Math.ceil(this.camera.node.position.y + (winSize.height) / 2);
        return position;
    }

    public setTarget(target: cc.Node, moveType: MoveType = MoveType.Tween) {
        this.target = target;
        this.moveType = moveType;
    }

    public setTargetPoint(target: cc.Vec3, moveType: MoveType = MoveType.Tween) {
        const winSize = cc.view.getDesignResolutionSize();
        this.moveType = moveType;
        this.target = null;
        this.targetPos = target.subtract(cc.v3(winSize.width / 2, winSize.height / 2));

        const mapWidth = this.mapData.width;
        const mapHeight = this.mapData.height;

        if (mapWidth < winSize.width) {
            this.targetPos.x = (mapWidth - winSize.width) / 2;
        } else {
            if (this.targetPos.x > mapWidth - winSize.width) {
                this.targetPos.x = mapWidth - winSize.width;
            } else if (this.targetPos.x < 0) {
                this.targetPos.x = 0;
            }
        }

        if (mapHeight < winSize.height) {
            this.targetPos.y = (mapHeight - winSize.height) / 2;
        } else {
            if (this.targetPos.y > mapHeight - winSize.height) {
                this.targetPos.y = mapHeight - winSize.height - this.offsetHeight;
            } else if (this.targetPos.y < 0) {
                this.targetPos.y = 0;
            }
        }
    }
    /**
     * 视野跟随目标
     * @param dt
     */
    public followTarget(dt: number): void {
        if (this.camera == null) {
            return;
        }

        //定位新的目标坐标
        if (this.target && this.target.isValid) {
            const winSize = cc.view.getDesignResolutionSize();
            this.targetPos = this.target.position.subtract(cc.v3(winSize.width / 2, winSize.height / 2));
            this.targetPos.y += this.offsetHeight;

            const mapWidth = this.mapData.width;
            const mapHeight = this.mapData.height;

            if (mapWidth < winSize.width) {
                this.targetPos.x = (mapWidth - winSize.width) / 2;
            } else {
                if (this.targetPos.x > mapWidth - winSize.width) {
                    this.targetPos.x = mapWidth - winSize.width;
                } else if (this.targetPos.x < 0) {
                    this.targetPos.x = 0;
                }
            }

            if (mapHeight < winSize.height) {
                this.targetPos.y = (mapHeight - winSize.height) / 2;
            } else {
                if (this.targetPos.y > mapHeight - winSize.height) {
                    this.targetPos.y = mapHeight - winSize.height - this.offsetHeight;
                } else if (this.targetPos.y < 0) {
                    this.targetPos.y = 0;
                }
            }
        }

        if (this.moveType == MoveType.Tween || this.moveType == MoveType.QuickTween) {//摄像机平滑跟随
            let _tweenRate: number = this.moveType == MoveType.Tween ? 5 : 6.5;
            this.camera.node.position.lerp(this.targetPos, dt * _tweenRate);

            this.camera.node.setPosition(this.camera.node.position.x, this.camera.node.position.y, 0);
            if (cc.Vec3.distance(this.camera.node.position, this.targetPos) <= 10) {

                if (this.targetList && this.targetList.length > 0) {
                    let target = this.targetList.pop();
                    if (target instanceof cc.Node) {
                        this.setTarget(target);
                    } else if (target instanceof cc.Vec3) {
                        this.setTarget(null);
                        this.setViewToPoint(target.x, target.y, true);
                    }
                }
                else {
                    if (this.moveCmpCallBack != null) {
                        this.moveCmpCallBack();
                        this.moveCmpCallBack = null;
                    }
                    this.isMoveing = false;
                }

            }
        } else {
            this.camera.node.position = this.targetPos;
            if (this.targetList && this.targetList.length > 0) {
                let target = this.targetList.pop();
                if (target instanceof cc.Node) {
                    this.setTarget(target);
                } else if (target instanceof cc.Vec2) {
                    this.setTarget(null);
                    this.setViewToPoint(target.x, target.y, true);
                }
            }
            else {
                if (this.moveCmpCallBack != null) {
                    this.moveCmpCallBack();
                    this.moveCmpCallBack = null;
                }
                this.isMoveing = false;
            }
        }
    }

    /**
     *把视野定位到给定位置
     * @param px 中心点 x
     * @param py 中心点 y
     *
     */
    public setViewToPoint(px: number, py: number, move: boolean = false): void {
        console.log("Camera Manager set view point to", px, py);
        const mapWidth = this.mapData.width;
        const mapHeight = this.mapData.height;
        if (!this.targetPos) this.targetPos = cc.v3(0, 0, 1000);
        const designResolutionSize = cc.view.getDesignResolutionSize();

        py += this.offsetHeight; // 增加偏移高度

        let xRight = px + designResolutionSize.width / 2;
        let xLeft = px - designResolutionSize.width / 2;
        let yTop = py + designResolutionSize.height / 2;
        let yBottom = py - designResolutionSize.height / 2;

        if (xRight > mapWidth) {
            px = mapWidth - designResolutionSize.width / 2;
        } else if (xLeft < 0) {
            px = designResolutionSize.width / 2;
        }

        if (yTop > mapHeight) {
            py = mapHeight - designResolutionSize.height / 2;
        } else if (yBottom < 0) {
            py = designResolutionSize.height / 2;
        }


        this.targetPos.x = px;
        this.targetPos.y = py;
        console.log("CameraController setViewToPoint", this.targetPos.x, this.targetPos.y, this.camera, this.camera.node, move);
        if (this.camera && this.camera.node && !move) {
            console.log("CameraController setViewToPoint setPosition", this.targetPos.x, this.targetPos.y);
            this.camera.node.setPosition(this.targetPos);
        }
        console.log("camera position after setViewToPoint", this.camera.node.position);
    }

    /** 显示任务目标, 点击屏幕遮罩之后, 再返回玩家当前位置 */
    public showTaskTarget(targetList: cc.Node[] | cc.Vec2[], cmpCallback: Function = null, backCallback: Function = null): void {
        // let guidePlayer = HeroManager.instance.getGuidePlayer();
        let centerPoint = cc.Vec3.ZERO;
        // if (guidePlayer) {
        //     centerPoint = guidePlayer.getPosition(centerPoint)
        // }
        // else {
        //     centerPoint = CameraController.instance.getCenterPosition();
        // }
        this.isMoveing = true;
        centerPoint = CameraController.instance.getCenterPosition();
        // HeroManager.instance.lockAllHerosMove();
        this.targetList = targetList.reverse();
        let target = this.targetList.pop();
        if (!target) return;
        this.moveType = MoveType.Tween;
        if (target instanceof cc.Node) {
            this.setTarget(target);
        } else if (target instanceof cc.Vec2) {
            this.setTarget(null);
            this.setTargetPoint(new cc.Vec3(target.x, target.y));
        }
        this.moveCmpCallBack = cmpCallback;

        // App.showMask(() => {
        //     if (MapManager.curMap) MapManager.curMap.pause();
        //     self.targetList = []

        //     CameraController.instance.setTargetPoint(centerPoint, MoveType.Tween);
        //     if (backCallback != null) {
        //         backCallback();
        //     }
        //     self.moveCmpCallBack = () => {
        //         self.moveCmpCallBack = null;
        //         self.isMoveing = false;
        //         if (MapManager.curMap) MapManager.curMap.resume();
        //         HeroManager.instance.unLockAllHerosMove();
        //         if (!App.UserController.isShwoOpenNewFuntionClose) {
        //             App.UserController.isShwoOpenNewFuntionClose = true;
        //         }
        //         App.hideMask();
        //     }

        // });
    }

    protected onDestroy(): void {
        Logger.trace("CameraController onDestroy");
        CameraController.instance = null;
    }

    /** -----------------------------------------------------------
     * 镜头高度
     * ------------------------------------------------------------ */
    public setZoomValue(value: number): void {
        this._targetZoomRatio = value;
        this._realizeZoomRatio();
    }

    public testZoomAdd(speed: number = 0.01): void {
        this.currentZoomRatio += speed;
        this._realizeZoomRatio();
    }

    public testZoomSub(speed: number = 0.01): void {
        // this.setZoomToNormal();
        this.currentZoomRatio -= speed;
        this._realizeZoomRatio();
    }

    private updateZoomRatio(dt: number): void {
        if (!this._targetZoomRatio) {
            return;
        }

        if (Math.abs(this._targetZoomRatio - this.currentZoomRatio) < 0.01) {
            this.currentZoomRatio = this._targetZoomRatio;
            this._targetZoomRatio = null;
            this._speedZoomRatio = this.currentZoomRatio == NORMAL_ZOOM_RATIO ? 0.15 : this._speedZoomRatio;
            this._realizeZoomRatio();
            return;
        }

        if (this._targetZoomRatio > this.currentZoomRatio) {
            this.currentZoomRatio += this._speedZoomRatio * dt;
        } else {
            this.currentZoomRatio -= this._speedZoomRatio * dt;
        }
        this._realizeZoomRatio();
    }

    private _realizeZoomRatio() {
        // TODO
    }

    private _targetZoomRatio: number = null;
    private _speedZoomRatio: number = 0.15;

    public setZoomToHight(): void {
        this._targetZoomRatio = HEIGHT_ZOOM_RATIO;
    }

    public setZoomToNormal(): void {
        this._targetZoomRatio = NORMAL_ZOOM_RATIO;
    }

    public setZoomToValue(value: number): void {
        this._targetZoomRatio = value;
    }

    public setSpeedZoomRatio(value: number): void {
        this._speedZoomRatio = value;
    }

    public debugDumpCameraInfo() {
        const position = this.node.position;
        console.log(`Camera Position: x=${position.x}, y=${position.y}, z=${position.z}`);
    }
}
