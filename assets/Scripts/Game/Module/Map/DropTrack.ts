import * as cc from "cc";
// import { url } from "inspector";
// import { ViewUtil } from "../../Utils/ViewUtils";
// import GuideBoxArrow from "./GuideBoxArrow";
// import ItemCache, { CFG_Item } from "../../../Config/GameCfg/ItemCache";
// import MapManager from "../../Managers/MapManager";
// import GameManager from "../../Managers/Res/GameManager";
// import ResSprite from "../../Components/ResSprite";
// import { BagMessage } from "../../Net/Message/BagMessage";
// import { pb } from "../../Net/Proto/GameProto";
// import RandomUtils from "../../Utils/RandomUtils";
// import { Bundles } from "../Bundles";
// import { ItemType } from "../bag/model/ItemType";

const { ccclass, property } = cc._decorator;

@ccclass
export default class DropTrack extends cc.Component {

    /**速度 */
    private moveSpeed: number = 50;

    private isMove: boolean = false;

    // init(pos: cc.Vec2, dropType: number, dropHeirgth: number = 0, dropNum: number = 0) {

    //     let item: ResSprite = this.node.getChildByName("skin").getComponent(ResSprite);
    //     let itemCfg: CFG_Item = ItemCache.Instance.getOrNull(dropType);
    //     if (itemCfg.getKind() === ItemType.EQUIP) {//动态加载装备图片
    //         item.setSpriteFrame(Bundles.EQUIPMENT + itemCfg.getIcon());
    //     } else { //动态加载道具图片
    //         item.setSpriteFrame(Bundles.ITEM + itemCfg.getIcon());
    //     }
    //     this.node.setScale(1);
    //     this.node.setPosition(pos);
    //     let beamEff: cc.Node = null;//掉落光柱特效
    //     if (itemCfg.getBeam() != "") {
    //         beamEff = GameManager.instance.otherMgr.getDropBeam(itemCfg.getQuality());
    //         beamEff.name = "beamEff";
    //         beamEff.active = false;
    //         beamEff.setScale(0.8);
    //         this.node.addChild(beamEff, -1);
    //         beamEff.setPosition(0, 0);
    //         this.node.setScale(1.2);
    //     }
    //     if (dropType == ItemType.Meat || dropType == ItemType.Wood || dropType == ItemType.Ore || ItemType.Exp) {
    //         this.node.setScale(1.3);
    //     } else {
    //         //不是资源道具，飘窗提示
    //         const item: pb.IPbThing = { identity: dropType, num: dropNum };
    //         BagMessage.showThings(3, [item]);
    //     }
    //     this.node.parent = MapManager.curMap.skillBottomLayer;
    //     let randX = RandomUtils.random(20, 200);
    //     randX = Math.random() < 0.5 ? randX : -randX;

    //     let randY = dropHeirgth;
    //     var bezier1 = [cc.v2(0, randY), cc.v2(randX, randY), cc.v2(randX, 0)];

    //     this.node.runAction(cc.rotateBy(0.3, 360));
    //     this.node.runAction(
    //         cc.sequence(
    //             cc.bezierBy(0.6, bezier1),
    //             cc.callFunc(() => {
    //                 if (this.node?.getChildByName("beamEff")) {
    //                     this.node.getChildByName("beamEff").active = true;
    //                 }
    //             }),
    //             cc.delayTime(1),
    //             cc.callFunc(() => {
    //                 if (this.node?.getChildByName("beamEff")) {
    //                     this.node.getChildByName("beamEff").active = false;
    //                 }
    //                 this.isMove = true;
    //             }),
    //         ),
    //     );
    // }

    // update(dt) {
    //     if (this.isMove) {
    //         const targetPos = MapManager.curMap.mainPlayer.node.getPosition();
    //         const dropPos = cc.v2(this.node.position.x, this.node.position.y);
    //         const normalizeVec = targetPos.subtract(dropPos).normalize();
    //         const x = normalizeVec.x * this.moveSpeed;
    //         const y = normalizeVec.y * this.moveSpeed;
    //         this.node.setPosition(this.node.position.add(cc.v3(x, y)));
    //         if (cc.Vec2.distance(dropPos, MapManager.curMap.mainPlayer.node.getPosition()) <= 50) {
    //             this.destroy();
    //             this.node.removeFromParent(true);
    //             // ViewUtil.removePrefabNode(dropNode);
    //             ViewUtil.removePrefabNode(this.node);
    //         }
    //     }
    // }

    protected onDestroy(): void {

    }
}
