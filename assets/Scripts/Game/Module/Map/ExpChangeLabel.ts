// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import * as cc from "cc";
const {ccclass, property} = cc._decorator;

@ccclass
export default class ExpChangeLabel extends cc.Component {

    /** 暴击伤害 */
    @property(cc.Label)
    exp: cc.Label = null;

    start () {

    }

    setExp(exp){
        this.exp.string = "+"+exp;
    }

    // setColor( color ){
    //     this.exp.node.color = color == 0 ? cc.Color.RED: cc.Color.GREEN;
    // }
}
