// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import * as cc from "cc";
const {ccclass, property} = cc._decorator;

@ccclass
export default class MBossName extends cc.Component {
    @property(cc.Label)
    public level:cc.Label = null;

    @property(cc.Label)
    public Mname:cc.Label = null;

    start () {

    }

    setMonsterLevel(level:number): void {
        this.level.string = level.toString();
    }

    setMonsterName(name:string): void {
        this.Mname.string = name;
    }
}
