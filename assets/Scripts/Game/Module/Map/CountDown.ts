// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import * as cc from "cc";
import MapManager from "../MapInstance/Managers/MapManager";
import Player from "../../Unit/Actor/player/PlayerActor";


const { ccclass, property } = cc._decorator;

@ccclass
export default class CountDown extends cc.ProgressBar {

    unit: Player = null;
    // LIFE-CYCLE CALLBACKS:

    // onLoad () {}

    time = 0;

    runTime = 0;

    isEnd = false;

    start() {

    }

    update(dt) {
        if (!this.node.isValid || this.isEnd) {
            return;
        }
        this.runTime;
        if (this.time > 0 && this.runTime < this.time) {
            this.runTime += dt;
            this.progress = 1 - this.runTime / this.time;

            if (this.runTime >= this.time) {
                this.isEnd = true;
                this.unit.updatePosition();
                MapManager.curMap.rebornUnit(this.unit);
            }
        }
    }

    setTime(t) {
        this.time = t;
    }

    setUnit(unit: Player) {
        this.unit = unit;
    }
}
