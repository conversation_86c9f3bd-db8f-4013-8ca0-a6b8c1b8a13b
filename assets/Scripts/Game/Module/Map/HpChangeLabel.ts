// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import * as cc from "cc";
const {ccclass, property} = cc._decorator;

@ccclass
export default class HpChangeLabel extends cc.Component {

    /** 普通伤害 */
    // @property(cc.Label)
    // hp0: cc.Label = null;

    /** 暴击伤害 */
    @property(cc.Label)
    hp: cc.Label = null;


    start () {

    }

    setHp(hp , baoji = false){
        this.hp.string = hp;
    }

    setColor( color: cc.Color | number ){
        if(color instanceof cc.color) {
            this.hp.color = color;
        } else {
            this.hp.color = color == 0 ? cc.Color.RED : cc.Color.GREEN;
        }
    }

    set color(value: cc.Color) {
        this.hp.color = value;
    }
}
