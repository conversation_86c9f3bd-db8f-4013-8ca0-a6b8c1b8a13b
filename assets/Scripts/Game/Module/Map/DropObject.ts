import * as cc from "cc";

import ResSprite from "../../Components/ResSprite";
import { ItemType } from "../../../Config/ItemType";


const { ccclass, property } = cc._decorator;

@ccclass
export default class DropObject extends cc.Component {

    @property(cc.SpriteFrame)
    sp: cc.SpriteFrame[] = [];

    @property(ResSprite)
    ico: ResSprite = null;

    // LIFE-CYCLE CALLBACKS:

    // onLoad () {}

    start() {

    }

    setType(type: ItemType) {
        if (type == ItemType.Meat) {
            this.ico.spriteFrame = this.sp[1];
        } else if (type == ItemType.Wood) {
            this.ico.spriteFrame = this.sp[0];
        } else if (type == ItemType.Ore) {
            this.ico.spriteFrame = this.sp[2];
        } else {
            this.ico.spriteFrame = null;
        }
    }

    // update (dt) {}
}
