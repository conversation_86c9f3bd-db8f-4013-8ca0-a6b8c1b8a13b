import * as cc from "cc";

import { App } from "../../../App";
import { MapEvent } from "../../../GameEvent/Events";
import Logger from "../../../Framework/Logger";
import Timer from "../../Utils/Timer";
import BaseView from "../../Components/BaseView";
import EventManagerComponent from "../../GlobalComponent/EventManagerComponent";
import ViewManager from "../../GlobalComponent/ViewManagerComponent";

const { ccclass, property } = cc._decorator;

/**
 * 地图切换转场界面.
 */
@ccclass
export default class MapLoading extends BaseView {

    /** 等待最小时间 */
    private readonly MIN_WAIT_TIME: number = 2580;

    protected isLoaded: boolean = false;

    protected isTimeEnd: boolean = false;

    protected onLoad(): void {
        this.isLoaded = false;
        this.isTimeEnd = false;
        this.scheduleOnce(this.onTimeEnded, this.MIN_WAIT_TIME);
        this.scheduleOnce(this.onUpdateMapLoad, 100);
        EventManagerComponent.instance().addEventListener(
            MapEvent.Map_INIT_COMPLETE, 
            this.onLoadComplete, 
            this,
        );
    }

    protected start(): void {
        
    }

    protected onUpdateMapLoad(): void {
        EventManagerComponent.instance().dispatch(
            MapEvent.START_MAP_LOAD,
            null
        );
    }

    protected onDestroy(): void {
        this.unschedule(this.onTimeEnded);
        EventManagerComponent.instance().removeEventListener(
            MapEvent.Map_INIT_COMPLETE, 
            this.onLoadComplete, 
            this,
        );
        EventManagerComponent.instance().dispatch(
            MapEvent.MAP_REMOVE_LOAD,
            null,
        );
    }

    /** 地图初始化完成 */
    protected onLoadComplete() {
        this.isLoaded = true;
        this.onMapReady();
    }

    /** 时间结束  */
    protected onTimeEnded() {
        this.isTimeEnd = true;
        this.onMapReady();
    }

    protected onMapReady() {
        if (!this.isLoaded || !this.isTimeEnd) {
            return;
        }
        Logger.trace("onMapReady");
        EventManagerComponent.instance().dispatch(
            MapEvent.MAP_ACTIVE,
            null,
        );
        ViewManager.instance().close(this);
    }
}
