import * as cc from "cc";

import { ViewId } from "../../../Config/ViewConst";
import MapManager from "../MapInstance/Managers/MapManager";
import UIButton from "../../Components/UIButton";
import ViewManager from "../../GlobalComponent/ViewManagerComponent";


const { ccclass, property } = cc._decorator;

@ccclass
export default class MiniMap extends cc.Component {
    @property(cc.Node)
    mainHeroNode: cc.Node = null;

    // 世界地图
    @property(UIButton)
    btnWorldMap: UIButton = null;

    // 世界地图
    @property(UIButton)
    btnWorldMapBig: UIButton = null;

    /** 宝箱 */
    @property(cc.Node)
    mark_box: cc.Node = null;

    /** boss */
    @property(cc.Node)
    mark_boss: cc.Node = null;

    /** 传送点 */
    @property(cc.Node)
    mark_transPoint: cc.Node = null;

    /** 副本点 */
    @property(cc.Node)
    mark_copyPoint: cc.Node = null;

    // 坐标
    @property(cc.Label)
    posLabel: cc.Label = null;

    // LIFE-CYCLE CALLBACKS:

    onLoad() {
        this.btnWorldMap.setOnClick(() => {
            ViewManager.instance().show(ViewId.WorldMap_VIew);
        }, this);

        this.btnWorldMapBig.setOnClick(() => {
            ViewManager.instance().show(ViewId.WorldMap_VIew);
        }, this);
    }

    start() {}

    dts = 2;
    dts_5 = 5;
    update(dt) {
        // this.updateNPC();

        this.dts -= dt;
        this.dts_5 -= dt;
        if (this.dts <= 0) {
            this.dts = 0.5;

            if(!MapManager.curMap || !MapManager.curMap.data) {
                return;
            }

            let data: any = MapManager.curMap.data;
            // const gamemap = GameMap.instance;
            // if (gamemap) {
            //     let mainHero = gamemap.mainPlayer;
            //     if (mainHero) {
            //         // mainhero的位置 转换小地图上的位置
            //         let pos = mainHero.node.position;
            //         const mapWidth = data.width;
            //         const mapHeight = data.height;
            //         this.mainHeroNode.position = cc.v3((pos.x * 246) / mapWidth, (pos.y * 153) / mapHeight, 0);

            //         // this.posLabel.string = "[" + mainHero.roadNode.cx.toFixed(0) + "," + mainHero.roadNode.cy.toFixed(0) + "]";
            //     }
            // }
        }

        if (this.dts_5 <= 0) {
            this.dts_5 = 5;
            // this.updateMonster();
        }
    }

    updateMonster() {
        // const gamemap = GameMap.instance;

        // // boss
        // let data: any = MapManager.curMap.data;
        // let monsterList = gamemap.monsterList;
        // let monster: Monster = null;
        // for (let i = 0; i < monsterList.length; i++) {
        //     monster = monsterList[i];

        //     if (monster.getHp() > 0 && monster.isBoss()) {
        //         this.mark_boss.active = true;
        //         const mapWidth = data.width;
        //         const mapHeight = data.height;
        //         this.mark_boss.position = cc.v3((monster.node.position.x * 246) / mapWidth, (monster.node.position.y * 153) / mapHeight, 0);
        //         break;
        //     }
        // }
    }

    /** -----------------------------------------------------------
     * TEST
     * ------------------------------------------------------------ */
    private _npcInstances: cc.Node[] = [];
    updateNPC() {
        //const npcList: MapNpc[] = GameWorld.instance.gameMap?.npcList;
        //if (!npcList) return;

        // if (this._npcInstances.length < npcList.length) {
        //     for (let i = this._npcInstances.length; i < npcList.length; i++) {
        //         let npc = cc.instantiate(this.mark_box);
        //         this._npcInstances.push(npc);
        //         npc.parent = this.mark_box.parent;
        //         npc.zIndex = -1;
        //         npc.scale = 0.7;
        //     }
        // }
        // const gamemap = GameMap.instance;

        // let data: any = MapManager.curMap.data;

        // const mapWidth = data.width;
        // const mapHeight = data.height;
        // for (let i = 0; i < npcList.length; i++) {
        //     // let randomItem = npcList[Math.floor(Math.random() * npcList.length)];
        //     let randomItem = npcList[i];
        //     this._npcInstances[i].active = true;
        //     this._npcInstances[i].setPosition((randomItem.node.position.x * 246) / mapWidth, (randomItem.node.position.y * 153) / mapHeight, 0);
        // }
    }
    /** ----------------------------------------------------------- */
}

