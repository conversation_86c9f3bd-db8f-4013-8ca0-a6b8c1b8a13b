import * as cc from "cc";

import { MapEvent } from "../../../GameEvent/Events";
import Logger from "../../../Framework/Logger";
import Timer from "../../Utils/Timer";
import BaseView from "../../Components/BaseView";
import ViewManagerComponent from "../../GlobalComponent/ViewManagerComponent";
// import BattleController from "../../Control/BattleController";

const { ccclass, property } = cc._decorator;

/**
 * 地图切换转场界面.
 */
@ccclass
export default class LoadingHeimu extends BaseView {

    /** 等待最小时间 */
    private readonly MIN_WAIT_TIME: number = 1250;

    protected onLoad(): void {
        Timer.once(this.MIN_WAIT_TIME, this, this.onTimeEnded);
    }

    protected start(): void {
        
    }

    protected onDestroy(): void {
        Timer.clear(this, this.onTimeEnded);
    }

    /** 时间结束  */
    protected onTimeEnded() {
        let battleType = this.viewData["battleType"];
        let mapType = this.viewData["mapType"];
        let posList = this.viewData["posList"];
        let heros = this.viewData["heros"];
        let mainHeroId = this.viewData["mainHeroId"];
        let crossMapType = this.viewData["crossMapType"];
        let param = this.viewData["param"]
        // TODO
        // BattleController.requestEnterBattle(battleType,mapType,posList,heros,mainHeroId,crossMapType,param);
        ViewManagerComponent.instance().close(this);
    }
}
