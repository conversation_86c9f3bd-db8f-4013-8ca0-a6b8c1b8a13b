import * as cc from "cc";
import { GameEvent } from "../../../GameEvent/Events";
import MapManager from "../MapInstance/Managers/MapManager";
import { SoundId, SoundType } from "../Sound/SoundManagers";
import CameraController from "../Camera/CameraManager";
import BaseView from "../../Components/BaseView";
import Timer from "../../Utils/Timer";
import SoundManager from "../../GlobalComponent/SoundManager";
import EventManagerComponent from "../../GlobalComponent/EventManagerComponent";
import ViewManager from "../../GlobalComponent/ViewManagerComponent";

const { ccclass, property } = cc._decorator;

/**
 * 地图切换转场界面.
 */
@ccclass
export default class GmaeOpen extends BaseView {
    @property(cc.Animation)
    public anim1: cc.Animation = null;

    @property(cc.Animation)
    public anim2: cc.Animation = null;

    protected onLoad(): void {
        this.anim1.play("open1");
        CameraController.instance.setSpeedZoomRatio(0.2);
        CameraController.instance.setZoomToValue(1.2);
        this.anim1.node.active = true;
        this.anim2.node.active = false;
        MapManager.curMap && MapManager.curMap.pause();
        Timer.once(1000, this, () => {
            SoundManager.instance().playEffect(SoundId.GAMEOPEN, SoundType.COMMON);
        });
        this.anim1.once(cc.Animation.EventType.FINISHED, () => {
            EventManagerComponent.instance().dispatch(GameEvent.OPEN_GAME_END_CREATROLE, false);
            this.anim2.node.active = true;
            this.anim1.node.active = false;
            this.onTimeEnded();
        }, this);
    }

    protected start(): void {

    }

    protected onDestroy(): void {

    }

    /** 时间结束  */
    protected onTimeEnded() {
        this.anim2.play("open2");
        this.scheduleOnce(() => {
            CameraController.instance.setZoomToValue(0.85);
        }, 0.2);
        this.anim2.once(cc.Animation.EventType.FINISHED, () => {
            MapManager.curMap.resume();
            MapManager.curMap.areaDefine = true;
            // App.UserController.userInfo.isFirstPlayGame = true;
            EventManagerComponent.instance().dispatch(GameEvent.OPEN_GAME_END_CREATROLE, true);
            ViewManager.instance().close(this);
        }, this);
    }

    // protected update(dt: number): void {
    //     if (CameraController.instance._speedZoomRatio == 0.15) {

    //     }
    // }
}
