import * as cc from "cc";

import { GameEvent } from "../../../GameEvent/Events";
import { ViewUtil } from "../../Utils/ViewUtils";
import { MapEnumType } from "../MapInstance/Base/MapDefine";
import MapManager from "../MapInstance/Managers/MapManager";
import EventManagerComponent from "../../GlobalComponent/EventManagerComponent";
import LayerManagerComponent from "../../GlobalComponent/LayerManagerComponent";

const { ccclass, property } = cc._decorator;

@ccclass
export default class GuideBoxArrow extends cc.Component {
    /**目标位置 */
    private targetPos: cc.Vec2 = null;
    /**伙伴团队位置 */
    private mainPlayerPos: cc.Vec2 = null;
    /**宝箱类型 */
    private boxType: number = 0;

    private isShow: boolean = true;

    protected onLoad(): void {
        EventManagerComponent.instance().addEventListener(GameEvent.RECEIVE_TREASURE_BOX_SUCCESS, this.onReceiveBox, this);
    }

    protected onDestroy(): void {
        EventManagerComponent.instance().removeEventListener(GameEvent.RECEIVE_TREASURE_BOX_SUCCESS, this.onReceiveBox, this);
    }

    public init(targetPos: cc.Vec2, boxType: number): void {
        this.boxType = boxType;
        this.targetPos = targetPos;
        const parent = LayerManagerComponent.instance().getLayer();
        // setNodeZIndex(this.node, ZIndex.MIN);
        this.node.active = true;
        const scale = this.node.getChildByName('img_arrow').scale;
        cc.tween(this.node.getChildByName('img_arrow')).repeatForever(
            cc.tween().to(0.3, { scale: cc.v3(scale.x - 0.1, -scale.y + 0.1)}).to(0.3, { scaleX: scale, scaleY: -scale })
        ).start();
    }

    update(dt) {
        // if (!MapManager.curMap.mainPlayer) {
        //     return;
        // }
        // if (this.node?.isValid) {
        //     this.mainPlayerPos = MapManager.curMap.mainPlayer.node.getPosition();
        //     if (MapEnumType.WORLD_MAP_LIST.indexOf(MapManager.curMap.mapId) == -1 || cc.Vec2.distance(this.mainPlayerPos, this.targetPos) >= 6000) {
        //         this.node.active = false;
        //         App.TreasureBoxController.clearTreasureBox(this.boxType);
        //         return;
        //     }
        //     if (cc.Vec2.distance(this.mainPlayerPos, this.targetPos) <= 1200) {
        //         if (this.isShow) {
        //             this.isShow = false;
        //             this.node.getChildByName('img_box').active = false;
        //             this.node.getChildByName('img_arrow').active = false;
        //         }
        //     } else {
        //         if (!this.isShow) {
        //             this.isShow = true;
        //             this.node.getChildByName('img_box').active = true;
        //             this.node.getChildByName('img_arrow').active = true;
        //         }
        //         const screenPos = cc.v2(cc.Camera.main.getWorldToScreenPoint(this.mainPlayerPos));
        //         const startPos = LayerManagerComponent.instance().getLayer().convertToNodeSpaceAR(screenPos);
        //         this.node.setPosition(0, 0);
        //         const dx: number = this.mainPlayerPos.x - this.targetPos.x;
        //         const dy: number = this.mainPlayerPos.y - this.targetPos.y;
        //         const radian = Math.atan2(dy, dx);
        //         let angle = radian / Math.PI * 180;
        //         const ang = angle + 90;
        //         this.node.angle = ang;
        //         this.node.getChildByName('img_box').angle = -ang;
        //     }
        // }
    }


    private onReceiveBox(): void {
        // if (this.boxType === App.TreasureBoxController.currTakeBox.boxType) {
        //     this.destroySelf();
        // }
    }

    public destroySelf(): void {
        cc.Tween.stopAllByTarget(this.node.getChildByName('img_arrow'));
        this.node.removeFromParent();
        this.node.destroy();
        ViewUtil.removePrefabNode(this.node);
    }
}
