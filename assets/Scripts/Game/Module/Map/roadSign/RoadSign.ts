import * as cc from "cc";
import MovieClip from "../../../Utils/MovieClip";



const { ccclass, property } = cc._decorator;

/**
 * 路标
 */
@ccclass
export default class RoadSign extends cc.Component {

    @property(MovieClip)
    signMc: MovieClip = null;


    // LIFE-CYCLE CALLBACKS:

    // onLoad () {}

    start() {

        this.signMc.node.on("complete", () => {
            this.node.active = false;
        }, this);
    }

    // update (dt) {}

    public play() {
        this.node.active = true;
        this.signMc.reset();
        this.signMc.play();
    }
}
