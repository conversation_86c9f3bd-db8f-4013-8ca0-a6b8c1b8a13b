import * as cc from "cc";
import { ITiledMapData } from "./MapStruct";
import { XmlParser } from "./XmlParser";

// const ParseArrayName = ["layer", "objectgroup", "object", "tileset", "property"];
const ParseArrayName = ["layer"];

/**
 * 地图加载器
 */
export default class MapLoader {
    public static parseTmx(data: cc.TiledMapAsset): ITiledMapData {
        let tmxJson = new XmlParser().toJson(data["tmxXmlStr"], { removeLineBreaks: true, parseArrayName: ParseArrayName });
        // Logger.trace("tmxJson", tmxJson);

        if (!tmxJson || !tmxJson["map"]) {
            return null;
        }
        const sceneName = tmxJson["map"]["@attributes"]["scenename"];
        const mapName = tmxJson["map"]["@attributes"]["mapname"];
        const tileWidth = Number(tmxJson["map"]["@attributes"]["tilewidth"]);
        const tileHeight = Number(tmxJson["map"]["@attributes"]["tileheight"]);
        const width = Number(tmxJson["map"]["@attributes"]["width"]) * tileWidth;
        const height = Number(tmxJson["map"]["@attributes"]["height"]) * tileHeight;

        // for (let index in tmxJson["map"]["layer"]) {
        //     const layer = tmxJson["map"]["layer"][index];
        //     const name: string = layer["@attributes"].name;

        //     if (name.search("impassable_area") >= 0) {
        //         TiledMap.instance.data.passableArea = this.parsePassableArea(layer);
        //     }
        // }

        const ret: ITiledMapData = {
            sceneName: sceneName,
            mapName: mapName,
            tileWidth: tileWidth,
            tileHeight: tileHeight,
            width: width,
            height: height,
            asset: data,
            // layerGroups: layerGroups,
            // objectGroups: objGroups,
            // passable_area,
        };
        return ret;
    }

    private static parsePassableArea(data: string | JSON | {}): number[][] {
        let ret: number[][] = [];
        let width = data["@attributes"].width;
        let height = data["@attributes"].height;
        // console.info("%c%s", "color:blue", "================");
        // console.info("%c%s", "color:blue", "width:" + width);
        // console.info("%c%s", "color:blue", "height:" + height);
        // Logger.trace("data", data["data"]["#text"]);
        let content = data["data"]["#text"] as String;
        // Logger.trace("content", content);

        let y = 0;
        content.split("\n").forEach((value) => {
            let x = 0;
            let line = [];
            value.split(",").forEach((u) => {
                x = x + 1;
                if (u.length > 0) line.push(u == "0" ? 0 : 1);
            });
            if (line.length >= width) {
                ret.push(line);
            }

            y = y + 1;
        });
        return ret;
    }
}
