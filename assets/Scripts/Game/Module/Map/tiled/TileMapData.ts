import * as cc from "cc";

import { MapAreaType, TILE_SPLIT_NUM } from "../../MapInstance/Base/MapDefine";
// import { NpcType } from "../../../unit/npc/base/NpcDefine";
import MapRoadUtils from "../road/MapRoadUtils";
import TiledUtils from "./TiledUtils";

/** 多边形碰撞体数据  */
export interface ITiledMapPolygon {
    width: number;
    height: number;
    points: number[][];
}

/** 地图基础数据  */
interface ITiledMapBaseObjectAttr {
    x?: number;
    y?: number;
    width?: number;
    height?: number;
}


export interface IResourcePoint extends ITiledMapBaseObjectAttr {
    id?: number;
    /** 最大数量 */
    max_num?: number;
    /** 刷新时间 */
    reborn_time?: number;
}

export interface IMonsterPoint extends ITiledMapBaseObjectAttr {
    id?: number;
    /** 最大数量 */
    max_num?: number;
    /** 复活时间 */
    reborn_time?: number;
}

export interface IMapMonsterPoint extends ITiledMapBaseObjectAttr {
    id?: number;
}

export interface INpcTask extends ITiledMapBaseObjectAttr {
    id?: number;
}

/** NPC数据抽象  */
export interface INpcData extends ITiledMapBaseObjectAttr {
    id?: number;
    type?: number;
}


/** -----------------------------------------------------------
 * Interface for Layer
 * ------------------------------------------------------------ */
interface ITiledMapBaseLayer {
    id?: number;
    matrix?: number[][];
}

export interface IFogArea extends ITiledMapBaseLayer {
    // layer: cc.TiledLayer;
}

export interface IImpassableArea extends ITiledMapBaseLayer {
    layer: cc.TiledLayer;
}

export interface ISafeArea extends ITiledMapBaseLayer {
    layer: cc.TiledLayer;
}

export interface IOutsideArea extends ITiledMapBaseLayer {
    layer: cc.TiledLayer;
}

/** -----------------------------------------------------------
 * Map Data
 * ------------------------------------------------------------ */
export class TileMapData {
    public get size(): cc.Size {
        return new cc.Size(this._width, this._height);
    }

    /** 地图宽度 像素 */
    private _width: number = 0;
    /** 地图宽度 像素 */
    public set width(value: number) {
        this._width = value;
    }
    /** 地图宽度 像素 */
    public get width(): number {
        return this._width;
    }

    /** 地图高度 像素 */
    private _height: number = 0;
    /** 地图高度 像素 */
    public set height(value: number) {
        this._height = value;
    }

    /** 地图高度 像素 */
    public get height(): number {
        return this._height;
    }

    /** 网格宽度 */
    private _tileWidth: number = 0;
    public set tileWidth(value: number) {
        this._tileWidth = value;
    }
    /** 网格宽度 */
    public get tileWidth(): number {
        return this._tileWidth;
    }

    /** 网格高度 */
    private _tileHeight: number = 0;
    public set tileHeight(value: number) {
        this._tileHeight = value;
    }
    /** 网格高度 */
    public get tileHeight(): number {
        return this._tileHeight;
    }

    /** 网格数量(横) */
    private _mapWidth: number = 0;
    public set mapWidth(value: number) {
        this._mapWidth = value;
    }
    /** 网格数量(横) */
    public get mapWidth(): number {
        return this._mapWidth;
    }

    /** 网格数量(纵) */
    private _mapHeight: number = 0;
    public set mapHeight(value: number) {
        this._mapHeight = value;
    }
    /** 网格数量(纵) */
    public get mapHeight(): number {
        return this._mapHeight;
    }

    /** 出生点 */
    private _spawnPoint: cc.Vec2 = null;
    public set spawnPoint(value: cc.Vec2) {
        if (this._spawnPoint == null) {
            this._spawnPoint = value;
            console.info("%c%s", "color:blue", "出生点坐标为: ", value.x, value.y);
        } else {
            console.error("出生点重复设置", value, this._spawnPoint);
        }
    }
    public get spawnPoint(): cc.Vec2 {
        return this._spawnPoint || cc.Vec2.ZERO;
    }

    /** 回城点 */
    private _town_portal: cc.Vec2 = null;
    public set townPortal(value: cc.Vec2) {
        if (this._town_portal == null) {
            this._town_portal = value;
            console.info("%c%s", "color:blue", "回城点坐标为: ", value.x, value.y);
        } else {
            console.error("回城点重复设置", value, this._town_portal);
        }
    }
    public get townPortal(): cc.Vec2 {
        return this._town_portal || cc.Vec2.ZERO;
    }

    /** 重生点 */
    private _rebornPoint: cc.Vec2 = null;
    public set rebornPoint(value: cc.Vec2) {
        if (this._rebornPoint == null) {
            this._rebornPoint = value;
            console.info("%c%s", "color:blue", "重生点坐标为: ", value.x, value.y);
        } else {
            console.error("重生点重复设置", value, this._rebornPoint);
        }
    }
    public get rebornPoint(): cc.Vec2 {
        return this._rebornPoint || cc.Vec2.ZERO;
    }

    /** 不可通行区域 */
    private _impassableArea: number[][] = null;
    public set impassableArea(value: number[][]) {
        this._impassableArea = value;
    }
    public get impassableArea(): number[][] {
        if (!!this._impassableArea) return this._impassableArea;
        return TiledUtils.makeArray2WithZero(this._mapWidth, this._mapHeight);
    }

    /** 安全区 */
    private _safeArea: number[][] = null;
    public set safeArea(value: number[][]) {
        this._safeArea = value;
    }
    public get safeArea(): number[][] {
        if (!!this._safeArea) return this._safeArea;
        return TiledUtils.makeArray2WithZero(this._mapWidth, this._mapHeight);
    }

    /** 野外区域 */
    private _outsideArea: number[][] = null;
    public set outsideArea(value: number[][]) {
        this._outsideArea = value;
    }
    public get outsideArea(): number[][] {
        if (!!this._outsideArea) return this._outsideArea;
        return TiledUtils.makeArray2WithZero(this._mapWidth, this._mapHeight);
    }

    /** 地图区域状态 */
    // private _mapAreaStatusMap: { [key: string]: number };
    private _mapAreaStatus: number[][];
    public set mapAreaStatus(value: number[][]) {
        this._mapAreaStatus = value;
    }
    public getMapAreaStatusByWorldPoint(x, y): number {
        return (this._mapAreaStatus[y] && this._mapAreaStatus[y][x]) || -1;
    }

    public isPassableByWorldPoint(x, y): boolean {
        const status = this.getMapAreaStatusByWorldPoint(x, y);
        switch (status) {
            case MapAreaType.PASSABLE_AREA:
                return true;
            case MapAreaType.IMPASSABLE_AREA:
                return false;
            case MapAreaType.SAFE_AREA:
                return true;
            case MapAreaType.OUTSIDE_AREA:
                return true;
            case MapAreaType.FOG_AREA:
                return false;
            default:
                return true;
        }
    }

    //----------------------野外副本-------------------------
    private _npcDungeon: INpcData[] = [];
    private npcDungeonMap: Map<number, INpcData> = new Map();
    public addNpcDungeon(data) {
        // data.type = NpcType.Dungeon;
        this._npcDungeon.push(data as INpcData);
        this.npcDungeonMap.set(data.id, data);
    }

    public getNpcDungeon(id: number): INpcData {
        return this.npcDungeonMap.get(id);
    }

    public getNpcDungeonList(): INpcData[] {
        return this._npcDungeon;
    }
    //---------------------------------------------------------------------

    //--------------------建筑__Begin-------------------------------
    private npcBuilding: INpcData[] = [];
    private npcBuildingMap: Map<number, INpcData> = new Map();
    public addNpcBuilding(data: INpcData) {
        // data.type = NpcType.Building;
        this.npcBuilding.push(data);
        this.npcBuildingMap.set(data.id, data);
    }
    public getNpcBuilding(id: number): INpcData {
        return this.npcBuildingMap.get(id);
    }

    public getNpcBuildingList(): INpcData[] {
        return this.npcBuilding;
    }
    //------------------建筑__End-----------------------------------

    //-------------------迷雾NPC----------------------------------
    private npcFog: INpcData[] = [];
    private npcFogMap: Map<number, INpcData> = new Map();
    public addNpcFog(data) {
        // data.type = NpcType.Fog;
        this.npcFog.push(data as INpcData);
        this.npcFogMap.set(data.id, data);
    }
    public getNpcFog(id: number): INpcData {
        return this.npcFogMap.get(id);
    }
    public getNpcFogList(): INpcData[] {
        return this.npcFog;
    }
    //----------------------------------------------------

    //-----------------传送阵----------------------------------
    private npcTeleport: INpcData[] = [];
    private npcTeleportMap: Map<number, INpcData> = new Map();
    public addTeleportPoint(data) {
        // data.type = NpcType.Teleport;
        this.npcTeleport.push(data as INpcData);
        this.npcTeleportMap.set(data.id, data);
    }
    public getTeleportPoint(id: number): INpcData {
        return this.npcTeleportMap.get(id);
    }
    public getTeleportPointList(): INpcData[] {
        return this.npcTeleport;
    }

    //-----------------跨地图传送阵----------------------------------
    private npcCrossTeleport: INpcData[] = [];
    private npcCrossTeleportMap: Map<number, INpcData> = new Map();
    public addCrossTeleportPoint(data) {
        // data.type = NpcType.CrossTeleport;
        this.npcCrossTeleport.push(data as INpcData);
        this.npcCrossTeleportMap.set(data.id, data);
    }
    public getCrossTeleportPoint(id: number): INpcData {
        return this.npcCrossTeleportMap.get(id);
    }
    public getCrossTeleportPointList(): INpcData[] {
        return this.npcCrossTeleport;
    }

    //--------------------------任务NPc---------------------------
    private npcTask: INpcData[] = [];
    private npcTaskMap: Map<number, INpcData> = new Map();
    public addNpcTask(data) {
        // data.type = NpcType.Task;
        this.npcTask.push(data as INpcData);
        this.npcTaskMap.set(data.id, data);
    }
    public getNpcTask(id: number): INpcData {
        return this.npcTaskMap.get(id);
    }
    public getNpcTaskList(): INpcData[] {
        return this.npcTask;
    }

    //------------------------------------------
    /** -----------------------------------------------------------
     * polygon
     * ------------------------------------------------------------ */
    private _polygonTile: { [key: string]: ITiledMapPolygon };
    public get polygonTile() {
        return this._polygonTile;
    }

    private _polygonLayer: { [key: string]: number[][] };
    public get polygonLayer() {
        return this._polygonLayer;
    }

    private _polygonObject: { [key: string]: number[][] };
    public get polygonObject() {
        return this._polygonObject;
    }

    public getPolygonData(gid: number): ITiledMapPolygon {
        if (!this._polygonTile) return null;
        const data = this._polygonTile[gid];
        if (!data) return null;
        return data;
    }

    public setPolygonTile(data: { [key: string]: ITiledMapPolygon }): void {
        !!data && (this._polygonTile = data);
    }

    public setPolygonLayer(data: { [key: string]: number[][] }): void {
        !!data && (this._polygonLayer = data);
    }

    public setPolygonObject(data: { [key: string]: number[][] }): void {
        !!data && (this._polygonObject = data);
    }

    /** -----------------------------------------------------------
     * Parse Layer Data
     * ------------------------------------------------------------ */

    /** 设置不可通行区域 */
    public setImpassableArea(matrix: number[][]): void {
        !!matrix && (this.impassableArea = TiledUtils.mulMatrix(matrix, TILE_SPLIT_NUM));
    }

    /** 设置安全区域 */
    public setSafeArea(matrix: number[][]): void {
        !!matrix && (this.safeArea = TiledUtils.mulMatrix(matrix, TILE_SPLIT_NUM));
    }

    /** 设置野外区域 */
    public setOutsideArea(matrix: number[][]): void {
        !!matrix && (this.outsideArea = TiledUtils.mulMatrix(matrix, TILE_SPLIT_NUM));
    }

    /** 不可通行区域 */
    public addImpassableArea(id: number, matrix: number[][], layer: cc.TiledLayer): void {
        this.impassableArea = TiledUtils.mergerMatrixWithZeroOne(this.impassableArea, matrix);
    }

    /** 安全区域 */
    public addSafeArea(id: number, matrix: number[][], layer: cc.TiledLayer): void {
        this.safeArea = TiledUtils.mergerMatrixWithZeroOne(this.safeArea, matrix);
    }

    /** 野外区域 */
    public addOutsideArea(id: number, matrix: number[][], layer: cc.TiledLayer): void {
        this.outsideArea = TiledUtils.mergerMatrixWithZeroOne(this.outsideArea, matrix);
    }

    /** 迷雾数据 迷雾id，迷雾所占的格子，主要用来和不可行走区域混合计算，获得真正的不可行走区域 */
    private fogArea: Map<number, IFogArea> = new Map();
    public addFogArea(id: number, matrix: number[][]): void {
        const ret: IFogArea = { id, matrix };
        this.fogArea.set(id, ret);
    }
    public getFogArea(id: number): IFogArea {
        return this.fogArea.get(id);
    }
    public getAllFogArea(): Map<number, IFogArea> {
        return this.fogArea;
    }

    /** 云层区域 [minX,maxX,minY,maxY,centerX,centerY]  */
    private fogRectArea: Map<number, number[]> = new Map();
    public setFogRectArea(areaData: object): void {
        for (let id in areaData) {
            this.fogRectArea.set(Number(id), areaData[id]);
        }
    }
    public getFogRectArea(id: number): number[] {
        return this.fogRectArea.get(id);
    }

    /** 设置区域信息 */
    private _regionRectArea: Map<number, number[]> = new Map();
    public setRegionData(regionData: object): void {
        for (let id in regionData) {
            this._regionRectArea.set(Number(id), regionData[id]);
        }
    }

    /** 根据坐标，获取所在区域 */
    public getRegionId(x, y): number {
        let _regionId = 1;
        let _point = MapRoadUtils.instance.getWorldPointByPixel(x, y);
        this._regionRectArea.forEach((values, regionId) => {
            if (_point.x > values[0] && _point.x <= values[1] && _point.y > values[2] && _point.y <= values[3]) {
                _regionId = regionId
            }
        })
        return _regionId;
    }

    /** 云层图片信息  */
    private _fogImgPosMap: Map<number, number[]> = new Map();
    public setFogImgPosData(fogImgPosData: object): void {
        for (let id in fogImgPosData) {
            this._fogImgPosMap.set(Number(id), fogImgPosData[id]);
        }
    }

    /** 根据坐标，获取所在区域 */
    public getFogImgPosData(fog_id: number): number[] {
        return this._fogImgPosMap.get(fog_id);
    }

    /** 云层图片信息  */
    private _atlasIdList: number[] = [];
    public setAtlasIdData(atlasIdList: object): void {
        for (let id in atlasIdList) {
            this._atlasIdList[Number(id)] = atlasIdList[id];
        }
    }

    /** 根据坐标，获取所在区域 */
    public getAtlasIdData(img_id: number): number {
        return this._atlasIdList[img_id];
    }

    /** 区域大小 */
    public getRegionRectSize(): number {
        return this._regionRectArea.size;
    }

    /** 单个区域信息  */
    public getRegionRectArea(regionId: number): number[] {
        if (this._regionRectArea.get(regionId) == null) //安全区
            return this._regionRectArea.get(regionId - 1);
        else
            return this._regionRectArea.get(regionId);
    }

    /** -----------------------------------------------------------
     * Other functions
     * ------------------------------------------------------------ */
    /** 已激活迷雾 */
    private activatedFogAreaMap: Map<number, boolean> = new Map();
    public setFogAreaActive(id: number, active: boolean): void {
        this.activatedFogAreaMap.set(id, active);
    }

    /** 获取未激活迷雾列表 */
    public getNonactivatedList(): number[] {
        let ret: number[] = [];
        let self = this;
        this.fogArea.forEach((v, i) => {
            const id = v.id;
            let flag = self.activatedFogAreaMap.get(id) || false;
            if (flag === false) {
                ret.push(id);
            }
        });
        return ret;
    }


    destroy() {
        this.fogArea.clear();
        this.activatedFogAreaMap.clear();
        this.npcTask.length = 0;
        this.npcTaskMap.clear();
        this.npcTeleport.length = 0;
        this.npcTeleportMap.clear();
        this.npcFog.length = 0;
        this.npcFogMap.clear();
        this.npcBuilding.length = 0;
        this.npcBuildingMap.clear();
        this._npcDungeon.length = 0;
        this.npcDungeonMap.clear();
        this._rebornPoint = null;

        if (this._mapAreaStatus) this._mapAreaStatus.length = 0;
        if (this._impassableArea) this._impassableArea.length = 0;
        if (this._outsideArea) this._outsideArea.length = 0;
        if (this._safeArea) this._safeArea.length = 0;
    }
}
