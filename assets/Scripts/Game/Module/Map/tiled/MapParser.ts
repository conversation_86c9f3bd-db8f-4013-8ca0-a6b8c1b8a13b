import * as cc from "cc";
import CFG_SceneFog from "../../../../Config/GameCfg/CFG_SceneFog";
import { MapConfigType, TILE_SPLIT_NUM } from "../../MapInstance/Base/MapDefine";
import MapManager from "../../MapInstance/Managers/MapManager";
// import NpcManager from "../../../Managers/NpcManager";
// import QuadtreeManager from "../../../Managers/QuadtreeManager";
import { TileMapData } from "./TileMapData";
import Timer from "../../../Utils/Timer";
import AppManager from "../../../Managers/AppManager";

/**
 * 地图解析器
 */
export default class MapParser {
    public data: TileMapData;
    private _groups: cc.TiledObjectGroup[] = [];

    public parse(tiledMap: cc.TiledMap, data: TileMapData): void {
        this.data = data;

        const layers = tiledMap.getLayers();
        this._groups = tiledMap.getObjectGroups();
        const mapSize = tiledMap.getMapSize();
        const tileSize = tiledMap.getTileSize();

        this.data.mapWidth = mapSize.width * TILE_SPLIT_NUM;
        this.data.mapHeight = mapSize.height * TILE_SPLIT_NUM;
        this.data.tileWidth = tileSize.width / TILE_SPLIT_NUM;
        this.data.tileHeight = tileSize.height / TILE_SPLIT_NUM;
        this.data.width = mapSize.width * tileSize.width;
        this.data.height = mapSize.height * tileSize.height;
        // QuadtreeManager.instance.init(this.data.width, this.data.height);

        MapManager.curMap.fogLayer.x = this.data.width / 2;
        MapManager.curMap.fogLayer.y = this.data.height / 2;
        MapManager.curMap.fogLayer.getComponent(cc.UITransform).width = this.data.width;
        MapManager.curMap.fogLayer.getComponent(cc.UITransform).height = this.data.height;

        const group_funcs = {
            spawn_point: this.parseSpawnPoint.bind(this),
            town_portal_point: this.parseTownPortal.bind(this),
            reborn_point: this.parseRebornPoint.bind(this),
            teleport_point: this.parseTeleportPoint.bind(this),
            npc_task: this.parseNpcTask.bind(this),
            npc_dungeon: this.parseNpcDungeon.bind(this),
            npc_building: this.parseNpcBuilding.bind(this),
            npc_fog: this.parseNpcFog.bind(this),
            cross_teleport_point: this.parseCrossTeleportPoint.bind(this),
        };
        for (let i = 0, n = this._groups.length; i < n; i++) {
            const group = this._groups[i];
            this.deal_entity(group, group_funcs);
        }
        this.parseFogMatrix();
    }

    //解析地表
    public parseSurface(): void {
        if (this._groups == null) {
            return;
        }

        for (let i = 0, n = this._groups.length; i < n; i++) {
            const group = this._groups[i];
            this.deal_group(group, "object_low", (id, x, y, width, height, node) => {
                // QuadtreeManager.instance.addTree(id, x, y, width, height, node);
            });

            // this.deal_group(group, "wasteland", (id, x, y, width, height, node) => {
            //     QuadtreeManager.instance.addWastelandTree(id, x, y, width, height, node);
            // });

            this.deal_group(group, "normal", (id, x, y, width, height, node, v) => {
                const transform = MapManager.curMap.fogLayer.getComponent(cc.UITransform);
                let halfWidth = transform.width / 2;
                let halfHeight = transform.height / 2;
                let pos = node.getPosition();
                node.parent = MapManager.curMap.entityLayer;
                node.position = new cc.Vec2(pos.x + halfWidth, pos.y + halfHeight);
                // QuadtreeManager.instance.addNormalTree(id, x, y, width, height, node);
            });
        }
        Timer.once(100, this, this.putMountainIntoQuad);
    }


    /** 把山体放进四叉树管理  */
    private putMountainIntoQuad(): void {
        for (let i = 0, n = this._groups.length; i < n; i++) {
            const group = this._groups[i];
            this.deal_group(group, "object_high", (id, x, y, width, height, node) => {
                let halfWidth = this.data.width / 2;
                let halfHeight = this.data.height / 2;
                let pos = node.getPosition();
                node.parent = MapManager.curMap.entityLayer;
                node.position = new cc.Vec2(pos.x + halfWidth, pos.y + halfHeight);
                // QuadtreeManager.instance.addTree(id, x, y, width, height, node);
            });
        }
    }

    /** 解析entity层  */
    private deal_entity(group: cc.TiledObjectGroup, group_funcs: object) {
        if (group.name.search("entity") >= 0) {
            let objects = group.getObjects();
            for (let i = 0, n = objects.length; i < n; i++) {
                const v = objects[i];
                group_funcs[v.name] && group_funcs[v.name](this.changePropertyToInt(v), group);
            }

            let children = group.node.children;
            for (let i = 0, n = children.length; i < n; i++) {
                children[i].active = false;
            }
        }
    }

    private deal_group(group: cc.TiledObjectGroup, keyword: string, callback: Function = null) {
        if (group.name.search(keyword) >= 0) {
            let objects = group.getObjects();
            for (let i = 0, n = objects.length; i < n; i++) {
                const v = objects[i];
                const data = this.changePropertyToInt(v);
                const id = data["id"];
                const x = data["x"];
                const y = data["y"];
                const width = data["width"];
                const height = data["height"];
                const node = group.node.getChildByName("img" + id);
                if (node) {
                    callback && callback(id, x, y, width, height, node, v);
                    node.active = false;
                } else {
                    console.warn("地图object缺失:", id);
                }
            }
        }
    }

    private changePropertyToInt(values) {
        let ret = {};
        for (const key in values) {
            let element = values[key];
            if (typeof element === "string") {
                const group = element.match("[0..9]*");
                if (group !== null) {
                    ret[key] = Number(element);
                } else {
                    ret[key] = element;
                }
            } else {
                ret[key] = element;
            }
        }
        return ret;
    }

    /** 解析迷雾格子数据 记录迷雾所在格子的二维数据，主要用途是跟不可行走区域混合计算，获得真正的不可行走区域 */
    private parseFogMatrix(): void {
        const _list: CFG_SceneFog[] = CFG_SceneFog.list;
        const mapCfg = MapManager.curMap.mapConfig;
        const cfgData: { [key in string]: number[][] } = AppManager.instance().ResManager.readConfigFile(mapCfg)[MapConfigType.FOG_GIRD_LIST];
        if (cfgData == null)
            return;
        for (var i: number = 0; i < _list.length; i++) {
            let _fogId: number = _list[i].fogId;
            let data = cfgData[_fogId];
            if (data) {
                // if (NpcManager.instance.isActiveFogNpc(_fogId)) {
                //     this.data.setFogAreaActive(_fogId, true);
                //     this.data.addFogArea(_fogId, data);
                // } else {
                //     this.data.addFogArea(_fogId, data);
                // }
            }
        }
    }

    private parseSpawnPoint(data) {
        this.data.spawnPoint = cc.v2(data.x, data.y);
    }

    private parseTownPortal(data) {
        this.data.townPortal = cc.v2(data.x, data.y);
    }

    private parseRebornPoint(data) {
        this.data.rebornPoint = cc.v2(data.x, data.y);
    }

    private parseTeleportPoint(data) {
        this.data.addTeleportPoint(data);
    }

    private parseCrossTeleportPoint(data) {
        this.data.addCrossTeleportPoint(data);
    }

    private parseNpcTask(data) {
        this.data.addNpcTask(data);
    }

    private parseNpcDungeon(data) {
        this.data.addNpcDungeon(data);
    }

    private parseNpcBuilding(data) {
        this.data.addNpcBuilding(data);
    }

    private parseNpcFog(data) {
        this.data.addNpcFog(data);
    }

    destroy() {
        this.data = null;
        this._groups = null;
    }
}
