export interface IParserConfiguration {
    /*
    Removes all properties which has the name '#text' and type array.
    Sample: json.#text = []
    */
    removeLineBreaks?: boolean;
    /*
    Removes all properties with name '#comment'.
    Sample: json.#comment
    */
    removeComments?: boolean;
    /*
    Transforms a property with #text == string only into
    property == string.
    Sample: json.myProperty.#text = 'sample' -> json.myProperty = 'sample'
    Attention: if json.myProperty has more properties than only #text it 
    is not converted
    */
    transformTextOnly?: boolean;

    // ParseArrayName里的属性强制性解析成array
    parseArrayName?: Array<string>;
}

export class XmlParser {
    constructor() {}

    // Converts a xml string into json object
    // Accepted parameters:
    // includeLineBreaks: If you want to include the linebreaks as an array property, you need to enable this
    // includeComments: If you want to include the xml comments as an #comment property, you need to enable this
    toJson(xmlString: string, config?: IParserConfiguration): JSON {
        // 1. Basic parsing
        let domElement = this.xmlStringToXmlDom(xmlString);
        let json = this.xmlToJson(domElement, config.parseArrayName || []);
        if (!config) return json as JSON;

        // 2. Running the config file settings
        if (config.removeLineBreaks) this.removeLineBreaks(json);
        if (config.removeComments) this.removeCommentProperties(json);
        if (config.transformTextOnly) this.transformTextOnly(json);

        return json as JSON;
    }

    // Parses an xml string into a xml dom element
    private xmlStringToXmlDom(xmlString: string): Document {
        // NOTE: window.DOMParser cocos自己也在用，应该不会因平台问题造成大错的。
        // 唯一需要注意的是，IE浏览器里不是这样用的，懒得兼容了。要兼容可以查查cocos内部是怎么兼容的
        let parser = new DOMParser();
        let xmlDoc = parser.parseFromString(xmlString, "text/xml");
        return xmlDoc;
    }

    // Tis method cleans up the JSON object which was created from the xmlToJson method.
    // Why? -> If the XML was multilined the JSON object gets on parsing a property which is
    // called '#text' and an array with line breaks. These linebreaks will be removed in this method.
    private removeLineBreaks(json: {}): void {
        Object.keys(json).forEach((key, index) => {
            if (key === "#text" && Array.isArray(json[key])) delete json[key];
            if (typeof json[key] === "object") this.removeLineBreaks(json[key]);
        });
    }

    // Sometimes xml contains comments. This method removes all comments of the JSON object
    private removeCommentProperties(json: {}): void {
        Object.keys(json).forEach((key, index) => {
            if (key === "#comment") delete json[key];
            if (typeof json[key] === "object") this.removeCommentProperties(json[key]);
        });
    }

    // if the object has only one property with name '#text'
    // it would be transformed to property == '#text' value
    private transformTextOnly(json: {}): void {
        Object.keys(json).forEach((key, index) => {
            let hasMoreProps = Object.keys(json[key]).length > 1;
            let firstPropertyOfProperty = Object.keys(json[key])[0];
            if (hasMoreProps || typeof json[key][firstPropertyOfProperty] === "object") {
                this.transformTextOnly(json[key]);
                return;
            }

            if (typeof json[key] === "object" && json[key]["#text"]) json[key] = json[key]["#text"];
        });
    }

    // This method parses an XML DOM element into a JSON object
    private xmlToJson(xml, parseArrayName: Array<string> = []): {} {
        // Create the return object
        let obj = {};

        if (xml.nodeType == 1) {
            // element
            // do attributes
            if (xml.attributes.length > 0) {
                obj["@attributes"] = {};
                for (let j = 0; j < xml.attributes.length; j++) {
                    let attribute = xml.attributes.item(j);
                    obj["@attributes"][attribute.nodeName] = attribute.nodeValue;
                }
            }
        } else if (xml.nodeType == 3) {
            // text
            obj = xml.nodeValue;
        }

        // do children
        if (xml.hasChildNodes()) {
            for (let i = 0; i < xml.childNodes.length; i++) {
                let item = xml.childNodes.item(i);
                let nodeName = item.nodeName;
                if (typeof obj[nodeName] == "undefined") {
                    obj[nodeName] = this.xmlToJson(item, parseArrayName);

                    if (parseArrayName.indexOf(nodeName) < 0) {
                        obj[nodeName] = this.xmlToJson(item, parseArrayName);
                    } else {
                        obj[nodeName] = [];
                        obj[nodeName].push(this.xmlToJson(item, parseArrayName));
                    }
                } else {
                    if (typeof obj[nodeName].push == "undefined") {
                        let old = obj[nodeName];
                        obj[nodeName] = [];
                        obj[nodeName].push(old);
                    }
                    obj[nodeName].push(this.xmlToJson(item, parseArrayName));
                }
            }
        }
        return obj;
    }
}
