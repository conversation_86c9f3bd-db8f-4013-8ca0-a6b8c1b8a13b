/** -----------------------------------------------------------
 * [Tiled](https://www.mapeditor.org)
 * ------------------------------------------------------------ */
import * as cc from "cc";

/**
 * LINK: https://www.mapeditor.org/docs/scripting/index.html#TiledObjectPropertyValue
 * number | string | boolean | ObjectRef | FilePath | MapObject | PropertyValue | undefined;
 */
export type TiledObjectPropertyValue = number | string | boolean | cc.Color | undefined;

export interface ITiledMapData {
    mapName?: string;
    sceneName?: string;
    asset?: cc.TiledMapAsset;
    width?: number;
    height?: number;
    tileWidth?: number;
    tileHeight?: number;
    layerGroups?: Array<ITiledMapLayerGroup>;
    objectGroups?: Array<ITiledMapObjectGroup>;
}

export interface ITiledMapLayerGroup {
    id: number;
    name?: string;
    objectGroups?: Array<ITiledMapObjectGroup>;
}

export interface ITiledMapObjectGroup {
    id: number;
    name?: string;
    objects?: Array<ITiledMapObject>;
    properties?: { [name: string]: boolean | number | string | cc.Color };
}

/**
 * LINK: https://www.mapeditor.org/docs/scripting/classes/MapObject.html
 */
export interface ITiledMapObject {
    id: number;
    gid?: number;
    name?: string;
    template?: string;
    type?: string;
    size?: cc.Size;
    pos?: cc.Vec2;
    properties?: { [name: string]: boolean | number | string | cc.Color };
}
