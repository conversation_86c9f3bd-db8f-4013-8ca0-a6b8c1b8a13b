import Logger from "../../../../Framework/Logger";

export default class TiledUtils {
    /** 伪造一个全是0的二维数组  */
    public static makeArray2WithZero(width: number, height: number): number[][] {
        let tmp, x;
        let ret: number[][] = [];
        for (let y = 0; y < height; y++) {
            tmp = [];
            for (x = 0; x < width; x++) {
                tmp.push(0);
            }
            ret.push(tmp);
        }
        return ret;
    }

    /** 检查两个矩阵大小是否一样  */
    private static checkMatrixSize(a: number[][], b: number[][]): boolean {
        const aHeight = a.length;
        const aWidth = a[0]?.length || 0;
        const bHeight = b.length;
        const bWidth = b[0]?.length || 0;

        if (aHeight != bHeight || aWidth != bWidth) {
            console.error("2个矩阵大小不一致");
            Logger.error("aWidth, aHeight, bWidth, bHeight" + aWidth + " " + aHeight + " " + bWidth + " " + bHeight);
            return false;
        }
        return true;
    }

    /** 合并 0/1 矩阵 */
    public static mergerMatrixWithZeroOne(a: number[][], b: number[][]): number[][] {
        if (!this.checkMatrixSize(a, b)) {
            return a;
        }
        const height = a.length;
        const width = a[0]?.length || 0;

        var x = 0;
        for (let y = 0; y < height; y++) {
            for (x = 0; x < width; x++) {
                a[y][x] = Math.min(b[y][x] + a[y][x], 1);
            }
        }
        return a;
    }

    private static min(a: number, b: number): number {
        return a > b ? b : a;
    }

    /** 给矩阵赋值 */
    public static AssignValueToMatrix(a: number[][], b: number[][], value: number): number[][] {
        if (!this.checkMatrixSize(a, b)) {
            return a;
        }
        const height = a.length;
        const width = a[0]?.length || 0;

        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                if (b[y][x] > 0) {
                    a[y][x] = value;
                }
            }
        }
        return a;
    }

    /** 放大矩阵 */
    public static mulMatrix(matrix: number[][], scale: number = 1): number[][] {
        if (!matrix) return [];

        const ret: number[][] = [];
        const height = matrix.length;
        const width = matrix[0]?.length || 0;
        let x, k;

        for (let y = 0; y < height; y++) {
            for (let h = 0; h < scale; h++) {
                const list = [];
                for (x = 0; x < width; x++) {
                    for (k = 0; k < scale; k++) {
                        list.push(matrix[y][x]);
                    }
                }
                ret.push(list);
            }
        }
        return ret;
    }
}
