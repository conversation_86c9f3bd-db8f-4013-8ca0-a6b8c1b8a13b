import * as cc from "cc";

import { GameEvent } from "../../../GameEvent/Events";
import { ViewId } from "../../../Config/ViewConst";
import { LoadUtils } from "../../Utils/LoadUtils";
import { ViewUtil } from "../../Utils/ViewUtils";
import UIButton from "../../Components/UIButton";
import  pb from "../../../Proto/pb";
// import { TreasureBoxType } from "../TreasureBox/TreasureBoxController";
import GuideBoxArrow from "./GuideBoxArrow";
import EventManagerComponent from "../../GlobalComponent/EventManagerComponent";
import ViewManager from "../../GlobalComponent/ViewManagerComponent";
import Timer from "../../Utils/Timer";
import AppManager from "../../Managers/AppManager";

const { ccclass, property } = cc._decorator;

@ccclass
export default class TreasureBox extends cc.Component {

    @property(UIButton)
    clickBtn: UIButton = null;
    @property(cc.Animation)
    anim_effect: cc.Animation = null;

    /**引到宝箱箭头 */
    private guideBoxArrow: cc.Node = null;
    // /**宝箱数据 */
    // private info: pb.IPbTreasureBox = null;
    private info: any = null

    onLoad() {
        EventManagerComponent.instance().addEventListener(GameEvent.RECEIVE_TREASURE_BOX_SUCCESS, this.dropRewardItem, this);
        this.clickBtn.setOnClick(() => {
            ViewManager.instance().show(ViewId.TreasureBoxInfo, { info: this.info });
        }, this);
    }

    // init(info: pb.IPbTreasureBox) {
    //     this.info = info;
    //     cc.tween(this.clickBtn.node).repeatForever(
    //         cc.tween<cc.Node>().to(0.3, { scale: this.clickBtn.node.scale + 0.1 }).to(0.3, { scale: this.clickBtn.node.scale })
    //     ).start();
    //     oops.timer.clear(this, this.treasureBoxCountdown);
    //     this.treasureBoxCountdown();
    //     oops.timer.loop(1000, this, this.treasureBoxCountdown);
    //     this.createGuideArrwo();
    // }

    start() {
        this.anim_effect.node.getComponent(cc.UIOpacity).opacity = 255;
        this.anim_effect.play();
    }
    protected onDestroy(): void {
        Timer.clear(this, this.treasureBoxCountdown);
        if (this.guideBoxArrow) {
            this.guideBoxArrow.getComponent(GuideBoxArrow).destroySelf();
        }
        cc.Tween.stopAllByTarget(this.clickBtn.node);
        EventManagerComponent.instance().removeEventListener(GameEvent.RECEIVE_TREASURE_BOX_SUCCESS, this.dropRewardItem, this);
    }

    update(dt) {
    }

    /**宝箱计时器 */
    private treasureBoxCountdown(): void {
        if (!this.info) {
            Timer.clear(this, this.treasureBoxCountdown);
            return;
        }
        const time: number = (this.info.disappearTime - AppManager.instance().TimeManager.serverTimeMs) / 1000;
        if (time <= 0) {
            Timer.clear(this, this.treasureBoxCountdown);
            // App.TreasureBoxController.clearTreasureBox(this.info.boxType);
        }
    }

    /**创建引导宝箱箭头 */
    private createGuideArrwo(): void {
        // if (this.info.boxType === TreasureBoxType.BossBox) return;
        const url: string = "prefabs/game/map/guideBoxArrow";
        let self = this;
        LoadUtils.loadPrefab(url, () => {
            self.guideBoxArrow = ViewUtil.createPrefabNode(url);
            const targetPos = cc.v2(self.node.position.x, self.node.position.y);
            self.guideBoxArrow.addComponent(GuideBoxArrow).init(targetPos, self.info.boxType);
        });
    }

    /**掉落宝箱奖励 */
    private dropRewardItem(): void {
        // this.scheduleOnce(() => {
        //     const dropPath = "prefabs/game/map/drop";
        //     const msPrefab2 = "effect/ui/effect_ty_tw02";
        //     let cfg = MainlineTreasureBoxCache.Instance.getInTypeTypeArgIndex(this.info.boxType, this.info.boxArg)
        //     let rewards = cfg.getRewardShow();
        //     for (let i = 0; i < rewards.length; i++) {
        //         const item = rewards[i];
        //         let reward = { id: item, num: 1 };
        //         LoadUtils.loadPrefabArray([dropPath, msPrefab2], () => {
        //             let dropNode: cc.Node = ViewUtil.createPrefabNode(dropPath);
        //             let nodeMS: cc.Node = ViewUtil.createPrefabNode(msPrefab2);
        //             dropNode.addChild(nodeMS, cc.macro.MIN_ZINDEX);
        //             dropNode.addComponent(DropTrack).init(cc.v2(this.node.position.x, this.node.position.y), reward.id, this.node.height * 10, reward.num);
        //             if (i === rewards.length - 1) {
        //                 App.TreasureBoxController.clearTreasureBox(this.info.boxType);
        //             }
        //         })
        //         // EffectUtils.showDropEffect(cc.v2(this.node.position.x, this.node.position.y), reward.id, this.node.height * 10, reward.num, true);
        //     }
        // }, 0.5);

        // TODO:
        // App.TreasureBoxController.clearTreasureBox(this.info.boxType);
    }

    public destroySelf(): void {
        if (this.node?.isValid) {
            this.node.removeFromParent();
            this.node.destroy();
        }
    }
}
