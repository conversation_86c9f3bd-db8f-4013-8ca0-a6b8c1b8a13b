import * as cc from "cc";
import { GameEvent } from "../../../GameEvent/Events";
import { LoadUtils } from "../../Utils/LoadUtils";
import { ViewUtil } from "../../Utils/ViewUtils";
import { HERO_AXIS_RATE, Player_Init_data } from "../../Unit/Actor/PlayerType";
import GuideArrow from "./GuideArrow";
import EventManagerComponent from "../../GlobalComponent/EventManagerComponent";

enum RangePointerStatus {
    NONE = 0,
    LOADING = 1,
    INITED = 2,
}

/**
 * 范围指示器
 */
export class RangePointer {

    private hitRangeNode: cc.Node;

    private range: cc.Node;

    private pointer: cc.Node;

    private _attackRange: number = Player_Init_data.TeamRange;

    /**0.未设置   1.加载中   2.加载完成 */
    private _status: RangePointerStatus = RangePointerStatus.NONE;

    private _parent: cc.Node;

    private _point: cc.Vec3 = cc.Vec3.ZERO;

    private _timeId: number;

    private _moveAngle: number;


    /**一直显示 */
    private alwaysShow: boolean = false;
    /**任务目标引导箭头 */
    private guideArrow: cc.Node = null;

    constructor() {
        this.init();
    }

    private init(): void {
        EventManagerComponent.instance().addEventListener(GameEvent.ADD_TASK_TARGET_GUIDE_ARROW, this.createGuideArrwo, this);
        this._attackRange = Player_Init_data.TeamRange;
    }

    private createGuideArrwo(targetPos: cc.Vec2): void {
        if (this.guideArrow?.isValid) {
            this.guideArrow.getComponent(GuideArrow).updateTargetPos(targetPos);
            return;
        }

        const url: string = "prefabs/game/map/guideArrow";
        LoadUtils.loadPrefab(url, () => {
            this.guideArrow = ViewUtil.createPrefabNode(url);
            this.guideArrow.addComponent(GuideArrow).init(targetPos);
        });
    }

    private loadPreb(callback: Function) {
        if (this._status == RangePointerStatus.LOADING) {
            return;
        }
        if (this._status == RangePointerStatus.INITED) {
            callback && callback.apply(this);
            return;
        }
        this._status = RangePointerStatus.LOADING;
        // App.LoadManager.load({
        //     url: "game/hitRange",
        //     type: cc.Asset
        // }, () => {
        //     let hitRange = App.ResManager.getRes("game/hitRange", cc.Prefab);
        //     if (hitRange) {
        //         this._status = RangePointerStatus.INITED;

        //         this.hitRangeNode = cc.instantiate(hitRange);
        //         this.hitRangeNode.setPosition(0, 0);

        //         this.range = this.hitRangeNode.getChildByName("range");
        //         this.range.width = this._attackRange * 2;
        //         this.range.height = this._attackRange * 1.5;

        //         this.pointer = this.hitRangeNode.getChildByName("pointer");

        //         callback && callback.apply(this);
        //     }
        // }, null, null, this);


        const url: string = "game/hitRange";
        LoadUtils.loadPrefab(url, () => {
            // oops.res.load(url, () => {
            this.hitRangeNode = ViewUtil.createPrefabNode(url);
            if (this.hitRangeNode) {
                this._status = RangePointerStatus.INITED;
                this.hitRangeNode.setPosition(0, 0);
                this.range = this.hitRangeNode.getChildByName('range');
                const transform = this.range.getComponent(cc.UITransform);
                transform.setContentSize(this._attackRange * 2, this._attackRange * 1.5);
                this.pointer = this.hitRangeNode.getChildByName('pointer');
                callback && callback.apply(this);
            }
        });
    }

    private stopTimer() {
        if (this._timeId > 0) {
            window.clearTimeout(this._timeId);
            this._timeId = 0;
        }
    }

    /**
     * 显示
     * @param x 
     * @param y 
     */
    public showRange(x: number, y: number) {
        this.setPoint(x, y);
        this.stopTimer();
        this.loadPreb(this._showRange);
    }

    private _showRange() {
        if (!this.parent) {
            //未设置父对象
            return;
        }
        this.stopTimer();
        if (!this.hitRangeNode.parent) {
            this.parent.addChild(this.hitRangeNode);
        }
        this.range.active = true;
        this.pointer.active = true;
        this.hitRangeNode.setPosition(this._point);

        this.hitRangeNode.x = this._point.x;
        this.hitRangeNode.y = this._point.y;

        //效果播放完成后激活指示器
        this._timeId = window.setTimeout(() => {
            this.hideRange();
        }, 3000);
    }

    /**
     * 隐藏范围
     */
    public hideRange() {
        if (this.hitRangeNode && !this.alwaysShow) {
            this.hitRangeNode.removeFromParent();
        }
    }

    /**父对象 */
    public set parent(value: cc.Node) {
        this._parent = value;
    }

    public get parent(): cc.Node {
        return this._parent;
    }

    /**
     * 设置坐标
     * @param x 
     * @param y 
     */
    private setPoint(x: number, y: number) {
        this._point.x = x;
        this._point.y = y;

        if (this.hitRangeNode) {
            this.hitRangeNode.x = this._point.x;
            this.hitRangeNode.y = this._point.y;
        }
    }

    get point(): cc.Vec3 {
        return this._point;
    }



    /** 显示指示器 */
    public showPointer(point: cc.Vec3, moveAngle: number, range: number) {
        this._moveAngle = moveAngle;
        this._attackRange = range;
        this.setPoint(point.x, point.y);
        this.stopTimer();
        this.loadPreb(this._showPointer);
    }

    private _showPointer() {
        if (!this.parent) {
            //未设置父对象
            return;
        }
        if (!this.hitRangeNode.parent) {
            this.parent.addChild(this.hitRangeNode);
        }
        if (this.range && this.range.active) {
            this.range.active = true;
        }
        if (!this.guideArrow?.isValid && this.pointer) {
            this.pointer.active = true;
        } else {
            this.pointer.active = false;
        }
        if (this.pointer && this.pointer.active) {
            this.pointer.angle = this._moveAngle;
        }
        this.hitRangeNode.x = this._point.x;
        this.hitRangeNode.y = this._point.y;
        const transform = this.range.getComponent(cc.UITransform);
        transform.setContentSize(this._attackRange * 2, this._attackRange * 2 * HERO_AXIS_RATE);
    }
}