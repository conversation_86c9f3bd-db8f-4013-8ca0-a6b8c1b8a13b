// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import * as cc from "cc";
const {ccclass, property} = cc._decorator;

@ccclass
export default class HpBar extends cc.ProgressBar {
    @property(cc.Label)
    public level:cc.Label = null;
    // LIFE-CYCLE CALLBACKS:

    // onLoad () {}

    start () {

    }

    // update (dt) {}

    setProcess( process ){
        this.progress = process;
    }

    setLevel(level:number): void {
        this.level.string = level.toString();
    }
}
