import * as cc from "cc";
import { MapType } from "../MapType";
import Point from "./Point";
import RoadNode from "./RoadNode";

/**
 * 地图路点信息算法工具，包括方形，菱形，六边形的路点算法
 */
export default class MapRoadUtils {
    private static _instance: MapRoadUtils;

    public static get instance(): MapRoadUtils {
        if (this._instance == null) {
            this._instance = new MapRoadUtils();
        }
        return this._instance;
    }

    /**
     * 地图宽度
     */
    private _mapWidth: number;

    /**
     *地图高度
     */
    private _mapHeight: number;

    /**
     *地图一共分成几行
     */
    private _row: number;

    /**
     *地图一共分成几列
     */
    private _col: number;

    /**
     *地图路点单元格宽
     */
    private _nodeWidth: number;

    /**
     *地图路点单元格高
     */
    private _nodeHeight: number;

    /**
     *地图路点单元宽的一半
     */
    private _halfNodeWidth: number;

    /**
     *地图路点单元高的一半
     */
    private _halfNodeHeight: number;

    /**
     * 地图类型
     */
    private _mapType: MapType;

    private _mapRoad: IMapRoad;

    /** 
     * @mapWidth 72000
     * @mapHeight 43200
     * @nodeWidth 100
     * @nodeHeight 60
      */
    public updateMapInfo(mapWidth: number, mapHeight: number, nodeWidth: number, nodeHeight: number, mapType: MapType): void {
        this._mapWidth = mapWidth;
        this._mapHeight = mapHeight;
        this._nodeWidth = nodeWidth;
        this._nodeHeight = nodeHeight;

        this._halfNodeWidth = Math.floor(this._nodeWidth / 2);
        this._halfNodeHeight = Math.floor(this._nodeHeight / 2);

        this._mapType = mapType;

        switch (this._mapType) {
            case MapType.angle45:
                this._col = Math.ceil(mapWidth / this._nodeWidth);
                this._row = Math.ceil(mapHeight / this._nodeHeight);
                this._mapRoad = new MapRoad45Angle(this._row, this._col, this._nodeWidth, this._nodeHeight, this._halfNodeWidth, this._halfNodeHeight);
                break;
        }
    }

    /**
     *根据地图平面像素坐标获得路节点
     * @param x
     * @param y
     * @return
     *
     */
    public getNodeByPixel(x: number, y: number): RoadNode {
        if (this._mapRoad) {
            return this._mapRoad.getNodeByPixel(x, y);
        }
        return new RoadNode();
    }

    /**
     *根据路点场景世界坐标获得路节点
     * @param cx
     * @param cy
     * @return
     *
     */
    public getNodeByWorldPoint(cx: number, cy: number): RoadNode {
        if (this._mapRoad) {
            return this._mapRoad.getNodeByWorldPoint(cx, cy);
        }
        return new RoadNode();
    }

    /**
     *根据像素坐标得到场景世界坐标
     * @param x
     * @param y
     * @return
     *
     */
    public getWorldPointByPixel(x: number, y: number): Point {
        if (this._mapRoad) {
            return this._mapRoad.getWorldPointByPixel(x, y);
        }
        return new Point();
    }

    /**
     *根据世界坐标获得像素坐标
     * @param cx
     * @param cy
     * @return
     *
     */
    public getPixelByWorldPoint(cx: number, cy: number): Point {
        if (this._mapRoad) {
            return this._mapRoad.getPixelByWorldPoint(cx, cy);
        }
        return new Point();
    }

    /**
     * 根据TileMap地图像素坐标获得游戏中的坐标
     * @param px
     * @param py
     */
    public getGamePixelByTileMapPoint(px: number, py: number): Point {
        if (this._mapRoad) {
            return this._mapRoad.getGamePixelByTileMapPoint(px, py);
        }
        return new Point();
    }

    // public getGridXYByTileMapPoint(tx:number,ty:number):Point{
    //     if (this._mapRoad) {
    //         return this._mapRoad.getGridXYByTileMapPoint(tx, tx);
    //     }
    //     return new Point();
    // }

    /**
     * 地图宽
     */
    public get mapWidth(): number {
        return this._mapWidth;
    }

    /**
     * 地图高
     */
    public get mapHeight(): number {
        return this._mapHeight;
    }

    /**
     * 路点宽
     */
    public get nodeWidth(): number {
        return this._nodeWidth;
    }

    /**
     * 路点高
     */
    public get nodeHeight(): number {
        return this._nodeHeight;
    }

    /**
     * 路点总行数
     */
    public get row(): number {
        return this._row;
    }

    /**
     * 路点总列数
     */
    public get col(): number {
        return this._col;
    }

    /**
     * 路点宽的一半
     */
    public get halfNodeWidth(): number {
        return this._halfNodeWidth;
    }

    /**
     * 路点高的一半
     */
    public get halfNodeHeight(): number {
        return this._halfNodeHeight;
    }

    /**
     *地图类型
     */
    public get mapType(): MapType {
        return this._mapType;
    }
}

/**
 *地图路点处理接口
 * <AUTHOR>
 *
 */
interface IMapRoad {
    /**
     *根据地图平面像素坐标获得路节点
     * @param x
     * @param y
     * @return
     *
     */
    getNodeByPixel(x: number, y: number): RoadNode;

    /**
     *根据路点场景世界坐标获得路节点
     * @param cx
     * @param cy
     * @return
     *
     */
    getNodeByWorldPoint(cx: number, cy: number): RoadNode;

    /**
     *根据像素坐标得到场景世界坐标
     * @param x
     * @param y
     * @return
     *
     */
    getWorldPointByPixel(x: number, y: number): Point;

    /**
     *根据世界坐标获得像素坐标
     * @param cx
     * @param cy
     * @return
     *
     */
    getPixelByWorldPoint(cx: number, cy: number): Point;

    /**
     * 根据TileMap地图像素坐标获得游戏中的坐标
     * @param px
     * @param py
     */
    getGamePixelByTileMapPoint(px: number, py: number): Point;

    // getGridXYByTileMapPoint(tx: number, ty: number): Point;
}

/**
 *45度等视角地图路点处理接口实现
 * <AUTHOR>
 *
 */
class MapRoad45Angle implements IMapRoad {
    /**
     *地图一共分成几行
     */
    private _row: number;

    /**
     *地图一共分成几列
     */
    private _col: number;

    /**
     *地图路点单元格宽
     */
    private _nodeWidth: number;

    /**
     *地图路点单元格高
     */
    private _nodeHeight: number;

    /**
     *地图路点单元宽的一半
     */
    private _halfNodeWidth: number;

    /**
     *地图路点单元高的一半
     */
    private _halfNodeHeight: number;

    public constructor(row: number, col: number, nodeWidth: number, nodeHeight: number, halfNodeWidth: number, halfNodeHeight: number) {
        this._row = row;
        this._col = col;
        this._nodeWidth = nodeWidth;
        this._nodeHeight = nodeHeight;
        this._halfNodeWidth = halfNodeWidth;
        this._halfNodeHeight = halfNodeHeight;
    }

    /**
     *根据地图平面像素坐标获得路节点
     * @param x
     * @param y
     * @return
     *
     */
    public getNodeByPixel(x: number, y: number): RoadNode {
        var wPoint: Point = this.getWorldPointByPixel(x, y);
        var fPoint: Point = this.getPixelByWorldPoint(wPoint.x, wPoint.y);

        var node: RoadNode = new RoadNode();

        node.cx = wPoint.x;
        node.cy = wPoint.y;

        node.px = fPoint.x;
        node.py = fPoint.y;

        return node;
    }

    /**
     *根据路点场景世界坐标获得路节点
     * @param cx
     * @param cy
     * @return
     *
     */
    public getNodeByWorldPoint(cx: number, cy: number): RoadNode {
        var point: Point = this.getPixelByWorldPoint(cx, cy);
        return this.getNodeByPixel(point.x, point.y);
    }

    /**
     *根据像素坐标得到场景世界坐标
     * @param x
     * @param y
     * @return
     *
     */
    public getWorldPointByPixel(x: number, y: number): Point {
        const map_width = this._row;
        const map_height = this._col;
        const tile_width = this._nodeWidth;
        const tile_height = this._nodeHeight;

        const out = new Point();
        out.x = x;
        out.y = y;

        const div_pos = cc.v2(out.x / tile_width, out.y / tile_height);

        out.x = Math.floor(map_width - div_pos.y + div_pos.x - map_width / 2);
        out.y = Math.floor(map_height - div_pos.y - div_pos.x + map_height / 2);
        return out;
    }

    /**
     *根据世界坐标获得游戏像素坐标
     * @param cx
     * @param cy
     * @return
     *
     */
    public getPixelByWorldPoint(cx: number, cy: number): Point {
        const map_width = this._row;
        const map_height = this._col;
        const tile_width = this._nodeWidth;
        const tile_height = this._nodeHeight;

        let x = ((cx - cy + map_width) * tile_width) / 2;
        let y = ((map_height * 2 - cx - cy) / 2) * tile_height - tile_height / 2;
        return new Point(x, y);
    }

    /**
     * 根据TileMap地图坐标获得游戏中的坐标
     * @param px
     * @param py
     */
    public getGamePixelByTileMapPoint(px: number, py: number): Point {
        let cx = Math.floor(px / this._nodeHeight); //x有多少格
        let cy = Math.floor(py / this._nodeHeight); //y有多少格
        let point = this.getPixelByWorldPoint(cx, cy);

        let deltaX = px % this._nodeHeight;
        let deltaY = py % this._nodeHeight;

        let offsetX: number = deltaX * 0.834 - deltaY * 0.834;
        let offsetY: number = deltaX / 3 + deltaY / 3 + 25;
        return new Point(point.x + offsetX, point.y + this._nodeHeight / 2 - offsetY);
    }
}




