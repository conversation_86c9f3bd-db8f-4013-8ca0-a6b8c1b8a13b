import Timer from "../../../Utils/Timer";
import { MapType } from "../MapType";
import AStarRoadSeeker from "./AStarRoadSeeker";
import IRoadSeeker from "./IRoadSeeker";
import MapRoadUtils from "./MapRoadUtils";
import { PathOptimize } from "./PathOptimize";
import { PathQuadSeek } from "./PathQuadSeek";
import Point from "./Point";
import RoadNode from "./RoadNode";

/**
 * 寻路代理器
 */
export default class PathFindingAgent {
    private static _instance: PathFindingAgent;

    public static get instance(): PathFindingAgent {
        if (this._instance == null) {
            this._instance = new PathFindingAgent();
        }
        return this._instance;
    }

    private _roadDic: { [key: string]: RoadNode } = {};

    private _roadSeeker: IRoadSeeker;

    private _mapId: number = 0; //对应的地图

    /**
     * 获得寻路接口
     */
    public get roadSeeker(): IRoadSeeker {
        return this._roadSeeker;
    }

    /**
     *用于检索一个节点周围8个点的向量数组 (四边形格子用)
     */
    protected _round: number[][] = [
        [0, -1],
        [1, -1],
        [1, 0],
        [1, 1],
        [0, 1],
        [-1, 1],
        [-1, 0],
        [-1, -1],
    ];

    /**
     *用于检索一个节点周围6个点的向量数组 格子列数为偶数时使用  (六边形格子用)
     */
    protected _round1: number[][] = [
        [0, -1],
        [1, -1],
        [1, 0],
        [0, 1],
        [-1, 0],
        [-1, -1],
    ];

    /**
     *用于检索一个节点周围6个点的向量数组 格子列数为奇数时使用  (六边形格子用)
     */
    protected _round2: number[][] = [
        [0, -1],
        [1, 0],
        [1, 1],
        [0, 1],
        [-1, 1],
        [-1, 0],
    ];

    private curIndex: number = 0;
    private readonly preCount: number = 20;
    private tiledMapWidth: number = 0;
    private tiledMapHeight: number = 0;
    private passableArea: number[][];
    private drawFunc: Function;

    /**
     * 
     * 初始化寻路数据
     * @mapWidth 地图像素宽度
     * @mapHeight 地图像素高度
     * @tiledMapWidth 网格数量(横)
     * @tiledMapHeight 网格数量(竖)
     * @nodeWidth 一个格子的宽度
     * @param roadDataArr 路电数据
     * @param mapType 地图类型
     */
    public init(mapWidth, mapHeight, tiledMapWidth, tiledMapHeight, nodeWidth, nodeHeight, passableArea, drawFunc: Function, mapId: number) {
        this.tiledMapWidth = tiledMapWidth;
        this.tiledMapHeight = tiledMapHeight;
        this.passableArea = passableArea;
        this.drawFunc = drawFunc;

        //  72000 43200 720 720 100 60
        MapRoadUtils.instance.updateMapInfo(mapWidth, mapHeight, nodeWidth, nodeHeight, MapType.angle45);
        this.curIndex = 0;
        Timer.frameLoop(5, this, this.onFrame);
        this._roadSeeker = new AStarRoadSeeker(this._roadDic);//先创建一个空的路点，以防玩家动太快，报错
        this._mapId = mapId;
    }

    public getMapId(): number {
        return this._mapId;
    }

    private onFrame() {
        let node: RoadNode;
        const endIndex: number = this.curIndex + this.preCount;
        for (var x: number = this.curIndex; x < endIndex; x++) {
            for (var y: number = 0; y < this.tiledMapHeight; y++) {
                if (this._roadDic[x + "_" + y]) {
                    node = this._roadDic[x + "_" + y]
                    node.value = this.passableArea[y][x];
                } else {
                    node = MapRoadUtils.instance.getNodeByWorldPoint(x, y);
                    node.value = this.passableArea[y][x];
                    this._roadDic[node.cx + "_" + node.cy] = node;
                }
            }
        }
        this.curIndex += this.preCount;
        if (this.curIndex >= this.tiledMapWidth) {
            Timer.clear(this, this.onFrame);
            this._roadSeeker = new AStarRoadSeeker(this._roadDic);
            this.drawFunc();
            this.drawFunc = null;
            this.passableArea = null;
        }
    }


    /**
     * 寻路方法，如果目标不可到达，不会返回任何路径。  备注：2d寻路，参数的单位是像素，3d寻路单位是米，2d和3d寻路是通用的
     *
     *
     * @param startX 起始像素位置X
     * @param startY 起始像素位置Y
     * @param targetX 目标像素位置X
     * @param targetY 目标像素位置Y
     * @param radius 寻路的半径 （2d是像素，3d是米）。备注：如果寻路的角色想按自身的占地面积进行寻路，可以设置这个值
     * @returns 返回寻路路点路径
     */
    public seekPath(startX: number, startY: number, targetX: number, targetY: number, radius: number = 0): RoadNode[] {
        var startNode: RoadNode = this.getRoadNodeByPixel(startX, startY);
        var targetNode: RoadNode = this.getRoadNodeByPixel(targetX, targetY);
        var seakRadius: number = this.getSeakRadius(radius);

        var roadNodeArr: RoadNode[] = this._roadSeeker.seekPath(startNode, targetNode, seakRadius);

        return roadNodeArr;
    }

    /**
     * 寻路方法，如果目标点不可达到，则返回离目标点最近的路径。  备注：2d寻路，参数的单位是像素，3d寻路单位是米，2d和3d寻路是通用的
     *
     *
     * @param startX 起始像素位置X
     * @param startY 起始像素位置Y
     * @param targetX 目标像素位置X
     * @param targetY 目标像素位置Y
     * @param radius 寻路的半径 （2d是像素，3d是米）。备注：如果寻路的角色想按自身的占地面积进行寻路，可以设置这个值
     * @returns 返回寻路路点路径
     */
    public seekPath2(startX: number, startY: number, targetX: number, targetY: number, radius: number = 0): RoadNode[] {
        var startNode: RoadNode = this.getRoadNodeByPixel(startX, startY);
        var targetNode: RoadNode = this.getRoadNodeByPixel(targetX, targetY);
        var seakRadius: number = this.getSeakRadius(radius);

        var roadNodeArr: RoadNode[] = this._roadSeeker.seekPath2(startNode, targetNode, seakRadius);

        return roadNodeArr;
    }

    /**
     * 测试寻路过程，测试用，如果遇到bug，可以通过这个函数监测寻路的每一个步骤的情况。能快速定位问题。
     * @param startX 起始像素位置X
     * @param startY 目标像素位置Y
     * @param targetX 目标像素位置X
     * @param targetY 目标像素位置Y
     * @param radius 寻路的像素位置
     * @param seekRoadCallback 寻路的每个步骤都会执行这个回调
     * @param target 回调函数的目标对象，用于给回调函数指定this是什么
     * @param time 测试寻路时，每个步骤之间的时间间隔，单位是毫秒，如果想慢点查看每个寻路步骤，可以把这个值设置大点
     */
    public testSeekRoad(startX: number, startY: number, targetX: number, targetY: number, radius: number, seekRoadCallback: Function, target: any, time) {
        var startNode: RoadNode = this.getRoadNodeByPixel(startX, startY);
        var targetNode: RoadNode = this.getRoadNodeByPixel(targetX, targetY);
        var seakRadius: number = this.getSeakRadius(radius);

        this._roadSeeker.testSeekPathStep(startNode, targetNode, seakRadius, seekRoadCallback, target, time);
    }

    /**
     * 把像素半径转换为寻路半径,寻路半径是以路点为单位的半径，不能用像素半径进行寻路，所以要转换
     * @param radius 像素半径
     */
    public getSeakRadius(radius: number = 0): number {
        var nodeRadius: number = Math.min(MapRoadUtils.instance.halfNodeWidth, MapRoadUtils.instance.halfNodeHeight); //把路点的宽半径和高半径之间的最小值作为路点的半径

        //seakRadius 是寻路用的半径，单位以路点为单位。和radius以像素为单位有区别，所以要把radius转换成寻路用的半径
        var seakRadius = Math.ceil((radius - nodeRadius) / (2 * nodeRadius));
        if (seakRadius > 0) {
            return seakRadius;
        }

        return 0;
    }

    /**
     * 两个路点之间是否可直达（即是否可以两点一线到达）
     * @param startNode
     * @param targetNode
     */
    public isArriveBetweenTwoNodes(startNode: RoadNode, targetNode: RoadNode): boolean {
        if (startNode == null || targetNode == null) {
            //两个点，只要有一个不在地图内，就是不可直达
            return false;
        }

        return this._roadSeeker.isArriveBetweenTwoNodes(startNode, targetNode);
    }

    /**
     * 在地图上，两个位置之间是否可直达。2d是像素位置，3d是米
     * @param x1 位置1 x轴
     * @param y1 位置1 y轴
     * @param x2 位置2 x轴
     * @param y2 位置2 y轴
     * @returns
     */
    public isArriveBetweenTwoPos(x1: number, y1: number, x2: number, y2: number): boolean {
        var startNode: RoadNode = this.getRoadNodeByPixel(x1, y1);
        var targetNode: RoadNode = this.getRoadNodeByPixel(x2, y2);
        return this._roadSeeker.isArriveBetweenTwoNodes(startNode, targetNode);
    }

    /**
     * 根据像素坐标获得路节点
     * @param px 像素坐标x
     * @param py 像素坐标y
     * @returns
     */
    public getRoadNodeByPixel(px: number, py: number): RoadNode {
        var point: Point = MapRoadUtils.instance.getWorldPointByPixel(px, py);
        var node: RoadNode = null;
        node = this.getRoadNode(point.x, point.y);
        return node;
    }

    /**
     * 根据世界坐标获得路节点
     * @param cx
     * @param cy
     */
    public getRoadNode(cx: number, cy: number): RoadNode {
        return this._roadSeeker.getRoadNode(cx, cy);
    }

    /**
     * 获得一个路点周围相邻的所有路点
     * @param roadNode 选定的路点
     * @param isIncludeSelf 是否包含选定的路点
     * @returns
     */
    public getRoundRoadNodes(roadNode: RoadNode, isIncludeSelf: boolean = false): RoadNode[] {
        if (roadNode == null) {
            return [];
        }

        var nodeArr: RoadNode[] = [];
        var round: number[][];

        if (isIncludeSelf) {
            nodeArr.push(roadNode);
        }
        round = this._round;
        for (var i: number = 0; i < round.length; i++) {
            var cx: number = roadNode.cx + round[i][0];
            var cy: number = roadNode.cy + round[i][1];

            nodeArr.push(this.getRoadNode(cx, cy));
        }
        return nodeArr;
    }

    /**
     * 设置最大寻路步骤，超过这个寻路步骤还寻不到终点，就默认无法达到终点。
     * 设置这个限制的目的是，防止地图太大，路点太多，没有限制的话，寻路消耗会很大，甚至会卡死
     * @param maxStep
     */
    public setMaxSeekStep(maxStep: number) {
        this._roadSeeker.setMaxSeekStep(maxStep);
    }

    /**
     * 设置路径优化等级，具体优化内容请查看脚本PathOptimize，里面有对各种优化等级的详细解释
     * @param optimize
     */
    public setPathOptimize(optimize: PathOptimize) {
        this._roadSeeker.setPathOptimize(optimize);
    }

    /**
     * 设置4方向路点地图的寻路类型，只针对45度和90度地图，不包括六边形地图（六边形地图设置这个值无任何反应）
     * @param pathQuadSeek  4方向路点地图的寻路类型
     */
    public setPathQuadSeek(pathQuadSeek: PathQuadSeek) {
        this._roadSeeker.setPathQuadSeek(pathQuadSeek);
    }

    /**
     * 自定义路点是否能通过的条件，
     * @param callback 回调方法用于自定义路点是否可通过的条件，如果参数是null，则用默认判断条件，不是null，则取代默认判断条件
     */
    public setRoadNodePassCondition(callback: Function) {
        this._roadSeeker.setRoadNodePassCondition(callback);
    }

    /** 清理路点  */
    public clear(mapId: number): void {
        if (this._mapId == mapId) {
            this._roadDic = {};
        }
        if (this._roadSeeker) {
            this._roadSeeker.dispose();
            this._roadSeeker = null;
        }
        this._mapId = 0;
    }
}
