import * as cc from "cc";

import { ViewUtil } from "../../Utils/ViewUtils";
import MapManager from "../MapInstance/Managers/MapManager";
import { MathUtils } from "../../Utils/MathUtils";
import LayerManagerComponent from "../../GlobalComponent/LayerManagerComponent";

const { ccclass, property } = cc._decorator;

@ccclass
export default class GuideArrow extends cc.Component {
    /**目标位置 */
    private targetPos: cc.Vec2 = null;
    /**伙伴团队位置 */
    private mainPlayerPos: cc.Vec2 = null;

    public init(targetPos: cc.Vec2): void {
        this.targetPos = targetPos;
        const parent = LayerManagerComponent.instance().getLayer();
        // setNodeZIndex(this.node, ZIndex.MIN);
    }

    public updateTargetPos(targetPos: cc.Vec2): void {
        this.targetPos = targetPos;
    }

    update(dt) {
        if (this.node.isValid) {
            // this.mainPlayerPos = MapManager.curMap.mainPlayer.node.getPosition();
            // if (cc.Vec2.distance(this.mainPlayerPos, this.targetPos) <= 1000) {
            //     this.node.removeFromParent();
            //     this.node.destroy();
            //     ViewUtil.removePrefabNode(this.node);
            // } else {
            //     const screenPos = cc.v2(cc.Camera.main.getWorldToScreenPoint(this.mainPlayerPos));
            //     const startPos = App.LayerManager.getLayer().convertToNodeSpaceAR(screenPos);
            //     this.node.setPosition(startPos);
            //     const dx: number = this.mainPlayerPos.x - this.targetPos.x;
            //     const dy: number = this.mainPlayerPos.y - this.targetPos.y;
            //     const radian = Math.atan2(dy, dx);
            //     let angle = radian / Math.PI * 180;
            //     this.node.angle = angle + 90;
            // }
        }
    }
}
