import * as cc from "cc";

import { UnitType } from "../../../Config/GameDefine";
import { GameEvent, MapEvent } from "../../../GameEvent/Events";
import CFG_Constant from "../../../Config/GameCfgExt/BattleConst"
import { CFG_SceneZone } from "../../../Config/GameCfg/CFGManager"
import  Logger from "../../../Framework/Logger";
import { MapAreaType, MapEnumType } from "./Base/MapDefine";
import { MapUnitBase } from "./Base/MapUnitBase";
// import NpcManager from "../../Managers/Npc/NpcManager";
// import { TaskStatus } from "../../../GameData/TaskInfo";
import { VoEntity } from "../../Unit/VoEntity";
import { VoUnit } from "../../Unit/VoUnit";
import Player, { QiPaoType } from "../../Unit/Actor/player/PlayerActor";
import { IUnit } from "../../Unit/Base/IUnit";
import EventManagerComponent from "../../GlobalComponent/EventManagerComponent";
import Timer from "../../Utils/Timer";
import SceneZoneCache from "../../../Config/GameCfgExt/SceneZone";
// import { EntityManager } from "../../Fight/Manager/EntityManager";
// import { HeroManager } from "../../Fight/Manager/HeroManager";

export default class WorldMap extends MapUnitBase {
    public static _instance: WorldMap = null;
    public static isDrawRoadLayer = true; //是否隐藏路点辅助线
    private addHeroCount: number = 0;

    private zoneIndex: number = -1;

    private readonly zoneRange: number[] = [2, 20];

    constructor() {
        super();

        this.zoneId = 1;
        this.offsetHeight = 0;
        // this.mapId = MapEnumType.WORLD_MAP;
        this.isAwake = true;
        // TODO: 
        // this.isAwake = App.UserController.mapInfo.isWorldMapActive;
    }

    protected override addEvent(): void {
        super.addEvent();
        EventManagerComponent.instance().addEventListener(
            GameEvent.UNLOCK_VILLAGE, this.onUnlockVillage, this);
    }

    protected override removeEvent(): void {
        super.removeEvent();
        EventManagerComponent.instance().removeEventListener(
            GameEvent.UNLOCK_VILLAGE, this.onUnlockVillage, this);
    }


    protected initMap(): void {
        // TODO: 
        super.initMap();
        Timer.once(200, this, () => {
            // this.createFogNpc();
            // this.createTaskNpc();
            // this.createDungeonNpc();
            // this.createTeleport();
            // this.createCrossTeleport();
            // NpcManager.instance.createNpcList();
        });
        //进入主场景。清理之前副本的数据
        // App.DailyDupManager.curDupId = 0;

        //第一次会自动激活主场. 下次从其他场景跳转到主场景需要激活.
        // App.UserController.mapInfo.isWorldMapActive = false;
        EventManagerComponent.instance().addEventListener(MapEvent.MAP_REMOVE_LOAD, this.onMapRemoveLoadHandler, this);
    }

    /** 重新连接成功 */
    reconnect(): void {
        //人物回到出生点
        // this.onHeroRebornSafe();
        this.zoneIndex = -1;
        this.lastPoint = new cc.Vec3();
        // if (this.mainPlayer && this.mainPlayer.node.isValid) {
        //     this.mainPlayer.node.getPosition(this.lastPoint);
        // } else if (this.guardCenterPos) {
        //     this.lastPoint = this.guardCenterPos.clone();
        // } else {
        //     this.lastPoint = this.getSpawnPoint();
        // }
        //移除所有怪物数目.
        this.dataArray.length = 0;
        this.entities.forEach(unit => {
            unit.removeSelf();
        })
        this.entities.length = 0;
        // this.treeList.length = 0;
        // this.monsterList.length = 0;
        this.rebornList.length = 0;
        // this.heroMgr.clearPlayer();
        // EntityManager.instance.clear();

        this.onDecodeComplete();
        Logger.trace("reconnect");
    }

    protected override onDecodeComplete(): void {
        super.onDecodeComplete();
        EventManagerComponent.instance().dispatch(GameEvent.ENTER_WORLD_COMP, null);
    }

    // clearMapData() {
    //     super.clearMapData();
    //     this.lastPoint = null;
    //     this.zoneIndex = -1;
    //     //移除所有怪物数目.
    //     this.dataArray.length = 0;
    //     this.entities.forEach(unit => {
    //         unit.removeSelf();
    //     })
    //     this.entities.length = 0;
    //     this.treeList.length = 0;
    //     this.monsterList.length = 0;
    //     this.rebornList.length = 0;
    //     this.heroMgr.clearPlayer();
    // }
    /**
     * 解锁完村庄
     */
    protected onUnlockVillage() {
        //村庄怪物
        let villageMonster = [];
        // for (let i = this.allDataArray.length - 1; i >= 0; i--) {
        //     let voEntity = this.allDataArray[i];
        //     if (!(voEntity instanceof VoEntity)) continue;
        //     if (villageMonster.indexOf(voEntity.idExcel) > -1) {
        //         this.removeEntity(voEntity);
        //         this.removeUnit(voEntity);
        //     }
        // }
        //重生列表的怪物
        for (let i = this.rebornList.length - 1; i >= 0; i--) {
            let data: VoUnit = this.rebornList[i];
            if (villageMonster.indexOf(data.idExcel) > -1) {
                this.rebornList.splice(i, 1);
            }
        }
        //清楚上次的记录. 下一帧提醒进入了某个状态
        this._lastMainPlayerRoadPoint = null;
        // this.createHomeNpc();
    }

    private lastZone: MapAreaType = null;
    override changeZoneStatus(statusPoint: number): void {
        switch (statusPoint) {
            case MapAreaType.PASSABLE_AREA:

                break;
            case MapAreaType.IMPASSABLE_AREA:

                break;
            case MapAreaType.SAFE_AREA:
                // App.EventManager.emitEvent(GameEvent.ENTER_AREA_SAFE);
                // if (this.lastZone == MapAreaType.OUTSIDE_AREA)
                //     HeroManager.instance.showQiPao(QiPaoType.EnterSafeZone);
                break;
            case MapAreaType.OUTSIDE_AREA:
                // App.EventManager.emitEvent(GameEvent.ENTER_AREA_OUTSIDE);
                // if (this.lastZone == MapAreaType.SAFE_AREA)
                //     HeroManager.instance.showQiPao(QiPaoType.ExitSafeZone);
                break;
            case MapAreaType.FOG_AREA:
                break;
            default:
                break;
        }
        this.isInSafeArea = true;
        // if (App.UserController.isUnlockVillage() && statusPoint == MapAreaType.SAFE_AREA) {
        //     this.isInSafeArea = true;
        // } else {
        //     this.isInSafeArea = false;
        // }
        this.areaDefine = true;
        this.lastZone = statusPoint;
    }

    public destroySelf() {
        EventManagerComponent.instance().targetOff(this);
        // App.Socket.removeEventListener(this);
    }

    //先把大地图的所有区怪显示出来
    override requestZoneInfo() {
        for (let i = 1; i <= 1; i++) {
            super.requestZoneInfo(i);
        }
    }

    protected loadMap(): void {
        if (this.isAwake) {
            this._loadMap();
        } else {
            super.loadMap();
        }
    }

    protected loadMapBundle(res: string): void {
        super.loadMapBundle(
            SceneZoneCache.getData(this._mapId, this._zoneId)!.mapResource.split("#")[0]);
    }

    /**
     * 初始化摄像机
     */
    public initCamera() {
        super.initCamera();
        Timer.frameLoop(1, this, this.onFrameRequest);
    }

    /**
     * 按照帧请求区数据
     */
    private onFrameRequest() {
        if (this.zoneIndex == -1) {
            this.zoneIndex = this.zoneRange[0];
        }
        if (this.zoneIndex <= SceneZoneCache.getMaxZone(this._mapId)) {
            super.requestZoneInfo(this.zoneIndex);
            this.zoneIndex++;
        } else {
            Timer.clear(this, this.onFrameRequest);
        }
    }

    override getSpawnPoint(): cc.Vec3 {
        /*
        if (App.UserController.lastLeaveVillagePos != null) {
            let pos = new cc.Vec3(App.UserController.lastLeaveVillagePos.x, App.UserController.lastLeaveVillagePos.y);
            App.UserController.lastLeaveVillagePos = null;
            return pos;
        }

        let spawnPointX: number, spawnPointY: number;
        if (this._crossFlag == -1) {
            if (App.UserController.isUnlockVillage() && this.mapId == MapEnumType.WORLD_MAP) {
                if ((App.UserController.mainLineTask.id == Number(CFG_Constant.getValue("unlock_safe_zone")) && App.UserController.mainLineTask.status == TaskStatus.Finished)||(App.UserController.mainLineTask.id > Number(CFG_Constant.getValue("unlock_safe_zone")))) {
                    spawnPointX = this.data.rebornPoint.x;
                    spawnPointY = this.data.rebornPoint.y;
                } else {
                    spawnPointX = this.data.spawnPoint.x;
                    spawnPointY = this.data.spawnPoint.y;
                }
            } else {
                spawnPointX = this.data.spawnPoint.x;
                spawnPointY = this.data.spawnPoint.y;
            }
        } else {
            spawnPointX = this.data.getTeleportPoint(this._crossFlag).x;
            spawnPointY = this.data.getTeleportPoint(this._crossFlag).y;
        }
        
        App.UserController.lastLeaveVillagePos = null;
        return new cc.Vec3(spawnPointX, spawnPointY);
        */
       // 14654
        const spawnPoint = this.data.spawnPoint.clone();
        return new cc.Vec3(spawnPoint.x, spawnPoint.y);
    }

    public playTeamTranferEndEffect(isScaleEff?: boolean): void {
        // super.playTeamTranferEndEffect(false);
    }

    /**
     * 复活点
     * @returns
     */
    override getRebornPoint(): cc.Vec3 {
        // let spawnPointX: number, spawnPointY: number;
        // if (App.UserController.isUnlockVillage() && this.mapId == MapEnumType.WORLD_MAP) {
        //     if ((App.UserController.mainLineTask.id == Number(CFG_Constant.getValue("unlock_safe_zone")) && App.UserController.mainLineTask.status == TaskStatus.Finished)||(App.UserController.mainLineTask.id > Number(CFG_Constant.getValue("unlock_safe_zone")))) {
        //         spawnPointX = this.data.rebornPoint.x;
        //         spawnPointY = this.data.rebornPoint.y;
        //     } else {
        //         spawnPointX = this.data.spawnPoint.x;
        //         spawnPointY = this.data.spawnPoint.y;
        //     }
        // } else {
        //     spawnPointX = this.data.spawnPoint.x;
        //     spawnPointY = this.data.spawnPoint.y;
        // }
        // return new cc.Vec3(spawnPointX, spawnPointY);
        return new cc.Vec3(this.data.spawnPoint.x, this.data.spawnPoint.y, 0);
    }

    /** 判断mainplayer 和全部followPlayers 已经死亡 */
    checkAllDie() {
        // if (this.mainPlayer && this.mainPlayer.getHp() <= 0) {
        //     for (let i = 0; i < this.allPlayers.length; i++) {
        //         const element = this.allPlayers[i];
        //         if (element.getHp() > 0) {
        //             return false;
        //         }
        //     }
        //     return true;
        // }
        return false;
    }

    override isUnlockVillage(): boolean {
        return true;
        // return App.UserController.isUnlockVillage();
    }

    /** 进入地图成功  */
    private onMapRemoveLoadHandler(): void {
        // if (App.OpenFuncManager.getDelayFunIdList.length > 0) {
        //     for (let i: number = 0; i < App.OpenFuncManager.getDelayFunIdList.length; i++) {
        //         App.OpenFuncManager.openFunction(App.OpenFuncManager.getDelayFunIdList[i]);
        //     }
        //     App.OpenFuncManager.clearDelayFunIdList();
        // }

        // if (App.UserController.firstRechargeCenter.isAutoShowView == true) {
        //     App.ViewManager.show(ViewId.FIRST_RECHARGE_VIEW);
        //     App.UserController.firstRechargeCenter.isAutoShowView = false;
        // }
    }

    override addUnitAfter(entity: IUnit, voEntity: VoEntity): void {
        super.addUnitAfter(entity, voEntity);

        if (voEntity.unitType == UnitType.Hero && this._crossFlag != -1) {
            (entity as Player)?.hideSkin();
            this.addHeroCount++;
            // if (this.addHeroCount == this.heroMgr.battleHeroNum) {
            //     Timer.once(1500, this,() => {
            //         this.playTeamTranferEndEffect();
            //     });
            // }
        }

    }

    override stop() {
        super.stop();
        EventManagerComponent.instance().removeEventListener(MapEvent.MAP_REMOVE_LOAD, this.onMapRemoveLoadHandler, this);
        Timer.clear(this, this.onFrameRequest);
    }
}

