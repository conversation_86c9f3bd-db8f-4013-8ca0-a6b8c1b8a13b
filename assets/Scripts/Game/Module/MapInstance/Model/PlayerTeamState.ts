import * as cc from "cc";
import PlayerActor from "../../../Unit/Actor/player/PlayerActor";
import { MapInstance } from "../Components/MapInstance";
import GuidePlayer from "../../../Unit/Actor/player/GuidePlayer";
import CameraController from "../../Camera/CameraManager";
import { MathUtils } from "../../../Utils/MathUtils";
import { ArraySort } from "../../../Utils/ArraySort";
import { AppConfig } from "db://assets/Scripts/Config/GameConfig";
import { Player_Init_data } from "../../../Unit/Actor/PlayerType";
import BattleConst from "db://assets/Scripts/Config/GameCfgExt/BattleConst";

export default class PlayerTeamState {
    private mapInst: MapInstance | null = null;
    private players: PlayerActor[] = [];
    private followIndex: number[] = [];
    private mainHeroIdx: number = 0;
    private lastJoyDir: cc.Vec2 | null = null;
    private joyMoveDir = cc.v2(0, 0);
    private guidePlayer = null as PlayerActor | null;

    private controlEnabled: boolean = true;

    private moveResult: {
        moveDir: cc.Vec2;
        isFinPoint: boolean;
        isFindCenter: boolean;
        isFan: boolean;
    } = {
        moveDir: cc.v2(0, 0),
        isFinPoint: false,
        isFindCenter: false,
        isFan: false
    }

    public init(map: MapInstance) {
        this.mapInst = map;
        this.newGuidePlayer();
    }

    public setPlayerList(players: PlayerActor[]) {
        this.players = players;
        this.mainHeroIdx = 0;
        this.followIndex.length = 0;
        for (let i = 0; i < players.length; i++) {
            this.followIndex.push(-1);
        }
    }

    public onJoyStartControl() {
        let players = this.players;
        players.forEach((player) => {
            if (!player.isDie()) {
                player.openCollision();
            }
            player.startControl();
        });
        this.guidePlayer.startControl();
        this.controlEnabled = true;
    }

    public onJoyMoved(dir: cc.Vec2) {
        this.joyMoveDir = dir.clone();

    }

    public onJoyEndControl() {
        this.controlEnabled = false;
        this.joyMoveDir.set(0, 0);
        this.lastJoyDir = null;

        this.guidePlayer && this.guidePlayer.setFollow(null);
        // // 确定驻守的中心点
        let guardCenterPos = cc.Vec3.ZERO;
        if (this.guidePlayer) {
            guardCenterPos = this.guidePlayer.getPosition(guardCenterPos)
        }
        else {
            guardCenterPos = CameraController.instance.getCenterPosition();
        }


        // this.guardCenterPos = guardCenterPos;
        // this.showRange(this.guardCenterPos);

        // //这是跟随位置.
        // let index = 0;
        let return_distance = Number(BattleConst.getValue("return_distance"));
        this.players.forEach(player => {
            player.stopControl();

            player.isFollow = false;
            if (!player.hasEnemy()) {
                let position: cc.Vec3 = cc.Vec3.ZERO;
                position = player.getPosition(position);
                if (MathUtils.getDistance(new cc.Vec2(guardCenterPos.x, guardCenterPos.y), new cc.Vec2(position.x, position.y)) > return_distance) {
                    player.isFollow = true;
                    player.setFollow(guardCenterPos);
                }
                else {
                    player.stopMove();
                    player.setFollow(null);
                    player.userMoveStop();
                }
            }
            else {
                player.stopMove();
                player.setFollow(null);
            }

        });

        this.guidePlayer.stopControl();

    }

    public update(dt: number): void {
        if (!this.controlEnabled) {
            return;
        }
        this.updateCameraPosition();
        this.updateMainHeroPosition();
    }

    private get mainHero(): PlayerActor {
        return this.players[this.mainHeroIdx];
    }

    private calculateMoveDir(joyDir: cc.Vec2) {
        const nJoyDir = joyDir.normalize();

        this.moveResult.isFan = false;
        this.moveResult.isFinPoint = false;
        this.moveResult.isFindCenter = false;
        this.moveResult.moveDir.set(0, 0);

        if (!this.lastJoyDir) {
            this.lastJoyDir.set(nJoyDir.x, nJoyDir.y);
            this.moveResult.moveDir = nJoyDir.clone();
            this.moveResult.isFinPoint = true;
        }
        else {
            let angle = joyDir.signAngle(this.lastJoyDir) * 180 / Math.PI;
            if (Math.abs(angle) > 4) {
                this.moveResult.isFindCenter = true;
                this.moveResult.moveDir.set(joyDir.x, joyDir.y);
            }
            else {
                let d = cc.v2(1, 0);
                let angle1 = joyDir.signAngle(d) * 180 / Math.PI;
                if (angle1 >= 0 && angle1 <= 180)
                    this.moveResult.moveDir.set(joyDir.x, joyDir.y);
                else {
                    this.moveResult.isFan = true;
                    this.moveResult.moveDir.set(-joyDir.x, -joyDir.y);
                }

                this.moveResult.isFinPoint = true;
            }
        }
        this.lastJoyDir.x = joyDir.x;
        this.lastJoyDir.y = joyDir.y;
        cc.Vec2.normalize(this.moveResult.moveDir, this.moveResult.moveDir);
    }

    private newGuidePlayer() {
        let node = new cc.Node();
        this.mapInst.unitLayer.addChild(node);
        this.guidePlayer = node.addComponent(GuidePlayer);
        let centerPoint = CameraController.instance.getCenterPosition();
        this.guidePlayer.node.x = centerPoint.x;
        this.guidePlayer.node.y = centerPoint.y;
    }

    private updateMainHeroPosition() {
        let centerPoint = CameraController.instance.getCenterPosition();
        if (!this.guidePlayer.getFollowPoint()) {
            for (let j = 0; j < this.players.length; j++) {
                let player = this.players[j];
                if (!player.isValidUnit() || player.isDie()) {
                    continue;
                }
                player.isFollow = true;

                player.setFollow(centerPoint);
            }
            return;
        }
        this.calculateMoveDir(this.joyMoveDir);
        let position: cc.Vec3;
        position = centerPoint.add(cc.v3(this.lastJoyDir.x, this.lastJoyDir.y));
        let dx = position.x - centerPoint.x;
        let dy = position.y - centerPoint.y;
        if (dy == 0 && dx == 0)
            return;

        const moveDir = this.moveResult.moveDir;

        let newCenterPoint = centerPoint.add(new cc.Vec3(moveDir.x, moveDir.y).multiplyScalar(30));
        if (!this.mapInst.isPassablePoint(centerPoint, newCenterPoint)) {
            // if (moveAgle < 0) moveAgle = moveAgle + 360;
            // let angle = moveAgle % 90;
            // let d = 140 - angle;
            // let rad = cc.misc.degreesToRadians(d);
            // this.moveDir = this.moveDir.rotateSelf(rad).normalize();
            return;
        }

        if (this.moveResult.isFinPoint) {
            for (let j = 0; j < this.players.length; j++) {
                this.followIndex[j] = -1;
            }
        }

        let temp: cc.Vec3 = this.guidePlayer.getFollowPoint()
        if (this.moveResult.isFindCenter) {
            // this.clearRoad();
            cc.Vec2.multiplyScalar(this.moveResult.moveDir, this.moveResult.moveDir, 4);
            for (let j = 0; j < this.players.length; j++) {
                let player = this.players[j];
                if (!player.isValidUnit() || player.isDie()) {
                    continue;
                }
                player.isFollow = true;
                let playerPos = cc.v3();
                playerPos = player.getPosition(playerPos);
                let playerDir = temp.subtract(playerPos).normalize();
                let resultDir = cc.v3(0, 0, 0);
                resultDir = cc.Vec3.add(resultDir, playerDir, cc.v3(this.moveResult.moveDir.x, this.moveResult.moveDir.y));
                let newPoint = playerPos.add(resultDir.multiplyScalar(30));
                player.setFollow(cc.v3(newPoint.x, newPoint.y));
            }
        } else {
            let tempDir = new cc.Vec3(this.moveResult.moveDir.x, this.moveResult.moveDir.y);
            let pList = this.getHeroDestPoints(tempDir, new cc.Vec3(temp.x, temp.y));
            // if (AppConfig.isDebug)
                // this.drawRoad(pList);
            for (var i = 0; i < pList.length; i++) {
                let playerIndex = this.getNearestPlayer(pList[i].toVec2());
                this.followIndex[playerIndex] = i;
            }
            if (this.moveResult.isFan) {
                this.moveResult.moveDir.x = -this.moveResult.moveDir.x;
                this.moveResult.moveDir.y = -this.moveResult.moveDir.y;
            }

            for (let j = 0; j < this.players.length; j++) {
                let player = this.players[j];
                if (!player.isValidUnit() || player.isDie()) {
                    continue;
                }
                if (player.followIndex == -1) {
                    continue;
                }
                player.isFollow = true;
                let temp = pList[player.followIndex];
                let playerPos = cc.v3();
                playerPos = player.getPosition(playerPos);
                let dis = MathUtils.getDistance(cc.v2(newCenterPoint.x, newCenterPoint.y), new cc.Vec2(playerPos.x, playerPos.y))
                if (dis <= Player_Init_data.TeamRange) {
                    let playerDir = temp.subtract(playerPos).normalize();
                    let resultDir = cc.v3(0, 0, 0);
                    resultDir = cc.Vec3.add(resultDir, playerDir, new cc.Vec3(moveDir.x, moveDir.y)).normalize();
                    let newPoint = playerPos.add(resultDir.multiplyScalar(30));
                    player.setFollow(newPoint);
                }
                else {
                    player.setFollow(temp);
                }
            }
        }
    }

    private updateCameraPosition() {
        let mainPlayer = this.mainHero;
        if (mainPlayer != null && mainPlayer.isDie() == false) {
            if (this.guidePlayer) {

                if (this.guidePlayer.sleep) {
                    return;
                }
                let centerPoint = cc.v3(0, 0);
                centerPoint = this.guidePlayer.getPosition(centerPoint)//CameraController.instance.getCenterPosition();
                let position: cc.Vec3;
                let dir = this.joyMoveDir.clone().normalize() as unknown as cc.Vec2;
                position = centerPoint.add(cc.v3(dir.x, dir.y).multiplyScalar(40));
                let dx = position.x - centerPoint.x;
                let dy = position.y - centerPoint.y;
                let moveAgle = MathUtils.radiansToDegrees(Math.atan2(dy, dx));
                // this.showPointer(position, moveAgle)
                if (this.mapInst.isPassablePoint(centerPoint, position)) {
                    this.guidePlayer.setFollow(position)
                }
                else {
                    // let dx = position.x - centerPoint.x;
                    // let dy = position.y - centerPoint.y;
                    // let moveAgle = MathUtils.radiansToDegrees(Math.atan2(dy, dx));
                    // if (moveAgle < 0) moveAgle = moveAgle + 360;
                    // let angle = moveAgle % 180;
                    // let d = 140 - angle;
                    // let rad = cc.misc.degreesToRadians(d);
                    // dir = dir.rotateSelf(rad).normalize();
                    // position = centerPoint.add(cc.v3(dir.x, dir.y).multiplyScalar(25));
                    // if (MapHelper.isPassablePoint(centerPoint, position)) {
                    //     this.guidePlayer.setFollow(position)
                    // }
                    // else {
                    //     this.guidePlayer.setFollow(null)
                    // }
                    this.guidePlayer.setFollow(null)
                }
            }
        }
    }

    private getNearestPlayer(centerPoint: cc.Vec2): number | null {
        let dist = Number.MAX_VALUE;
        let idx = -1;
        for (let i = 0; i < this.players.length; i++) {
            let player = this.players[i];
            if (player.isDie()) {
                continue;
            }
            if (this.followIndex[i] !== -1) {
                continue;
            }

            const p = player.getPosition().toVec2();
            const newDist = cc.Vec2.distance(centerPoint, p);
            if (newDist < dist) {
                dist = newDist;
                idx = i;
            }
        }
        if (idx !== -1) {
            return idx;
        }
        return null;
    }

    /**
     * Generates destination points for heroes in a formation pattern around a center point.
     * 
     * Creates two parallel lines of points (left and right) relative to the movement direction:
     * - Left line: 5 points positioned to the left of the center, offset by 90 degrees
     * - Right line: 5 points positioned to the right of the center, offset by -90 degrees
     * - Points are spaced at 40 unit intervals along the movement direction
     * - Lines are separated by 30 units perpendicular to the movement direction
     * 
     * @param dir - The movement direction vector (will be normalized)
     * @param centerPoint - The center position to build the formation around
     * @returns Array of 10 Vec3 positions in a specific order for hero placement:
     *          [left2, right2, left1, right1, left3, right3, left0, right0, left4, right4]
     *          where numbers indicate distance from center (0=closest, 4=farthest)
     */
    private getHeroDestPoints(dir: cc.Vec3, centerPoint: cc.Vec3): cc.Vec3[] {
        let setp = 40;
        let dd = 30;
        let pList: cc.Vec3[] = [];
        let dir0 = dir.clone().normalize();

        //左向量
        let tempDir = dir0.clone();
        let rad = cc.misc.degreesToRadians(90);
        let dir1 = dir0.toVec2().rotate(rad);
        let tempDir1 = dir1.multiplyScalar(dd)
        let start1 = centerPoint.add(new cc.Vec3(tempDir1.x, tempDir1.y));
        let temp = dir0.clone();
        start1 = start1.add(temp.multiplyScalar(-1.5 * setp));
        pList.push(start1.clone())
        for (var i = 1; i < 5; i++) {
            const p = start1.add(dir0.multiplyScalar(setp * i));
            pList.push(p)
        }
        //右向量
        tempDir = dir0.clone();
        rad = cc.misc.degreesToRadians(-90);

        let dir2 = new cc.Vec2(tempDir.x, tempDir.y).rotate(rad)
        tempDir1 = dir2.multiplyScalar(dd)
        let start2 = centerPoint.add(new cc.Vec3(tempDir1.x, tempDir1.y));
        temp = dir0.clone();
        start2 = start2.add(temp.multiplyScalar(-1 * setp))
        pList.push(start2.clone())
        for (var i = 1; i < 5; i++) {
            let p = start2.clone();
            temp = dir0.clone();
            p = p.add(temp.multiplyScalar(setp * i));
            pList.push(p)
        }

        let result: cc.Vec3[] = [pList[2], pList[7], pList[1], pList[6], pList[3], pList[8], pList[0], pList[5], pList[4], pList[9]];

        return result;
    }
}
