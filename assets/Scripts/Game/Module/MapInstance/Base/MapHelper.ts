import * as cc from "cc";

import { CHASE_WALK_RATE, UnitType } from "../../../../Config/GameDefine";
import Logger from "../../../../Framework/Logger";
import MapRoadUtils from "../../Map/road/MapRoadUtils";
import PathFindingAgent from "../../Map/road/PathFindingAgent";
import Point from "../../Map/road/Point";
// import { Player_collect } from "../../game/unit/actor/PlayerType";
import Player from "../../../Unit/Actor/player/PlayerActor";
import { IUnit } from "../../../Unit/Base/IUnit";
import RandomUtils from "../../../Utils/RandomUtils";
import MapManager from "../Managers/MapManager";

/**
 * 场景上公共方法.
 * 
 */
export class MapHelper {

    /**
     * 随机范围内的值.
     * @param num1 
     * @param num2 
     * @returns 
     */
    static Range(num1: number, num2: number) {
        if (num2 > num1) {
            return Math.random() * (num2 - num1) + num1;
        }
        return Math.random() * (num1 - num2) + num2;
    }

    /**
     * 设置最优坐标列表
     * @param players 
     * @param pos 
     */
    static getOptimalPosition(pos: cc.Vec3): Map<Player, cc.Vec3> {
        let ret: Map<Player, cc.Vec3> = new Map<Player, cc.Vec3>;
        // let players = MapManager.curMap.allPlayers;
        // let array: Array<cc.Vec3> = this.getHeroPositionList(cc.v3(pos), players.length);
        // Logger.trace("getOptimalPosition" + array);
        // for (let i = 0; i < players.length; i++) {
        //     let player: Player = players[i];
        //     let minIndex: number = 0;
        //     let minDistance: number = Number.MAX_VALUE;
        //     for (let n = 0; n < array.length; n++) {
        //         let distance = cc.Vec3.distance(player.node.position, array[n]);
        //         if (distance < minDistance) {
        //             minDistance = distance;
        //             minIndex = n;
        //         }
        //     }
        //     ret.set(player, array[minIndex]);
        //     array.splice(minIndex, 1);
        // }
        return ret;
    }

    /** 
     * src:源坐标
     * result：保存结果列表
     * radius： ?? [2,10] * 120
     * interval:?? 120
      */
    private static searchCirclePoint(src: cc.Vec3, result: cc.Vec3[], radius: number, interval: number) {
        let radian: number = Math.atan(interval / radius);
        let angle: number = (180 / Math.PI) * radian * 2;
        let num: number = Math.floor(360 / angle);
        const theta = (2 * Math.PI) / num;

        let x: number = 0;
        let y: number = radius;

        let tiledMap = MapManager.curMap.data;
        for (let i = 0; i < num; i++) {
            let x1 = x * Math.cos(theta) - y * Math.sin(theta);
            let y1 = x * Math.sin(theta) + y * Math.cos(theta);
            x = x1;
            y = y1;

            x1 = src.x + x;
            y1 = src.y + (y / 4) * 2.4 - 30;
            const point = MapRoadUtils.instance.getWorldPointByPixel(x1, y1);
            if (tiledMap.isPassableByWorldPoint(point.x, point.y)) {
                result.push(cc.v3(x1, y1));
            }
        }
    }

    /**
     * 获取英雄可站的坐标.
     * @param src 
     * @param players 
     */
    static getHeroPoints(src: cc.Vec3, players: Player[], num: number): cc.Vec3[] {
        if (players.length == 0 && num == 1) {
            return [src];
        }

        const radiusStep = 120;
        let result: cc.Vec3[] = [src];
        //判断坐标是否被其他英雄使用了
        let isUse = (point: cc.Vec3) => {
            let content = players.filter(player => {
                return cc.Vec3.distance(player.node.position, point) < radiusStep;
            })
            return content && content.length > 0;
        }

        //便利查找可用的坐标.  
        for (let radiusCount = 1; radiusCount <= 10; radiusCount++) {
            this.searchCirclePoint(src, result, radiusCount * radiusStep, radiusStep);

            for (let i = result.length - 1; i >= 0; i--) {
                let point = result[i];
                if (isUse(point)) {
                    result.splice(i, 1);
                }
            }

            if (result.length >= num) {
                break;
            }
        }

        RandomUtils.shuffleArray(result);
        return result;
    }

    /**
     * 获取英雄坐标列表.
     * @param src 起点
     * @param num 需求数量
     * @returns 
     */
    static getHeroPositionList(src: cc.Vec3, num: number): cc.Vec3[] {
        if (num == 1) {
            return [src];
        }
        const radiusStep = 120;
        let result: cc.Vec3[] = [];

        for (let radiusCount = 2; radiusCount <= 10; radiusCount++) {//遍历8次
            this.searchCirclePoint(src, result, radiusCount * radiusStep, radiusStep);
            if (result.length + 1 >= num) {
                break;
            }
        }
        RandomUtils.shuffleArray(result);
        result.unshift(src);
        return result;
    }

    /**英雄到目标坐标是否可通行. */
    static isPassable(unit: IUnit, end: cc.Vec3): boolean {
        //度量长度.
        const checkDistance: number = 5;
        let dx: number = end.x - unit.position.x;
        let dy: number = end.y - unit.position.y;
        let moveAngle: number = Math.atan2(dy, dx);
        var xspeed: number = Math.cos(moveAngle) * checkDistance;
        var yspeed: number = Math.sin(moveAngle) * checkDistance;

        let start: cc.Vec3 = new cc.Vec3();
        start = unit.getPosition(start);
        let totalDistance: number = start.subtract(end).length();

        let tiledMap = MapManager.curMap.data;
        //目标未知肯定不能站人.
        let n: number = Math.ceil(totalDistance / checkDistance) - 1;
        for (let i = 0; i < n; i++) {
            let nx: number = start.x + xspeed * i;
            let ny: number = start.y + yspeed * i;
            const point = MapRoadUtils.instance.getWorldPointByPixel(nx, ny);
            if (!tiledMap.isPassableByWorldPoint(point.x, point.y)) {
                return false;
            }
        }
        return true;
    }

    static isPassablePoint(begin: cc.Vec3, end: cc.Vec3) {
        //度量长度.
        if(!begin || !end)
        {
            return false;
        }
        const checkDistance: number = 5;
        let dx: number = end.x - begin.x;
        let dy: number = end.y - begin.y;
        let moveAngle: number = Math.atan2(dy, dx);
        var xspeed: number = Math.cos(moveAngle) * checkDistance;
        var yspeed: number = Math.sin(moveAngle) * checkDistance;


        let totalDistance: number = begin.subtract(end).length();

        let tiledMap = MapManager.curMap.data;
        //目标未知肯定不能站人.
        let n: number = Math.ceil(totalDistance / checkDistance) - 1;
        for (let i = 0; i < n; i++) {
            let nx: number = begin.x + xspeed * i;
            let ny: number = begin.y + yspeed * i;
            const point = MapRoadUtils.instance.getWorldPointByPixel(nx, ny);
            if (!tiledMap.isPassableByWorldPoint(point.x, point.y)) {
                return false;
            }
        }
        return true;
    }

    //遇到障碍物后找最远的可走区域
    static getStopPoint(begin: cc.Vec3, end: cc.Vec3) {
        //度量长度.
        const checkDistance: number = 5;
        let dx: number = end.x - begin.x;
        let dy: number = end.y - begin.y;
        let moveAngle: number = Math.atan2(dy, dx);
        var xspeed: number = Math.cos(moveAngle) * checkDistance;
        var yspeed: number = Math.sin(moveAngle) * checkDistance;


        let totalDistance: number = begin.subtract(end).length();

        let tiledMap = MapManager.curMap.data;
        //目标未知肯定不能站人.
        let n: number = Math.ceil(totalDistance / checkDistance) - 1;
        for (let i = 0; i < n; i++) {
            let nx: number = begin.x + xspeed * i;
            let ny: number = begin.y + yspeed * i;
            const point = MapRoadUtils.instance.getWorldPointByPixel(nx, ny);
            if (!tiledMap.isPassableByWorldPoint(point.x, point.y)) {
                if (i > 0) {
                    let nx: number = begin.x + xspeed * (i - 1);
                    let ny: number = begin.y + yspeed * (i - 1);
                    const point1 = MapRoadUtils.instance.getWorldPointByPixel(nx, ny);
                    if (tiledMap.isPassableByWorldPoint(point.x, point.y))
                        return point1;
                }
            }
        }
        return null;
    }
    /** 获取到目标需要的攻击距离 */
    static getNearestDistance(self: IUnit, target: IUnit): number {
        let distance: number = 0;
        // if (target.data.unitType == UnitType.Tree) {
        //     distance = Player_collect.AttackRange;
        // } else {
        //     distance = self.skillLearned.getNormalAtkDistance();
        // }
        return distance * CHASE_WALK_RATE;
    }

    /**
     * 获取距离某个点一定距离的可通行点
     * srcPoint:起始点
     * radius:半径
     * minRadius:最小半径(随机出来的点不能小于这个范围)
     */
    //
    static getRandomPassAblePoint(srcPoint: cc.Vec3, radius: number, minRadius: number): cc.Vec3 {
        let point: Point = MapRoadUtils.instance.getWorldPointByPixel(srcPoint.x, srcPoint.y);
        let srcPxPoint: Point = new Point(point.x * 120, point.y * 120);

        let _minX: number = srcPxPoint.x - radius;
        let _maxX: number = srcPxPoint.x + radius;
        let _minY: number = srcPxPoint.y - radius;
        let _maxY: number = srcPxPoint.y + radius;

        let _minMinX: number = srcPxPoint.x - minRadius;
        let _minMaxX: number = srcPxPoint.x + minRadius;
        let _minMinY: number = srcPxPoint.y - minRadius;
        let _minMaxY: number = srcPxPoint.y + minRadius;

        let _pointMinX: number = Math.floor(_minX / 120);
        let _pointMaxX: number = Math.floor(_maxX / 120);
        let _pointMinY: number = Math.floor(_minY / 120);
        let _pointMaxY: number = Math.floor(_maxY / 120);

        let _minPointMinX: number = Math.floor(_minMinX / 120);
        let _minPointMaxX: number = Math.floor(_minMaxX / 120);
        let _minPointMinY: number = Math.floor(_minMinY / 120);
        let _minPointMaxY: number = Math.floor(_minMaxY / 120);

        let tiledMap = MapManager.curMap.data;
        let result: Point[] = []
        for (var i: number = _pointMinX; i < _pointMaxX; i++) {
            for (var j: number = _pointMinY; j < _pointMaxY; j++) {
                if ((i < _minPointMinX || i > _minPointMaxX) || (j < _minPointMinY || j > _minPointMaxY)) {
                    let worldPoint: Point = MapRoadUtils.instance.getPixelByWorldPoint(i, j); //游戏里面的坐标
                    let tempPos: cc.Vec3 = new cc.Vec3(worldPoint.x, worldPoint.y);
                    if (tiledMap.isPassableByWorldPoint(i, j) && cc.Vec3.distance(tempPos, srcPoint) <= radius) {
                        result.push(new Point(i, j));
                    }
                }
            }
        }
        RandomUtils.shuffleArray(result);
        for (var i: number = 0; i < result.length; i++) {
            let randomPoint = result[i];
            let worldPoint: Point = MapRoadUtils.instance.getPixelByWorldPoint(randomPoint.x, randomPoint.y); //游戏里面的坐标
            // let pathNodeList = PathFindingAgent.instance.seekPath(MapManager.curMap.mainPlayer.node.x, MapManager.curMap.mainPlayer.node.y, worldPoint.x, worldPoint.y, 100);
            // if (pathNodeList.length > 0) {
            //     return new cc.Vec3(worldPoint.x, worldPoint.y, 0);
            // }
        }
        return cc.Vec3.ZERO;
    }

}