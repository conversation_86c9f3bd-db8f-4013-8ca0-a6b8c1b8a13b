import * as cc from "cc";
import App from "../../../Managers/AppManager";
import { BattleAttr, UnitType } from "../../../../Config/GameDefine";
import { GameEvent, MapEvent } from "../../../../GameEvent/Events";
import { ViewId } from "../../../../Config/ViewConst";
import {
    CFG_Monster,
    CFG_SceneFog,
    CFG_SceneNpc,
    CFG_SceneZone,
    CFG_SkillLevel
} from "../../../../Config/GameCfg/CFGManager";

// import { CFG_SkillLevel } from "../../config/game_config/SkillLevelCache";
import Timer from "../../../Utils/Timer";
import Logger from "../../../../Framework/Logger";
import CameraController from "../../Camera/CameraManager";
// import BattleController from "../../Game/Control/BattleController";
import GameController from "../../../Control/GameController";
import Joystick from "../../../Control/Joystick";
// import TaskInfo, { TaskStatus } from "../../game/data/TaskInfo";
// import { BattleType } from "../../game/modules/challenge/model/BattleType";
// import { FightContext } from "../../game/modules/fight/FightContext";
import { GameObject } from "../../Fight/GameObject";
// import BulletManager from "../../Module/Fight/bullet/BulletManager";
// import { EntityManager } from "../../Module/Fight/manager/EntityManager";
// import { HeroManager } from "../../Module/Fight/manager/HeroManager";
// import SkillManager from "../../Module/Fight/skill/SkillManager";
import MapRoadUtils from "../../Map/road/MapRoadUtils";
import ObstacleEdgeUtils from "../../Map/road/ObstacleEdgeUtils";
import PathFindingAgent from "../../Map/road/PathFindingAgent";
import PathLog from "../../Map/road/PathLog";
import Point from "../../Map/road/Point";
import { IFogArea } from "../../Map/tiled/TileMapData";
import { pb } from "../../../../Proto/pb.js"
import { UnitState } from "../../../Unit/UnitState";
import { VoCharacter } from "../../../Unit/VoCharacter";
import { VoEntity } from "../../../Unit/VoEntity";
// import { VoHero } from "../../Unit/VoHero";
// import { VoMonster } from "../../Unit/VoMonster";
// import { VoTree } from "../../Unit/VoTree";
import { VoUnit } from "../../../Unit/VoUnit";
// import { MonsterType, Player_Init_data, TreeType } from "../../game/unit/actor/PlayerType";
// import Monster from "../../game/unit/actor/monster/Monster";
// import Player, { QiPaoType } from "../../game/unit/actor/player/Player";
// import Tree from "../../game/unit/actor/tree/Tree";
import { IUnit } from "../../../Unit/Base/IUnit";
import Unit from "../../../Unit/Base/Unit";
// import NpcManager from "../npc/NpcManager";
// import FogNpc from "../../Unit/npc/FogNpc";
// import TaskNpc from "../../Unit/npc/TaskNpc";
// import Npc from "../../Unit/npc/base/Npc";
// import { NpcHomeEnum } from "../../game/unit/npc/base/NpcDefine";
// import NpcShell from "../../game/unit/npc/base/NpcShell";
import { MathUtils } from "../../../Utils/MathUtils";
// import QuadtreeManager from "../quadtree/QuadtreeManager";
import { SoundId, SoundType } from "../../../GlobalComponent/SoundManager";
// import FogNodePool from "./FogNodePool";
import { MapBase } from "./MapBase";
import { MapAreaType, MapEnumType } from "./MapDefine";
import { MapHelper } from "./MapHelper";
import MapManager from "../Managers/MapManager";
import { MapUnitFactory } from "./MapUnitFactory";
import PlayerActor from "../../../Unit/Actor/player/PlayerActor";
import { AppConfig } from "../../../../Config/GameConfig";
import EventManagerComponent from "../../../GlobalComponent/EventManagerComponent";
import AppManager from "../../../Managers/AppManager";
import SceneZoneCache from "../../../../Config/GameCfgExt/SceneZone";
import { HeroManager } from "../../Fight/Managers/HeroManager";

/**
 * 有建筑，玩家可移动的地图
 */
export class MapUnitBase extends MapBase {


    /**
     * 场景里所有的怪物[当前区域显示的]
     */
    // public monsterList: Monster[] = [];

    /**
     * 场景剩余怪物数量
     */
    public lastMonCount: number = 0;

    /**重生列表 */
    rebornList: VoUnit[] = [];
    /**记录死亡的树桩，等复活时从这个列表删除，将树桩一同从场景上删除*/
    rebornTreeMap: Map<number, Unit> = new Map();

    // /**
    //  * 场景里所有的树木，矿，资源
    //  */
    // public treeList: Tree[] = [];

    /** 战斗类型 */ 
    // public battleType: number = BattleType.MainLine;

    /** 原来的坐标 */
    lastPoint: cc.Vec3;

    /**是否在安全区 */
    public isInSafeArea: boolean = false;

    /** 是否激活 */
    protected isAwake: boolean = false;

    /** 屏幕摄像机偏移高度. */
    protected offsetHeight: number = 0;

    /**怪物死亡延迟消失时间 */
    protected monsterDieDelayTime = 800;

    /**树死亡延迟消失时间 */
    protected treeDieDelayTime = 800;

    /** 死亡停滞的时间. 技能释放需要此目标,不能立即移除 */
    protected deathRetentionTime: number = 10000;

    protected heroMgr: HeroManager = HeroManager.instance;

    private mMonsterCount: number = 0;

    private mTreeCount: number = 0;

    /** 死亡单位列表. */
    protected deathMap: Map<IUnit, number> = new Map();

    /** 区域是否定义  */
    public areaDefine: boolean = false;

    private notifyServerDieUnitIdList = [];

    private bossChallengeMap: Map<number, boolean> = new Map();

    /**
     * 主角色(操控)
     */
    public get mainPlayer(): PlayerActor {
        return this.heroMgr.mainPlayer;
    }

    public get allPlayers(): PlayerActor[] {
        return this.heroMgr.allPlayers;
    }

    public get players(): PlayerActor[] {
        return this.heroMgr.allPlayers;
    }

    constructor() {
        super();
        // this.context = new FightContext(this);
    }

    protected initMap() {
        this.clearMapData();

        this.areaDefine = false;
        PathLog.setLogEnable(false); //关闭寻路日志打印信息
        this.initPathFinding();

        if (this.isAwake) this.awake();

        this.addEvent();


        // let heroInfo = App.UserController.heroInfo;
        const mainHero: number = 1040001;
        const heros: pb.IPbSprite[] = [
            {
                id: 1,
                type: pb.PbSpriteType.HERO,
                identity: 1040001,
                level: 1,
                rebornTime: null,
                ctype: 1,
                cx: 0,
                cy: 0,
                battleAttr: null,
                configId: 1040001,
                fogArea: null
            }
        ]
        this.reloadAllHero(mainHero, heros, this.lastPoint);
        this.initCamera();

        this.requestZoneInfo(this.zoneId);
        this.requestNPCData();
        Timer.loop(1000, this, this.checkInRegion);

        CameraController.instance.debugDumpCameraInfo();
        // let curtask: TaskInfo = App.UserController.getMainLineTask();
        // if (this.mapId == MapEnumType.NEW_WORLD_MAP && curtask.id == 10001 && curtask.status == TaskStatus.Unfinished) {
        //     App.ViewManager.show(ViewId.GAME_OPEN, null, false, App.LayerManager.getNetLayer());
        // }
    }

    /** 初始化路点  */
    protected initPathFinding() {
        PathFindingAgent.instance.init(this.data.width, this.data.height, this.data.mapWidth, this.data.mapHeight, this.data.tileWidth, this.data.tileHeight, this.calcPassableArea(), this.initPathCallBack.bind(this), this.mapId); //初始化寻路系统
        // QuadtreeManager.instance.froceUpdate(App.UserController.isUnlockVillage());
    }

    /** 画完路点后回调  */
    private initPathCallBack(): void {
        this.drawObstacleEdge();
        this.putFogToQuadtree();
    }

    //画不可达域边缘
    private drawObstacleEdge(): void {
        if (AppConfig.isDebug) {
            var graphic = this.roadLayer.addComponent(cc.Graphics);
            ObstacleEdgeUtils.instance.showObstacleEdge(graphic);
        }
    }

    /** 放云层到四叉树  */
    private putFogToQuadtree(): void {
        if (!this.data) return;
        let _fogArea: Map<number, IFogArea> = this.data.getAllFogArea();
        const winSize = cc.view.getDesignResolutionSize();
        let _offsetY: number = winSize.height;
        _fogArea.forEach((singleArea, id) => {
            let _rectData: number[] = this.data.getFogRectArea(id);
            let _layerNode: cc.Node = MapManager.curMap.fogLayer.getChildByName("fogArea_" + id);
            if (_rectData != null && !_layerNode) {
                let _minX: number = _rectData[0];
                let _maxX: number = _rectData[1];
                let _minY: number = _rectData[2];
                let _maxY: number = _rectData[3];
                let _x: number = _rectData[4];
                let _y: number = _rectData[5];
                let _point: Point = MapRoadUtils.instance.getPixelByWorldPoint(_x, _y);
                let _point1: Point = MapRoadUtils.instance.getPixelByWorldPoint(_minX, _minY);
                let _point2: Point = MapRoadUtils.instance.getPixelByWorldPoint(_minX, _maxY);

                let _point3: Point = MapRoadUtils.instance.getPixelByWorldPoint(_maxX, _minY);
                let _point4: Point = MapRoadUtils.instance.getPixelByWorldPoint(_maxX, _maxY);
                let _newWidth: number = _point3.x - _point2.x + 1000000;
                let _newHeight: number = _point1.y - _point4.y;

                let fogAreaNode: cc.Node = new cc.Node("fogArea_" + id);
                fogAreaNode.active = false;
                fogAreaNode.parent = this.fogLayer;
                fogAreaNode.addComponent(cc.UITransform).setContentSize(this.data.width, this.data.height);
                // setNodeZIndex(fogAreaNode, 200 -id);
                fogAreaNode.x = -this.data.width / 2;
                fogAreaNode.y = -this.data.height / 2;
                // QuadtreeManager.instance.addFogLayer(id, _point.x - _offsetY, _point.y, _newWidth, _newHeight, fogAreaNode);
            }
        });
    }

    protected addEvent() {
        EventManagerComponent.instance().addEventListener(GameEvent.MapData_Update_Zone, this.onUpdateZoneData, this);
        EventManagerComponent.instance().addEventListener(GameEvent.HERO_REBORN_SAFE, this.onHeroRebornSafe, this);
        EventManagerComponent.instance().addEventListener(GameEvent.HERO_REBORN_SITU, this.onHeroRebornSitu, this);
        // EventManagerComponent.instance().addEventListener(GameEvent.UNIT_DIE, this.onUnitDie, this);
        EventManagerComponent.instance().addEventListener(MapEvent.MAP_ACTIVE, this.onActiveMap, this);
        EventManagerComponent.instance().addEventListener(GameEvent.BOSS_BE_CHALLENGE, this.bossBeChallenge, this);
        EventManagerComponent.instance().addEventListener(GameEvent.CREATE_TREASURE_BOX, this.createTreasureBox, this);
        EventManagerComponent.instance().addEventListener(GameEvent.OPEN_GAME_END_CREATROLE, this.onAllHeroShow, this);
        EventManagerComponent.instance().addEventListener(GameEvent.BOSS_BE_RANGSKILL, this.bossBeRangSkill, this);
    }

    protected removeEvent() {
        EventManagerComponent.instance().removeEventListener(GameEvent.MapData_Update_Zone, this.onUpdateZoneData, this);
        EventManagerComponent.instance().removeEventListener(GameEvent.HERO_REBORN_SAFE, this.onHeroRebornSafe, this);
        EventManagerComponent.instance().removeEventListener(GameEvent.HERO_REBORN_SITU, this.onHeroRebornSitu, this);
        // EventManagerComponent.instance().removeEventListener(GameEvent.UNIT_DIE, this.onUnitDie, this);
        EventManagerComponent.instance().removeEventListener(MapEvent.MAP_ACTIVE, this.onActiveMap, this);
        EventManagerComponent.instance().removeEventListener(GameEvent.BOSS_BE_CHALLENGE, this.bossBeChallenge, this);
        EventManagerComponent.instance().removeEventListener(GameEvent.CREATE_TREASURE_BOX, this.createTreasureBox, this);
        EventManagerComponent.instance().removeEventListener(GameEvent.OPEN_GAME_END_CREATROLE, this.onAllHeroShow, this);
        EventManagerComponent.instance().removeEventListener(GameEvent.BOSS_BE_RANGSKILL, this.bossBeRangSkill, this);
    }

    private bossBeRangSkill(): void {
        // this.mainPlayer.showBossSkillAttDialog("危险！快走开", 3000, 500)
    }

    private bossBeChallenge(bossId: number) {
        if (this.bossChallengeMap[bossId]) return;
        let monstercfg = CFG_Monster.get(bossId);
        // if (monstercfg && monstercfg.type == MonsterType.BOSS) {
        //     let req = pb.MainlineChallengeBossRequest.fromObject({});
        //     req.bossId = bossId;
        //     App.Socket.send(pb.MainlineChallengeBossRequest.Proto.ID, pb.MainlineChallengeBossRequest.encode(req).finish());
        //     this.bossChallengeMap[bossId] = true;
        // }
    }

    /** loading显示完. 正式激活地图 */
    protected onActiveMap() {
        this.awake();
    }

    /** 重新连接成功 */
    reconnect() {
    }

    public stop(): void {
        super.stop();
        this.sleep();
        this.removeEvent();
        this.clearMapData();
        PathFindingAgent.instance.clear(this.mapId);

        //清理地图资源顺序. 先删除引用然后卸载,顺序不能乱
        // QuadtreeManager.instance.removeAllData();
        this.clearTiledMap();
        Timer.clear(this, this.checkInRegion);
    }

    protected requestNPCData(): void {
        // let req = pb.RecruitInfoRequest.fromObject({});
        // App.Socket.send(pb.RecruitInfoRequest.Proto.ID, pb.RecruitInfoRequest.encode(req).finish());
    }

    /**
     * 清理旧数据
     */
    clearMapData() {
        this.mTreeCount = 0;
        this.mMonsterCount = 0;
        this.dataArray.length = 0;
        this.entities.length = 0;
        this.heroMgr.clearPlayer();
        this.heroMgr.stopJoystick();
        // this.monsterList.forEach((monster) => {
        //     monster.removeSelf();
        // });
        // this.treeList.forEach((tree) => {
        //     tree.removeSelf();
        // });
        this.deathMap.forEach((time: number, unit: IUnit) => {
            unit.removeSelf();
        })
        this.deathMap.clear();

        this.rebornList = [];
        this.rebornTreeMap.forEach((unit: Unit, unitId: number) => {
            unit.removeSelf();
        })
        this.rebornTreeMap.clear();
        // this.treeList = [];
        // this.monsterList = [];
        this.notifyServerDieUnitIdList = [];
        this.lastDieNotifyServerTime = 0;
        this._lastMainPlayerRoadPoint = null;
        this.lastPoint = null;
        // SkillManager.instance.clear();
        // BulletManager.instance.clear();
        // EntityManager.instance.clear();
        //清理地图数据
        // App.UserController.mapInfo.clear();
    }

    public getDeathMap() {
        return this.deathMap;
    }

    /** 请求视野范围内的区域信息 */
    protected requestZoneInfo(zoneId: number) {
        // BattleController.requestZoneInfo(zoneId);
    }

    public requestZoneInfoEx(zoneIndex: number) {
        this.requestZoneInfo(zoneIndex);
    }
    /**
     * 屏幕中心点
     */
    public get guardCenterPos(): cc.Vec3 {
        return HeroManager.instance.guardCenterPos;
    }

    public set guardCenterPos(value: cc.Vec3) {
        HeroManager.instance.guardCenterPos = value;
    }

    /**
     * 打印单位的基本信息
     * @param id 
     */
    printUnit(id: number) {
        // let entityList = EntityManager.instance.getEntityList();

        // let result = entityList.filter((data) => {
        //     return (data instanceof VoEntity) ? (data as VoEntity).unitId == id : false;
        // })
        // if (result && result.length > 0) {
        //     let data: VoCharacter = result[0] as unknown as VoCharacter;
        //     let content: string = "";
        //     content += " name:" + data.name + "\n";
        //     content += " id:" + data.unitId + "\n";
        //     content += " ap:" + data.getAttr(BattleAttr.atk) + "\n";
        //     content += " hp:" + data.getHp() + "\n";
        //     content += " hpMax:" + data.getHpMax() + "\n";
        //     content += " 出生点:" + Math.floor(data.rebornPosX) + "#" + Math.floor(data.rebornPosY);
        //     Logger.trace(content);
        //     Logger.trace(data);
        // }
    }

    addUnitBuff(id: number, buffId: number, skillCfg: CFG_SkillLevel) {
        let content: IUnit[] = this.units.filter((unit: IUnit) => {
            return unit.data.unitId === id;
        });

        if (content && content.length > 0) {
            let unit: IUnit = content[0];

            //测试对象
            let caster = new Unit();
            caster.data = new VoUnit();
            caster.data.setAttr(BattleAttr.atk, 1000);
            caster.data.setAttr(BattleAttr.maxHp, 1000);
            caster.data.setAttr(BattleAttr.hp, 1000);
            caster.data.setAttr(BattleAttr.dmgRate, 0);
            caster.data.setAttr(BattleAttr.dmg, 0);

            // unit.addBuffs(caster, [{ buffId: buffId, rate: 10000, targetType: null, limitNum: null, frameIndex: -1 }], skillCfg);
        }
    }

    /**进入区域 */
    protected enterZone(zoneId: number) {
        // console.info("~~~~EnterZoneRequest~~~~进入区域~~~~",zoneId)
        // BattleController.requestEnterZone(zoneId);
    }

    /**
     * 更新区域
     */
    protected onUpdateZoneData() {
        // let mapinfo = App.UserController.mapInfo;
        // //初始怪物
        // let zoneInfo: pb.IPbZone = mapinfo.zoneInfo;
        // let sprites: pb.IPbSprite[] = zoneInfo.sprites;
        // let zoneId: number = zoneInfo.zoneId;

        // this.mMonsterCount += mapinfo.zoneInfo.sprites.length;
        // this.mTreeCount += mapinfo.zoneInfo.harvests.length;

        // Logger.trace("获得地图数据" + "" + "zoneId:" + "" + zoneId + "" + "怪物数量:" + mapinfo.zoneInfo.sprites.length + "" + "树木数量:" + mapinfo.zoneInfo.harvests.length);

        // Logger.trace("当前总怪物数量:" + this.mMonsterCount + "" + "当前总树木数量:" + this.mTreeCount);

        // if (mapinfo.zoneInfo.zoneId == this.zoneId) {
        //     this.createZoneMonster(sprites, zoneId);
        //     this.createZoneTree(zoneInfo.harvests, zoneId);
        // }
        // else {
        //     this.createZoneMonster(sprites, zoneId);
        //     Timer.once(1000, this, () => {
        //         this.createZoneTree(zoneInfo.harvests, zoneId);
        //     })
        // }
    }

    // protected createZoneMonster(sprites: pb.IPbSprite[], zoneId: number) {
    //     let time: number = App.TimeManager.serverTimeMs;
    //     for (let i = 0; i < sprites.length; i++) {
    //         let spriteItem: pb.IPbSprite = sprites[i];
    //         // console.log("刷怪点:mapid,id,zoneId,x,y", MapManager.curMapId, spriteItem.identity, zoneId, spriteItem.cx, spriteItem.cy);
    //         let voMonster: VoMonster = new VoMonster();
    //         voMonster.init(spriteItem, 1);
    //         if (time < voMonster.rebornTime) {
    //             if (voMonster.rebornTime > 0) {
    //                 this.rebornList.push(voMonster);
    //             }
    //             continue;
    //         }
    //         //this.allDataArray.push(voMonster);
    //         EntityManager.instance.add(voMonster);
    //     }
    //     this.lastMonCount = sprites.length;
    // }

    // protected createZoneTree(harvests: pb.IPbHarvest[], zoneId: number) {
    //     //初始若干个树木
    //     let time: number = App.TimeManager.serverTimeMs;
    //     for (let i = 0; i < harvests.length; i++) {
    //         let spriteItem: pb.IPbHarvest = harvests[i];

    //         let voTree: VoTree = new VoTree();
    //         voTree.init(spriteItem);
    //         voTree.zoneId = zoneId;

    //         if (time < voTree.rebornTime && voTree.rebornTime > 0) {
    //             this.rebornList.push(voTree);
    //             continue;
    //         }
    //         EntityManager.instance.add(voTree);
    //     }
    // }

    // protected onUnitDie(unit: IUnit) {
    //     if (unit instanceof Monster) {
    //         this.onMonsterDie(unit as Monster);
    //     } else if (unit instanceof Player) {
    //         this.onEnemyPalyerDie(unit as Player);
    //     } else if (unit instanceof Tree) {
    //         this.onTreeDie(unit as Tree);
    //     }
    // }

    /**
     * 树死亡处理
     * @param unit
     */
    // protected onTreeDie(unit: Tree) {
    //     let id = unit.data.unitId;
    //     let zoneId = unit.data.zoneId;
    //     this.dieTree(unit);
    //     BattleController.requestHarvest(zoneId, id);
    // }

    /**
     * 怪物死亡处理
     * @param monster
     */
    // protected onMonsterDie(unit: Monster) {
    //     // console.info(">>>onMonsterDie")
    //     let id: number = unit.data.unitId; 0
    //     this.dieMonster(unit);
    //     this.notifyServerDieUnitIdList.push(id);
    // }

    /**
    * 敌方英雄死亡处理
    * @param player
    */
    // protected onEnemyPalyerDie(unit: Player) {
    //     // console.info(">>>onMonsterDie")
    //     let id: number = unit.data.unitId;
    //     this.dieEntity(unit);
    //     this.notifyServerDieUnitIdList.push(id)
    // }

    /**实体死亡处理 */
    // protected dieEntity(unit: IUnit) {
    //     //放入重生列表
    //     let index: number = this.rebornList.indexOf(unit.data);
    //     if (index == -1) {
    //         this.rebornList.push(unit.data);
    //     } else {
    //         console.warn("dieEntity rebornList", index);
    //     }
    //     //在数据里面删除
    //     EntityManager.instance.remove(unit.data);
    //     //当前显示数据里面删除
    //     index = this.dataArray.indexOf(unit.data);
    //     if (index > -1) {
    //         this.dataArray.splice(index, 1);
    //     } else {
    //         console.warn("dieEntity dataArray", index);
    //     }

    //     if (unit.unitType != UnitType.Hero) {
    //         //当前显示对象数据里面删除
    //         index = this.entities.indexOf(unit);
    //         if (index > -1) {
    //             this.entities.splice(index, 1);
    //         } else {
    //             console.warn("dieEntity entitys", index);
    //         }
    //     }
    // }

    /**
     * 移除重生
     * @param spriteId
     */
    removeReborn(spriteId) {
        for (let i = this.rebornList.length - 1; i >= 0; i--) {
            if (this.rebornList[i].unitId == spriteId) {
                this.rebornList.splice(i, 1);
                return;
            }
        }
    }

    /**
     * 重生对象
     * @param spriteId
     * @returns
     */
    getRebornListById(spriteId) {
        for (let i = 0; i < this.rebornList.length; i++) {
            if (this.rebornList[i].unitId == spriteId) {
                return this.rebornList[i];
            }
        }
    }

    /**创建宝箱 */
    private createTreasureBox(boxType: number): void {
        // App.TreasureBoxController.createTreasureBox(boxType);
    }

    /**是否可以重生 */
    protected isReborn(entity: VoEntity): boolean {
        if (entity.unitType == UnitType.Monster || entity.unitType == UnitType.Tree) {
            let data: VoUnit = entity as VoUnit;
            //怪物重生中
            if (data.rebornTime > 0 && AppManager.instance().TimeManager.serverTimeMs >= data.rebornTime) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检测存活
     * @param data
     * @returns
     */
    protected checkAlive(data: VoEntity): boolean {

        return super.checkAlive(data);
    }

    /**
     * 检测重生列表
     */
    checkRebornUnit() {
        if (this.rebornList.length > 0) {
            for (let i = this.rebornList.length - 1; i >= 0; i--) {
                const element = this.rebornList[i];

                if (!this.isReborn(element)) continue;

                element.reborn();
                // EntityManager.instance.add(element);
                this.rebornList.splice(i, 1);
                if (this.rebornTreeMap[element.unitId]) this.rebornTreeMap[element.unitId].removeSelf();
            }
        }
    }

    /**
     *重生
     */
    rebornUnit(unit: IUnit) {
        //放入重生列表
        let index: number = this.rebornList.indexOf(unit.data);
        if (index > -1) {
            this.rebornList.slice(index, 1);
        }
        //在数据里面删除
        // EntityManager.instance.add(unit.data);
        //当前显示数据里面删除
        index = this.dataArray.indexOf(unit.data);
        if (index == -1) {
            this.dataArray.push(unit.data);
        }
        //当前显示对象数据里面删除
        index = this.entities.indexOf(unit);
        if (index == -1) {
            this.entities.push(unit);
        }
        unit.data.reborn();
        // unit.reborn();
    }

    /**
     * 增加一个死亡单位. <英雄不放入此列表>
     * @param monster 
     */
    private addDeathUnit(unit: IUnit) {
        // unit && unit.onDeadVanish();
        if (!unit || this.deathMap.has(unit)) {
            console.error("已包含当前死亡的对象", unit);
            return;
        }
        this.deathMap.set(unit, Date.now());
    }

    /** 死亡处理 */
    protected onDeathUpdate() {
        let time = Date.now();
        this.deathMap.forEach((deathTime: number, unit: IUnit) => {
            if (time >= deathTime + this.deathRetentionTime) {
                this.recycleUnit(unit);
                this.deathMap.delete(unit);
            }
        })
    }

    /** 回收单位 */
    protected recycleUnit(unit: IUnit) {
        unit.removeSelf();
    }

    override update(dt: number): void {
        super.update(dt);

        this.checkRebornUnit();
        this._checkMainPlayerZoneStatus();
        this.updateTiledMapEntity();
        // SkillManager.instance.update();
        this.checkDieNotifyServer();
    }

    private lastDieNotifyServerTime = 0;
    private checkDieNotifyServer() {
        let now = Date.now();
        if (now - this.lastDieNotifyServerTime < 300) {
            return;
        }
        this.lastDieNotifyServerTime = now;
        if (this.notifyServerDieUnitIdList && this.notifyServerDieUnitIdList.length > 0) {
            // BattleController.requestUnitDie(this.notifyServerDieUnitIdList);
            this.notifyServerDieUnitIdList = [];
        }
    }
    /** 判断mainplayer 和全部followPlayers 已经死亡 */
    checkAllDie() {
        // for (let i = 0; i < this.allPlayers.length; i++) {
        //     const element = this.allPlayers[i];
        //     if (element.getHp() > 0) {
        //         return false;
        //     }
        // }
        // return true;
        return false;
    }

    /** 停止英雄身上的复活倒计时 */
    stopRebornCountDown() {
        // this.allPlayers.forEach((player) => {
        //     player.hideCountDown();
        // })
    }

    protected _lastMainPlayerRoadPoint: Point = null;
    private _checkDifferentRoadPointTime: number = 0;
    /** 获取主英雄的路点, 判断是否切换区域**/
    private _checkMainPlayerZoneStatus() {
        // if (!this.mainPlayer || !this.mainPlayer.node) return;

        // const pos = this.mainPlayer.node.position;
        // const point = MapRoadUtils.instance.getWorldPointByPixel(pos.x, pos.y);
        // const lastPoint = this._lastMainPlayerRoadPoint;

        // if (lastPoint && lastPoint.isEqual(point)) {
        //     return;
        // }

        // const statusPoint = this.data.getMapAreaStatusByWorldPoint(point.x, point.y);
        // const statusLast = lastPoint ? this.data.getMapAreaStatusByWorldPoint(lastPoint.x, lastPoint.y) : -1;
        // if (statusPoint != statusLast) {
        //     this._checkDifferentRoadPointTime += 1;

        //     // NOTE: 加一个缓冲值, 以防2个刚体重合的情况.
        //     if (this._checkDifferentRoadPointTime > 0) {
        //         this._checkDifferentRoadPointTime = 0;
        //         this.changeZoneStatus(statusPoint);
        //         this._lastMainPlayerRoadPoint = point;
        //     }
        // }
    }

    /**
     * 切换区域状态
     * @param statusPoint
     */
    protected changeZoneStatus(statusPoint: number) {
        // switch (statusPoint) {
        //     case MapAreaType.PASSABLE_AREA:
        //         // App.showTip("进入可行走区域");
        //         break;
        //     case MapAreaType.IMPASSABLE_AREA:
        //         // App.showTip("进入不可行走区域");
        //         break;
        //     case MapAreaType.SAFE_AREA:
        //         App.EventManager.emitEvent(GameEvent.ENTER_AREA_SAFE);
        //         break;
        //     case MapAreaType.OUTSIDE_AREA:
        //         App.EventManager.emitEvent(GameEvent.ENTER_AREA_OUTSIDE);
        //         break;
        //     case MapAreaType.FOG_AREA:
        //         // App.showTip("进入迷雾区域");
        //         break;
        //     default:
        //         // console.error("进到了未知区域");
        //         break;
        // }
        this.isInSafeArea = statusPoint == MapAreaType.SAFE_AREA;
        this.areaDefine = true;
    }

    /**英雄到目标坐标是否可通行. */
    protected isPassable(player: PlayerActor, end: cc.Vec2): boolean {
        //度量长度.
        // let checkDistance: number = 10;
        // let dx: number = end.x - player.node.x;
        // let dy: number = end.y - player.node.y;
        // let moveAngle: number = Math.atan2(dy, dx);
        // var xspeed: number = Math.cos(moveAngle) * checkDistance;
        // var yspeed: number = Math.sin(moveAngle) * checkDistance;
        // let start: cc.Vec2 = player.node.getPosition();
        // let totalDistance: number = start.sub(end).mag();
        // //目标未知肯定不能站人.
        // let n: number = Math.ceil(totalDistance / checkDistance) - 1;
        // for (let i = 0; i < n; i++) {
        //     let nx: number = start.x + xspeed * i;
        //     let ny: number = start.y + yspeed * i;
        //     const point = MapRoadUtils.instance.getWorldPointByPixel(nx, ny);
        //     if (!this.data.isPassableByWorldPoint(point.x, point.y)) {
        //         return false;
        //     }
        // }
        return true;
    }

    /**停止摇杆 */
    public stopJoystick() {
        this.heroMgr.stopJoystick();
    }

    public onJoystickTouchEnd(event: cc.EventTouch, joyStick: Joystick) {
        this.heroMgr.onJoystickTouchEnd(event, joyStick);
    }

    public onJoystickTouchStart(event: cc.EventTouch, joyStick: Joystick) {
        this.heroMgr.onJoystickTouchStart(event, joyStick);
    }

    public onJoystickTouchMove(event: cc.EventTouch, joyStick: Joystick) {
        this.heroMgr.onJoystickTouchMove(event, joyStick);
    }

    /** 解锁迷雾 */
    public unlockFog(id: number): void {
        // const data = this.data.getFogArea(id);
        // if (!!data) {
        //     App.SoundManager.playEffect(SoundId.MIWU, SoundType.COMMON);
        //     FogNodePool.instance.clearFogLayer(id);
        //     QuadtreeManager.instance.clearEdgeFogTree();
        //     // data.layer.node.active = false;
        //     this.data.setFogAreaActive(id, true);
        //     NpcManager.instance.activeFogNpc(id);
        //     this.initPathFinding();
        //     MapManager.curMap.parseRigidBodyEdgeFog();
        //     this.data.mapAreaStatus = this.calcMapAreaStatus();
        //     this.cameraViewPortUpdateFlag = true;
        //     this.refresAllFogNpcLayer();
        //     this.refresAllTaskNpcLayer();
        // }
    }

    /** 刷新迷雾NPC层级  */
    // private refresAllFogNpcLayer(): void {
    //     let _list: CFG_SceneFog[] = CFG_SceneFog.list;
    //     let _cfgData: CFG_SceneFog = null;
    //     for (let i: number = 0; i < _list.length; i++) {
    //         _cfgData = _list[i];
    //         let _fogNpc: Npc = NpcManager.instance.getFogNpc(_cfgData.fogId);
    //         (_fogNpc as FogNpc)?.updateLayerIdx();
    //     }
    // }

    // /** 刷新任务NPC层级  */
    // private refresAllTaskNpcLayer(): void {
    //     let _list2: CFG_SceneNpc[] = CFG_SceneNpc.list;
    //     let _cfgData2: CFG_SceneNpc = null;
    //     for (let i: number = 0; i < _list2.length; i++) {
    //         _cfgData2 = _list2[i];
    //         let _taskNpc: Npc = NpcManager.instance.getTaskNpc(_cfgData2.id);
    //         (_taskNpc as TaskNpc)?.updateLayerIdx();
    //     }
    // }


    // /** 解锁传送点 */
    // public unlockTelePort(id: number): void {
    //     const data = this.data.getTeleportPoint(id);
    //     if (!!data) {
    //         NpcManager.instance.activeTeleportNpc(id);
    //     }
    // }

    // /** 解锁跨地图传送点 */
    // public unlockCrossTelePort(id: number): void {
    //     const data = this.data.getCrossTeleportPoint(id);
    //     if (!!data) {
    //         NpcManager.instance.activeCrossTeleportNpc(id);
    //     }
    // }

    /**
     * point坐标是否允许unit巡逻
     * @param unit 巡逻的单位
     * @param point 巡逻的坐标
     *
     * 判断是否可以巡逻.   是否被遮挡, 是否为不可行走
     */
    // public isPatrolPoint(unit: Monster, pointX: number, pointY: number): boolean {
    //     //TODO...
    //     return true;
    // }

    /**
     * 出生点
     * @returns
     */
    public getSpawnPoint(): cc.Vec3 {
        let spawnPointX: number, spawnPointY: number;
        spawnPointX = this.data.spawnPoint.x;
        spawnPointY = this.data.spawnPoint.y;
        return new cc.Vec3(spawnPointX, spawnPointY);
    }

    /**
     * 复活点
     * @returns
     */
    public getRebornPoint(): cc.Vec3 {
        let pointX: number, pointY: number;
        pointX = this.data.rebornPoint.x;
        pointY = this.data.rebornPoint.y;
        return new cc.Vec3(pointX, pointY);
    }

    onAllHeroShow(isOpenGame: boolean): void {
        this.entityLayer.active = isOpenGame;
    }

    /** 初始全部英雄 */
    reloadAllHero(mainHeroIdentity: number, heros: pb.IPbSprite[], spawnPoint?: cc.Vec3) {
        if (!spawnPoint) spawnPoint = this.getSpawnPoint();
        this.heroMgr.reloadAllHero(mainHeroIdentity, heros, spawnPoint);
    }

    public initCamera() {
        const winSize = cc.view.getDesignResolutionSize();
        //摄像机视野范围
        this.cameraViewPort.x = this.guardCenterPos.x - winSize.width;
        this.cameraViewPort.y = this.guardCenterPos.y - winSize.height;
        this.cameraViewPort.width = winSize.width * 1.5;
        this.cameraViewPort.height = winSize.height * 1.5;

        //团队视野范围
        let _cfgSightRange: number = 
            SceneZoneCache.getData(this.mapId, this.zoneId) == null ? 
                2000 : // Player_Init_data.TeamSightRange : 
                SceneZoneCache.getData(this.mapId, this.zoneId).heroVision;
        this.teamViewPort.x = this.guardCenterPos.x - _cfgSightRange;
        this.teamViewPort.y = this.guardCenterPos.y - _cfgSightRange;
        this.teamViewPort.width = _cfgSightRange * 2;
        this.teamViewPort.height = _cfgSightRange * 2;

        CameraController.instance.init(this.offsetHeight);
        CameraController.instance.setViewToPoint(this.guardCenterPos.x, this.guardCenterPos.y, false);
    }

    /**
     * 回安全区复活
     */
    protected onHeroRebornSafe() {
        // 重置主角位置
        // let mainPlayer = this.heroMgr.allPlayers[0];
        // let point: cc.Vec3 = this.getRebornPoint();
        // this.guardCenterPos = point;

        // mainPlayer.setFollow(this.guardCenterPos);
        // mainPlayer.node.setPosition(point.x, point.y);
        // MapManager.curMap.rebornUnit(mainPlayer);

        // GameController.instance.mainUIControler.onEnterAreaSafe();

        // CameraController.instance.setTarget(mainPlayer.node);

        // let followPlayers = this.allPlayers;
        // let posArr = MapHelper.getHeroPositionList(point, this.allPlayers.length);

        // for (let index = 1; index < followPlayers.length; index++) {
        //     let player = followPlayers[index];
        //     player.setFollow(posArr[index]);
        //     player.node.setPosition(posArr[index]);
        //     MapManager.curMap.rebornUnit(player);
        // }

        // CameraController.instance.setViewToPoint(point.x, point.y, false);
        // let guidePlayer = HeroManager.instance.getGuidePlayer();
        // if (guidePlayer) {
        //     guidePlayer.setPos(new cc.Vec3(point.x, point.y));
        // }
    }

    /**
     * 原地复活
     */
    protected onHeroRebornSitu() {
        // let followPlayers = this.allPlayers;
        // for (let index = 0; index < followPlayers.length; index++) {
        //     let player = followPlayers[index];
        //     player.updatePosition();
        //     MapManager.curMap.rebornUnit(player);
        // }
    }

    /** 已经加载好的英雄数量  */
    protected addedHeroCount: number = 0;
    /**
     * 排序所有显示节点
     * @param nodes
     */
    override sortAllEntitys(nodes: cc.Node[]) {
        nodes.sort((node1: cc.Node, node2: cc.Node): number => {
            if (node1.y > node2.y) {
                return -1;
            } else if (node1.y < node2.y) {
                return 1;
            }
            return 0;
        });
    }

    protected changeSceneQiPao() {
        // if (this.addedHeroCount == this.heroMgr.battleHeroNum) {
        //     HeroManager.instance.showQiPao(QiPaoType.ChangeScene);
        // }
    }
    override addUnitAfter(entity: IUnit, voEntity: VoEntity): void {
        // if (voEntity.unitType == UnitType.Hero) {
        //     let voHero: VoHero = voEntity as VoHero;
        //     if (voHero.isMainPlayer) {
        //         this.heroMgr.allPlayers.unshift(entity as unknown as Player);
        //     } else {
        //         this.heroMgr.allPlayers.push(entity as unknown as Player);
        //     }
        //     this.heroMgr.updatePlayerAttribute(entity as unknown as Player);
        // } else if (voEntity.unitType == UnitType.Monster) {
        //     this.monsterList.push(entity as unknown as Monster);
        //     App.EventManager.emitEvent(GameEvent.ADD_TASK_TARGET_TAB);
        // } else if (voEntity.unitType == UnitType.Tree) {
        //     this.treeList.push(entity as unknown as Tree);
        // }
        // App.EventManager.emitEvent(GameEvent.ADD_UNIT);

        // if (voEntity.unitType == UnitType.Hero) {
        //     (entity as Player)?.hideSkin();
        //     this.addedHeroCount++;
        //     if (this.addedHeroCount == 1) {
        //         App.EventManager.emitEvent(MapEvent.ENTER_WORLD_SUCCESS);
        //         this.handlerEnterWorldSuccess();
        //     }
        //     this.changeSceneQiPao();
        // }
    }

    override removeUnitAfter(entity: IUnit, voEntity: VoEntity): void {
        // if (voEntity.unitType == UnitType.Hero) {
        //     let index: number = this.heroMgr.allPlayers.indexOf(entity as unknown as Player);
        //     if (index > -1) this.heroMgr.allPlayers.splice(index, 1);
        // } else if (voEntity.unitType == UnitType.Monster) {
        //     let index: number = this.monsterList.indexOf(entity as unknown as Monster);
        //     if (index > -1) this.monsterList.splice(index, 1);
        // } else if (voEntity.unitType == UnitType.Tree) {
        //     let index: number = this.treeList.indexOf(entity as unknown as Tree);
        //     if (index > -1) this.treeList.splice(index, 1);
        // }
    }

    /**
     * 怪物死亡处理
     * @param unit
     */
    // dieMonster(unit: Monster) {
    //     (unit.data as VoMonster).rebornTime = -1;
    //     (unit.data as VoMonster).dieX = unit.node.x;
    //     (unit.data as VoMonster).dieY = unit.node.y;

    //     let id = (unit.data as VoMonster).cfgMonster.id;
    //     if (this.bossChallengeMap[id]) {
    //         //boss死亡的，清空boss被挑战的记录
    //         this.bossChallengeMap[id] = false;
    //     }

    //     let index: number = this.monsterList.indexOf(unit);
    //     if (index > -1) {
    //         this.monsterList.splice(index, 1);
    //     } else {
    //         console.warn("dieMonster", index);
    //     }
    //     this.dieEntity(unit);

    //     this.lastMonCount--;
    //     if (App.DailyDupManager.curDupId <= 0) {
    //         oops.timer.once(this.monsterDieDelayTime, this, () => {
    //             this.addDeathUnit(unit);
    //         })
    //     }
    //     else {
    //         //curDupId大于0为在副本中
    //         let time = parseInt(CFG_Constant.getValue("disappears_time"))
    //         oops.timer.once(time * 1000, this, () => {
    //             cc.tween(unit.node)
    //                 .to(0.2, { opacity: 0 })
    //                 .call(() => {
    //                     this.addDeathUnit(unit);
    //                 })
    //                 .start();
    //         })
    //     }
    // }

    // dieTree(unit: Tree) {

    //     (unit.data as VoTree).rebornTime = -1;
    //     (unit.data as VoTree).dieX = unit.node.x;
    //     (unit.data as VoTree).dieY = unit.node.x;

    //     //树木不消失.
    //     let index: number = this.treeList.indexOf(unit);
    //     if (index > -1) {
    //         this.treeList.splice(index, 1);
    //     } else {
    //         console.warn("dieTree", index);
    //     }
    //     this.dieEntity(unit);
    //     this.rebornTreeMap[unit.data.unitId] = unit;
    //     // oops.timer.once(rebornTime, this, () => {
    //     //     unit.removeSelf();
    //     // })
    // }

    // /** 获取存活的英雄 */
    // getAliveHero(): Player {
    //     return this.heroMgr.getAliveHero();
    // }

    // public createHomeNpc(): void {
    //     MapUnitFactory.createHomeNpc(this.context);
    // }

    // public createFogNpc(): void {
    //     MapUnitFactory.createFogNpc(this.context);
    // }

    // public createTaskNpc(): void {
    //     MapUnitFactory.createTaskNpc(this.context);
    // }

    // public createDungeonNpc(): void {
    //     MapUnitFactory.createDungeonNpc(this.context);
    // }

    // public createRebuildNpc(): void {
    //     MapUnitFactory.createRebuildNpc(this.context);
    // }

    // public createTeleport(): void {
    //     MapUnitFactory.createTeleport(this.context);
    // }

    // public createCrossTeleport(): void {
    //     MapUnitFactory.createCrossTeleport(this.context);
    // }

    /** -----------------------------------------------------------
     * 传送
     * ------------------------------------------------------------ */

    /** 请求设置回城传送门 */
    // public requestSetTownPortal(): void {
    //     const pos = this.mainPlayer.node.getPosition();
    //     let req = pb.MainlineSetPortalRequest.fromObject({
    //         portalX: pos.x,
    //         portalY: pos.y,
    //     });
    //     App.Socket.send(pb.MainlineSetPortalRequest.Proto.ID, pb.MainlineSetPortalRequest.encode(req).finish());
    //     Logger.trace("请求设置回城传送门:" + pos.x + " " + pos.y);
    // }

    // /** 请求使用回城传送门 */
    // public requestUseTownPortal(): void {
    //     let req = pb.MainlineUsePortalRequest.fromObject({});
    //     App.Socket.send(pb.MainlineUsePortalRequest.Proto.ID, pb.MainlineUsePortalRequest.encode(req).finish());
    //     Logger.trace("请求使用回城传送门");
    // }

    // public createTownPortal(x?: number, y?: number): void {
    //     MapUnitFactory.removeTownPortal();
    //     MapUnitFactory.createTownPortal(this.context, x, y);
    // }

    // public removeTownPortal(): void {
    //     MapUnitFactory.removeTownPortal();
    // }

    /** 更新 Tiled Map 上的物体 TODO 有性能问题 */
    protected updateTiledMapEntity() {
        if (!!this.cameraViewPort) {
            // QuadtreeManager.instance.update(this.cameraViewPort, this.isUnlockVillage(), this.cameraViewPortUpdateFlag);
            this.cameraViewPortUpdateFlag = false;
        }
    }

    /** 伙伴是不是在受击 1s内被攻击过都算受击
      */
    public isHeroBeAttacking(): boolean {
        // let _result:boolean = false;
        // this.heroMgr.allPlayers.forEach((player)=>{
        //     if(player.isBeAttackIng()){
        //         _result = true;
        //     }
        // })
        // return _result;
        return false;
    }

    /** 整个队伍传送到(x,y) */
    // private transfer(x: number, y: number, isCross: boolean = false): void {
    //     App.showMask();
    //     CameraController.instance.setZoomToHight();
    //     this.allPlayers.forEach((player: Player, index: number) => {
    //         player.stopMove();
    //         player.sleep = true;
    //         player.state = UnitState.idle;
    //         isCross
    //             ?
    //             player.crossTransferByTownPortal()
    //             :
    //             player.transferByTownPortal(
    //                 x,
    //                 y,
    //                 index == 0
    //                     ? () => {
    //                         CameraController.instance.setZoomToNormal();
    //                         player.sleep = false;
    //                         this.guardCenterPos = new cc.Vec3(x, y);
    //                         const mainPlayer = this.mainPlayer?.node;
    //                         CameraController.instance.setViewToPoint(mainPlayer.x, mainPlayer.y, false);
    //                         let guidePlayer = HeroManager.instance.getGuidePlayer();
    //                         if (guidePlayer) {
    //                             guidePlayer.setPos(new cc.Vec3(x, y));
    //                         }
    //                     }
    //                     : () => {
    //                         player.sleep = false;
    //                     },
    //             );
    //     });
    // }

    // /** 
    // 整个队伍播放传送结束效果
    // isScaleEff 是否让场景播放缩放特效
    //  */
    // public playTeamTranferEndEffect(isScaleEff: boolean = true): void {
    //     App.showMask();
    //     if (isScaleEff) {
    //         CameraController.instance.setZoomToHight();
    //     }
    //     this.allPlayers.forEach((player: Player, index: number) => {
    //         player.stopMove();
    //         player.sleep = true;
    //         player.state = UnitState.idle;
    //         player.playTranferEndEffect((play) => {
    //             player.sleep = false;
    //         });
    //     });
    // }

    // /** 回城点传送到野外 */
    // public transferByTownPortalHome(): void {
    //     if (!NpcManager.instance.TownPortalHome || !NpcManager.instance.TownPortalOutside) {
    //         return;
    //     }

    //     const pos = NpcManager.instance.TownPortalOutside.node.getPosition();
    //     this.transfer(pos.x, pos.y);
    // }

    // public transferByTownPortal(): void {
    //     // if (!NpcManager.instance.TownPortalOutside) {
    //     //     return;
    //     // }
    //     // MapEnumType.WORLD_MAP
    //     const pos = MapManager.curMap.data.townPortal;
    //     if (MapManager.curMapId == MapEnumType.WORLD_MAP) {
    //         this.transfer(pos.x, pos.y);
    //     } else {
    //         this.transfer(pos.x, pos.y, true);
    //         oops.timer.once(1500, this, () => {
    //             App.ViewManager.show(ViewId.LOADING_HEIMU, { battleType: BattleType.MainLine, mapType: MapEnumType.WORLD_MAP });
    //         });
    //     }
    // }

    // /** 传送到传送点  */
    // public transferToTelePort(id: number): void {
    //     if (id == 0) {
    //         let data = MapManager.curMap.data.getNpcBuilding(NpcHomeEnum.Teleport);
    //         if (data != null) {
    //             this.transfer(data.x, data.y);
    //         }
    //     } else {
    //         let data = MapManager.curMap.data.getTeleportPoint(id);
    //         if (data != null) {
    //             this.transfer(data.x, data.y);
    //         }
    //     }
    // }

    // /** 传送到跨地图传送点  */
    // public transferToCrossTelePort(sceneId: number, id: number): void {
    //     this.transfer(0, 0, true);
    //     oops.timer.once(1000, this, () => {
    //         // BattleController.requestEnterBattle(BattleType.MainLine, sceneId, null, null, 0, id);
    //         App.ViewManager.show(ViewId.LOADING_HEIMU, { battleType: BattleType.MainLine, mapType: sceneId, crossMapType: id });
    //     });
    // }

    // /** -----------------------------------------------------------
    //  * 查找 Unit
    //  * ------------------------------------------------------------ */
    // private _getUnitNearest(content: IUnit[]): cc.Node {
    //     let ret: IUnit = null; // 距离最近的怪物
    //     let minDis = Number.MAX_SAFE_INTEGER;
    //     const mainPlayerPos = this.mainPlayer.getPosition();

    //     for (let i = 0; i < content.length; i++) {
    //         const element = content[i];
    //         let dis = element.getPosition().sub(mainPlayerPos).mag();
    //         if (dis < minDis) {
    //             ret = element;
    //             minDis = dis;
    //         }
    //     }
    //     return ret?.node || null;
    // }

    // /** 获取最近的怪物(精元) */
    // public getMonsterGroupNearest(): cc.Node {
    //     return this._getUnitNearest(this.monsterList);
    // }

    // /** 获取最近的树木 */
    // public getTreeGroupNearest(): cc.Node {
    //     const content: Unit[] = this.treeList.filter((v: Tree) => {
    //         return v.type == TreeType.Tree;
    //     });
    //     return this._getUnitNearest(content);
    // }

    // /** 获取最近的矿 */
    // public getOreGroupNearest(): cc.Node {
    //     const content: Unit[] = this.treeList.filter((v: Tree) => {
    //         return v.type == TreeType.Ore;
    //     });
    //     return this._getUnitNearest(content);
    // }

    // /**
    //  * 寻找距离最近的怪物(根据怪物ID)
    //  */
    // public getMonsterGroupById(idExcel: number): cc.Node {
    //     const content: Unit[] = this.monsterList.filter((v: Monster) => {
    //         return v.data.idExcel == idExcel;
    //     });
    //     return this._getUnitNearest(content);
    // }

    // /**获取任务目标怪物 */
    // public getTaskMonsterTargets(idExcel: number): Unit[] {
    //     const content: Unit[] = this.monsterList.filter((v: Monster) => {
    //         return v.data.idExcel == idExcel;
    //     });
    //     return content;
    // }

    // /**
    //  * 寻找距离最近的怪物(根据类型)
    //  */
    // public getMonsterGroupByType(type: number): cc.Node {
    //     const content: Unit[] = this.monsterList.filter((v: Monster) => {
    //         return v.data.unitType == type;
    //     });
    //     return this._getUnitNearest(content);
    // }
    // /** -----------------------------------------------------------
    //  * 查找: 资源点 | 怪物点
    //  * ------------------------------------------------------------ */
    // private _getPointNearest(content: VoEntity[]): cc.Vec2 {
    //     let ret: cc.Vec2 = null; // 距离最近的怪物
    //     let minDis = Number.MAX_SAFE_INTEGER;
    //     const mainPlayerPos = this.mainPlayer.node.getPosition();

    //     for (let i = 0; i < content.length; i++) {
    //         const element = content[i];
    //         const pt = cc.v2(element.point.x, element.point.y);
    //         let dis = pt.sub(mainPlayerPos).mag();
    //         if (dis < minDis) {
    //             ret = pt;
    //             minDis = dis;
    //         }
    //     }
    //     return ret;
    // }

    // /**
    //  * 获取最近的怪物(精元)的坐ss标
    //  */
    // public getMonsterPointNearest(): cc.Vec2 {
    //     const content: any[] = this.allDataArray.filter((v: VoEntity) => {
    //         if (!(v instanceof VoEntity)) return false;
    //         return v.unitType == UnitType.Monster;
    //     });
    //     return this._getPointNearest(content);
    // }

    // /**
    //  * 获取最近的树木的坐标
    //  */
    // public getTreePointNearest(): cc.Vec2 {
    //     const content: any[] = this.allDataArray.filter((v: VoEntity) => {
    //         if (!(v instanceof VoEntity)) return false;
    //         return v.unitType == UnitType.Tree && (v as VoTree).subType == TreeType.Tree;
    //     });
    //     return this._getPointNearest(content);
    // }

    // /**
    //  * 获取最近的矿的坐标
    //  */
    // public getOrePointNearest(): cc.Vec2 {
    //     const content: any[] = this.allDataArray.filter((v: VoEntity) => {
    //         if (!(v instanceof VoEntity)) return false;
    //         return v.unitType == UnitType.Tree && (v as VoTree).subType == TreeType.Ore;
    //     });
    //     return this._getPointNearest(content);
    // }

    // /**
    //  * 寻找距离最近的怪物(根据怪物ID)的坐标
    //  */
    // public getMonsterPointById(idExcel: number): cc.Vec2 {
    //     const content: any[] = this.allDataArray.filter((v: VoEntity) => {
    //         if (!(v instanceof VoEntity)) return false;
    //         return v.unitType == UnitType.Monster && v.idExcel == idExcel;
    //     });
    //     return this._getPointNearest(content);
    // }

    // /**
    //  * 寻找距离最近的怪物(根据类型)的坐标
    //  */
    // public getMonsterPointByType(subType: number): cc.Vec2 {
    //     const content: any[] = this.allDataArray.filter((v: VoEntity) => {
    //         if (!(v instanceof VoEntity)) return false;
    //         return v.unitType == UnitType.Monster && (v as VoMonster).subType == subType;
    //     });
    //     return this._getPointNearest(content);
    // }

    // /**
    //  * 寻找距离最近的怪物(根据类型)的坐标
    //  */
    // public getBuildingById(id: number): cc.Vec2 {
    //     const content: any[] = this.allDataArray.filter((v: NpcShell) => {
    //         if (!(v instanceof NpcShell)) return false;
    //         return v.npcData.id == id;
    //     });

    //     if (content.length > 0) {
    //         return new cc.Vec2((content[0] as NpcShell).npcData.x, (content[0] as NpcShell).npcData.y);
    //     }
    //     return null
    // }

    // /**
    //  * 对应的点范围内存活的怪物
    //  * @param dx
    //  * @param dy
    //  * @param range
    //  */
    // public getAliveRangeMonster(dx: number, dy: number, range: number): any[] {
    //     const content: any[] = this.allDataArray.filter((v: VoEntity) => {
    //         if (!(v instanceof VoEntity)) return false;
    //         return v.unitType == UnitType.Monster && (v as VoMonster).getHp() > 0 && MathUtils.getDistancePosition(v.point.x, v.point.y, dx, dy) <= range;
    //     });
    //     return content;
    // }

    // /**
    //  * 对应的点范围内存活的树
    //  * @param dx
    //  * @param dy
    //  * @param range
    //  */
    // public getAliveRangeTree(dx: number, dy: number, range: number): any[] {
    //     const content: any[] = this.allDataArray.filter((v: VoEntity) => {
    //         if (!(v instanceof VoEntity)) return false;
    //         return v.unitType == UnitType.Tree && (v as VoMonster).getHp() > 0 && MathUtils.getDistancePosition(v.point.x, v.point.y, dx, dy) <= range;
    //     });
    //     return content;
    // }

    // /**
    //  * 对应的点范围内存活的矿
    //  * @param dx
    //  * @param dy
    //  * @param range
    //  */
    // public getAliveRangeOre(dx: number, dy: number, range: number): VoEntity[] {
    //     const content: any[] = this.allDataArray.filter((v: VoEntity) => {
    //         if (!(v instanceof VoEntity)) return false;
    //         return v.unitType == UnitType.Tree && (v as VoTree).subType == TreeType.Ore && (v as VoMonster).getHp() > 0 && MathUtils.getDistancePosition(v.point.x, v.point.y, dx, dy) <= range;
    //     });
    //     return content;
    // }

    // /**
    //  * 获取boss列表
    //  */
    // public getBoss(): Monster[] {
    //     let monsters: Monster[] = [];
    //     for (let i = 0; i < this.monsterList.length; i++) {
    //         let monster = this.monsterList[i];
    //         if (monster.data.cfgMonster.type == MonsterType.BOSS) {
    //             monsters.push(monster);
    //         }
    //     }
    //     return monsters;
    // }

    // protected get allDataArray(): GameObject[] {
    //     return EntityManager.instance.getEntityList();
    // }

    // /** 检测队伍现在在哪那个区域  */
    public checkInRegion(): void {
        // if (this.heroMgr.mainPlayer == null || this.data == null) {
        //     return;
        // }
        // let curRegionId = this.data.getRegionId(this.heroMgr.mainPlayer.node.position.x, this.heroMgr.mainPlayer.node.position.y);

        // if (curRegionId != this._curRegionId) {
        //     let path: string = CFG_SceneZone.getCfg(this.mapId, curRegionId).mapName;
        //     if (path.length > 0) {
        //         App.showTip("", false, 1, 0, path);
        //     }

        //     this.enterZone(curRegionId);
        // }
        // this._curRegionId = curRegionId;
        // if (this._curRegionId == 2) {
        //     App.UserController.resetCanReliveCount();
        // }
    }

    public get curRegionId(): number {
        return this._curRegionId;
    }
}
