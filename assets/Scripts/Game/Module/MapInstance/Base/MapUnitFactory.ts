import * as cc from "cc";
import { <PERSON><PERSON><PERSON><PERSON>, UnitAnim } from "../../../../Config/GameDefine";
import {
    CFG_Harvest,
    CFG_NpcBuilding,
    CFG_SceneFog,
    CFG_SceneNpc,
} from "../../../../Config/GameCfg/CFGManager";
import Logger from "../../../../Framework/Logger";
import { LoadUtils } from "../../../Utils/LoadUtils";
import { ViewUtil } from "../../../Utils/ViewUtils";
import { Bundles } from "../../Bundles";
// import { FightContext } from "../../Modules/fight/FightContext";
import { ITiledMapPolygon } from "../../Map/tiled/TileMapData";
// import { VoSkill } from "../../game/skill/VoSkill";
import { VoEntity } from "../../../Unit/VoEntity";
// import { VoH<PERSON> } from "../../Unit/VoHero";
// import { VoMonster } from "../../game/unit/VoMonster";
// import Monster from "../../game/unit/actor/monster/Monster";
// import Player from "../../game/unit/actor/player/Player";
// import Tree from "../../game/unit/actor/tree/Tree";
// import Unit from "../../game/unit/base/Unit";
// import Npc from "../../game/unit/npc/base/Npc";
// import {
//     NPC_CROSS_TELEPORT_PREFAB_PATH,
//     NPC_DUNGEON_PREFAB_PATH,
//     NPC_FOG_PREFAB_PATH,
//     NPC_HOME_PREFAB_PATH,
//     NPC_TASK_PREFAB_PATH,
//     NPC_TELEPORT_PREFAB_PATH,
//     NPC_TOWN_PORTAL_PREFAB_PATH,
//     NpcActiveOperate,
//     NpcHomeEnum,
//     NpcType,
// } from "../../game/unit/npc/base/NpcDefine";
// import NpcManager from "../npc/NpcManager";
import PoolEdgeRigidBody from "./PoolEdgeRigidBody";
// import PoolTiledMapPolygon from "../pool/PoolTiledMapPolygon";
// import QuadtreeManager from "../quadtree/QuadtreeManager";
import { MAP_FRICTION, POLYGON_POINTS } from "./MapDefine";
import MapManager from "../Managers/MapManager";
import PoolTiledMapPolygon, { PoolPreloadNum } from "./PoolTiledMapPolygon";
import { MapInstance } from "../Components/MapInstance";
import Unit from "../../../Unit/Base/Unit";
import PlayerActor from "../../../Unit/Actor/player/PlayerActor";
import { VoHero } from "../../../Unit/VoHero";
import { Player } from "db://assets/Scripts/GameData";


export class MapUnitFactory {
    /** -----------------------------------------------------------
     * NPC
     * ------------------------------------------------------------ */
    // private static _createNpc(context: FightContext, prefabPath: string, className: string, data: { x?: number; y?: number, id?: number }, callback: Function = null): Promise<Npc> {
    //     if (!data || !data.x || !data.y) return null;
    //     return new Promise<Npc>((resolve, reject) => {
    //         LoadUtils.loadPrefab(prefabPath, () => {
    //             const node: cc.Node = ViewUtil.createPrefabNode(prefabPath);
    //             node.setPosition(data.x, data.y);
    //             node.setParent(context.entityLayer);
    //             const comp = node.getComponent(className);
    //             callback && callback();
    //             callback = null;
    //             resolve(comp);
    //         });
    //     });
    // }

    // public static createNpc(layer: cc.Node, prefabPath: string, className: string, data: { x?: number; y?: number, id?: number }, callback: Function = null): Promise<Npc> {
    //     if (!data || !data.x || !data.y) return null;
    //     return new Promise<Npc>((resolve, reject) => {
    //         LoadUtils.loadPrefab(prefabPath, () => {
    //             const node: cc.Node = ViewUtil.createPrefabNode(prefabPath);
    //             node.setPosition(data.x, data.y);
    //             node.setParent(layer);
    //             const comp = node.getComponent(className);
    //             callback && callback();
    //             callback = null;
    //             resolve(comp);
    //         });
    //     });
    // }

    // /**创建建筑NPC */
    // private static async _createBuildingNpc(context: FightContext, buildingId: number, className: string) {
    //     if (NpcManager.instance.isActiveHomeNpc(buildingId) && CFG_NpcBuilding.get(buildingId).activeOperate == NpcActiveOperate.Remove) {
    //         return;
    //     }

    //     let prefabPath = CFG_NpcBuilding.get(buildingId).prefabPath;
    //     if (!prefabPath) {
    //         console.error("建筑NPC预制体路径没有配置: " + buildingId);
    //         return;
    //     }
    //     if (NpcManager.instance.hasNpc(buildingId)) {
    //         return;
    //     }

    //     prefabPath = NPC_HOME_PREFAB_PATH + prefabPath;
    //     let data = MapManager.curMap.data.getNpcBuilding(buildingId);
    //     let comp = await this._createNpc(context, prefabPath, className, data);
    //     NpcManager.instance.addHomeNpc(buildingId, comp);
    //     return comp;
    // }
    // /**创建任务NPC */
    // private static async _createTaskNpc(context: FightContext, npcId: number, className: string) {
    //     if (NpcManager.instance.isActiveTaskNpc(npcId) && CFG_SceneNpc.get(npcId).activeOperate == NpcActiveOperate.Remove) {
    //         return;
    //     }
    //     if (NpcManager.instance.hasTaskNpc(npcId)) return null;
    //     let prefabPath = NPC_TASK_PREFAB_PATH;
    //     let data = MapManager.curMap.data.getNpcTask(npcId);
    //     let comp = await this._createNpc(context, prefabPath, className, data);
    //     NpcManager.instance.addTaskNpc(npcId, comp);
    //     return comp;
    // }

    // private static async _createFogNpc(context: FightContext, npcId: number, className: string) {
    //     if (NpcManager.instance.isActiveFogNpc(npcId) && CFG_SceneFog.get(npcId).activeOperate == NpcActiveOperate.Remove) {
    //         return;
    //     }
    //     if (NpcManager.instance.hasFogNpc(npcId)) return null;
    //     let data = MapManager.curMap.data.getNpcFog(npcId);
    //     let comp = await this._createNpc(context, NPC_FOG_PREFAB_PATH, className, data);
    //     NpcManager.instance.addFogNpc(npcId, comp);
    //     return comp;
    // }

    // private static async _createDungeonNpc(context: FightContext, npcId: number, className: string) {
    //     if (NpcManager.instance.hasDungeonNpc(npcId)) return null;
    //     let data = MapManager.curMap.data.getNpcDungeon(npcId);
    //     let comp = await this._createNpc(context, NPC_DUNGEON_PREFAB_PATH, className, data);
    //     NpcManager.instance.addDungeonNpc(npcId, comp);
    //     return comp;
    // }

    // private static async _createTeleportNpc(context: FightContext, npcId: number, className: string) {
    //     if (NpcManager.instance.hasTeleportNpc(npcId)) return null;
    //     let data = MapManager.curMap.data.getTeleportPoint(npcId);
    //     let comp = await this._createNpc(context, NPC_TELEPORT_PREFAB_PATH, className, data);
    //     NpcManager.instance.addTeleportNpc(npcId, comp);
    //     return comp;
    // }

    // private static async _createCrossTeleportNpc(context: FightContext, npcId: number, className: string) {
    //     if (NpcManager.instance.hasCrossTeleportNpc(npcId)) return null;
    //     let data = MapManager.curMap.data.getCrossTeleportPoint(npcId);
    //     let comp = await this._createNpc(context, NPC_CROSS_TELEPORT_PREFAB_PATH, className, data);
    //     NpcManager.instance.addCrossTeleportNpc(npcId, comp);
    //     return comp;
    // }

    // static async createHomeNpc(context: FightContext): Promise<void> {
    //     // 酒馆
    //     this._createBuildingNpc(context, NpcHomeEnum.Recruit, "RecruitNpc");
    //     // 神坛
    //     this._createBuildingNpc(context, NpcHomeEnum.Training, "TrainingNpc");
    //     // 神像
    //     this._createBuildingNpc(context, NpcHomeEnum.Equipment, "EquipmentNpc");
    //     // 炼妖
    //     this._createBuildingNpc(context, NpcHomeEnum.Refine, "RefineNpc");
    //     // 伐木场
    //     this._createBuildingNpc(context, NpcHomeEnum.Lumberyard, "HomeLumberyardNpc");
    //     // 灵脉
    //     this._createBuildingNpc(context, NpcHomeEnum.PsychicPulse, "HomePsychicPulseNpc");
    //     // 铸币坊
    //     this._createBuildingNpc(context, NpcHomeEnum.MintShop, "HomeMintShopNpc");
    //     // 传送法阵
    //     this._createBuildingNpc(context, NpcHomeEnum.Teleport, "TeleportNpc");
    //     // 日常副本
    //     this._createBuildingNpc(context, NpcHomeEnum.DailyDup, "DailyDupNpc");
    //     //兑换商店
    //     this._createBuildingNpc(context, NpcHomeEnum.ExchangeShop, "ExchangeShopNpc");
    //     // 仓库
    //     for (let i = 1; i < 4; i++) {
    //         const comp = await this._createBuildingNpc(context, 300 + i, "HomeStorageNpc");
    //         comp?.setParams(i);
    //     }
    // }

    // // 重建NPC
    // static async createRebuildNpc(context: FightContext): Promise<void> {
    //     this._createBuildingNpc(context, NpcHomeEnum.Rebuild, "RebuildNpc");
    // }

    // static async createTaskNpc(context: FightContext): Promise<void> {
    //     for (const data of MapManager.curMap.data.getNpcTaskList()) {
    //         const { id } = data;
    //         const comp: Npc = await this._createTaskNpc(context, id, "TaskNpc");
    //         if (comp != null) {
    //             let treeData = { id: id, node: comp.node, comp: comp, type: NpcType.Task };
    //             QuadtreeManager.instance.addTaskNpcTree(data.x, data.y, data.width, data.height, treeData);
    //         }
    //         comp?.setParams(id);
    //     }
    // }

    // static async createFogNpc(context: FightContext): Promise<void> {
    //     for (const data of MapManager.curMap.data.getNpcFogList()) {
    //         const { id } = data;
    //         const comp = await this._createFogNpc(context, id, "FogNpc");
    //         if (comp != null) {
    //             let treeData = { id: id, node: comp.node, comp: comp, type: NpcType.Fog };
    //             QuadtreeManager.instance.addFogNpcTree(data.x, data.y, data.width, data.height, treeData);
    //         }
    //         comp?.setParams(id);
    //     }
    // }

    // static async createDungeonNpc(context: FightContext): Promise<void> {
    //     for (const data of MapManager.curMap.data.getNpcDungeonList()) {
    //         const { id } = data;
    //         if (!NpcManager.instance.isActiveDungeonNpc(id)) {
    //             const comp = await this._createDungeonNpc(context, id, "DungeonNpc");
    //             if (comp != null) {
    //                 let treeData = { id: id, node: comp.node, comp: comp, type: NpcType.Dungeon };
    //                 QuadtreeManager.instance.addDungeonNpcTree(data.x, data.y, data.width, data.height, treeData);
    //             }
    //             comp?.setParams(id);
    //         }
    //     }
    // }

    // static async createTeleport(context: FightContext): Promise<void> {
    //     for (const data of MapManager.curMap.data.getTeleportPointList()) {
    //         const { id } = data;
    //         const comp = await this._createTeleportNpc(context, id, "WildTelePortNpc");
    //         comp?.setParams(id);
    //     }
    // }

    // static async createCrossTeleport(context: FightContext): Promise<void> {
    //     for (const data of MapManager.curMap.data.getCrossTeleportPointList()) {
    //         const { id } = data;
    //         const comp = await this._createCrossTeleportNpc(context, id, "CrossTelePortNpc");
    //         comp?.setParams(id);
    //     }
    // }

    // static async createTownPortal(context: FightContext, x?: number, y?: number): Promise<void> {
    //     const tp1 = MapManager.curMap.data.townPortal;
    //     let tp2;
    //     if (!!x && !!y) {
    //         tp2 = cc.v2(x, y);
    //     } else {
    //         tp2 = MapManager.curMap.mainPlayer.node.getPosition();
    //     }

    //     // NpcManager.instance.TownPortalHome = await this._createNpc(context, NPC_TOWN_PORTAL_PREFAB_PATH, "TownPortalNpc", tp1);
    //     NpcManager.instance.TownPortalOutside = await this._createNpc(context, NPC_TOWN_PORTAL_PREFAB_PATH, "TownPortalNpc", { x: tp2.x, y: tp2.y + 5 });
    //     // NpcManager.instance.TownPortalHome.setParams(0);
    //     NpcManager.instance.TownPortalOutside.setParams(1);
    // }

    // static removeTownPortal(): void {
    //     // const func = (npc: Npc) => {
    //     //     npc && npc.node.removeFromParent(true); // TODO: 这里应该有个回收
    //     // };
    //     // func(NpcManager.instance.TownPortalHome);
    //     // func(NpcManager.instance.TownPortalOutside);
    //     // NpcManager.instance.TownPortalHome = null;
    //     // NpcManager.instance.TownPortalOutside = null;
    //     NpcManager.instance.clearTownPortal();
    // }
    // /** ----------------------------------------------------------- */

    /**
     * 回收实体
     * @param entity
     */
    static recycle(entity: any) { }

    // /**
    //  * 创建实体  异步创建
    //  * @param VoEntity
    //  */
    // static createAsync(voEntity: VoEntity, context: FightContext, complete: Function, caller: any) {
    //     let entity = null;
    //     switch (voEntity.unitType) {
    //         case UnitType.Monster:
    //             this.createMonsterAsync(voEntity, context, complete, caller);
    //             break;
    //         case UnitType.Hero:
    //             this.createHeroAsync(voEntity, context, complete, caller);
    //             break;
    //         case UnitType.Tree:
    //             this.createTreeAsync(voEntity, context, complete, caller);
    //             break;

    //         default:
    //             console.error("MapUnitFactory.createAsync 未知的单位类型", voEntity.unitType);
    //             break;
    //     }
    //     if (entity) {
    //         entity.context = context;
    //         entity.data = voEntity;
    //         entity.identity = voEntity.identity * 10;
    //     }
    // }

    // static createMonsterAsync(voEntity: VoEntity, context: FightContext, complete: Function, caller: any) {
    //     let data: VoMonster = voEntity as VoMonster;
    //     let path = data.identity + "/" + data.identity;
    //     let bundle = Bundles.MODEL;
    //     const animArr: UnitAnim[] = [UnitAnim.attack, UnitAnim.skill01, UnitAnim.skill02];
    //     // Logger.trace("create Monster 1>", voEntity.unitId, Date.now());

    //     let onMonsterLoaded = () => {
    //         if (!context || !context.entityLayer) {
    //             complete && complete.apply(caller, [null]);
    //             return;
    //         }
    //         let entityPref = LoadUtils.get(bundle + path, cc.Prefab) as cc.Prefab;
    //         // Logger.trace("create Monster 2>", voEntity.unitId, Date.now());
    //         if (entityPref) {

    //             let prefNode: cc.Node = ViewUtil.createPrefabNode(bundle + path, bundle)
    //             let playerScript: Player = prefNode.getComponent(Player);

    //             if (playerScript) {
    //                 // playerScript.enabled = false;
    //                 let anim = playerScript.anim;
    //                 let skin = playerScript.skin;
    //                 let anim_back = playerScript.anim_back;
    //                 let anim_front = playerScript.anim_front;
    //                 let moveSpeed = playerScript.moveSpeed;
    //                 let body = playerScript.body;
    //                 prefNode.removeComponent(Player);
    //                 let monsterScript = prefNode.addComponent(Monster);
    //                 monsterScript.anim = anim;
    //                 monsterScript.skin = skin;
    //                 monsterScript.anim_back = anim_back;
    //                 monsterScript.anim_front = anim_front;
    //                 monsterScript.moveSpeed = moveSpeed;
    //                 monsterScript.body = body;

    //             }

    //             let monster = prefNode.getComponent(Monster);
    //             monster.data = voEntity;
    //             monster.scale = data.scale / 100;
    //             monster.node.parent = context.entityLayer;
    //             monster.context = context;
    //             monster.node.name = "M:" + voEntity.unitId + ">" + data.rebornPosX + "." + data.rebornPosY;
    //             monster.unitType = data.unitType
    //             // 技能


    //             data.x = data.rebornPosX;
    //             data.y = data.rebornPosY;
    //             monster.node.x = data.rebornPosX;
    //             monster.node.y = data.rebornPosY;
    //             monster.refreshHp();

    //             // Logger.trace("create Monster 3>", voEntity.unitId, Date.now());
    //             complete && complete.apply(caller, [monster]);
    //             let skills = data.cfgMonster.getSkill();
    //             for (let i = 0; i < skills.length; i++) {
    //                 let table = VoSkill.create();
    //                 table.init(skills[i].id, skills[i].level);
    //                 monster.skillLearned && monster.skillLearned.skills.push(table);
    //             }
    //         }
    //     }

    //     let prefab = LoadUtils.get(bundle + path, cc.Prefab);
    //     if (prefab && prefab.isValid) {
    //         onMonsterLoaded();
    //     } else {
    //         LoadUtils.loadPrefab(bundle + path, onMonsterLoaded.bind(this));
    //         //oops.res.loadAsync(bundle, path, cc.Prefab, null, onMonsterLoaded.bind(this));
    //     }
    // }

    static createHeroAsync(voEntity: VoHero, mapInst: MapInstance) {
        return new Promise<PlayerActor>((resolve, reject) => {

            let data: VoHero = voEntity;
            const animArr: UnitAnim[] = [UnitAnim.attack, UnitAnim.skill01, UnitAnim.skill02];

            let path = data.identity + "/" + data.identity;
            let bundleName = Bundles.MODEL;

            Logger.trace("开始加载英雄" + path + " " + bundleName);

            let onHeroLoaded = () => {
                Logger.trace("英雄加载完成" + path + " " + bundleName);
                let prefNode: cc.Node = ViewUtil.createPrefabNode(bundleName + path, bundleName);

                // let monsterScript: Monster = prefNode.getComponent(Monster)
                // if (monsterScript) {
                //     let anim = monsterScript.anim;
                //     let skin = monsterScript.skin;
                //     let anim_back = monsterScript.anim_back;
                //     let anim_front = monsterScript.anim_front;
                //     let body = monsterScript.body;
                //     let moveSpeed = monsterScript.moveSpeed;

                //     prefNode.removeComponent(Monster);
                //     monsterScript.enabled = false;
                //     let monsterScript = prefNode.addComponent(Player)

                //     monsterScript.anim = anim;
                //     monsterScript.skin = skin;
                //     monsterScript.anim_back = anim_back;
                //     monsterScript.anim_front = anim_front;
                //     monsterScript.body = body;
                //     monsterScript.moveSpeed = moveSpeed;
                // }

                let hero: PlayerActor = prefNode.getComponent(PlayerActor);
                hero.setEnv(mapInst);
                hero.data = data;
                // hero.initSkill();

                // let skills = data.getSkills();
                // for (let i = 0; i < skills.length; i++) {
                //     let table = VoSkill.create();
                //     table.init(skills[i].id, skills[i].level);
                //     hero.skillLearned && hero.skillLearned.skills.push(table);
                // }

                prefNode.name = "H:" + data.name + ">" + data.identity;
                hero.scale = data.scale / 100;
                mapInst.unitLayer.addChild(prefNode);

                hero.node.x = data.rebornPosX;
                hero.node.y = data.rebornPosY;
                data.x = data.rebornPosX;
                data.x = data.rebornPosY;
                // hero.refreshHp();
                resolve(hero);
            }
            let prefab = LoadUtils.get(bundleName + path, cc.Prefab);
            if (prefab && prefab.isValid) {
                onHeroLoaded();
            } else {
                // oops.res.loadAsync(bundleName, path, cc.Prefab, null, onHeroLoaded.bind(this));
                LoadUtils.loadPrefab(bundleName + path, onHeroLoaded.bind(this));
            }
        });
    }

    // static createTreeAsync(voEntity: VoEntity, context: FightContext, complete: Function, caller: any) {
    //     let data: VoHero = voEntity as VoHero;
    //     let path = "" + data.identity;
    //     let bundle = Bundles.TREE;

    //     let onTreeLoaded = () => {
    //         if (!context || !context.entityLayer) {
    //             complete && complete.apply(caller, [null]);
    //             return;
    //         }
    //         let entityPref = LoadUtils.get(bundle + path, cc.Prefab);
    //         if (entityPref) {

    //             let prefNode: cc.Node = ViewUtil.createPrefabNode(bundle + path, bundle);
    //             let tree: Tree = prefNode.getComponent(Tree);
    //             tree.data = voEntity;
    //             tree.scale = data.scale / 100;
    //             tree.node.parent = context.entityLayer;
    //             tree.node.name = "T:" + tree.data.unitId + ">" + data.rebornPosX + "." + data.rebornPosY;
    //             tree.context = context;
    //             let cfg = CFG_Harvest.get(data.idExcel)
    //             tree.type = cfg.type;
    //             data.x = data.rebornPosX;
    //             data.y = data.rebornPosY;
    //             tree.node.x = data.rebornPosX;
    //             tree.node.y = data.rebornPosY;
    //             tree.refreshHp();

    //             complete && complete.apply(caller, [tree]);
    //         }
    //     }
    //     let prefab = LoadUtils.get(bundle + path, cc.Prefab);
    //     if (prefab && prefab.isValid) {
    //         onTreeLoaded();
    //     } else {
    //         LoadUtils.loadPrefab(bundle + path, onTreeLoaded.bind(this));
    //     }
    // }


    /** -----------------------------------------------------------
     * Tiled Map Polygon
     * ------------------------------------------------------------ */

    public static regTiledMapRigidBody(data: number[][]) {
        const node = new cc.Node();
        node.name = "ImpassableArea";
        node.layer = GameLayer.wall;
        node.active = false;

        const rigidBody = node.addComponent(cc.RigidBody2D);
        rigidBody.type = cc.ERigidBody2DType.Static;
        rigidBody.allowSleep = true;
        rigidBody.wakeUp();

        const polygonCollider = node.addComponent(cc.PolygonCollider2D);
        polygonCollider.points = POLYGON_POINTS;
        polygonCollider.friction = MAP_FRICTION;
        polygonCollider.apply(); // 应用碰撞器

        // PoolEdgeRigidBody.regPool("", node, PoolPreloadNum.EdgeRigidBody);
    }


    public static regTiledMapPolygon(data: { [key: string]: ITiledMapPolygon }) {
        if (!data) return;
        for (const gid in data) {
            const element = data[gid];
            const { width, height, points } = element;

            const node = new cc.Node();
            node.name = "Polygon";
            node.layer = GameLayer.wall;
            node.active = false;

            const rigidBody = node.addComponent(cc.RigidBody2D);
            rigidBody.type = cc.ERigidBody2DType.Static;
            rigidBody.allowSleep = true;
            rigidBody.wakeUp();

            const _points = [];
            points.forEach((pos) => {
                _points.push(cc.v2(pos[0], pos[1]));
            });
            _points.push(cc.v2(points[0][0], points[0][1]));

            const polygonCollider = node.addComponent(cc.PolygonCollider2D);
            polygonCollider.points = _points;
            polygonCollider.friction = MAP_FRICTION;
            polygonCollider.apply();


            PoolTiledMapPolygon.regPool(gid, node, PoolPreloadNum);
        }
    }


    /** ----------------------------------------------------------- */
}
