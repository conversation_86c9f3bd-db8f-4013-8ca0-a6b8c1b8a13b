import * as cc from "cc";

import { UnitType } from "../../../../Config/GameDefine";
import { MapEvent, GameEvent } from "../../../../GameEvent/Events";
import { ViewId } from "../../../../Config/ViewConst";
import SceneZoneCache from "../../../../Config/GameCfgExt/SceneZone";
import CFG_SceneZone from "../../../../Config/GameCfg/CFG_SceneZone";
import Logger from "../../../../Framework/Logger";
import { LoadUtils } from "../../../Utils/LoadUtils";
import { ViewUtil } from "../../../Utils/ViewUtils";
import CameraController from "../../Camera/CameraManager";
import ResSprite from "../../../Components/ResSprite";
import { Bound } from "../../Fight/Bound";
// import { FightContext } from "../../Module/Fight/FightContext";
import { GameObject } from "../../Fight/GameObject";
// import { EntityManager, IEntityUpdate } from "../../Module/Fight/manager/EntityManager";
// import { HeroManager } from "../../game/modules/fight/manager/HeroManager";
import MapRoadUtils from "../../Map/road/MapRoadUtils";
import PathFindingAgent from "../../Map/road/PathFindingAgent";
import Point from "../../Map/road/Point";
import MapParser from "../../Map/tiled/MapParser";
import { TileMapData } from "../../Map/tiled/TileMapData";
import TiledUtils from "../../Map/tiled/TiledUtils";
import { VoEntity } from "../../../Unit/VoEntity";
import { VoUnit } from "../../../Unit/VoUnit";
import { IUnit } from "../../../Unit/Base/IUnit";
// import NpcManager from "../npc/NpcManager";
// import QuadtreeManager from "../quadtree/QuadtreeManager";
// import FogNodePool from "./FogNodePool";
import { MapAreaType, MapConfigType, TILE_SPLIT_NUM } from "./MapDefine";
import MapManager from "../Managers/MapManager";
import { MapUnitFactory } from "./MapUnitFactory";
import EventManagerComponent from "../../../GlobalComponent/EventManagerComponent";
import Timer from "../../../Utils/Timer";
import ResManager from "../../Loading/Managers/ResManager";
import AppManager from "../../../Managers/AppManager";
import ViewManager from "../../../GlobalComponent/ViewManagerComponent";
import LayerManagerComponent from "../../../GlobalComponent/LayerManagerComponent";

/**
 * 地图基类. <加载场景资源, 初始化配置>
 *
 */
// export class MapBase implements IEntityUpdate {
export class MapBase {
    static assetMap = {}
    protected _mapConfig: string;
    /**地图id */
    protected _mapId: number;

    /**区域Id */
    protected _zoneId: number = 1;

    /** 跟服务端交互的进入战斗场景的id */
    protected _battleSceneId: number;

    /**是否初始化 */
    protected _isInited: boolean = false;

    /**地图配置数据 */
    protected _config: CFG_SceneZone;

    protected _mapName: string;

    protected _data: TileMapData;

    protected parser: MapParser;

    protected running: boolean = false;

    /**当前地图正在显示的单位 */
    protected dataArray: Array<VoEntity>;
    /**当前正在显示的单位 */
    protected entities: IUnit[];
    /**容器 */
    protected _container: cc.Node;
    /**地图层 */
    protected _mapLayer: cc.Node;
    /**对象层 */
    protected _unitLayer: cc.Node;

    protected _skillBottomLayer: cc.Node;

    protected _skillTopLayer: cc.Node;

    protected _fogLayer: cc.Node;

    protected _topUILayer: cc.Node;

    protected _roadLayer: cc.Node;

    // protected context: FightContext;

    /**临时用的坐标对象 */
    protected tempPoint: cc.Vec3 = new cc.Vec3();

    /** 摄像机的窗口 */
    protected cameraViewPort: Bound = new Bound;

    /** 是否需要更新4叉树范围  */
    protected cameraViewPortUpdateFlag: boolean = false;

    /** 团队视野窗口 */
    protected teamViewPort: Bound = new Bound();

    protected bundleNames: string[];

    protected _isPause: boolean = false;

    /** 当前所在的区域  */
    protected _curRegionId: number = 0;

    /** 是否是普通跨图  */
    protected _crossFlag: number = -1;

    constructor() {
        this.init();
    }

    /**
     * 初始化层级
     */
    protected init(): void {
        this.entities = [];
        this.dataArray = [];
        this.parser = new MapParser();
        this._data = new TileMapData();
        this._container = this.createLayer("container", null, 0, 0);
        const winSize = cc.view.getDesignResolutionSize();
        this._container.x = - Math.floor(winSize.width / 2);
        this._container.y = - Math.floor(winSize.height / 2);

        this._mapLayer = this.createLayer("mapLayer", this._container, 0.5, 0.5);
        this._roadLayer = this.createLayer("roadLayer", this._container, 0.5, 0.5);
        this._skillBottomLayer = this.createLayer("skillBottomLayer", this._container);
        this._unitLayer = this.createLayer("unitLayer", this._container, 0.5, 0.5);
        this._skillTopLayer = this.createLayer("skillTopLayer", this._container, 0, 0);
        this._fogLayer = this.createLayer("fogLayer", this._container, 0.5, 0.5);
        this._topUILayer = this.createLayer("topUILayer", this._container, 0.5, 0.5);
    }

    private createLayer(name: string, parent?: cc.Node, anchorX: number = 0, anchorY: number = 0): cc.Node {
        let node: cc.Node = new cc.Node(name);
        const transform = node.addComponent(cc.UITransform);
        transform.setAnchorPoint(anchorX, anchorY);
        parent && parent.addChild(node);
        return node;
    }

    getContainer(): cc.Node {
        return this._container;
    }

    public get skillBottomLayer(): cc.Node {
        return this._skillBottomLayer;
    }
    public get skillTopLayer(): cc.Node {
        return this._skillTopLayer;
    }
    public get entityLayer(): cc.Node {
        return this._unitLayer;
    }
    public get mapLayer(): cc.Node {
        return this._mapLayer;
    }
    public get fogLayer(): cc.Node {
        return this._fogLayer;
    }
    public get roadLayer(): cc.Node {
        return this._roadLayer;
    }
    public get topUILayer(): cc.Node {
        return this._topUILayer;
    }

    /**
     * 地图上的显示单位
     */
    get units(): IUnit[] {
        return this.entities;
    }

    /**
     * 关闭地图
     *
     */
    stop() {
        if (this._data) {
            this._data.destroy();
            this._data = null;
        }
        if (this.parser) {
            this.parser.destroy();
            this.parser = null;
        }
        // if (this.context) {
        //     this.context.destroy();
        //     this.context = null;
        // }

        this.dataArray.length = 0;
        this.entities.length = 0;
        // NpcManager.instance.clear();
        // EntityManager.instance.clear();
        // HeroManager.instance.clear();
        if (this._container && this._container.children.length) {
            this._container.removeAllChildren();
            this._container.removeFromParent();
            this._container.destroy();
            this._container = null;
        }
    }

    /**
     * 显示地图
     *
     */
    start() {
        if (this._isInited) {
            this.initMap();
        } else {
            this.loadMap();
        }
    }

    /** 
     * 暂停场景
     */
    pause() {
        this._isPause = true;
    }

    /**
     * 恢复暂停的场景
     * 
     */
    resume() {
        if (this._isPause) {
            this._isPause = false;
        }
    }

    /**
     * 是否暂停中
     * @returns 
     */
    isPause(): boolean {
        return this._isPause;
    }


    /**
     * 地图数据
     */
    public get data(): TileMapData {
        return this._data;
    }

    public get mapConfig(): string {
        return this._mapConfig;
    }

    /**
     * 地图Id
     */
    set mapId(mapId: number) {
        this._mapId = mapId;
        this.reloadConfig();
    }

    get mapId(): any {
        return this._mapId;
    }

    // /**
    //  * 主线地图Id
    //  */
    // set mainMapId(mainMapId: number) {
    //     this._mainMapId = mainMapId;
    // }

    // get mainMapId(): any {
    //     return this._mainMapId;
    // }

    /**
     * 是否是普通跨图传送
     */
    set crossFlag(crossFlag: number) {
        this._crossFlag = crossFlag;
    }

    get crossFlag(): any {
        return this._crossFlag;
    }

    set battleSceneId(sceneId: number) {
        this._battleSceneId = sceneId;
    }

    get battleSceneId(): number {
        return this._battleSceneId;
    }

    /**
     * 区域id
     */
    get zoneId(): number {
        return this._zoneId;
    }

    set zoneId(value: number) {
        this._zoneId = value;
        this.reloadConfig();
    }

    protected reloadConfig() {
        if (this._mapId > 0 && this._zoneId > 0) {
            this._config = SceneZoneCache.getData(this._mapId, this._zoneId);
            if (this._config && this._config.mapResource) {
                let resStrs = this._config.mapResource.split("#");
                let temp = resStrs[0].split("_");
                this._mapConfig = "map/" + temp[1];
            }
            else {
                console.error("MapBase的mapResource报错", "_mapId=" + this._mapId, "_zoneId=" + this._zoneId)
            }
        }
    }

    public clearTiledMap() {
        let children = this.mapLayer.children;
        for (let i = 0, n = children.length; i < n; i++) {
            let node = children[i];
            ViewUtil.destroyNode(node);
        }

        if (this.bundleNames && this.bundleNames.length > 0) {
            let bundleName = this.bundleNames[0];
            let temp = bundleName.split("_");
            let bundlePath = "map/" + bundleName + "/" + temp[1];
            LoadUtils.releaseAsset(bundlePath);
            Logger.trace("release:" + bundlePath + " 成功");
            this.bundleNames.length = 0;
            this.bundleNames = null;
        }
        this.mapLayer?.removeAllChildren();
        this.fogLayer?.removeAllChildren();
    }

    public loadMapConfig(): void {
        Logger.zz_log("zhangzhen_解析地图 begin:" + Date.now());
        const data = AppManager.instance().ResManager.readConfigFile(this.mapConfig);
        this.data.setImpassableArea(data[MapConfigType.IMPASSABLE_AREA]);
        this.data.setSafeArea(data[MapConfigType.SAFE_AREA]);
        this.data.setOutsideArea(data[MapConfigType.OUTSIDE_AREA]);

        this.data.setPolygonTile(data[MapConfigType.POLYGON_TILE]);
        this.data.setPolygonLayer(data[MapConfigType.POLYGON_LAYER]);
        this.data.setPolygonObject(data[MapConfigType.POLYGON_OBJECT]);
        this.data.setFogRectArea(data[MapConfigType.FOG_RECT]);
        this.data.setRegionData(data[MapConfigType.REGION_RECT]);
        this.data.setFogImgPosData(data[MapConfigType.FOG_POS_IMG_LIST]);
        this.data.setAtlasIdData(data[MapConfigType.ATLAS_ID_LIST]);
        MapUnitFactory.regTiledMapPolygon(data[MapConfigType.POLYGON_TILE]);
        MapUnitFactory.regTiledMapRigidBody(data[MapConfigType.IMPASSABLE_AREA]);
    }

    private nodeCount = 0;
    public createFogArea(fog_id: number): void {
        this.nodeCount = 0
        const fogPosImgList = this.data.getFogImgPosData(fog_id);
        if (fogPosImgList == undefined)
            return;
        if (PathFindingAgent.instance.getMapId() != this.mapId) {
            console.info(">>>当前路点地图:", PathFindingAgent.instance.getMapId()), this.mapId;
            return;
        }

        let _layerNode: cc.Node = MapManager.curMap.fogLayer.getChildByName("fogArea_" + fog_id);
       
        let imgId = 0;
        for (var i: number = 0; i < fogPosImgList.length; i++) {
            if (this.data.getAtlasIdData(fogPosImgList[i][2])) {
                imgId = fogPosImgList[i][2]-1;
                break;
            }
        } 
        
        if (_layerNode.children.length > 0) return;
        for (var i: number = 0; i < fogPosImgList.length; i++) {
            let _data = fogPosImgList[i];
            let _realPos: Point = MapRoadUtils.instance.getPixelByWorldPoint(_data[0], _data[1]);
            // TODO: 处理迷雾
            // let _node: cc.Node = FogNodePool.instance.createNode();
            // let _resSprite: ResSprite = _node.getComponent(ResSprite);
            // //let _resName: string = "fog_00" + (_data[2] - 433).toString();
            // let _resName: string = "fog_00" + (_data[2] - imgId).toString();
            // _resSprite.setSpriteFrame("mapThing/fog/fog", _resName);
            // _node.parent = _layerNode;
            // _node.x = _realPos.x;
            // _node.y = _realPos.y+85;
            // if (_resName == "fog_006") {
            //     _node.x += 25;
            // } else if (_resName == "fog_007") {
            //     _node.x -= 25;
            // } else if (_resName == "fog_003") {
            //     _node.y -= 10;
            // } else if (_resName == "fog_005") {
            //     _node.y += 10;
            // }
            // this.nodeCount++;
        }
    }

    /**
     * 加载地图. 多区域地图重写此方法
     *
     */
    protected loadMap() {
        EventManagerComponent.instance().addEventListener(MapEvent.START_MAP_LOAD, this._loadMap, this);
        if (this.zoneId <= 1) {
            ViewManager.instance().show(ViewId.MAP_LOADING, null, false, LayerManagerComponent.instance().getNetLayer());
        } else {
            EventManagerComponent.instance().dispatch(MapEvent.START_MAP_LOAD, null);
        }
    }

    protected _loadMap() {
        Logger.zz_log("zhangzhen__loadMap:" + Date.now());
        EventManagerComponent.instance().removeEventListener(MapEvent.START_MAP_LOAD, this._loadMap, this);

        this.clearTiledMap();
        this.loadMapConfig();
        if (this._config && this._config.mapResource) this.loadMapBundle(this._config.mapResource);
    }

    protected loadMapBundle(res: string) {
        this.bundleNames = res.split("#");
        Logger.zz_log("zhangzhen_loadMapBundle" + this.bundleNames + ":" + + Date.now());
        let name = this.bundleNames[0];
        let temp = name.split("_");

        let bundlePath = "map/" + name + "/" + temp[1];
        LoadUtils.load(bundlePath, cc.TiledMapAsset,
            (asset: cc.TiledMapAsset) => {
                this._loadComplete(asset);
                MapBase.assetMap[name] = Object.assign({}, asset);
                asset.addRef();
            },
            (percent: number) => {
                EventManagerComponent.instance().dispatch(MapEvent.LOAD_MAP_PROGRESS, {
                    percent: percent,
                    totalCount: this.bundleNames.length,
                });
            });
    }

    protected _loadComplete(asset: cc.TiledMapAsset) {
        Logger.zz_log("zhangzhen__loadComplete_begin:" + Date.now())
        Logger.trace("_loadMap " + Date.now());

        const root = this.mapLayer;
        if (!root) {
            console.error("场景未设置TileMap组件.mapTmx");
            return;
        }
        let index = 0;

        Logger.zz_log("zhangzhen__loadComplete_000:" + Date.now())
        // MapLoader.parseTmx(asset);
        Logger.zz_log("zhangzhen__loadComplete_111:" + Date.now())
        const node = new cc.Node();
        node.name = "TiledMap";
        const transform = node.addComponent(cc.UITransform);
        root.addChild(node);
        console.log("root add node", root.name);

        const map = node.addComponent(cc.TiledMap);
        Logger.zz_log("zhangzhen__loadComplete_222:" + Date.now())
        map.tmxAsset = asset;
        map.enabled = true;
        map.enableCulling = true;
        Logger.zz_log("zhangzhen__loadComplete_333:" + Date.now())
        this.parser.parse(map, this._data);
        // for (const node of root.children) {
        //     node.x = this._data.width / 2;
        //     node.y = this._data.height / 2;
        // }
        Logger.zz_log("zhangzhen__loadComplete_444:" + Date.now())
        this.onDecodeComplete();
        EventManagerComponent.instance().dispatch(
            MapEvent.Map_INIT_COMPLETE,
            null,
        );

        // if (App.UserController.lastLeaveVillagePos) {
        //     let pos = new cc.Vec3(App.UserController.lastLeaveVillagePos.x, App.UserController.lastLeaveVillagePos.y);
        //     this._curRegionId = this.data.getRegionId(pos.x, pos.y);
        //     MapManager.curMap.requestZoneInfoEx(this._curRegionId);

        // }


        Logger.zz_log("zhangzhen__loadComplete_end:" + Date.now())
    }

    /**
     * 解析地图完成
     *
     */
    protected onDecodeComplete() {
        this._isInited = true;
        this.data.mapAreaStatus = this.calcMapAreaStatus();
        // EntityManager.instance.initSize(this.data.width, this.data.height, this);

        this.initBGM();

        this.initMap();

        Timer.once(200, this, () => {
            // 残留的回城传送门
            this.parseRigidBodyEdgeFog();
            this.parseRigidBodyEdgeImpassable();
            // App.UserController.checkTownPortal();
        })
        // QuadtreeManager.instance.clearEdgeRigidBodyTree();
        // QuadtreeManager.instance.clearEdgeFogTree();
        cc.game.emit(MapEvent.LOAD_MAP_COMPLETE, this.mapId);
        EventManagerComponent.instance().dispatch(MapEvent.LOAD_MAP_COMPLETE, {
            mapId: this.mapId,
        });
    }

    /** 解析不可行走区域轮廓刚体 */
    protected parseRigidBodyEdgeImpassable(): void {
        const data: number[][] = AppManager.instance().ResManager.readConfigFile(this.mapConfig)[MapConfigType.EDGE_IMPASSABLE_AREA];
        if (data == null) {
            return;
        }
        var count: number = 1;
        for (const pos of data) {
            const x = pos[0];
            const y = pos[1];
            const roadNode = MapRoadUtils.instance.getNodeByWorldPoint(x * TILE_SPLIT_NUM, y * TILE_SPLIT_NUM);
            // QuadtreeManager.instance.addEdgeRigidBodyTree(roadNode.px, roadNode.py, this.data.tileWidth * TILE_SPLIT_NUM, this.data.tileHeight * TILE_SPLIT_NUM, count, cc.Color.YELLOW);
            count++;
        }
    }

    /** 解析迷雾区域轮廓刚体 */
    public parseRigidBodyEdgeFog(): void {
        let count = 0;
        const data: { [key in string]: number[][] } = AppManager.instance().ResManager.readConfigFile(this.mapConfig)[MapConfigType.EDGE_FOG_AREA];
        for (const key in data) {
            if (key && key.length > 0) {
                const fogId = Number(key);
                // if (NpcManager.instance.isActiveFogNpc(fogId)) {
                //     continue;
                // }
                const element = data[key];
                for (const pos of element) {
                    const x = pos[0];
                    const y = pos[1];
                    const roadNode = MapRoadUtils.instance.getNodeByWorldPoint(x * TILE_SPLIT_NUM, y * TILE_SPLIT_NUM);
                    // QuadtreeManager.instance.addEdgeFogTree(roadNode.px, roadNode.py, this.data.tileWidth * TILE_SPLIT_NUM, this.data.tileHeight * TILE_SPLIT_NUM, cc.Color.YELLOW);
                    count++;
                }
            }
        }
    }

    /** 计算地图各个区域 */
    public calcMapAreaStatus(): number[][] {
        let ret = TiledUtils.makeArray2WithZero(this.data.mapWidth, this.data.mapHeight);
        const imPassableArea = this.data.impassableArea;
        const safeArea = this.data.safeArea;
        const outsideArea = this.data.outsideArea;
        const fogArea = this.calcFogArea();

        ret = TiledUtils.AssignValueToMatrix(ret, safeArea, MapAreaType.SAFE_AREA);
        ret = TiledUtils.AssignValueToMatrix(ret, outsideArea, MapAreaType.OUTSIDE_AREA);
        ret = TiledUtils.AssignValueToMatrix(ret, fogArea, MapAreaType.FOG_AREA);
        ret = TiledUtils.AssignValueToMatrix(ret, imPassableArea, MapAreaType.IMPASSABLE_AREA);
        return ret;
    }

    /** 计算迷雾区域  */
    public calcFogArea(): number[][] {
        let ret = TiledUtils.makeArray2WithZero(this.data.mapWidth, this.data.mapHeight);
        const unActivatedList = this.data.getNonactivatedList();
        for (let i = 0, n = unActivatedList.length; i < n; i++) {
            const id = unActivatedList[i];
            const matrix = this.data.getFogArea(id).matrix;
            ret = TiledUtils.mergerMatrixWithZeroOne(ret, matrix);
        }
        return ret;
    }

    /** 计算地图可通行区域  */
    public calcPassableArea(): number[][] {
        let fogArea = this.calcFogArea();
        fogArea = TiledUtils.mergerMatrixWithZeroOne(fogArea, this.data.impassableArea);
        return fogArea;
    }

    /**
     * 初始化地图
     *
     */
    protected initMap() { }

    /**
     * 初始化背景声音
     */
    protected initBGM() {
    }

    /**
     * 检测存活
     * @param data
     * @returns
     */
    protected checkAlive(data: VoEntity): boolean {

        if (data instanceof VoUnit) {
            let time: number = AppManager.instance().TimeManager.serverTimeMs;
            if (data.rebornTime < 0 || time < data.rebornTime)
                return false;
        }

        return true;
    }

    /**在显示范围内创建单位 */
    protected addUnit(voEntity: VoEntity) {
        let index: number = this.dataArray.indexOf(voEntity);
        if (index == -1 && this.checkAlive(voEntity)) {
            this.dataArray.push(voEntity);
            // TODO: 战斗单位
            // MapUnitFactory.createAsync(
            //     voEntity,
            //     this.context,
            //     (entity) => {
            //         if (entity) {
            //             if (this.dataArray.indexOf(voEntity) > -1) {
            //                 this.entities.push(entity);
            //                 this.addUnitAfter(entity, voEntity);
            //             } else {
            //                 //加载完时，已经被删除了.
            //                 entity.removeSelf();
            //                 MapUnitFactory.recycle(entity);
            //                 index = this.entities.indexOf(entity);
            //                 if (index > -1) this.entities.splice(index, 1);
            //                 this.removeUnitAfter(entity, voEntity);
            //                 console.warn("加载完时，已经被删除了", voEntity.unitId);
            //             }
            //         } else {
            //             console.error("addUnits 未找到Entity显示对象", voEntity.unitId);
            //         }
            //     },
            //     this,
            // );
        }
    }

    /** 获取实体数据对应的显示对象 */
    protected getEntity(voEntity: VoEntity): IUnit {
        for (let i = 0, n = this.entities.length; i < n; i++) {
            // @ts-ignore
            if (this.entities[i].data == voEntity) {
                return this.entities[i];
            }
        }
        return null;
    }

    /** 不在范围内移除单位  注意未从总数据中移除 */
    public removeUnit(voEntity: VoEntity) {
        let index: number = this.dataArray.indexOf(voEntity);
        if (index > -1) {
            this.dataArray.splice(index, 1);
            let entity: IUnit = this.getEntity(voEntity);
            if (entity) {
                entity.removeSelf();
                MapUnitFactory.recycle(entity);
                index = this.entities.indexOf(entity);
                if (index > -1) this.entities.splice(index, 1);
                this.removeUnitAfter(entity, voEntity);
                EventManagerComponent.instance().dispatch(GameEvent.REMOVE_UNIT, null);
            } else {
                console.warn("removeUnits 未找到Entity显示对象", voEntity.unitId);
            }
        }
    }

    /**
     * 排序所有显示节点
     * @param nodes
     */
    protected sortAllEntitys(nodes: cc.Node[]) {
        nodes.sort((node1: cc.Node, node2: cc.Node): number => {
            if (node1.y > node2.y) {
                return -1;
            } else if (node1.y < node2.y) {
                return 1;
            }
            return 0;
        });
    }

    /**
     * 永远删除一个对象.
     * @param data 
     */
    public deleteForever(data: VoEntity) {
        if (!data) return;

        this.removeEntity(data);

        this.removeUnit(data);
    }

    onEntityUpdate(invalidList: any[], validList: any[]) {
        invalidList.forEach((entity) => {
            if (entity instanceof VoEntity) {
                if (entity.unitType != UnitType.Hero) {
                    this.removeUnit(entity);
                }
            }
            else if (entity instanceof GameObject) {
                entity.active = false;
            }
        });

        validList.forEach((entity) => {
            if (entity instanceof VoEntity) {
                this.addUnit(entity);
            }
            else if (entity instanceof GameObject) {
                entity.active = true;
            }
        })
    }

    /** 村庄是否解锁 */
    protected isUnlockVillage(): boolean {
        return false;
    }

    /**移除完显示对象之后.  方便重写维护 */
    protected removeUnitAfter(entity: IUnit, voEntity: VoEntity) { }

    /**添加完显示对象之后.  方便重写维护 */
    protected addUnitAfter(entity: IUnit, voEntity: VoEntity) { }

    addEntity(entity: GameObject) {
        // EntityManager.instance.add(entity);

    }

    removeEntity(entity: GameObject) {
        // EntityManager.instance.remove(entity);
    }

    zIndexRefreshTime: number = 0;
    private updateEntityNodeZIndex(dt: number) {
        this.zIndexRefreshTime += dt;
        if (this.zIndexRefreshTime < 0.5) {
            return;
        }
        this.zIndexRefreshTime = 0;
        //调整层级
        var allEntityNodes: cc.Node[] = this._unitLayer.children.slice();
        var entiryCount: number = allEntityNodes.length;

        for (var i = 0; i < entiryCount; i++) {
            // setNodeZIndex(
            //     allEntityNodes[i],
            //     -allEntityNodes[i].y,
            // )
        }
    }

    /**
     *
     * @param dt
     */
    public update(dt: number) {
        this.updateTeamViewPort();

        this.updateCameraViewPort();

        this.updateEntityNodeZIndex(dt);
    }

    //更新英雄的视野范围.
    private updateTeamViewPort() {
        // HeroManager.instance.getTeamCenterPoint(this.tempPoint);
        this.tempPoint.x = this.tempPoint.x - this.teamViewPort.width / 2;
        this.tempPoint.y = this.tempPoint.y - this.teamViewPort.height / 2;
        if (this.tempPoint.x != this.teamViewPort.x || this.tempPoint.y != this.teamViewPort.y) {
            //视野变化
        }
        this.teamViewPort.x = this.tempPoint.x;
        this.teamViewPort.y = this.tempPoint.y;
        // EntityManager.instance.setTeamBound(this.teamViewPort);
    }

    //更新摄像机视野范围
    private updateCameraViewPort() {
        CameraController.instance.getCenterPosition(this.tempPoint);
        this.tempPoint.x = this.tempPoint.x - this.cameraViewPort.width / 2;
        this.tempPoint.y = this.tempPoint.y - this.cameraViewPort.height / 2;
        if (this.tempPoint.x != this.cameraViewPort.x || this.tempPoint.y != this.cameraViewPort.y) {
            //视野变化
        }
        this.cameraViewPort.x = this.tempPoint.x;
        this.cameraViewPort.y = this.tempPoint.y;
        // EntityManager.instance.setCameraBound(this.cameraViewPort);
    }

    private _update() {
        if (!this.isPause()) {
            this.update(cc.game.deltaTime);
        }
    }

    /**唤醒 */
    public awake() {
        if (!this.running) {
            this.running = true;
            Timer.frameLoop(10, this, this._update);
        }
    }

    /**休眠 */
    public sleep() {
        if (this.running) {
            this.running = false;
            Timer.clear(this, this._update);
        }
    }

    /** 进入场景完成后的处理  */
    protected handlerEnterWorldSuccess(): void {
        this.parser.parseSurface();
    }
}
