import * as cc from "cc";

import PoolBase from "../../Pool/PoolBase";

const POOL_RESOURCE_NAME = "Polygon";
export const PoolPreloadNum = 20;

export default class PoolTiledMapPolygon extends PoolBase {
    public static getName(content: string): string {
        return POOL_RESOURCE_NAME + content;
    }

    public static regPool(gid: string, prefab: cc.Node, size: number): void {
        const name = this.getName(gid);
        return super.regPool(name, prefab, size);
    }

    public static getNode(gid: string): cc.Node {
        const name = this.getName(gid);
        return super.getNode(name);
    }

    public static returnPool(gid: string, node: cc.Node) {
        const name = this.getName(gid);
        return super.returnPool(name, node);
    }
}
