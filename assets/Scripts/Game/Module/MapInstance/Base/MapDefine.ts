import * as cc from "cc";

/**
 * 地图拆分的倍数
 * Tile Map 格子在实际地图中细分的数量
 * 400 * 240 地图块拆分为对应的几个小的地图块
 */
export const TILE_SPLIT_NUM = 1;
export const MAP_FRICTION = 0;

export const halfTileWidth = 100;
export const halfTileHeight = 60;

/**多边形的点 */
//export const POLYGON_POINTS = [cc.v2(131, -102), cc.v2(64, -142), cc.v2(-68, -142), cc.v2(-135, -102), cc.v2(-135, -22), cc.v2(-68, 18), cc.v2(64, 18), cc.v2(131, -22), cc.v2(131, -102) ];
export const POLYGON_POINTS = [cc.v2(halfTileWidth, 0), cc.v2(0, halfTileHeight), cc.v2(-halfTileWidth, 0), cc.v2(0, -halfTileHeight), cc.v2(halfTileWidth, 0)];

export enum MapConfigType {
    IMPASSABLE_AREA = "impassable_area",
    OUTSIDE_AREA = "outside_area",
    SAFE_AREA = "safe_area",
    POLYGON_TILE = "polygon_tile",
    POLYGON_LAYER = "polygon_layer",
    POLYGON_OBJECT = "polygon_object",
    EDGE_IMPASSABLE_AREA = "edge_impassable_area",
    EDGE_FOG_AREA = "edge_fog_area",
    FOG_RECT = "fog_rect",
    REGION_RECT = "region_area_rect",
    FOG_GIRD_LIST = "fog_grid_list", //云层的2维数组
    FOG_POS_IMG_LIST = "fog_pos_img", //云层位置
    ATLAS_ID_LIST = "first_gid_list", //图集中图片id
}

export enum MapAreaType {
    /** 可行走区域 */
    PASSABLE_AREA = 0,

    /** 不可行走区域 */
    IMPASSABLE_AREA,

    /** 安全区域 */
    SAFE_AREA,

    /** 野外区域 */
    OUTSIDE_AREA,

    /** 迷雾区域 */
    FOG_AREA,
}

export class MapEnumType {
    /**新手地图 */
    public static readonly NEW_WORLD_MAP: number = 1000;
    /**世界地图 */
    public static readonly WORLD_MAP: number = 1001;

    /**世界地图合集 */
    public static readonly WORLD_MAP_LIST: number[] = [1000,1001,1002,1003,1004,1005,1011,1012,1013,1014,1015];

    /** 通天塔 */
    public static readonly DUNGEON1: number = 2001;
    /** 力量之塔 */
    public static readonly DUNGEON3: number = 3001;
    /** 敏捷之塔 */
    public static readonly DUNGEON4: number = 4001;
    /** 智慧之塔 */
    public static readonly DUNGEON5: number = 5001;

    /** 黄金洞窟 */
    public static readonly DUNGEON6: number = 6001;

    /** 竞技场 */
    public static readonly ARENASCENE: number = 10001;

    //----------日常----
    /** 藏宝阁 */
    public static readonly TREASURE: number = 9001;
    /** 史莱姆 */
    public static readonly DUNGEON_SLIME: number = 9002;
    /**血色牢狱  */
    public static readonly DUNGEON_BLOOD: number = 9003;
    /** 深渊洞穴  */
    public static readonly DUNGEON_CAVE: number = 9004;
    /** 绝望沙漠  */
    public static readonly DUNGEON_DESERT: number = 9005;
    public static readonly DAILY_DUP_9006: number = 9006;
    //----------日常----

    /** 野外副本  */
    public static readonly DAILY_DUP_9000: number = 9000;
    /** 野外副本  */
    public static readonly DAILY_DUP_9011: number = 9011;
    /** 野外副本  */
    public static readonly DAILY_DUP_9012: number = 9012;
    /** 野外副本  */
    public static readonly DAILY_DUP_9013: number = 9013;
    /** 野外副本  */
    public static readonly DAILY_DUP_9014: number = 9014;
    /** 野外副本  */
    public static readonly DAILY_DUP_9015: number = 9015;
}

/** 副本类型  */
export enum DupType {
    /* 不在副本内 */
    None,
    /** 日常副本 */
    DAILY_DUP,
    /** 世界副本 */
    WORLD_DUP,
    /** 通天塔副本 */
    TOWER_DUP,
    /** 黄金猪副本 */
    PIG_DUP,
    /** 竞技场 */
    ARENA_SCENE,
}
