import * as cc from "cc";

import PoolBase from "../../Pool/PoolBase";

export const PRELOAD_NUM = 400;
const POOL_RESOURCE_NAME = "EdgeRigidBody";

export default class PoolEdgeRigidBody extends PoolBase {

    public static getName(content: string): string {
        return POOL_RESOURCE_NAME + content;
    }

    public static regPool(content: string, prefab: cc.Node, size: number): void {
        const name = this.getName(content);
        return super.regPool(name, prefab, size);
    }

    public static getNode(content: string): cc.Node {
        const name = this.getName(content);
        return super.getNode(name);
    }

    public static returnPool(content: string, node: cc.Node) {
        const name = this.getName(content);
        return super.returnPool(name, node);
    }
}
