# PlayerTeamState Integration with MapController

## Overview

This document describes the integration of `PlayerTeamState` into the `MapController` system, providing team-based player movement and formation control for the game.

## Architecture

### PlayerTeamState Class

The `PlayerTeamState` class manages team-based player movement and formation control with the following key features:

- **Team Formation Management**: Coordinates multiple player characters in intelligent formations
- **Joystick Integration**: Responds to joystick input for team movement control
- **Pathfinding & Collision**: Handles collision detection and pathfinding for team movement
- **Camera Synchronization**: Synchronizes camera movement with team positioning
- **Guide Player System**: Uses a guide player to lead team formations
- **Formation Patterns**: Supports multiple formation patterns (line, fan, center formations)

### Key Components

#### Properties
- `mapInst`: Reference to the map instance for pathfinding and collision detection
- `players`: Array of player actors in the team
- `followIndex`: Tracks formation position assignments for each player
- `guidePlayer`: Invisible guide player that leads the team formation
- `controlEnabled`: Flag indicating whether team movement is currently active
- `moveResult`: Contains calculated movement direction and formation flags

#### Public Methods
- `init(map: MapInstance)`: Initializes the team state with a map instance
- `setPlayerList(players: <PERSON><PERSON><PERSON>[])`: Sets the list of players to manage
- `onJoyStartControl()`: Called when joystick control starts
- `onJoyMoved(dir: cc.Vec2)`: Called when joystick is moved
- `onJoyEndControl()`: Called when joystick control ends
- `update(dt: number)`: Updates team state each frame

#### Private Methods
- `calculateMoveDir(joyDir: cc.Vec2)`: Calculates movement direction and formation behavior
- `updateMainHeroPosition()`: Updates team member positions based on formation patterns
- `updateCameraPosition()`: Updates camera position based on guide player movement
- `getHeroDestPoints(dir: cc.Vec3, centerPoint: cc.Vec3)`: Generates formation destination points
- `getNearestPlayer(centerPoint: cc.Vec2)`: Finds nearest unassigned player for formation positioning

### MapController Integration

The `MapController` has been enhanced to integrate with `PlayerTeamState`:

#### New Properties
- `_playerTeamState`: Instance of PlayerTeamState for team management
- `_playerActors`: Array of player actors currently managed

#### New Methods
- `initPlayerTeamState()`: Initializes the PlayerTeamState system
- `setupTeamManagement()`: Sets up team management after players are created
- `onJoystickStartControl()`: Public API for joystick start events
- `onJoystickMoved(direction: cc.Vec2)`: Public API for joystick movement events
- `onJoystickEndControl()`: Public API for joystick end events
- `getPlayerTeamState()`: Returns the PlayerTeamState instance
- `getPlayerActors()`: Returns the array of managed player actors

### JoystickController Integration

The `JoystickController` has been updated to connect with the MapController:

#### New Features
- Automatically finds and connects to MapController instance
- Forwards joystick events to MapController for team management
- Maintains existing joystick UI functionality

## Usage Example

```typescript
// MapController automatically initializes PlayerTeamState when map loads
// and connects players when they are created

// JoystickController automatically forwards events to MapController
// No additional setup required for basic functionality

// Advanced usage - accessing team state directly
const mapController = cc.find("MapController").getComponent(MapController);
const teamState = mapController.getPlayerTeamState();

if (teamState) {
    // Direct control if needed
    teamState.onJoyStartControl();
    teamState.onJoyMoved(cc.v2(1, 0)); // Move right
    teamState.onJoyEndControl();
}
```

## Formation Patterns

The system supports multiple formation patterns:

### Line Formation
- Players arrange in parallel lines (left and right of movement direction)
- 5 points per line, spaced 40 units apart
- Lines separated by 30 units perpendicular to movement

### Fan Formation
- Used when movement direction changes significantly
- Players spread out in a fan pattern
- Triggered by `isFan` flag in movement calculations

### Center Formation
- Players converge toward a central point
- Used when `isFindCenter` flag is set
- Provides tight formation for navigation through narrow spaces

## Configuration

Key configuration values (from BattleConst):
- `return_distance`: Distance threshold for automatic return-to-formation behavior
- `TeamRange`: Maximum distance players can be from formation center

## Integration Points

1. **Map Loading**: PlayerTeamState is initialized when map loading completes
2. **Player Creation**: Players are added to team management when created
3. **Joystick Input**: JoystickController forwards events to MapController
4. **Frame Updates**: MapController updates PlayerTeamState each frame
5. **Camera Control**: Team movement influences camera positioning

## Future Enhancements

Potential areas for future development:
- Configurable formation patterns
- Dynamic team size adjustment
- Formation switching based on combat state
- Advanced pathfinding for complex terrain
- Formation persistence across map transitions
