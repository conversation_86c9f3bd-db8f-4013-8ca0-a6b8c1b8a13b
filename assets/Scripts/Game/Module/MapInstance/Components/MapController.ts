import { _decorator, Component, Node } from 'cc';
import * as cc from "cc";
import { ViewUtil } from '../../../Utils/ViewUtils';
import { LoadUtils } from '../../../Utils/LoadUtils';
import { Bundles } from '../../Bundles';
import EventManagerComponent from '../../../GlobalComponent/EventManagerComponent';
import { pb } from "../../../../Proto/pb.js";
import { GameEvent, MapEvent } from 'db://assets/Scripts/GameEvent/Events';
import { MapInstance } from './MapInstance';
import { MapUnitFactory } from '../Base/MapUnitFactory';
import { VoHero } from '../../../Unit/VoHero';
import CameraController from '../../Camera/CameraManager';
import { debugDumpNodeArch } from '../../../Utils/DebugUtils';
import PlayerActor from '../../../Unit/Actor/player/PlayerActor';
import PlayerTeamState from '../Model/PlayerTeamState';
const { ccclass, property } = _decorator;

/**
 * MapController - Manages map-related operations including loading, replacement, and player team management
 *
 * This controller handles:
 * - Map loading and initialization
 * - Player hero creation and management
 * - Team formation and movement coordination through PlayerTeamState
 * - Integration with joystick input for team control
 *
 * Must be bound to the map's root node.
 */
@ccclass('MapController')
export class MapController extends Component {

    private _currentMap: MapInstance | null = null;
    private _playerTeamState: PlayerTeamState | null = null;
    private _playerActors: PlayerActor[] = [];

    protected onLoad(): void {
        EventManagerComponent.instance().addEventListener(
            MapEvent.START_MAP_LOAD, this.onMapLoadStart, this);
        EventManagerComponent.instance().addEventListener(
            MapEvent.LOAD_MAP_COMPLETE, this.onMapLoadDone, this);
    }

    start() {
        EventManagerComponent.instance().addEventListener(
            GameEvent.GAME_INIT_COMPLETE, () => {
                this.openMap(1001, 1);
            }, null,
        )
        this.openMap(1001, 1);
    }

    update(deltaTime: number) {
        // Update player team state if initialized
        if (this._playerTeamState) {
            this._playerTeamState.update(deltaTime);
        }
    }

    openMap(mapId: number, zoneId: number): void {
        const p = Bundles.MAP + "MapInstance";
        LoadUtils.loadPrefab(p, (prefab: cc.Prefab) => {
            const node = ViewUtil.createPrefabNode(p);
            this.node.addChild(node);
            this._currentMap = node.getComponent(MapInstance);
            this._currentMap.mapId = mapId;
            this._currentMap.zoneId = zoneId;
            this._currentMap.loadMap();
        });
    }

    /**
     * Loads the map
     */
    private onMapLoadStart(): void {
    }

    private onMapLoadDone(): void {
        this.initPlayerHero();
        this.initPlayerTeamState();
    }

    /**
     * Initializes the player team state management system
     */
    private initPlayerTeamState(): void {
        if (!this._currentMap) {
            console.error("MapController: Cannot initialize PlayerTeamState without a current map");
            return;
        }

        this._playerTeamState = new PlayerTeamState();
        this._playerTeamState.init(this._currentMap);
    }

    private async initPlayerHero(): Promise<void> {
        const mainHero: number = 1010004;
        const heros: pb.IPbSprite[] = [
            {
                id: 1,
                type: pb.PbSpriteType.HERO,
                identity: 1010004,
                level: 1,
                rebornTime: null,
                ctype: 1,
                cx: 0,
                cy: 0,
                battleAttr: null,
                configId: 1010004,
                fogArea: null
            }
        ];
        const rebordPos = this._currentMap.getRebornPoint();
        let voHero = new VoHero();
        voHero.init(heros[0]);
        voHero.heroIndex = 0;
        voHero.point.x = 0;
        voHero.point.y = 0;
        voHero.isMainPlayer = true;
        voHero.rebornPosX = rebordPos.x;
        voHero.rebornPosY = rebordPos.y;

        voHero.x = voHero.rebornPosX;
        voHero.y = voHero.rebornPosY;
        voHero.width = voHero.bodyRadius;
        voHero.height = voHero.bodyRadius;

        const playerActor = await MapUnitFactory.createHeroAsync(voHero, this._currentMap)
        this._currentMap.addUnitNode(playerActor.node);

        // Add player to team management
        this._playerActors.push(playerActor);

        CameraController.instance.setViewToPoint(
            playerActor.node.x, playerActor.node.y,
            false,
        )

        debugDumpNodeArch(playerActor.node);

        // Initialize team state with players after hero creation
        this.setupTeamManagement();
    }

    /**
     * Sets up team management after players are created
     */
    private setupTeamManagement(): void {
        if (this._playerTeamState && this._playerActors.length > 0) {
            this._playerTeamState.setPlayerList(this._playerActors);
        }
    }

    // Public API for joystick integration

    /**
     * Called when joystick control starts
     * Delegates to PlayerTeamState for team movement coordination
     */
    public onJoystickStartControl(): void {
        if (this._playerTeamState) {
            this._playerTeamState.onJoyStartControl();
        }
    }

    /**
     * Called when joystick is moved
     * Updates team movement direction
     *
     * @param direction - Normalized direction vector from joystick input
     */
    public onJoystickMoved(direction: cc.Vec2): void {
        if (this._playerTeamState) {
            this._playerTeamState.onJoyMoved(direction);
        }
    }

    /**
     * Called when joystick control ends
     * Stops team movement and initiates formation return behavior
     */
    public onJoystickEndControl(): void {
        if (this._playerTeamState) {
            this._playerTeamState.onJoyEndControl();
        }
    }

    /**
     * Gets the current player team state instance
     * @returns The PlayerTeamState instance or null if not initialized
     */
    public getPlayerTeamState(): PlayerTeamState | null {
        return this._playerTeamState;
    }

    /**
     * Gets the list of player actors currently managed
     * @returns Array of PlayerActor instances
     */
    public getPlayerActors(): PlayerActor[] {
        return this._playerActors;
    }
}

