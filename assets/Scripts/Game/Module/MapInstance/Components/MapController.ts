import { _decorator, Component, Node } from 'cc';
import * as cc from "cc";
import { ViewUtil } from '../../../Utils/ViewUtils';
import { LoadUtils } from '../../../Utils/LoadUtils';
import { Bundles } from '../../Bundles';
import EventManagerComponent from '../../../GlobalComponent/EventManagerComponent';
import { pb } from "../../../../Proto/pb.js";
import { GameEvent, MapEvent } from 'db://assets/Scripts/GameEvent/Events';
import { MapInstance } from './MapInstance';
import { MapUnitFactory } from '../Base/MapUnitFactory';
import { VoHero } from '../../../Unit/VoHero';
import CameraController from '../../Camera/CameraManager';
import { debugDumpNodeArch } from '../../../Utils/DebugUtils';
import PlayerActor from '../../../Unit/Actor/player/PlayerActor';
import PlayerTeamState from '../Model/PlayerTeamState';
const { ccclass, property } = _decorator;

/**
 * 管理 map 的相关操作，比如加载，替换等
 * 需要绑定在 map 的根节点上
 */
@ccclass('MapController')
export class MapController extends Component {

    private _currentMap: MapInstance | null = null;

    protected onLoad(): void {
        EventManagerComponent.instance().addEventListener(
            MapEvent.START_MAP_LOAD, this.onMapLoadStart, this);
        EventManagerComponent.instance().addEventListener(
            MapEvent.LOAD_MAP_COMPLETE, this.onMapLoadDone, this);
    }

    start() {
        EventManagerComponent.instance().addEventListener(
            GameEvent.GAME_INIT_COMPLETE, () => {
                this.openMap(1001, 1);
            }, null,
        )
        this.openMap(1001, 1);
    }

    update(deltaTime: number) {

    }

    openMap(mapId: number, zoneId: number): void {
        const p = Bundles.MAP + "MapInstance";
        LoadUtils.loadPrefab(p, (prefab: cc.Prefab) => {
            const node = ViewUtil.createPrefabNode(p);
            this.node.addChild(node);
            this._currentMap = node.getComponent(MapInstance);
            this._currentMap.mapId = mapId;
            this._currentMap.zoneId = zoneId;
            this._currentMap.loadMap();
        });
    }

    /**
     * 加载地图
     */
    private onMapLoadStart(): void {
    }

    private onMapLoadDone(): void {
        this.initPlayerHero();

    }

    private async initPlayerHero(): Promise<void> {
        const mainHero: number = 1010004;
        const heros: pb.IPbSprite[] = [
            {
                id: 1,
                type: pb.PbSpriteType.HERO,
                identity: 1010004,
                level: 1,
                rebornTime: null,
                ctype: 1,
                cx: 0,
                cy: 0,
                battleAttr: null,
                configId: 1010004,
                fogArea: null
            }
        ];
        const rebordPos = this._currentMap.getRebornPoint();
        let voHero = new VoHero();
        voHero.init(heros[0]);
        voHero.heroIndex = 0;
        voHero.point.x = 0;
        voHero.point.y = 0;
        voHero.isMainPlayer = true;
        voHero.rebornPosX = rebordPos.x;
        voHero.rebornPosY = rebordPos.y;

        voHero.x = voHero.rebornPosX;
        voHero.y = voHero.rebornPosY;
        voHero.width = voHero.bodyRadius;
        voHero.height = voHero.bodyRadius;

        const playerActor = await MapUnitFactory.createHeroAsync(voHero, this._currentMap)
        this._currentMap.addUnitNode(playerActor.node);
        CameraController.instance.setViewToPoint(
            playerActor.node.x, playerActor.node.y,
            false,
        )

        debugDumpNodeArch(playerActor.node);
    }
}

