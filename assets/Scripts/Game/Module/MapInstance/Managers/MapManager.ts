import * as cc from "cc";

import { CFG_Dungeon} from "../../../../Config/GameCfg/CFGManager";
// import ArenaMap from "../../game/modules/arena/view/ArenaMap";
import { BattleType } from "../../../../GameData/Challenge/Const";
// import DailyDupMapUnit from "../../game/modules/dailyDup/model/DailyDupMapUnit";
// import GoldenCaveMap from "../../game/modules/goldenCave/model/GoldenCaveMap";
import WorldMap from "../WorldMap";
// import ChallengeDupMap from "./ChallengeDupMap";

import { MapBase } from "../Base/MapBase";
import { DupType, MapEnumType } from "../Base/MapDefine";
import { MapUnitBase } from "../Base/MapUnitBase";

const { ccclass, property } = cc._decorator;


/**
 * 地图加载状态
 */
export enum MapLoadStatus {
    /**
     * 没有进行加载
     */
    none = 0,

    /**
     * 加载中
     */
    loading = 1,

    /**
     * 已经加载完成
     */
    loaded = 2,
}

/**
 * 场景管理器
 */
@ccclass
export default class MapManager extends cc.Component {
    private static _instance: MapManager = null;
    private static _curMap: MapUnitBase;
    private static _lastMap: MapBase;
    public static mainMapId:number = 0
    rootNode: cc.Node; //Game.fire的MapLayer

    //GameController里面第一次初始化
    public static get instance(): MapManager {
        if (this._instance == null) {
            var node: cc.Node = new cc.Node("SceneManager");
            cc.game.addPersistRootNode(node);
            this._instance = node.addComponent(MapManager);
            this._instance.init();
        }
        return this._instance;
    }

    start() { }

    /**
     * 初始化
     */
    private init(): void { }

    /**
     * 监听事件
     * @param type
     * @param callback
     * @param target
     * @param useCapture
     */
    public on(type: string, callback: Function, target?: any, useCapture?: boolean): void {
        this.node.on(type, callback, target, useCapture);
    }

    /**
     * 广播事件
     * @param type
     * @param arg1
     * @param arg2
     * @param arg3
     * @param arg4
     * @param arg5
     */
    public emit(type: string, arg1?: any, arg2?: any, arg3?: any, arg4?: any, arg5?: any): void {
        this.node.emit(type, arg1, arg2, arg3, arg4, arg5);
    }

    /**
     * 切换地图
     * @param curMap
     * @returns
     */
    static showMap(curMap: MapUnitBase) {
        console.log("MapManager.showMap", curMap.mapId, curMap.zoneId, curMap.crossFlag);
        console.log(this._curMap)
        if (this._curMap == curMap) return;
        if (this._lastMap != this._curMap) this._lastMap = this._curMap;
        if (this._curMap) {
            this._curMap.stop();
        }
        this._curMap = curMap;
        this._curMap.start();
        MapManager.instance.rootNode.addChild(this._curMap.getContainer());
        // if (this._lastMap) {
        //     this._lastMap.stop();
        //     this._lastMap = null;
        // }
        for (let c of MapManager.instance.rootNode.children[0].children) {
            console.log("MapManager.showMap", c.name, c.uuid);
        }
    }

    static closeCurrMap() {
        if (this._curMap) {
            this._curMap.stop();
            this._curMap = null;
        }
        if (this._lastMap) {
            this._lastMap.stop();
            this._lastMap = null;
        }
    }

    /**
     * 当前地图
     */
    static get curMap(): MapUnitBase {
        return this._curMap ? this._curMap : null;
    }

    /**
     * 当前地图id
     */
    static get curMapId(): any {
        return this._curMap ? this._curMap.mapId : 0;
    }

    // /**
    //  * 当前主线地图id
    //  */
    // static get curMainMapId(): any {
    //     return this._curMap ? this._curMap.mainMapId : 0;
    // }

    /**
     * 进入地图/副本     <先进入,根据具体逻辑判断进入哪个区, 加载哪个地图>.
     * @param mapId  地图id
     * @param zondId  区id
     */
    static enter(mapId: number, battleType: number, zondId: number = 1, crossMapType: number = -1) {
        let map: MapUnitBase;
        map = new WorldMap();
        this.mainMapId = mapId;
        map.crossFlag = crossMapType;

        // if (MapEnumType.WORLD_MAP_LIST.indexOf(mapId) != -1) {
        //     map = new WorldMap();
        //     this.mainMapId = mapId;
        //     map.crossFlag = crossMapType;
        // } else if (mapId == MapEnumType.DUNGEON6) {
        //     map = new GoldenCaveMap();
        //     map.battleType = battleType;
        // } else if (mapId == MapEnumType.ARENASCENE) {
        //     map = new ArenaMap();
        //     map.battleType = battleType;
        // } else if (CFG_Dungeon.get(mapId) != null) {
        //     map = new DailyDupMapUnit();
        //     mapId = CFG_Dungeon.get(mapId).sceneId;
        //     zondId = App.DailyDupManager.zoneId;
        // } else if (this.isChallengeDup(mapId)) {
        //     map = new ChallengeDupMap();
        //     map.battleType = battleType;
        // } else {
        //     map = new WorldMap();
        //     this.mainMapId = mapId;
        //     map.crossFlag = crossMapType;
        // }
        map.mapId = mapId;
        map.zoneId = zondId;
        MapManager.showMap(map);

    }

    /** 通过地图ID获取是否是野外副本  */
    static isChallengeDup(mapId: number): boolean {
        if (mapId == MapEnumType.DUNGEON1 || mapId == MapEnumType.DUNGEON3 || mapId == MapEnumType.DUNGEON4 || mapId == MapEnumType.DUNGEON5) {
            return true;
        }
        return false;
    }

    static getDupType(): DupType {
        // if (MapManager.curMap.battleType == BattleType.Dungeon) {
        //     return DupType.DAILY_DUP;
        // } else if (MapManager.curMap.battleType == BattleType.Tower ||
        //     MapManager.curMap.battleType == BattleType.TowerOfStrength ||
        //     MapManager.curMap.battleType == BattleType.TowerOfAgility ||
        //     MapManager.curMap.battleType == BattleType.TowerOfWisdom) {
        //     return DupType.TOWER_DUP;
        // } else if (MapManager.curMap.battleType == BattleType.GoldenPig) {
        //     return DupType.PIG_DUP
        // } else if (MapManager.curMap.battleType == BattleType.ArenaAtt) {
        //     return DupType.ARENA_SCENE
        // }
        return DupType.None;
    }
}
