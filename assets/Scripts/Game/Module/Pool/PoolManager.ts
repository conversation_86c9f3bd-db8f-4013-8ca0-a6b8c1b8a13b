import * as cc from "cc";

/** -----------------------------------------------------------
 * 基于cc.Node节点的缓存管理器
 * ------------------------------------------------------------ */
export interface PoolItem {
    name: string;
    size: number;
    pool: cc.NodePool;
    prefab: cc.Node;
}

export default class PoolManager {
    private static _instance: PoolManager = null;
    private _prefabMap: Map<string, PoolItem> = new Map();

    public static get instance(): PoolManager {
        if (PoolManager._instance == null) {
            PoolManager._instance = new PoolManager();
        }
        return PoolManager._instance;
    }

    destroy() {
        this._prefabMap.clear();
        PoolManager._instance = null;
    }

    /**
     * 注册缓存池
     * @param name      名字
     * @param prefab    预制体
     * @param size      初始数量
     */
    public regPool(name: string, prefab: cc.Node, size: number = 100): void {
        if (size <= 0) {
            console.warn("[PoolManager] 数量必现大于0: ", size);
            return;
        }

        const pool = new cc.NodePool();
        for (let i = 0; i < size; i++) {
            const _prefab = cc.instantiate(prefab);
            pool.put(_prefab);
        }

        this._prefabMap.set(name, {
            name: name,
            size: size,
            pool: pool,
            prefab: prefab,
        });
    }

    /** 获取缓存节点 */
    public getNode(name: string): cc.Node {
        const singlePool = this._prefabMap.get(name);
        if (!singlePool) {
            console.warn("[PoolManager] 没有匹配的缓存池: ", name);
            return;
        }

        const { size, pool, prefab } = singlePool;
        let ret = pool.get();

        if (!ret) {
            //console.warn("缓存池数量不够: ", name, pool.size());
            const _prefab = cc.instantiate(prefab);
            pool.put(_prefab);
            return this.getNode(name);
        }
        return ret;
    }

    /**
     * 归还
     * @param name
     * @param node
     * @returns
     */
    public returnPool(name: string, node: cc.Node) {
        const singlePool = this._prefabMap.get(name);
        if (!singlePool) {
            console.warn("[PoolManager] 没有匹配的缓存池: ", name);
            return;
        }
        const { size, pool, prefab } = singlePool;
        pool.put(node);
    }
}
