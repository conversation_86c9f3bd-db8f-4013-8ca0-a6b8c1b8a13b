import * as cc from "cc";

import PoolManager from "./PoolManager";

export default class PoolBase {
    public static getName(content: string): string {
        console.error("有Pool没有重写这个方法");
        return "undefined";
    }

    public static regPool(name: string, prefab: cc.Node, size: number): void {
        PoolManager.instance.regPool(name, prefab, size);
    }

    public static getNode(name: string): cc.Node {
        return PoolManager.instance.getNode(name);
    }

    public static returnPool(name: string, node: cc.Node) {
        PoolManager.instance.returnPool(name, node);
    }
}
