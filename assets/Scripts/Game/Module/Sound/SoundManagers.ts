import * as cc from 'cc';

const { ccclass, property } = cc._decorator;

/**音效ID */
export enum SoundId {
    /**世界地图 */
    WORLD_MAP = "bg/city",
    /**副本场景 */
    BATTLE_COPY = "bg/fight",
    /**点击音效 */
    CLICK = "common/click2",
    /**技能音效 */
    SKILL = "skill/",
    /**npc音效 */
    NPC = "npc/",
    /**砍树音效 */
    CHOP = "common/Chop_wood",
    /**挖矿音效 */
    MINING = "common/mining",
    /**弹出界面音效 */
    OPENUI = "common/openUI",
    /**关闭界面音效 */
    CLOSEUI = "common/closeUI",
    /**传送音效 */
    CS = "common/chuansong",
    /**获得英雄音效 */
    HUODE_HERO = "common/huodekapai",
    /**提交物品和获得资源音效 */
    HUODE_ZY = "common/huodeziyuan",
    /**驱散迷雾音效 */
    MIWU = "common/qusanmiwu",
    /**完成任务音效 */
    TASK = "common/task",
    /**点亮技能点音效 */
    DIANLIANG = "common/dianliang",
    /**升级/领取宝箱音效 */
    LEVEL_UP = "common/levelUP",
    /**灯切换音效1 */
    XUANZEKA1 = "common/choose3",
    /**灯切换音效2 */
    XUANZEKA2 = "common/choose2",
    /**获得卡/功能开启音效 */
    HUODE_KA = "common/huodekapian",
    /**获得道具音效 */
    HUODE_ITEM = "common/huodejaingli",
    /**副本通关音效 */
    VICTOR = "common/victor",
    /**建造成功 */
    JZBUILD = "common/jianzaochenggong",
    /**副本失败音效 */
    FAIL = "common/fail",
    /**脚步 */
    JIAOBU = "common/jiaobu",
    /**卡牌开门 *G/
    KAIMEN = "common/kaimen",
    /**卡牌关门 */
    GUANMEN = "common/guanmen",
    /**游戏开场 */
    GAMEOPEN = "common/open",
}

/**音效类型 */
export enum SoundType {
    /**通用音效 */
    COMMON,
    /**攻击音效 */
    ATTACK,
    /**技能音效 */
    SKILL,
    /**npc音效 */
    NPC,
    /**点击音效 */
    CLICK,
}

// @ccclass
// export class SoundManager extends Singleton {

//     /**音效开关 */
//     private _effectOn: boolean;
//     /**背景音乐开关 */
//     private _bgOn: boolean;
//     /**音效音量 */
//     private _effectVolume: number;
//     /**背景音乐音量 */
//     private _bgVolume: number;
//     /**当前背景音乐 */
//     private _currBgName: string;
//     /**上一个背景音乐 */
//     private _lastBgName: string;
//     /**资源根目录 */
//     private _resRoot: string = "sound/";
//     /**声音资源ID字典 */
//     private _soundMap: Map<string, number> = new Map();

//     /**音乐淡出间隔时间 */
//     private readonly MusicFadeInterval: number = 1;
//     /**最小音量 */
//     private readonly MinMusicVolume: number = 0;
//     /**最大音量 */
//     private readonly MaxMusicVolume: number = 0.5;

//     /**初始化 */
//     public init(): void {
//         this._bgOn = App.UserController.userInfo.bgMusicSwitch;
//         this._effectOn = App.UserController.userInfo.audioEffectSwitch;
//         this.setBgVolume(this.MinMusicVolume);
//         this.setEffectVolume(this.MaxMusicVolume);
//         this._currBgName = SoundId.WORLD_MAP;
//         this._lastBgName = null;
//         this.stopAllMusicAndSound();
//     }

//     /**设置音效开关 */
//     public setEffectOn(isOn: boolean): void {
//         this._effectOn = isOn;
//         App.UserController.userInfo.audioEffectSwitch = isOn;
//     }
//     /**设置背景音乐开关 */
//     public setBgOn(isOn: boolean): void {
//         this._bgOn = isOn;
//         App.UserController.userInfo.bgMusicSwitch = isOn;
//         if (isOn && this._currBgName) {

//             this.playBgMusic(this._currBgName);
//         } else {
//             this.stopBG();
//         }
//     }

//     /**
//      * 播放背景音乐
//      * @param name 声音名称
//      * @param loop 是否重复
//      */
//     public playBgMusic(name: string, loop: boolean = true): void {
//         this._bgOn = App.UserController.userInfo.bgMusicSwitch;
//         if (!this._bgOn) return;
//         this._lastBgName = this._currBgName ?? null;
//         this._currBgName = name;

//         oops.timer.clear(this, this.musicFadeIn);
//         oops.timer.clear(this, this.musicFadeOut);
//         const path: string = this._resRoot + name;
//         LoadUtils.load(path, cc.AudioClip, (clip: cc.AudioClip) => {
//             if (this._lastBgName && this._lastBgName != name) {
//                 //淡出淡入
//                 let callback = () => {
//                     this.playMusic(clip, name, loop);
//                     oops.timer.loop(this.MusicFadeInterval * 100, this, this.musicFadeIn);
//                 }
//                 oops.timer.loop(this.MusicFadeInterval * 100, this, this.musicFadeOut, [callback.bind(this)]);
//                 LoadUtils.releaseAsset(this._resRoot + this._lastBgName);
//                 this._soundMap.delete(this._lastBgName);
//             } else {
//                 //直接淡入
//                 this.playMusic(clip, name, loop);
//                 oops.timer.loop(this.MusicFadeInterval * 100, this, this.musicFadeIn);
//             }
//         });
//     }
//     /**播放音乐 */
//     private playMusic(audioClip: cc.AudioClip, name: string, loop: boolean): void {
//         this.stopBG();
//         const audioID: number = cc.audioEngine.playMusic(audioClip, loop);
//         if (!this._soundMap.has(name)) {
//             this._soundMap.set(name, audioID);
//         }
//     }
//     /**音乐音量淡出效果 */
//     private musicFadeOut(callback: Function): void {
//         const volume: number = this._bgVolume - (this.MaxMusicVolume - this.MinMusicVolume) / (this.MusicFadeInterval / 0.05);
//         this.setBgVolume(volume);
//         if (volume <= this.MinMusicVolume) {
//             oops.timer.clear(this, this.musicFadeOut);
//             callback && callback();
//         }
//     }
//     /**音乐音量淡入效果 */
//     private musicFadeIn(): void {
//         const volume: number = this._bgVolume + (this.MaxMusicVolume - this.MinMusicVolume) / (this.MusicFadeInterval / 0.05);
//         this.setBgVolume(volume);
//         if (volume >= this.MaxMusicVolume) {
//             oops.timer.clear(this, this.musicFadeIn);
//         }
//     }
//     /**停止背景音乐 */
//     public stopBG(): void {
//         cc.audioEngine.stopMusic();
//     }

//     /**
//      * 播放音效
//      * @param name 声音名称
//      * @param type 声音类型
//      * @param loop 是否重复播放
//      */
//     public playEffect(name: string, type: SoundType, loop: boolean = false): void {
//         this._effectOn = App.UserController.userInfo.audioEffectSwitch;
//         if (!this._effectOn) return;
//         const path: string = this._resRoot + name;
//         LoadUtils.load(path, cc.AudioClip, (clip: cc.AudioClip) => {
//             // console.log("音效资源", name, clip);
//             const audioID: number = cc.audioEngine.playEffect(clip, loop);
//             if (!this._soundMap.has(name)) {
//                 this._soundMap.set(name, audioID);
//             }
//             if (type != SoundType.CLICK) {
//                 //释放资源
//                 cc.audioEngine.setFinishCallback(audioID, () => {
//                     LoadUtils.releaseAsset(path);
//                     this._soundMap.delete(name);
//                 });
//             }
//         });
//     }

//     private _clickTime: number = 0;
//     /**播放点击音效 */
//     public playClickEffect(): void {
//         //音效按钮有效点击时间
//         let time: number = new Date().getTime();
//         if ((time - this._clickTime) < 300) return;
//         this._clickTime = time;
//         this.playEffect(SoundId.CLICK, SoundType.CLICK);
//     }

//     /**停止音效播放 */
//     public stopEffect(name: string): void {
//         const audioID: number = this._soundMap.get(name);
//         cc.audioEngine.stopEffect(audioID);
//         LoadUtils.releaseAsset(this._resRoot + name);
//         this._soundMap.delete(name);
//     }

//     /**停止播放背景音乐和音效 */
//     public stopAllMusicAndSound() {
//         cc.audioEngine.stopAll();
//         //释放所有声音资源
//         this._soundMap.forEach((v, k, m) => {
//             LoadUtils.releaseAsset(this._resRoot + k);
//         });
//         this._soundMap.clear();
//     }

//     /**设置背景音乐音量 */
//     public setBgVolume(volume: number): void {
//         volume = Math.min(volume, 1);
//         volume = Math.max(volume, 0);
//         this._bgVolume = volume;
//         App.UserController.userInfo.bgMusicVolume = this._bgVolume;
//         cc.audioEngine.setMusicVolume(this._bgVolume);
//     }
//     /**获取背景音乐音量 */
//     public getBgVolume(): number {
//         return this._bgVolume;
//     }

//     /**设置音效音量 */
//     public setEffectVolume(volume: number): void {
//         volume = Math.min(volume, 1);
//         volume = Math.max(volume, 0);
//         this._effectVolume = volume;
//         App.UserController.userInfo.audioEffectVolume = this._effectVolume;
//         cc.audioEngine.setEffectsVolume(this._effectVolume);
//     }
//     /**获取音效音量 */
//     public getEffectVolume(): number {
//         return this._effectVolume;
//     }
// }

const soundManager = {
    playClickEffect() {
        
    }
}