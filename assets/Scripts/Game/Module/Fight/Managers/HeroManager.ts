import * as cc from "cc";

import { AppConfig } from "../../../../Config/GameConfig";
import { GameEvent } from "../../../../GameEvent/Events";
import CFG_Constant from "../../../../Config/GameCfgExt/BattleConst";
import GameController from "../../../Control/GameController";
import Joystick from "../../../Control/Joystick";
import JoystickController from "../../../Control/JoystickController";
import { pb } from "../../../../Proto/pb";
import { UnitState } from "../../../Unit/UnitState";
import { VoHero } from "../../../Unit/VoHero";
import { Player_Init_data } from "../../../Unit/Actor/PlayerType";
import GuidePlayer from "../../../Unit/Actor/player/GuidePlayer";
import PlayerActor, { QiPaoType } from "../../../Unit/Actor/player/PlayerActor";
import { IUnit } from "../../../Unit/Base/IUnit";
import { ArraySort } from "../../../Utils/ArraySort";
import { MathUtils } from "../../../Utils/MathUtils";
import RandomUtils from "../../../Utils/RandomUtils";
import HeroData from "../../../../GameData/Hero/HeroData";
import { RangePointer } from "../../Map/RangePointer";
import EventManagerComponent from "../../../GlobalComponent/EventManagerComponent";
import Timer from "../../../Utils/Timer";
import MapManager from "../../MapInstance/Managers/MapManager";
import { MapHelper } from "../../MapInstance/Base/MapHelper";
import CameraController from "../../Camera/CameraManager";

export class HeroManager {
    isTeamMove: boolean = false;//拖到要开开始整体移动的标志
    /** 战斗中的英雄数量. */
    battleHeroNum: number = 0;
    protected players: PlayerActor[] = [];
    /** 中心点坐标 */
    private _guardCenterPos: cc.Vec3 = null;
    /** 范围指示器 */
    protected rangePointer: RangePointer;
    /**摇杆方向 */
    private joyMoveDir: cc.Vec2 = cc.Vec2.ZERO.clone();
    private lastJoyMoveDir: cc.Vec2 = null;
    private moveDir: cc.Vec2 = cc.Vec2.ZERO.clone();
    /**触摸位置 */
    private touchPos: cc.Vec2 = cc.Vec2.ZERO.clone();
    private static _instance: HeroManager;

    private guidePlayer: GuidePlayer;
    private _canMove: boolean = true;

    static get instance(): HeroManager {
        return HeroManager._instance || (HeroManager._instance = new HeroManager);
    }

    public get allPlayers(): PlayerActor[] {
        return this.players;
    }

    /**
     * 主角色(操控)
     */
    public get mainPlayer(): PlayerActor {
        let hero = this.players[0];
        if (hero && hero.getHp() > 0) {
            return hero;
        }
        for (let i = 1; i < this.players.length; i++) {
            hero = this.players[i];
            if (hero.getHp() > 0) {
                return hero;
            }
        }

        //英雄全部死亡后，去最后一个死亡的英雄
        let rebornTime = -1;
        let rebornIndex = 0;
        for (let i = 0; i < this.players.length; i++) {
            hero = this.players[i];
            if (hero.data.rebornTime > rebornTime) {
                rebornTime = hero.data.rebornTime;
                rebornIndex = i;
            }
        }

        return this.players[rebornIndex];
    }

    public showQiPao(qiPaoType: QiPaoType) {
        let indexList = [];
        let max = Math.min(this.players.length, 3);

        while (true) {
            let index = RandomUtils.randomInt(0, this.players.length - 1);
            if (indexList.indexOf(index) < 0) indexList.push(index);
            if (indexList.length >= max) break;
        }

        for (var i = 0; i < indexList.length; i++) {
            this.players[indexList[i]].triggerQiPao(qiPaoType, RandomUtils.randomInt(100, 1000))
        }
    }
    /**
    * 警戒中心点
    */
    public get guardCenterPos(): cc.Vec3 {
        return this._guardCenterPos;
    }

    public set guardCenterPos(value: cc.Vec3) {
        this._guardCenterPos = value;

        this.players.forEach((player) => {
            player.data.point.x = this._guardCenterPos.x;
            player.data.point.y = this._guardCenterPos.y;
        })
    }

    /**
     * 团队中心点位置.
     * @param out 
     * @returns 
     */
    public getTeamCenterPoint(out?: cc.Vec3): cc.Vec3 {
        if (!out) out = new cc.Vec3();
        if (this.isTeamMove && this.mainPlayer) {
            this.mainPlayer.node.getPosition(out);
        } else {
            out.x = this.guardCenterPos.x;
            out.y = this.guardCenterPos.y;
            out.z = this.guardCenterPos.z;
        }
        return out;
    }

    public getHeroLv(unit: IUnit): number {
        return 1;
        // for (let i = 0; i < this.players.length; i++) {
        //     let hero = this.players[i];
        //     if (hero.data.unitId == unit.data.unitId) {
        //         return App.UserController.userInfo.level;
        //     }
        // }
        // let p = unit as Player
        // return p.data.level;
    }

    protected addEvent() {
        EventManagerComponent.instance().addEventListener(
            GameEvent.HEROLIST_UPDATE, this.onHereAttributeUpdate, this);
    }

    protected removeEvent() {
        EventManagerComponent.instance().removeEventListener(GameEvent.HEROLIST_UPDATE, this.onHereAttributeUpdate, this);
    }

    /** 初始全部英雄 */
    reloadAllHero(mainHeroIdentity: number, heros: pb.IPbSprite[], spawnPoint: cc.Vec3) {
        console.log("reloadAllHero", spawnPoint.x, spawnPoint.y)
        // 清除所有英雄
        let oldHeros: PlayerActor[] = this.allPlayers;
        let oldMainHeroPosX: number = spawnPoint.x;
        let oldMainHeroPosY: number = spawnPoint.y;

        for (let oldHero of oldHeros) {
            if (oldHero.data.isMainPlayer) {
                oldMainHeroPosX = oldHero.node.x;
                oldMainHeroPosY = oldHero.node.y;
                break;
            }
        }

        this.guardCenterPos = new cc.Vec3(oldMainHeroPosX, oldMainHeroPosY);

        //查找是否存在英雄.
        let has = (id: number) => {
            let content = heros.filter((data) => {
                return data.identity == id;
            })
            return content && content.length > 0;
        }

        //删除废弃的英雄.
        for (let i = this.allPlayers.length - 1; i >= 0; i--) {
            let player = this.allPlayers[i];
            if (!has(player.data.identity)) {
                MapManager.curMap.deleteForever(player.data);

                let index = this.allPlayers.indexOf(player);
                if (index > -1) this.allPlayers.splice(index, 1);
            }
        }

        //有新增的英雄. 找坐标给新英雄
        let aroundPos = [];
        let num: number = heros.length - this.allPlayers.length;
        if (num > 0) {
            aroundPos = MapHelper.getHeroPoints(cc.v3(oldMainHeroPosX, oldMainHeroPosY), this.allPlayers, num);
        }

        this.battleHeroNum = heros.length;
        // 创建英雄
        for (let i = 0; i < heros.length; i++) {
            let hero: pb.IPbSprite = heros[i];
            let player = this.getPlayer(hero.identity);
            if (!player) {
                let voHero: VoHero = new VoHero();
                voHero.init(hero);
                voHero.point.x = this.guardCenterPos.x;
                voHero.point.y = this.guardCenterPos.y;

                voHero.heroIndex = i;
                if (hero.identity == mainHeroIdentity) {
                    voHero.isMainPlayer = true;
                    voHero.rebornPosX = oldMainHeroPosX;
                    voHero.rebornPosY = oldMainHeroPosY;
                } else {
                    voHero.isMainPlayer = false;
                    let point: cc.Vec2 = aroundPos.shift();
                    voHero.rebornPosX = point.x;
                    voHero.rebornPosY = point.y;
                }
                voHero.x = voHero.rebornPosX;
                voHero.y = voHero.rebornPosY;
                voHero.width = voHero.bodyRadius;
                voHero.height = voHero.bodyRadius;
                MapManager.curMap.addEntity(voHero);
            } else {
                player.data.isMainPlayer = hero.identity == mainHeroIdentity;
                player.data.rebornPosX = oldMainHeroPosX;
                player.data.rebornPosY = oldMainHeroPosY;
            }
        }

        this.addEvent();

    }

    public getPlayer(identity: number): PlayerActor {
        let content = this.allPlayers.filter((player) => {
            return player.data.identity == identity;
        });
        return content && content[0] || null;
    }

    /** 是否拥有伙伴  */
    public hasPlayer(identity: number): boolean {
        return this.getPlayer(identity) != null;
    }
    strokeColor: cc.Color = new cc.Color(0xa9, 0x0f, 0x0f, 255);

    fillColor: cc.Color = null;

    lineWidth: number = 8;

    graphics: cc.Graphics;

    graphicsNode: cc.Node;
    private nodeList = [];
    drawRoad(roadNodeArr) {
        this.fillColor = cc.Color.TRANSPARENT;

        if (roadNodeArr && roadNodeArr.length <= 0) return;
        this.clearRoad();
        this.nodeList = [];
        for (var i = 0; i < roadNodeArr.length; i++) {
            let p = roadNodeArr[i];
            let range: number = 20;

            let graphicsNode = new cc.Node();
            let graphicsNodeTransform = graphicsNode.getComponent(cc.UITransform);
            graphicsNodeTransform.setAnchorPoint(0.5, 0.5);
            graphicsNode.name = "graphicsNode";
            graphicsNode.parent = MapManager.curMap.entityLayer;
            let labelNode = new cc.Node();
            labelNode.parent = graphicsNode;
            let lab = labelNode.addComponent(cc.Label);
            lab.string = `${i}`;
            let graphics = graphicsNode.addComponent(cc.Graphics);
            graphics.strokeColor = this.strokeColor;
            graphics.fillColor = this.fillColor;
            graphics.lineWidth = this.lineWidth;
            graphicsNode.setPosition(p.x, p.y)
            graphics.circle(0, 0, range);
            graphics.stroke();
            graphics.fill();


            this.nodeList.push(graphicsNode);
        }
    }

    clearRoad() {
        for (var k = 0; k < this.nodeList.length; k++) {
            this.nodeList[k].removeFromParent();
        }
    }

    /**
     * 控制主英雄走路
     * @returns
     */
    protected updateMainHeroWalk() {
        if (!GameController.instance().isCanControlPlayer()) {
            //不能控制玩家
            return;
        }
        //不是控制状态
        if (!this.isTeamMove) {
            return;
        }
        let centerPoint = CameraController.instance.getCenterPosition();
        if (!this.guidePlayer.getFollowPoint()) {
            for (let j = 0; j < this.players.length; j++) {
                let player = this.players[j];
                if (!player.isValidUnit() || player.isDie()) {
                    continue;
                }
                player.isFollow = true;

                player.setFollow(centerPoint);
            }
            return;
        }
        let isFindCenter = false;
        let isFinPoint = false;
        let isFan = false;

        if (!this.lastJoyMoveDir) {
            this.lastJoyMoveDir = this.joyMoveDir.clone().normalize();
            this.moveDir = cc.v2(this.lastJoyMoveDir.x, this.lastJoyMoveDir.y).normalize();
            isFinPoint = true;
        }
        else {
            let angle = this.joyMoveDir.signAngle(this.lastJoyMoveDir) * 180 / Math.PI;
            if (Math.abs(angle) > 4) {
                isFindCenter = true;
                this.moveDir = cc.v2(this.joyMoveDir.x, this.joyMoveDir.y).normalize();
            }
            else {
                let yDir = cc.Vec2.UNIT_Y;
                let angle1 = this.joyMoveDir.signAngle(yDir) * 180 / Math.PI;
                if (angle1 >= 0 && angle1 <= 180)
                    this.moveDir = cc.v2(this.joyMoveDir.x, this.joyMoveDir.y).normalize();
                else {
                    isFan = true;
                    this.moveDir = cc.v2(-this.joyMoveDir.x, -this.joyMoveDir.y).normalize();
                }

                isFinPoint = true;
            }
        }
        this.lastJoyMoveDir.x = this.joyMoveDir.x;
        this.lastJoyMoveDir.y = this.joyMoveDir.y;

        let position: cc.Vec3;
        position = centerPoint.add(cc.v3(this.lastJoyMoveDir.x, this.lastJoyMoveDir.y));
        let dx = position.x - centerPoint.x;
        let dy = position.y - centerPoint.y;
        if (dy == 0 && dx == 0)
            return;

        // //屏幕中心点向前寻路，判断屏幕中心点是否遇到障碍物，遇到障碍物后向左或向右改变一个夹角方向
        let newCenterPoint = centerPoint.add(new cc.Vec3(this.moveDir.x, this.moveDir.y).multiplyScalar(30));
        if (!MapHelper.isPassablePoint(centerPoint, newCenterPoint)) {
            // if (moveAgle < 0) moveAgle = moveAgle + 360;
            // let angle = moveAgle % 90;
            // let d = 140 - angle;
            // let rad = cc.misc.degreesToRadians(d);
            // this.moveDir = this.moveDir.rotateSelf(rad).normalize();
            return;
        }
        this.showRange(centerPoint);
        if (isFinPoint) {
            for (let j = 0; j < this.players.length; j++) {
                let player = this.players[j];
                player.followIndex = -1;
            }
        }

        let temp: cc.Vec3 = this.guidePlayer.getFollowPoint()
        if (isFindCenter) {
            this.clearRoad();
            this.moveDir = this.moveDir.multiplyScalar(4);
            for (let j = 0; j < this.players.length; j++) {
                let player = this.players[j];
                if (!player.isValidUnit() || player.isDie()) {
                    continue;
                }
                player.isFollow = true;
                let playerPos = cc.Vec3.ZERO;
                playerPos = player.getPosition(playerPos);
                let playerDir = temp.subtract(playerPos).normalize();
                let resultDir = cc.Vec3.ZERO;
                resultDir = cc.Vec3.add(resultDir, playerDir, new cc.Vec3(this.moveDir.x, this.moveDir.y));
                let newPoint = playerPos.add(resultDir.multiplyScalar(30));
                player.setFollow(new cc.Vec3(newPoint.x, newPoint.y));
            }
        }
        else {
            let tempDir = new cc.Vec3(this.moveDir.x, this.moveDir.y)
            let pList = this.getHeroDestPoints(tempDir, new cc.Vec3(temp.x, temp.y));
            if (AppConfig.isDebug)
                this.drawRoad(pList);
            for (var i = 0; i < pList.length; i++) {
                let playerList = this.getNearestPlayer(this.players, pList[i]);
                if (playerList.length > 0) {
                    (playerList[0]["unit"] as PlayerActor).followIndex = i;
                }
            }
            if (isFan) {
                this.moveDir.x = -this.moveDir.x;
                this.moveDir.y = -this.moveDir.y;
            }

            this.moveDir = this.moveDir.multiplyScalar(4);
            for (let j = 0; j < this.players.length; j++) {
                let player = this.players[j];
                if (!player.isValidUnit() || player.isDie()) {
                    continue;
                }
                if (player.followIndex == -1) {
                    continue;
                }
                player.isFollow = true;
                let temp = pList[player.followIndex];
                let playerPos = cc.Vec3.ZERO;
                playerPos = player.getPosition(playerPos);
                let dis = MathUtils.getDistance(new cc.Vec2(newCenterPoint.x, newCenterPoint.y), new cc.Vec2(playerPos.x, playerPos.y))
                if (dis <= Player_Init_data.TeamRange) {
                    let playerDir = temp.subtract(playerPos).normalize();
                    let resultDir = cc.Vec3.ZERO;
                    resultDir = cc.Vec3.add(resultDir, playerDir, new cc.Vec3(this.moveDir.x, this.moveDir.y)).normalize();
                    let newPoint = playerPos.add(resultDir.multiplyScalar(30));
                    player.setFollow(newPoint);
                }
                else {
                    player.setFollow(temp);
                }

            }
        }
    }

    getHeroDestPoints(dir: cc.Vec3, centerPoint: cc.Vec3): cc.Vec3[] {
        let setp = 40;
        let dd = 30;
        let pList: cc.Vec3[] = [];
        let dir0 = dir.clone().normalize();

        //左向量
        let tempDir = dir0.clone();
        let rad = cc.misc.degreesToRadians(90);
        let dir1 = new cc.Vec2(tempDir.x, tempDir.y).rotate(rad);
        let tempDir1 = dir1.multiplyScalar(dd)
        let start1 = centerPoint.add(new cc.Vec3(tempDir1.x, tempDir1.y));
        let temp = dir0.clone();
        start1 = start1.add(temp.multiplyScalar(-1.5 * setp));
        pList.push(start1.clone())
        for (var i = 1; i < 5; i++) {
            let p = start1.clone();
            temp = dir0.clone();
            p = p.add(temp.multiplyScalar(setp * i));
            pList.push(p)
        }
        //右向量
        tempDir = dir0.clone();
        rad = cc.misc.degreesToRadians(-90);

        let dir2 = new cc.Vec2(tempDir.x, tempDir.y).rotate(rad)
        tempDir1 = dir2.multiplyScalar(dd)
        let start2 = centerPoint.add(new cc.Vec3(tempDir1.x, tempDir1.y));
        temp = dir0.clone();
        start2 = start2.add(temp.multiplyScalar(-1 * setp))
        pList.push(start2.clone())
        for (var i = 1; i < 5; i++) {
            let p = start2.clone();
            temp = dir0.clone();
            p = p.add(temp.multiplyScalar(setp * i));
            pList.push(p)
        }

        let result: cc.Vec3[] = [pList[2], pList[7], pList[1], pList[6], pList[3], pList[8], pList[0], pList[5], pList[4], pList[9]];

        return result;
    }

    /** 离自己最近的点  */
    private checkNearest(playerPoint: cc.Vec3, target: cc.Vec3, result: any[]) {
        let len = result.length;

        let dis = cc.Vec3.distance(playerPoint, target);
        result.push({ "distance": dis, "unit": target });

        //从小到大排序
        if (len != result.length) {
            ArraySort.sortOn(result, "distance", ArraySort.NUMERIC);
        }
    }

    /** 离自己最近的玩家  */
    private checkNearestPlayer(playerPoint: cc.Vec3, target: PlayerActor, result: any[]) {
        let len = result.length;
        let destP: cc.Vec3 = cc.Vec3.ZERO;
        target.getPosition(destP);
        let dis = cc.Vec3.distance(playerPoint, destP);
        result.push({ "distance": dis, "unit": target });

        //从小到大排序
        if (len != result.length) {
            ArraySort.sortOn(result, "distance", ArraySort.NUMERIC);
        }
    }

    getNearestPoint(pList: cc.Vec3[] = [], playerPoint: cc.Vec3): cc.Vec3[] {
        let callback = this.checkNearest;
        let result: any = [];
        for (let i = 0, n = pList.length; i < n; i++) {
            let p: cc.Vec3 = pList[i];
            callback && callback(playerPoint, p, result);
        }
        return result;
    }

    getNearestPlayer(pList: PlayerActor[], point: cc.Vec3): cc.Vec3[] {
        let callback = this.checkNearestPlayer;
        let result: any = [];
        for (let i = 0, n = pList.length; i < n; i++) {
            let p: PlayerActor = pList[i];
            if (p.followIndex == -1)
                callback && callback(new cc.Vec3(point.x, point.y), p, result);
        }
        return result;
    }
    //
    getHeroPoints(): cc.Vec3[] {
        let pointList = [];
        for (let i = 0; i < this.players.length; i++) {
            let player = this.players[i];
            if (player.state != UnitState.dead) {
                let position: cc.Vec3 = cc.Vec3.ZERO;
                player.node.getPosition(position);
                pointList.push(position)
            }
        }
        return pointList;
    }

    private getHeroCountOfLive(): number {
        let count = 0;
        for (let i = 0; i < this.players.length; i++) {
            let player = this.players[i];
            if (player.state != UnitState.dead) {
                count++;
            }
        }
        return count;
    }
    // 假设points是一个包含各个点坐标的数组，例如[[x1, y1], [x2, y2], [x3, y3], ...]
    calculateCenterPoint(points): cc.Vec3 {
        let sumX = 0;
        let sumY = 0;

        // 计算所有点的x、y坐标之和
        for (let i = 0; i < points.length; i++) {
            sumX += points[i].x;
            sumY += points[i].y;
        }

        // 计算平均值得到中心点坐标
        let centerX = sumX / points.length;
        let centerY = sumY / points.length;

        return new cc.Vec3(centerX, centerY);
    }

    clearPlayer() {
        this.removeEvent();
        this.players.forEach((player) => {
            MapManager.curMap.removeEntity(player.data);
            MapManager.curMap.removeUnit(player.data);
            player.removeSelf();
        });
        this.players = [];
    }

    /**
    * 画寻敌范围圈
    * @param point
    */
    protected showRange(point: cc.Vec3) {
        this.createRangePointer();
        this.rangePointer.showRange(point.x, point.y);
    }

    /**创建范围指示器 */
    protected createRangePointer() {
        if (this.rangePointer == null) {
            this.rangePointer = new RangePointer();
            this.rangePointer.parent = MapManager.curMap.skillBottomLayer;
        }
    }

    protected showPointer(point: cc.Vec3, moveAgle: number, range: number = Player_Init_data.TeamRange) {
        this.createRangePointer();
        this.rangePointer.showPointer(point, moveAgle, range);
    }

    public onJoystickTouchEnd(event: cc.EventTouch, joyStick: Joystick) {
        joyStick.resetPos();
        this.joyMoveDir.x = 0;
        this.joyMoveDir.y = 0;
        this.isTeamMove = false;

        this.lastJoyMoveDir = null;
        JoystickController.Touching = false;

        Timer.clear(this, this.updateMainHeroWalk);
        Timer.clear(this, this.updateCameraPosition);
        // this.guidePlayer && this.guidePlayer.setFollow(null);
        // // 确定驻守的中心点
        if (!this.guardCenterPos) {
            this.guardCenterPos = new cc.Vec3();
        }
        let guardCenterPos = cc.Vec3.ZERO.clone();
        // if (this.guidePlayer) {
        //     guardCenterPos = this.guidePlayer.getPosition(guardCenterPos)
        // }
        // else {
        //     guardCenterPos = CameraController.instance.getCenterPosition();
        // }


        this.guardCenterPos = guardCenterPos;
        this.showRange(this.guardCenterPos);
        // //这是跟随位置.
        // let index = 0;
        let return_distance = Number(CFG_Constant.getValue("return_distance"));
        this.players.forEach(player => {
            player.stopControl();

            player.isFollow = false;
            if (!player.hasEnemy()) {
                let position: cc.Vec3 = cc.Vec3.ZERO;
                position = player.getPosition(position);
                if (MathUtils.getDistance(new cc.Vec2(this.guardCenterPos.x, this.guardCenterPos.y), new cc.Vec2(position.x, position.y)) > return_distance) {
                    player.isFollow = true;
                    player.setFollow(this.guardCenterPos);
                }
                else {
                    player.stopMove();
                    player.setFollow(null);
                    player.userMoveStop();
                }
            }
            else {
                player.stopMove();
                player.setFollow(null);
            }

        });

        // this.guidePlayer.stopControl();
    }

    public onJoystickTouchStart(event: cc.EventTouch, joyStick: Joystick) {
        const winSize = cc.view.getDesignResolutionSize();
        this.touchPos = event.getLocation();
        joyStick.setStartPoint(cc.v2(event.getLocation().x - winSize.width * 0.5, event.getLocation().y - winSize.height * 0.5));
        joyStick.show();

        this.isTeamMove = true;
        JoystickController.Touching = true;

        // 开启物理碰撞
        let players = this.players;
        players.forEach((player) => {
            if (!player.isDie()) {
                player.openCollision();
            }
            player.startControl();
        });
        this.resetGuidePlayerPos();
        this.updateCameraPosition();
        this.guidePlayer.startControl();
        Timer.frameLoop(1, this, this.updateCameraPosition);
        Timer.frameLoop(1, this, this.updateMainHeroWalk);
    }

    public onJoystickTouchMove(event: cc.EventTouch, joyStick: Joystick) {
        const winSize = cc.view.getDesignResolutionSize();
        let mainPlayer = this.mainPlayer;
        if (mainPlayer != null && mainPlayer.isDie() == false) {
            var currentPos = event.getLocation();
            var moveDir: cc.Vec2 = currentPos.subtract(this.touchPos).normalize();
            this.joyMoveDir.x = moveDir.x;
            this.joyMoveDir.y = moveDir.y;
            joyStick.cursorTo(
                cc.v2(event.getLocation().x - winSize.width * 0.5, event.getLocation().y - winSize.height * 0.5),
                new cc.Vec3(moveDir.x, moveDir.y));
            // TODO: this is realy weird
            MapManager.curMap.update(0.033)
            // App.TreasureBoxController.onEnterAreaOutside()
        }
    }

    updateCameraPosition() {
        let mainPlayer = this.mainPlayer;
        if (mainPlayer != null && mainPlayer.isDie() == false) {
            if (this.guidePlayer) {

                if (this.guidePlayer.sleep) {
                    return;
                }
                let centerPoint = cc.Vec3.ZERO;
                centerPoint = this.guidePlayer.getPosition(centerPoint)//CameraController.instance.getCenterPosition();
                let position: cc.Vec3;
                let dir = this.joyMoveDir.clone().normalize() as unknown as cc.Vec2;
                position = centerPoint.add(cc.v3(dir.x, dir.y).multiplyScalar(40));
                let dx = position.x - centerPoint.x;
                let dy = position.y - centerPoint.y;
                let moveAgle = MathUtils.radiansToDegrees(Math.atan2(dy, dx));
                this.showPointer(position, moveAgle)
                if (MapHelper.isPassablePoint(centerPoint, position)) {

                    this.guidePlayer.setFollow(position)
                }
                else {
                    // let dx = position.x - centerPoint.x;
                    // let dy = position.y - centerPoint.y;
                    // let moveAgle = MathUtils.radiansToDegrees(Math.atan2(dy, dx));
                    // if (moveAgle < 0) moveAgle = moveAgle + 360;
                    // let angle = moveAgle % 180;
                    // let d = 140 - angle;
                    // let rad = cc.misc.degreesToRadians(d);
                    // dir = dir.rotateSelf(rad).normalize();
                    // position = centerPoint.add(cc.v3(dir.x, dir.y).multiplyScalar(25));
                    // if (MapHelper.isPassablePoint(centerPoint, position)) {
                    //     this.guidePlayer.setFollow(position)
                    // }
                    // else {
                    //     this.guidePlayer.setFollow(null)
                    // }
                    this.guidePlayer.setFollow(null)
                }
            }
        }
    }

    public resetGuidePlayerPos() {

        if (!this.guidePlayer) {
            let node = new cc.Node();
            node.parent = MapManager.curMap.entityLayer;
            this.guidePlayer = node.addComponent(GuidePlayer);
            let centerPoint = CameraController.instance.getCenterPosition();
            this.guidePlayer.node.x = centerPoint.x;
            this.guidePlayer.node.y = centerPoint.y;
        }
    }
    public clear() {
        if (this.guidePlayer) {
            this.guidePlayer.removeSelf()
            this.guidePlayer = null;
        }
    }
    /**停止摇杆 */
    public stopJoystick() {
        this.joyMoveDir.x = 0;
        this.joyMoveDir.y = 0;
        JoystickController.Touching = false;
        Timer.clear(this, this.updateMainHeroWalk);
        Timer.clear(this, this.updateCameraPosition);
        this.guidePlayer && this.guidePlayer.setFollow(null)
        // App.EventManager.emitEvent(GameEvent.HIDE_JOYSTICK);
    }

    public getGuidePlayer() {
        return this.guidePlayer;
    }

    public getGuidePlayerPos(): cc.Vec3 {
        let centerPoint = cc.Vec3.ZERO;
        let guide = HeroManager.instance.getGuidePlayer();
        if (guide) {
            centerPoint = guide.getPosition(centerPoint);
        }
        else {
            centerPoint = HeroManager.instance.mainPlayer.getPosition(centerPoint);
        }
        return centerPoint
    }
    /**
    * 英雄属性更新
    */
    protected onHereAttributeUpdate() {
        this.players.forEach((player) => {
            this.updatePlayerAttribute(player);
        });
    }

    public updatePlayerAttribute(player: PlayerActor) {
        //新增的英雄. 设置跟随状态
        // if (!player.isDie()) {
        //     player.isFollow = this.isTeamMove;
        // }
        // let hero: HeroData = App.UserController.heroCenter.getHero(player.data.identity)
        // if (!hero || player.data.isAid) return;
        // player.data.updateAttr(hero);
        // player.updateAttr();
    }

    /** 获取存活的英雄 */
    getAliveHero(): PlayerActor {
        for (let i = 0; i < this.players.length; i++) {
            if (this.players[i].getHp() > 0 && this.players[i].state != UnitState.dead) {
                return this.players[i];
            }
        }
    }

    /** 锁定所有英雄移动  */
    public lockAllHerosMove(): void {
        Timer.clear(this, this.updateMainHeroWalk);
        Timer.clear(this, this.updateCameraPosition);
        this.players.forEach((player: PlayerActor, index: number) => {
            player.setFollow(null)
            player.stopMove();
            player.sleep = true;
            if (!player.isDie())
                player.state = UnitState.idle;
        });
        if (this.guidePlayer) {
            this.guidePlayer.setFollow(null)
            this.guidePlayer.sleep = true;
        }

        this._canMove = false;
    }
    /** 解锁所有英雄移动  */
    public unLockAllHerosMove(): void {
        this._canMove = true;
        this.players.forEach((player: PlayerActor) => {
            player.sleep = false;
            if (!player.isDie())
                player.state = UnitState.idle;
        });
        if (this.guidePlayer)
            this.guidePlayer.sleep = false;
    }

    public canMove(): boolean {
        return this._canMove;
    }

}