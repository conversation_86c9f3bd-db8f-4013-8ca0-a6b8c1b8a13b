export class Bound {
    x?: number;
    y?: number;
    width?: number;
    height?: number;
    data?: any;
    check?: boolean;
    refresh?: boolean;

    constructor(x?: number, y?: number, width?: number, height?: number) {
        this.x = x || 0;
        this.y = y || 0;
        this.width = width || 0;
        this.height = height || 0;
    }

    /**
     * 视野范围是否相同.
     * @param bound 
     * @returns 
     */
    equal(bound: Bound): boolean {
        if (bound.x != this.x) return false;
        if (bound.y != this.y) return false;
        if (bound.width != this.width) return false;
        if (bound.height != this.height) return false;

        return true;
    }


    public contains(rect: Bound): boolean {
        // 判断第一个矩形是否包含第二个矩形
        return (
            rect.x >= this.x &&
            rect.y >= this.y &&
            rect.x + rect.width <= this.x + this.width &&
            rect.y + rect.height <= this.y + this.height
        );
    }

    /**
     * 如果完全重叠就返回null, 如果相交部分. 就返回rect中不相交的部分.  完全不相交就返回rect.
     * @param rect 
     * @returns 
     */
    public getNonIntersectingRange(rect: Bound): Bound | null {
        if(!rect) return null;
        // 判断两个矩形是否相交
        if (
            rect.x + rect.width <= this.x ||
            rect.x >= this.x + this.width ||
            rect.y + rect.height <= this.y ||
            rect.y >= this.y + this.height
        ) {
            // 两个矩形不相交，返回第二个矩形的整个范围
            return new Bound(rect.x, rect.y, rect.width, rect.height);
        }

        if (this.contains(rect) || rect.contains(this)) {
            // 第一个矩形包含第二个矩形，返回 null
            return null;
        }

        // 计算相交部分的范围
        const minX = Math.max(this.x, rect.x);
        const minY = Math.max(this.y, rect.y);
        const maxX = Math.min(this.x + this.width, rect.x + rect.width);
        const maxY = Math.min(this.y + this.height, rect.y + rect.height);

        const nonIntersectingWidth = maxX - minX;
        const nonIntersectingHeight = maxY - minY;
        return new Bound(minX, minY, nonIntersectingWidth, nonIntersectingHeight);
    }


    copy(bound: Bound) {
        this.x = bound.x;
        this.y = bound.y;
        this.width = bound.width;
        this.height = bound.height;
        this.data = bound.data;
        this.check = bound.check;
    }
}