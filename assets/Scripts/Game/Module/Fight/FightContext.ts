import * as cc from "cc";

import { ViewUtil } from "../../Utils/ViewUtils";
import { MapHelper } from "../MapInstance/Base/MapHelper";
import { MapUnitBase } from "../MapInstance/Base/MapUnitBase";
// import Monster from "../../unit/actor/monster/Monster";
import Unit from "../../Unit/Base/Unit";
import { MathUtils } from "../../Utils/MathUtils";
import { Bundles } from "../Bundles";
// import { HeroManager } from "./manager/HeroManager";

/**
 *
 * 战斗环境 (不同的场景不同环境)
 *
 */
export class FightContext {
    protected _map: MapUnitBase;

    protected tempPoint: cc.Vec2 = new cc.Vec2();

    protected temp1Point: cc.Vec2 = new cc.Vec2();

    constructor(map: MapUnitBase) {
        this._map = map;
    }

    destroy() {
        this._map = null;
    }

    // /**
    //  * 进入巡逻
    //  * @param unit
    //  */
    // public patrol(unit: Monster) {
    //     let isFind: boolean = false;
    //     let pointX: number, pointY: number;
    //     //最多找10次. 10次找不到巡逻的坐标就不找了.
    //     for (let i: number = 0; i < 10; i++) {
    //         pointX = unit.data.rebornPosX + MapHelper.Range(-unit.patrolRange, unit.patrolRange);
    //         pointY = unit.data.rebornPosY + MapHelper.Range(-unit.patrolRange, unit.patrolRange);
    //         if (this._map.isPatrolPoint(unit, pointX, pointY)) {
    //             isFind = true;
    //             break;
    //         }
    //     }
    //     if (isFind) {
    //         unit.navTo(pointX, pointY);
    //     } else {
    //         console.warn("找不到巡逻的坐标" + unit);
    //     }
    // }

    // public get monsterList(): Monster[] {
    //     return this._map?.monsterList;
    // }

    // public get players(): any[] {
    //     return this._map?.allPlayers;
    // }

    // public get mainPlayer(): any {
    //     return this._map?.mainPlayer;
    // }





    /**
     * 显示经验增加效果
     * @param pos 坐标
     * @param exp 经验
     * @param color 颜色 0 红色 1 绿色
     */
    showExpEffect(pos: cc.Vec2, exp: number) {
        //let effectNode = GameManager.instance.otherMgr.getExpEffect();
        let effectNode = ViewUtil.createPrefabNode("ExpChangeNode", Bundles.COMMON);
        effectNode.parent = this.SkillTopLayer;
        effectNode.setPosition(pos.x, pos.y, 0);

        let expchangeTxt = effectNode.getChildByName("num").getComponent(cc.Label);
        expchangeTxt.string = "+" + exp;
        // expchangeTxt.setColor(color);
    }

    /** 节点到玩家的距离 */
    getPlayerDistance(unit: Unit): number {
        // if (!this.mainPlayer) return 0;
        // this.temp1Point.x = this.mainPlayer.node.x;
        // this.temp1Point.y = this.mainPlayer.node.y;
        // this.tempPoint.x = unit.node.x;
        // this.tempPoint.y = unit.node.y;
        // return MathUtils.getDistance(this.tempPoint, this.temp1Point);
        return 0;
    }

    /**节点到中心圈的距离 */
    getCenterDistance(unit: Unit): number {
        this.tempPoint.x = unit.node.x;
        this.tempPoint.y = unit.node.y;
        this.temp1Point.x = this.guardCenterPos.x;
        this.temp1Point.y = this.guardCenterPos.y;
        return MathUtils.getDistance(this.tempPoint, this.temp1Point);
    }

    /**节点到队伍中心点的距离 */
    // getPlayersCenterDistance(unit: Unit): number {
    //     this.tempPoint.x = unit.node.x;
    //     this.tempPoint.y = unit.node.y;

    //     let pointList = HeroManager.instance.getHeroPoints();
    //     let centerPoint = HeroManager.instance.calculateCenterPoint(pointList);

    //     this.temp1Point.x = centerPoint.x;
    //     this.temp1Point.y = centerPoint.y;
    //     return MathUtils.getDistance(this.tempPoint, this.temp1Point);
    // }

    /**
     * 是否在安全区域
     * @returns
     */
    isInSafeArea(): boolean {
        return this._map?.isInSafeArea;
    }

    get SkillTopLayer(): cc.Node {
        return this._map?.skillTopLayer;
    }

    get SkillBottomLayer(): cc.Node {
        return this._map?.skillBottomLayer;
    }

    get entityLayer(): cc.Node {
        return this._map?.entityLayer;
    }

    get guardCenterPos(): cc.Vec3 {
        return this._map?.guardCenterPos;
    }

    get mapLayer(): cc.Node {
        return this._map?.mapLayer;
    }

    get fogLayer(): cc.Node {
        return this._map?.fogLayer;
    }
}
