import { UnitState } from "../../../Unit/UnitState";


export interface State {
    /**状态类型 */
    type: UnitState;
    /**进入状态 */
    enter(): void;
    /**离开状态 */
    exit(): void;
    /**更新状态 */
    update(): void;
    /**是否能处于这个状态 */
    can(type: UnitState): boolean;
}

class BaseState implements State {

    type: UnitState;

    constructor(type: UnitState) {
        this.type = type;
    }

    enter(): void {
        console.log(`Entered ${this.type} state.`);
    }

    exit(): void {
        console.log(`Exited ${this.type} state.`);
    }

    update(): void {
        console.log(`Updating ${this.type} state.`);
    }

    can(type: UnitState): boolean {
        return true;
    }
}