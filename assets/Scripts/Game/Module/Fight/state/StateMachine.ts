import { UnitState } from "../../../Unit/UnitState";
import { State } from "./BaseState";



/**
 * 状态机类
 */
export class StateMachine {

    /** 状态集合 */
    private stateMap: Map<UnitState, State>;

    /**当前状态的集合 */
    private currentStates: State[];

    constructor() {
        this.stateMap = new Map();
        this.currentStates = [];
    }

    /** 添加一个状态 */
    addState(state: State): void {
        this.stateMap.set(state.type, state);
    }

    /** 进入一个状态 */
    enterState(type: UnitState): void {
        if (this.stateMap.has(type)) {
            const state = this.stateMap.get(type);
            if (state) {
                state.enter();
                this.currentStates.push(state);
            }
        } else {
            console.log(`State '${type}' doesn't exist.`);
        }
    }

    /** 退出一个状态 */
    exitState(type: UnitState): void {
        const index = this.currentStates.findIndex((state) => state.type === type);
        if (index !== -1) {
            const state = this.currentStates[index];
            state.exit();
            this.currentStates.splice(index, 1);
        }
    }

    /**
     * 是否处于这个状态
     * @param type 
     */
    is(type: UnitState): boolean {
        for (const state of this.currentStates) {
            if (state.type === type) {
                return true;
            }
        }
        return false;
    }

    /** 是否能转换到这个状态. */
    can(type: UnitState): boolean {
        for (const state of this.currentStates) {
            if (!state.can(type)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 处于控制状态
     */
    isControl(): boolean {
        let controlStates: UnitState[] = [
            UnitState.freeze
        ]

        return false;
    }

    /** 帧更新 */
    update(): void {
        for (const state of this.currentStates) {
            state.update();
        }
    }
}