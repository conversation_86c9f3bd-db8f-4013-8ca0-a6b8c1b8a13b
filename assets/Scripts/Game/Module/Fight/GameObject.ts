import * as cc from "cc";

/**
 * 游戏对象基类.
 */
export class GameObject {

    id: number;

    x: number;

    y: number;

    width: number;

    height: number;


    protected _active: boolean;
    /** 激活状态  重写请使用set_active*/
    set active(value: boolean) {
        if (this._active == value) {
            return;
        }
        this._active = value;
        this.set_active(value);
    }

    protected set_active(value: boolean) {
        value ? this.onEnable() : this.onDisable();
    }

    /** 激活状态  重写请使用get_active*/
    get active(): boolean {
        return this.get_active();
    }

    protected get_active(): boolean {
        return this._active;
    }

    /** 获取坐标信息 */
    getPosition(out?: cc.Vec3): cc.Vec3 {
        if (!out) out = new cc.Vec3();
        out.x = this.x;
        out.y = this.y;
        out.z = 0;
        return out;
    }

    /** 获取Size信息 */
    getSize(out?: cc.Size): cc.Size {
        if (!out) out = new cc.Size(0, 0);
        out.width = this.width;
        out.height = this.height;
        return out;
    }

    /**
     * 启用
     */
    protected onEnable() {

    }

    /**
     * 禁用
     */
    protected onDisable() {

    }
}