import { _decorator, Label, Node } from "cc";

import BaseView from "../../Components/BaseView";
import UIButton from "../../Components/UIButton";
const { ccclass, property } = _decorator;

@ccclass("MsgBoxEx")
export class MsgBoxEx extends BaseView{
    @property(UIButton)
    okBtn: UIButton = null;

    @property(Label)
    content: Label = null;
    /** 回调确认方法 */
    private okfunc: Function = null;
    private okTargetObj: any = null;

    onLoad() {
        this.okBtn.setOnClick(() => {
            this.callOkCallback();
        }, this);
        this.init();
    }

    init() {
        //英雄技能等级配置
        let content:string = this.viewData["content"];
        this.content.string = content;

        if (this.viewData["fun"])
            this.setOKCallBack(this.viewData["fun"],this.viewData["obj"])
    }

    /**
     * 设置确认按钮的回调方法
     * @param func 
     * @param target 
     */
    public setOKCallBack(func: Function, target: any): void {
        this.okTargetObj = target;
        this.okfunc = func;
    }

    public callOkCallback(): void {
        if (this.okfunc) {
            this.okfunc.apply(this.okTargetObj);
        }

        this.close();
    }

}