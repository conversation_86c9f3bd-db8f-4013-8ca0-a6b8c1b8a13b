import * as cc from "cc";

import { GameEvent, TipEvent} from "../../../GameEvent/Events";
import ColorUtils from "../../Utils/ColorUtils";
import { Bundles } from "../Bundles";
import { TipVo } from "./TipVo";
import ResSprite from "../../Components/ResSprite";
import EventManagerComponent from "../../GlobalComponent/EventManagerComponent";

const { ccclass, property } = cc._decorator;

/**
 * 通用提示组件
 */
@ccclass
export default class Tip extends cc.Component {
    /** 背景 */
    @property(cc.Sprite)
    public bg: cc.Sprite = null;

    /** 提示内容 */
    @property(cc.Label)
    public labContent: cc.Label = null;

    /** 资源图标 */
    @property(ResSprite)
    public icon: ResSprite = null;

    /** 最小宽度 */
    private minWidth: number = 350;
    /** 间距 */
    private spacing: number = 110;

    protected onLoad(): void {
        this.labContent.string = "";
        // this.icon.setSpriteFrame(null);
        const transform = this.bg.node.getComponent(cc.UITransform);
        transform.setContentSize(this.minWidth, transform.height);
    }

    protected start(): void {

        this.updateWidth();
    }

    protected update(dt: number): void {

    }

    protected onDestroy(): void {

    }

    public show(vo: TipVo) {
        // if (vo == null) {
        //     return;
        // }
        // let itemCfg: CFG_Item = ItemCache.Instance.getOrNull(vo.getId());
        // if (!itemCfg) {
        //     return;
        // }
        // this.bg.node.scaleY = 0.1;
        // this.bg.node.opacity = 255;
        // if(itemCfg.getKind()=== ItemType.EQUIP){
        //     this.icon.setSpriteFrame(Bundles.EQUIPMENT + itemCfg.getIcon());
        // }else{
        //     this.icon.setSpriteFrame(Bundles.ITEM + itemCfg.getIcon());
        // }
        // let itemName: string = App.getLan(itemCfg.getNameLang());
        // this.labContent.string = `${itemName}+${vo.getValue()}`;
        // this.labContent.node.color = ColorUtils.getColorByQuality(itemCfg.getQuality());
        // this.openUp();
    }

    public updateWidth() {
        const transform = this.labContent.node.getComponent(cc.UITransform);
        let maxWidth: number = transform.width + this.spacing;
        transform.setContentSize(maxWidth > this.minWidth ? maxWidth : this.minWidth, transform.height);
    }

    /**
     * 张开到显示，然后淡化消失效果
     */
    public openUp() {
        const iconTransform = this.icon.node.getComponent(cc.UITransform);
        cc.tween(this.bg.node).stop();
        let t0 = cc.tween().to(0.1, { scaleY: 1 }).call(() => {
            iconTransform.setContentSize(38, 38);
        }, this);
        // let t1 = cc.delayTime(1.5);
        let t2 = cc.tween().to(0.65, { y: 134 })
        // let t3 = cc.tween().parallel(t1, t2);
        let t4 = cc.tween().to(0.25, { opacity: 0 });
        let t5 = cc.tween().call(() => {
            this.node.removeFromParent();
            EventManagerComponent.instance().dispatch(TipEvent.ONCE_COMMON_TIP_FINISH, null);
            this.node.destroy();
            // EventManagerComponent.getInstance().dispatch(TipEvent.OPEN_GAIN_NEW_HERO_VIEW);
        }, this);
        let sequence = cc.tween(this.bg.node).sequence(t0, t2, t4, t5);
        sequence.start()
    }
}
