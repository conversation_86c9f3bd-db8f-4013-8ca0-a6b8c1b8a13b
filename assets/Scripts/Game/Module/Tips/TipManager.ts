import * as cc from "cc";
import { GameEvent, TipEvent } from "../../../GameEvent/Events";
import { TipPowerVo } from "./TipPowerVo";
import TipPower from "./TipPower";
import { TipVo } from "./TipVo";
import Tip from "./Tip";

import EventManagerComponent from "../../GlobalComponent/EventManagerComponent";
import SceneBase from "../../Components/SceneBase";

const { ccclass, property } = cc._decorator;

/**
 * 提示管理类
 */
@ccclass
export class TipManager extends cc.Component {
    private static _instance: TipManager;

    /** 战力消息列表 */
    private powerMsg: TipPowerVo[];
    private powerTipName: string = "power_tip";

    /** 通用提示消息列表 */
    private tipMsg: TipVo[];

    /** 战力飘字预制体 */
    @property(cc.Prefab)
    public tipPowerPrefab: cc.Prefab = null;

    /** 通用消息预制体 */
    @property(cc.Prefab)
    public tipPrefab: cc.Prefab = null;

    private isMultiPower: boolean = false;

    constructor() {
        super();

        this.init();
    }

    public static get ins(): TipManager {
        if (TipManager._instance == null) {
            TipManager._instance = new TipManager();
        }
        return TipManager._instance;
    }

    private init() {
        if (this.powerMsg == null) {
            this.powerMsg = [];
        }

        if (this.tipMsg == null) {
            this.tipMsg = [];
        }
    }

    protected onLoad(): void {
        EventManagerComponent.instance().addEventListener(
            TipEvent.ONCE_POWER_TIP_FINISH, this.onPowerTipFinish, this);
        EventManagerComponent.instance().addEventListener(
            TipEvent.ONCE_COMMON_TIP_FINISH, this.onPowerTipFinish, this);
    }

    protected start(): void {
    }

    protected update(dt: number): void {
    }

    protected onDestroy(): void {
        EventManagerComponent.instance().removeEventListener(
            TipEvent.ONCE_POWER_TIP_FINISH, this.onPowerTipFinish, this);
        EventManagerComponent.instance().removeEventListener(
            TipEvent.ONCE_COMMON_TIP_FINISH, this.onTipFinish, this);
    }

    /**
     * 插入战力提示消息
     * @param vo 战力提示数据
     */
    public pushPowerMsg(vo: TipPowerVo) {
        this.powerMsg.push(vo);
        this.isMultiPower = this.powerMsg.length > 1 ? true : false;
    }

    public showPowerMsg() {
        if (this.powerMsg.length > 0) {
            this.schedule(this.readPowerMsg, 0.4);
            // this.readPowerMsg();
        }
    }

    /**
     * 插入通用提示消息
     * @param vo 通用提示消息
     */
    public pushTipMsg(vo: TipVo) {
        this.tipMsg.push(vo);
    }

    public showTipMsg() {
        // let parent: cc.Node = this.getTipLayer();
        // if (parent == null) {
        //     return;
        // }
        // let showMax: number = (parent.height - 260 - 400) / 67;
        // this.maxShow = Math.floor(showMax);
        // Logger.trace("最大显示个数:", this.maxShow);

        if (this.tipMsg.length > 0) {
            this.schedule(this.readTipMsg, 1 / 3);
            this.readTipMsg();
        }
    }

    /**
     * 逐条读取战力消息提示
     */
    private readPowerMsg() {
        if (this.powerMsg.length > 0) {
            // let vo: TipPowerVo = this.powerMsg.shift();
            let vo: TipPowerVo = this.powerMsg[0];
            this.nextPowerTip(vo);
        }
    }

    private readTipMsg() {
        // if (this.tipShowing) {
        //     return;
        // }
        // if (this.tipMsg.length > 0) {
        //     this.tipShowing = true;
        //     for (let i = 0; i < this.maxShow; i++) {
        //         let vo: TipVo = this.tipMsg.shift();
        //         this.nextTip(vo, i);
        //     }
        // }
        if (this.tipMsg.length > 0) {
            let vo: TipVo = this.tipMsg.shift();
            this.nextTip(vo);
        }
    }

    /**
     * 读取下一条战力消息
     * @param vo 战力消息vo
     * @returns 
     */
    private nextPowerTip(vo: TipPowerVo) {
        if (vo == null) {
            return;
        }
        let parent: cc.Node = this.getTipLayer();
        if (parent == null) {
            return;
        }
        // let nodeTip: cc.Node = parent.getChildByName(this.powerTipName);
        // if (nodeTip) {
        //     let tipPower: TipPower = nodeTip.getComponent(TipPower);
        //     tipPower.show(vo);
        // } else {
        //     let node = cc.instantiate(this.tipPowerPrefab);
        //     if (node == null) {
        //         return;
        //     }
        //     node.name = this.powerTipName;
        //     parent.addChild(node);
        //     this.popUp(node);
        //     let tipPower: TipPower = node.getComponent(TipPower);
        //     tipPower.show(vo);
        // }
        let node = cc.instantiate(this.tipPowerPrefab);
        if (node == null) {
            return;
        }
        node.name = this.powerTipName;
        parent.addChild(node);
        this.popUp(node);
        let tipPower: TipPower = node.getComponent(TipPower);
        tipPower.show(vo);
    }

    /**
   * 读取下一条战力消息
   * @param vo 通用消息vo
   * @returns 
   */
    private nextTip(vo: TipVo) {
        if (vo == null) {
            return;
        }
        let parent: cc.Node = this.getTipLayer();
        if (parent == null) {
            return;
        }
        let node = cc.instantiate(this.tipPrefab);
        if (node == null) {
            return;
        }
        // node.y = -(parent.height / 2 - 300) + index * 67;
        parent.addChild(node);
        let tip: Tip = node.getComponent(Tip);
        tip.show(vo);
    }

    private onPowerTipFinish() {
        if (this.powerMsg.length <= 0) {
            this.unschedule(this.readPowerMsg);
            // let parent: cc.Node = this.getTipLayer();
            // let nodeTip: cc.Node = parent.getChildByName(this.powerTipName);
            // if (nodeTip) {
            //     this.shrink(nodeTip);
            // }
        }
    }

    private onTipFinish() {
        // this.tipShowing = false;
        // this.readTipMsg();
        if (this.tipMsg.length <= 0) {
            this.unschedule(this.readTipMsg);
        }
    }

    /**
     * 获取当前场景提示层
     */
    public getTipLayer(): cc.Node {
        let scene: cc.Scene = cc.director.getScene();
        if (scene == null) {
            return;
        }
        let sceneBase: SceneBase = scene.getComponentInChildren(SceneBase);
        if (sceneBase == null || sceneBase.tipLayer == null) {
            return;
        }
        return sceneBase.tipLayer;
    }

    /**
     * 弹出效果
     * @param node 作用节点
     * @returns 
     */
    public popUp(node: cc.Node) {
        if (node == null) {
            return;
        }
        const opacityComp = node.getComponent(cc.UIOpacity);
        cc.tween(node).stop();
        node.setScale(cc.v3(0, 0, 1));
        opacityComp.opacity = 0;
        let t0 = cc.tween().to(0.3, { scale: cc.v3(1, 1, 1) }, { easing: "backInOut" });
        let t1 = cc.tween().to(0.5, { opacity: 255 }, { easing: 'fade' });
        let pt = cc.tween().parallel(t0, t1);
        let t3 = cc.tween().delay(0.5);
        let t4 = cc.tween().to(0.3, { y: 100 })
        let t5 = cc.tween(opacityComp).to(0.2, { opacity: 0 }, { easing: 'fade' });
        let t6 = cc.tween().call(() => {
            this.shrink(node);
            EventManagerComponent.instance().dispatch(TipEvent.ONCE_POWER_TIP_FINISH, null);
        }, this);
        let sequence: cc.Tween<cc.Node> = null;
        if (this.isMultiPower) {
            sequence = cc.tween(node).sequence(pt, t3, t4, t5, t6);
        } else {
            sequence = cc.tween(node).sequence(pt, t3, t6);
        }
        sequence.start();
        this.powerMsg.shift()

        // cc.tween(node).to(0.3, { scale: 1 }, { easing: "backInOut" }).start();
        // let t0 = cc.tween().to(0.1, { scale: 1.2 }, { easing: "easeOutSine" });
        // let t1 = cc.tween().to(0.05, { scale: 1 });
        // let sequence = cc.tween(node).sequence(t0,t1);
        // sequence.start();
    }

    /**
     * 收回效果
     * @param node 作用效果
     */
    public shrink(node: cc.Node) {
        if (node == null) {
            return;
        }
        cc.tween(node).stop();
        // let t0 = cc.delayTime(0.15);
        let t1 = cc.tween().to(0.1, { scale: 1.2 });
        let t2 = cc.tween().to(0.1, { scale: 0 });
        let t3 = cc.tween().call(() => {
            node.removeFromParent();
            node.destroy();
        }, this);
        let sequence = cc.tween(node).sequence(t1, t2, t3);
        sequence.start();
    }
}