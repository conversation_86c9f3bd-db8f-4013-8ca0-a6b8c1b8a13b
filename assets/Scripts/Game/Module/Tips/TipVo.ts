/**
 * 提示vo
 */
export class TipVo {
    /** 道具id */
    private id: number;
    /** 值 */
    private value: number;

    constructor() {
        this.init();
    }

    private init() {
        this.id = 0;
        this.value = 0;
    }

    /**
     * 更新
     * @param id 道具id
     * @param num 更新数量
     */
    public update(id: number, num: number) {
        this.setId(id);
        this.setValue(num);
    }

    public getId(): number {
        return this.id;
    }

    /**
     * 设置id
     * @param id 道具id
     */
    public setId(id: number) {
        this.id = id;
    }

    public getValue(): number {
        return this.value;
    }

    /** 设置值 */
    public setValue(value: number) {
        this.value = value;
    }
}