/**
 * 战力提示VO
 */
export class TipPowerVo {
    /** 当前战力 */
    private power: number;
    /** 增加战力 */
    private addValue: number;
    /** 总战力 */
    private totalPower: number;

    constructor() {
        this.init();
    }

    private init() {
        this.power = 0;
        this.addValue = 0;
        this.totalPower = 0;
    }

    /**
     * 更新
     * @param power 当前战力
     * @param addValue 增加战力
     */
    public update(power: number, addValue: number) {
        if (power == null || addValue == null) {
            return;
        }
        this.setPower(power);
        this.setAddValue(addValue);
        this.setTotalPower(power + addValue);
    }

    public getPower(): number {
        return this.power;
    }

    /**
     * 设置当前战力
     * @param power 当前战力
     */
    public setPower(power: number) {
        this.power = power;
    }

    public getAddValue(): number {
        return this.addValue;
    }

    /**
     * 设置增加战力
     * @param value 增加战力
     */
    public setAddValue(value: number) {
        this.addValue = value;
    }

    public getTotalPower(): number {
        return this.totalPower;
    }

    /**
     * 设置总战力
     * @param totalPower 总战力
     */
    public setTotalPower(totalPower: number) {
        this.totalPower = totalPower;
    }
}