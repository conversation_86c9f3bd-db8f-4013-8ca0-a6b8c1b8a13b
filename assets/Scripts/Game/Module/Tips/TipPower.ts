import * as cc from "cc";

import { TipPowerVo } from "./TipPowerVo";
import EventManagerComponent from "../../GlobalComponent/EventManagerComponent";
import { TipEvent } from "../../../GameEvent/Events";
import NumberUtils from "../../Utils/NumberUtils";

const { ccclass, property } = cc._decorator;

/**
 * 战力提示组件
 */
@ccclass
export default class TipPower extends cc.Component {
    /** 背景 */
    @property(cc.Sprite)
    public bg: cc.Sprite = null;

    /** 当前战力 */
    @property(cc.Label)
    public labCurPower: cc.Label = null;

    /** 加值节点 */
    @property(cc.Node)
    public nodeAdd: cc.Node = null;

    /** 战力增加值 */
    @property(cc.Label)
    public labAdd: cc.Label = null;

    private vo: TipPowerVo;
    private minWidth: number = 407;
    private spacingX: number = 13;
    private leftValue: number = 105;

    protected onLoad(): void {
        this.labCurPower.string = "";
        this.labAdd.string = "";
        this.nodeAdd.active = false;
    }

    protected start(): void {
    }

    protected update(dt: number): void {
        this.updateBgWidth();
    }

    protected onDestroy(): void {

    }

    /**
     * 显示战力提示内容
     * @param vo 战力提示数据VO
     * @returns 
     */
    public show(vo: TipPowerVo) {
        if (vo == null) {
            return;
        }
        this.vo = vo;
        if (vo.getPower() != null) {
            this.labCurPower.string = `${vo.getPower()}`;
        }
        if (this.vo.getAddValue() != null) {
            this.labAdd.string = `${this.vo.getPower() + this.vo.getAddValue()}`;
            this.nodeAdd.x = this.labCurPower.node.x + this.labCurPower.getComponent(cc.UITransform).width + this.spacingX;
        }

        // let dl = cc.delayTime(0.8)
        // let t2 = cc.tween().to(0.5, { y: 100 })
        // let cf = cc.callFunc(() => {
        //     // this.moveToLeft();
        //     GameManager.instance.tipManager.shrink(this.node);
        //     App.EventManager.emitEvent(GameEvent.ONCE_POWER_TIP_FINISH);
        // }, this)
        // let sequence = cc.tween(this.node).sequence(dl, t2, cf);
        // sequence.start();
    }

    /**
  * 张开到显示，然后淡化消失效果
  */
    public openUp() {
        cc.tween(this.bg.node).stop();


        let t0 = cc.tween(this.bg.node).delay(0.3)
            .to(0.65, { y: 134 });

        let t1 = cc.tween(this.bg.color)
            .to(0.25, { a: 0 })
        let t2 = cc.tween(this.bg.node)
            .call(() => {
                this.node.removeFromParent();
                EventManagerComponent.instance().dispatch(TipEvent.ONCE_POWER_TIP_FINISH, null);
            })
        let t3 = cc.tween(this.bg.node)
            .call(() => {
                NumberUtils.rollNumber(this.labCurPower, this.vo.getPower(), this.vo.getAddValue());
                this.node.destroy();
            })
        let sequence = cc.tween(this.bg.node).sequence(t0, t1, t2, t3);
        sequence.start()
    }

    /**
     * 更新背景宽度
     */
    public updateBgWidth() {
        let width = this.labCurPower.getComponent(cc.UITransform).width + this.spacingX 
            + this.nodeAdd.getComponent(cc.UITransform).width + this.leftValue + 50;
        if (Math.floor(this.bg.getComponent(cc.UITransform).width) != Math.floor(width)) {
            // this.bg.node.width = width <= this.minWidth ? this.minWidth : width;
            // this.labCurPower.node.x = this.bg.node.x - this.bg.node.width / 2 + this.leftValue;
            // this.nodeAdd.x = this.labCurPower.node.x + this.labCurPower.node.width + this.spacingX;
            this.nodeAdd.x = this.labCurPower.getComponent(cc.UITransform).width / 2;
        }
    }

    /**
     * 数字左移(绿色字部分)
     */
    private moveToLeft() {
        cc.tween(this.nodeAdd).stop();
        const opacityComp = this.nodeAdd.getComponent(cc.UIOpacity);
        opacityComp.opacity = 0;
        let t0 = cc.tween().delay(0.15);
        let t1 = cc.tween(opacityComp).to(0.15, { opacity: 255 });
        let t2 = cc.tween(this.nodeAdd).call(() => {
            if (this.vo.getAddValue() != null) {
                this.nodeAdd.active = true;
                this.labAdd.string = `${this.vo.getAddValue()}`;

            } else {
                this.nodeAdd.active = false;
            }
        }, this);
        let t3 = cc.tween().delay(0.35);
        let t4 = cc.tween().to(0.3, { x: 0 });
        let t5 = cc.tween(opacityComp).to(0.3, { opacity: 0 })
        let pt = cc.tween().parallel(t4, t5);
        let t6 = cc.tween(this.nodeAdd).call(() => {
            NumberUtils.rollNumber(this.labCurPower, this.vo.getPower(), this.vo.getAddValue());
        });
        let t7 = cc.tween().delay(1.2);
        let t8 = cc.tween(this.nodeAdd).call(() => {
            EventManagerComponent.instance().dispatch(TipEvent.ONCE_POWER_TIP_FINISH, null);
        });
        let sequence = cc.tween(this.nodeAdd).sequence(t0, t1, t2, pt, t6, t7, t8);
        sequence.start();
    }
}