import * as cc from "cc";

import { AppConfig } from "../../Config/GameConfig";
import Logger from "../../Framework/Logger";
import Singleton from "../../Framework/Utils/Singleton";
// import GoogleSDK from "../sdk/GoogleSDK";
import { FileSystemManager}  from "../../Framework/FileSystem";
import * as biu from "@paygo-biu/minigame-sdk";
import { request } from "../../Framework/Network/http";
import { default as SdkBase } from "./SdkBase"

export enum TimingType {
    NONE = 0,
    SDK_LOGIN_BEGIN = 4,//SDK开始登陆
    SDK_LOGIN_SUCESS = 5,//SDK的登录成功
    SDK_LOGIN_FAIL = 6,//SDK的登录失败
    REQ_SERVER_VERIFY_BEGIN = 7,//请求服务器账号验证
    REQ_SERVER_VERIFY_SUCESS = 8,//请求服务器账号验证成功
    REQ_SERVER_VERIFY_FAIL = 9,//请求服务器账号验证失败

    REQ_SERVER_LIST_BEGIN = 10,//请求服务器列表
    REQ_SERVER_LIST_SUCESS = 11,//请求服务器列表成功
    REQ_SERVER_LIST_FAIL = 12,//请求服务器列表失败
    START_GAME = 13,//点击开始游戏
    REQ_SOCKET_CONNECT_SUCESS = 14,//向服务器socket连接成功

    REQ_SOCKET_LOGIN_BEGIN = 15,//向服务器发送登陆请求协议
    REQ_SOCKET_LOGIN_SUCESS = 16,//向服务器发送登陆请求协议成功
    REQ_SOCKET_LOGIN_FAIL = 17,//向服务器发送登陆请求协议失败
    REQ_SERVER_PUSH_DATA_END = 19,//服务器推送数据结束
    IN_GAME = 20, //进入游戏
    COUNT = IN_GAME + 1
}
/**
 * 上报数据类型
 */
export enum ReportType {
    None = 0,
    SelectServer = 1, //选择服务器
    CreateRole = 2, // 创建角色
    EnterGame = 3, // 进入游戏
    UpgradeLv = 4, // 等级提升
    ExitGame = 5, //退出游戏
}

export class ReportData {
    roleId: number;
    roleName: string;
    roleLevel: number;
    serverId: number;
    serverName: string;
    createRoleTime: number;
    money?: number;
    experience?: number;

}

export default class SdkManger extends Singleton {

    private _mInstance: SdkBase = null;
    private _stamp: number = 0;
    static pay1yuan = false;
    static logURL = "";

    //TODO:以后不止一个渠道，要写枚举
    /**SDK渠道标识 */
    public channelIdentity: string = "";

    /** 是否是安卓原生环境 */
    public static isAndroidRunTime(): boolean {
        if (cc.sys.isNative && cc.sys.os == cc.sys.OS.ANDROID) {
            return true;
        }
        return false;
    }

    /** 是否微信小程序环境 */
    public static isWeChatRunTime(): boolean {
        let isWeChat: boolean = false;
        //头条全局变量有wx跟tt这两个属性。。
        if (window.hasOwnProperty("wx") == true && window.hasOwnProperty("tt") == false) {
            isWeChat = true;
        }
        return isWeChat;
    }

    /** 策略判断为相对低配设备 */
    public static isdBadPerformance(): boolean {
        if (cc.sys.os === cc.sys.OS.IOS && cc.sys.platform === cc.sys.Platform.WECHAT_GAME) {

            let wx = window["wx"]
            if (wx) {
                Logger.trace("getSystemInfoSync" + wx.getSystemInfoSync())
            }

            return true;

        }
        return false;
    }



    /** 判断是否支持本地文件系统 */
    public static isSupportFileSystem(): boolean {
        if (FileSystemManager.getInstance()) {
            return true;
        } else {
            return false;
        }
    }

    public initGameSdk(): void {
        biu.initBiu({
            appId: AppConfig.biuAppId,
        }).then(() => {
            this._mInstance = new SdkBase();
            this._mInstance.sdkLogin(AppConfig.channelId, AppConfig.gameVersion);
            this._mInstance.requirePrivacyAuthorize();//弹出隐私框
        });
    }


    public getSdkBase(): SdkBase {
        return this._mInstance;
    }

    /**打点 */
    public static requestDot(channelId: number, openId: string, timing: TimingType) {

        if (timing <= TimingType.NONE || timing >= TimingType.COUNT) {
            return;
        }

        let timer;
        request({
            url: `${AppConfig.apiUrl}/api/v1/loginDot`,
            method: "GET",
            query: {
                channelId: channelId,
                openId: openId,
                timing: timing,
            },
        }).then((res) => {
            Logger.trace("requestDot res:", res);
        }).catch(e => {
            Logger.error("requestDot error:", e);
        });
    }
}

// let javaTojs = (fun: string, data: any) => {
//     console.log("js:javaTojs params:", fun, JSON.stringify(data));
//     App.SdkManger.getSdkBase()[fun](JSON.stringify(data))
// }

// window["javaTojs"] = javaTojs;