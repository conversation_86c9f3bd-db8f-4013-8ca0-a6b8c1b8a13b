import * as cc from "cc";
import { AppConfig } from "../../Config/GameConfig";
import Logger from "../../Framework/Logger";
import { ReportData, ReportType, TimingType } from "./SDKManager";
import EventManagerComponent from "../GlobalComponent/EventManagerComponent";
import * as biu from "@paygo-biu/minigame-sdk"
import { LoginEvent } from "../../GameEvent/Events";
import { Player } from "../../GameData"




declare var require: any;
//回调结构
export interface SdkCallBackType {
    succFunc: Function;
    failFun: Function;
    target: any;
}

export class AccountInfo {

    token: string = "";

    accoutId: string = "";

    timestamp: string = "";

    idCard: string = "";
}

export default class SdkBase {
    //SDK 名称
    public SdkName: string = "biu";

    public iosPayOpen: boolean = false;

    /** 显示切换账号 */
    public showSwitchAccount: boolean = false;

    /**  游戏角色ID*/
    public static account_gameUserId: string = "";
    /**  游戏邀请者ID*/
    public static account_gameInviter: string = "";

    /** 安卓apk版本 */
    public static android_apk_code: string = "";
    /** 安卓代码版本 */
    public static android_versioncode: string = "";

    /**分享者传入的信息 */
    protected _extraData: string = null;

    protected _loginResult: any;

    constructor() {
        this.SdkName = "biu";
    }

    public reset() {

    }

    /**
     * 初始化SDK
     */
    public initSDK(): Promise<void> {
        return biu.initBiu({
            appId: AppConfig.biuAppId,
        });
    }

    public msgSecCheck(type: number, content: string, callBack) {
        callBack && callBack(true)
    }

    /**监听隐私接口需要用户授权事件 */
    public onNeedPrivacyAuthorization(): void {
        const wx = window['wx'];
        wx && wx.onNeedPrivacyAuthorization((resolve, eventInfo) => {
            // ------ 自定义弹窗逻辑 ------ //
            // showCustomPopup()
            // -------弹窗后根据用户操作，进行以下逻辑逻辑 ------- //
            // 开发者弹出自定义的隐私弹窗，并调用 resolve 告知平台已经弹窗
            resolve({ event: 'exposureAuthorization' })
            // 用户点击同意后，开发者调用 resolve 告知平台用户已经同意
            resolve({ event: 'agree' })
            // 用户点击拒绝后，开发者调用 resolve 告知平台用户已经拒绝
            resolve({ event: 'disagree' })
        });
    }

    /**弹出隐私框接口调用，并触发隐私弹窗逻辑 */
    public requirePrivacyAuthorize(): void {
        const eventManager = EventManagerComponent.instance();
        const wx = window['wx'];
        if (wx) {
            if (wx.requirePrivacyAuthorize) {
                wx.requirePrivacyAuthorize({
                    success: () => {
                        // 用户同意授权
                        // runGame() 继续游戏逻辑
                        eventManager.dispatch(LoginEvent.CHECK_WX_PRIVACY, true);
                    },
                    fail: () => {
                        // 用户拒绝授权
                        eventManager.dispatch(LoginEvent.CHECK_WX_PRIVACY, false);
                    },
                    complete: () => {

                    }
                });
            }
            else {
                // wx.showModal({
                //     title: '提示',
                //     content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。'
                // })
            }
        }
    }

    /**跳转至隐私协议链接页面 */
    public openPrivacyContract(): void {
        const wx = window['wx'];
        wx.openPrivacyContract({
            success: () => { }, // 打开成功
            fail: () => { }, // 打开失败
            complete: () => { }
        })
    }

    /**复制文本剪切板 */
    public copyTextClipboard(text: string): void {
        const wx = window['wx'];
        wx.setClipboardData({
            data: text,
            success(res) {
                wx.getClipboardData({
                    success(res) {
                        Logger.trace("复制成功", res.data);
                    }
                })
            }
        })
    }


    /**
     * 调用sdk方登录
     */
    public sdkLogin(channelId: number, gameVersion: number) {
        Logger.trace("sdkLogin", channelId, gameVersion);
        Player.instance().setChannelAndVersin(channelId, gameVersion);
        return Player.instance().sdkLogin();
    }

    /**调用sdk支付 */
    public sdkPay(res: any) {

    }

    /**调用sdk分享 */
    public sdkShare() {

    }

    /**是否显示微信分享按钮 */
    public isShowShareBtn(): boolean {
        return false;
    }

    /**跳转问卷调查 */
    public sdkNavigateToQuestionnaire(): void {
        // let req = pb.SanQiHuYuJumpAskRequest.fromObject({});
        // App.Socket.send(pb.SanQiHuYuJumpAskRequest.Proto.ID, pb.SanQiHuYuJumpAskRequest.encode(req).finish());
    }


    /**获取用户数据信息 */
    public getUserInfo(): Player {
        return Player.instance();
    }


    public roleLogin(data) {
        // SdkManger.requestDot(AppConfig.channelId, AppConfig.openId, TimingType.REQ_SOCKET_LOGIN_BEGIN);
        // let req = pb.LoginRequest.fromObject({})
        // req.openId = CommonUtils.getClientId();
        // req.serverId = AppConfig.serverID;
        // req.deviceType = cc.sys.os.toUpperCase();
        // req.deviceId = "1";
        // req.channelId = AppConfig.channelId;
        // req.channelIdentity = App.SdkManger.channelIdentity;
        // //是否为断线重连状态
        // Logger.trace("断线重连状态：" + App.Socket.isReconnectState);
        // if (App.Socket.isReconnectState) {
        //     req.reConnect = true;
        //     App.Socket.isReconnectState = false;
        // } else {
        //     req.reConnect = false;
        // }
        // App.Socket.send(pb.LoginRequest.Proto.ID, pb.LoginRequest.encode(req).finish());
    }

    //是否直接进入服务器
    public isDirectlyEnter() {
        return false;
    }

    //退出游戏
    public eixtGame(): void {
        cc.game.end();
    }

    //退出账号
    public logout(): void {
        Player.instance().logout();
    }

    isLogined(): boolean {
        return Player.instance().isLogin;
    }

    /**
     * 手机震动 (短震)
     * @returns 返回true表示支持手机震动
     */
    public vibrateShort(): boolean {
        return false;
    }
    public vibrateLong(): boolean {
        return false;
    }

    //触发垃圾回收
    public triggerGC(): void {
        // this.sdkError('triggerGC');
    }

    /** 重登平台 */
    public reloginPlatform() {

    }

    /** 授权个人信息方案标识(0不支持 1代码拉起授权 2通过平台按钮拉起授权) */
    public isSupportUserInfo(): number {
        return 0;
    }
    /** 授权个人信息 */
    public showAuthorize(param?: object) {

    }

    //授权位置信息并获取位置
    public authLocation(): void {
    }

    //是否支持定位
    public isSupportLocaltion(): boolean {
        return true;
    }

    /**
     * 切换账号
     */
    public switchAccount() {
    }



    /** 广告是否启用 */
    public getAdEnableStatus(): boolean {
        return false;
    }

    /** 当前广告是否加载成功 */
    public getAdStatus(): boolean {
        return false;
    }

    public recharge(orderId, coinNum, productName, configId) {

    }

    public showAd(callBack) {
        callBack && callBack();
    }

    /**
     * 发起sdk充值方法
     */
    public pay() {

    }

    public payCallBack(res) {
    }


    /**
     * SDK 上报数据
     */
    public report(type: ReportType, data: ReportData) {
    }

    /*
    *    上报总额
    */
    public sumitSpHelperUtil() {

    }

    public PayResultTest(): void {

    }

    public getNotSafeAreaHeight(): number {
        return 0;
    }

    public checkRechargeOpen(): boolean {
        return true;
    }
    public checkTxt(txt, sucCB: Function, failCB: Function, target) {
        if (sucCB && target) {
            sucCB.apply(target);
        }
    }
    /** 
     * 分享
     * 
     */
    public share(): void {

    }
    /**
     * 客服
     */
    public customerService() {
    }

    public platformLaunchEnd(data) {
    }

    public platformLoginEnd(data) {

    }

    public chargeEnd(data) {

    }
    public gameLogout(data) {

    }
    public platformPlayAdEnd(data) {

    }
}
