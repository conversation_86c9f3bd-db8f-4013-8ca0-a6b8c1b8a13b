import * as cc from "cc";
import { TipManager } from "../../Module/Tips/TipManager";
// import OtherManager from "./OtherManager";

const { ccclass, property } = cc._decorator;

/**
 * 游戏管理器，用来管理各类子管理器
 */
@ccclass
export default class GameManager extends cc.Component {
    private static _instance: GameManager;

    public static get instance(): GameManager {
        return GameManager._instance;
    }

    //-----------------------------------------------------------------------------------------------------
    // private _otherMgr: OtherManager = null;

    /**获得杂货管理器 */
    // public get otherMgr(): OtherManager {
    //     if (this._otherMgr == null) {
    //         this._otherMgr = this.getComponentInChildren(OtherManager);
    //     }

    //     return this._otherMgr;
    // }

    private _tipManager: TipManager = null;
    public get tipManager(): TipManager {
        if (this._tipManager == null) {
            this._tipManager = this.getComponentInChildren(TipManager);
        }
        return this._tipManager;
    }

    onLoad() {
        if (!GameManager._instance) {
            GameManager._instance = this;
            cc.director.addPersistRootNode(this.node);
            this.init();
        } else {
            this.node.destroy(); //场景里只能有一个GameManager,有多余的必须销毁
        }
    }

    public init() { }
}
