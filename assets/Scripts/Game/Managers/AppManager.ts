import * as cc from "cc";
import Singleton from "../../Framework/Utils/Singleton";
import LoadManager from "./LoadManager";
import ResManager from "../Module/Loading/Managers/ResManager";
import TimeManager from "../Module/System/TimeManager";

export default class AppManager extends Singleton {
    private _loadManager: LoadManager = new LoadManager();
    private _resManager: ResManager = new ResManager();
    private _timeManager: TimeManager = new TimeManager();
    private _isDebug: boolean = false;

    public constructor() {
        super();
    }

    public get LoadManager(): LoadManager {
        return this._loadManager;
    }

    public get ResManager(): ResManager {
        return this._resManager;
    }

    public get TimeManager(): TimeManager {
        return this._timeManager;
    }

    public init(): void {
        this._resManager.init();
        this._loadManager.init(this._resManager);
    }

    public onUpdate(dt: number): void {
        this._resManager.onUpdate(dt);
    }
}
