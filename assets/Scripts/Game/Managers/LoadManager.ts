import * as cc from "cc";
import { ResFile } from "../../Config/CoreConst";
import { ResourceGroupUtils } from "../Utils/ResourceGroupUtils";
import ResManager from "../Module/Loading/Managers/ResManager";

type ResJson = {
    groups: Array<{ keys: string; name: string }>;
    resources: Array<{ name: string; type: string; url: string }>;
};

export default class LoadManager {
    // 加载资源配置
    private _resJson: ResJson;

    public init(resManager: ResManager): void {
        const resUrl: string = "resources";
        this._resJson = resManager.getRes(resUrl, cc.JsonAsset).json;
        ResourceGroupUtils.removeExtname(this._resJson);
        resManager.clearRes(resUrl);
    }

    /**P
     * 加载资源组
     * @param groupName 资源组名
     * @param completeFun 加载成功
     * @param errorFun 加载失败
     * @param progressFun 加载进度
     * @param thisObj 函数this对象
     */
    public loadGroup(groupName: string, completeFun: Function, errorFun: Function, progressFun: Function, thisObj: any): void {
        if (!groupName) return;
        const resFiles: Array<ResFile> = this.getGroupUrls(groupName);
        this.loadArray(resFiles, completeFun, errorFun, progressFun, thisObj);
    }

    /**
     * 加载资源组
     * @param groupNames 资源组名数组
     * @param completeFun 加载成功
     * @param errorFun 加载失败
     * @param progressFun 加载进度
     * @param thisObj 函数this对象
     */
    public loadArrayGroup(groupNames: Array<string>, completeFun: Function, errorFun: Function, progressFun: Function, thisObj: any): void {
        if (!groupNames || groupNames.length <= 0) {
            return;
        }

        let groupName: string;
        let resFiles: Array<ResFile> = [];
        for (let i = 0; i < groupNames.length; i++) {
            groupName = groupNames[i];
            resFiles = resFiles.concat(this.getGroupUrls(groupName));
        }
        this.loadArray(resFiles, completeFun, errorFun, progressFun, thisObj);
    }

    /**
     * 加载单个资源
     * @param resFile 资源信息
     * @param completeFun 加载完成返回
     * @param errorFun 加载错误返回
     * @param progressFun 加载进度返回
     * @param thisObj 加载this对象
     */
    public load(resFile: ResFile, completeFun: Function, errorFun: Function, progressFun: Function, thisObj: any): void {
        cc.resources.load(
            resFile.url,
            function (completedCount: number, totalCount: number, item: any) {
                progressFun && progressFun.apply(thisObj, arguments);
            },
            function (error: Error, resource: any) {
                if (error) {
                    cc.error(error, resFile.url);
                    errorFun && errorFun.apply(thisObj, arguments);
                } else {
                    completeFun && completeFun.apply(thisObj, arguments);
                }
            },
        );
    }

    /** 加载预制体 */
    loadPrefab(url: string, completeFun: Function, errorFun: Function, progressFun: Function, thisObj: any): void {
        this.load({ type: cc.Asset, url: url }, completeFun, errorFun, progressFun, thisObj);
    }
    /**
     * 加载多个资源
     * @param resFiles 资源地址
     * @param completeFun 加载完成返回
     * @param errorFun 加载错误返回
     * @param progressFun 加载进度返回
     * @param thisObj 加载this对象
     */
    public loadArray(resFiles: Array<ResFile>, completeFun: Function, errorFun: Function, progressFun: Function, thisObj: any): void {
        let comCount: number = 0;
        let errCount: number = 0;
        let err: Error;
        let asset: any;
        let resFile: ResFile;
        const resLen: number = resFiles.length;

        if (resLen == 0) {
            deal();
            return;
        }

        for (let i = 0; i < resLen; i++) {
            resFile = resFiles[i];
            this.load(
                resFile,
                () => {
                    ++comCount;
                    deal();
                },
                (error: Error, resource: any) => {
                    ++errCount;
                    err = error;
                    asset = resource;
                    console.error(err);
                    deal();
                },
                null,
                this,
            );
        }
        function deal() {
            progressFun && progressFun.apply(thisObj, [comCount, resLen]);
            if (comCount + errCount < resLen) return;

            if (errCount > 0) {
                console.error(err);
                errorFun && errorFun.apply(thisObj, [err, asset]);
            } else {
                completeFun && completeFun.apply(thisObj);
            }
        }
    }

    /**
     * 加载包名
     * @param pkgName 包名也是资源组名
     * @param completeFun 加载成功
     * @param errorFun 加载失败
     * @param progressFun 加载进度
     * @param thisObj 函数this对象
     * @param resManager 资源管理器
     */
    public loadPackage(pkgName: string, completeFun: Function, errorFun: Function, progressFun: Function, thisObj: any, resManager: ResManager): void {
        this.loadGroup(
            pkgName,
            function () {
                resManager.addUiPackage(pkgName);
                completeFun && completeFun.apply(thisObj);
            },
            errorFun,
            progressFun,
            thisObj,
        );
    }

    /**
     * 加载包名
     * @param pkgNameArr 包名也是资源组名
     * @param completeFun 加载成功
     * @param errorFun 加载失败
     * @param progressFun 加载进度
     * @param thisObj 函数this对象
     * @param resManager 资源管理器
     */
    public loadArrayPackage(pkgNameArr: Array<string>, completeFun: Function, errorFun: Function, progressFun: Function, thisObj: any, resManager: ResManager): void {
        this.loadArrayGroup(
            pkgNameArr,
            function () {
                for (let i = 0, len = pkgNameArr.length; i < len; i++) {
                    resManager.addUiPackage(pkgNameArr[i]);
                }
                completeFun && completeFun.apply(thisObj);
            },
            errorFun,
            progressFun,
            thisObj,
        );
    }

    /**
     * 获取资源组中所有资源url
     * @param groupName 资源组
     */
    public getGroupUrls(groupName: string): Array<ResFile> {
        return ResourceGroupUtils.getGroupUrls(groupName, this._resJson);
    }




}
