import * as cc from 'cc';

import Logger from "../../Framework/Logger";
import { LoadUtils } from "../Utils/LoadUtils";
import { ViewUtil } from "../Utils/ViewUtils";
import { GameEvent } from "../../GameEvent/Events";

import LayerManagerComponent from '../GlobalComponent/LayerManagerComponent';
import ViewManager from '../GlobalComponent/ViewManagerComponent';
import EventManagerComponent from '../GlobalComponent/EventManagerComponent';
import { ViewZIndex } from '../../Config/ViewConst';
import { initDataLayer } from "../../GameData";

const { ccclass, property } = cc._decorator;

@ccclass
export default class SceneBase extends cc.Component {

    public scenName: string = "";

    private isLoading: boolean = false;

    static LayerZIndex = {
        /** 弹出UI层 */
        UILayer: 10,
        /** 弹出指引层 */
        GuideLayer: 20,
        /** 特效层 */
        EffectLayer: 30,
        /** 提示层 */
        TipLayer: 40,
        /** 网络层 */
        NetWorkLayer: 50,
    };

    /** 弹出UI层 */
    protected _uiLayer: cc.Node = null;
    get uiLayer(): cc.Node {
        return this._uiLayer;
    }

    /** 弹出指引层 */
    protected _guideLayer: cc.Node = null;
    get guideLayer(): cc.Node {
        return this._guideLayer;
    }

    /** 提示层 */
    protected _tipLayer: cc.Node = null;
    get tipLayer(): cc.Node {
        return this._tipLayer;
    }

    /** 网络层 */
    protected _netWorkLayer: cc.Node = null;
    get networkLayer(): cc.Node {
        return this._netWorkLayer;
    }


    @property(cc.Node)
    layerParent: cc.Node = null;

    @property(cc.Node)
    tipsContiner: cc.Node = null;

    onLoad() {
        // 适配
        let _canvas = cc.director.getScene().getChildByName("Canvas").getComponent(cc.Canvas);
        // 设计分辨率比例
        let _rateR = cc.view.getDesignResolutionSize().height / cc.view.getDesignResolutionSize().width;
        // 显示分辨率比
        let _rateV = cc.view.getVisibleSize().height / cc.view.getVisibleSize().width;

        if (_rateV > _rateR) {
            cc.view.setDesignResolutionSize(
                cc.view.getDesignResolutionSize().width, 
                cc.view.getDesignResolutionSize().height, 
                cc.ResolutionPolicy.FIXED_WIDTH);
        }
        else {
            cc.view.setDesignResolutionSize(
                cc.view.getDesignResolutionSize().width, 
                cc.view.getDesignResolutionSize().height, 
                cc.ResolutionPolicy.FIXED_HEIGHT);
        }
    }

    init() {
        //添加层
        if (this.uiLayer == null) {
            this._uiLayer = this.buildFunc(SceneBase.LayerZIndex.UILayer);
        }

        if (this.guideLayer == null) {
            this._guideLayer = this.buildFunc(SceneBase.LayerZIndex.GuideLayer);
            this._guideLayer.name = "guideLayer";
        }

        if (this._tipLayer == null) {
            this._tipLayer = this.buildFunc(SceneBase.LayerZIndex.TipLayer);
            this._tipLayer.name = "tipsLayer";
        }

        if (this._netWorkLayer == null) {
            this._netWorkLayer = this.buildFunc(SceneBase.LayerZIndex.NetWorkLayer);
            this._netWorkLayer.name = "netWorkLayer";
        }

        const layerManager: LayerManagerComponent = LayerManagerComponent.instance();
        layerManager.setGameScene(this);

        const eventManager: EventManagerComponent = EventManagerComponent.instance();
        eventManager.addEventListener(GameEvent.WAITING_SHOW, this.showWaiting, this);
        eventManager.addEventListener(GameEvent.WAITING_HIDE, this.hideWaiting, this);

        initDataLayer();
    }

    private buildFunc(zIdx: number): cc.Node {
        let layer: cc.Node = new cc.Node();
        layer.addComponent(cc.UITransform);
        layer.getComponent(cc.UITransform).setAnchorPoint(0.5, 0.5);
        layer.getComponent(cc.UITransform).setContentSize(
            cc.view.getDesignResolutionSize(),
        );
        layer.setSiblingIndex(zIdx);
        let component: cc.Widget = layer.addComponent(cc.Widget);
        component.isAlignHorizontalCenter = true;
        component.isAlignVerticalCenter = true;
        if (this.layerParent) {
            this.layerParent.addChild(layer);
        } else {
            this.node.addChild(layer);
        }
        layer.setSiblingIndex(zIdx);
        return layer;
    };

    //界面大小事件被触发
    setResizeEvent() {
        cc.view.setResizeCallback(function () {

        });
    }

    start() {
    }

    protected onDestroy() {
        const eventManager: EventManagerComponent = EventManagerComponent.instance();
        eventManager.removeEventListener(GameEvent.WAITING_SHOW, this.showWaiting, this);
        eventManager.removeEventListener(GameEvent.WAITING_HIDE, this.hideWaiting, this);

        eventManager.targetOff(this);
        const layerManager: LayerManagerComponent = LayerManagerComponent.instance();
        layerManager.dispose();
        const viewManager: ViewManager = ViewManager.instance();
        viewManager.dispose();
    }

    loadResouceJson() {
    }

    protected onJson(error): void {

    }

    protected onResJsonFinish(): void { }

    //显示"别急"
    showWaiting(showTip = true) {
        this.isLoading = true;
        let rootNode = this.networkLayer;
        let old = rootNode.getChildByName("LOADING");
        // Logger.trace("showWaiting", old);
        if (old) {
            return;
        }

        // App.LoadManager.load({ url: "UIWaiting", type: cc.Prefab }, (err, prefab) => {
        //     if (!cc.isValid(rootNode) || !this.isLoading) {
        //         Logger.trace("waiting close " + this.isLoading);
        //         return;
        //     }

        //     let waitRes = App.ResManager.getRes("UIWaiting");
        //     if (waitRes == null) {
        //         return;
        //     }

        //     let modal: cc.Node = cc.instantiate(waitRes);

        //     if (modal) {
        //         modal.x = 0;
        //         modal.y = 0;
        //         modal.zIndex = cc.macro.MAX_ZINDEX;
        //         modal.name = "LOADING";
        //         modal.setPosition(0, 0);
        //         modal.stopAllActions();
        //         modal.opacity = 0;
        //         modal.runAction(cc.sequence(cc.delayTime(1), cc.fadeTo(0, 255)));
        //         rootNode.addChild(modal);

        //         // Logger.trace("showWaiting ok!", old);

        //         let tips = modal.getChildByName("Tips");
        //         if (tips) {
        //             tips.active = showTip;
        //         }

        //     }
        // }, null, null, this)

        const url: string = 'ui/offline/UIWaiting';
        LoadUtils.loadPrefab(url, () => {
            // oops.res.load(url, () => {
            if (!cc.isValid(rootNode) || !this.isLoading) {
                Logger.trace("waiting close " + this.isLoading);
                return;
            }
            const waitNode: cc.Node = ViewUtil.createPrefabNode(url);
            if (waitNode) {
                waitNode.setPosition(0, 0);
                waitNode.setSiblingIndex(ViewZIndex.MAX_ZINDEX);
                waitNode.name = 'LOADING';

                cc.Tween.stopAllByTarget(this.node);
                waitNode.getComponent(cc.UIOpacity).opacity = 0;
                ViewUtil.fadeTo(waitNode, 0, 255, 1).start();

                rootNode.addChild(waitNode);
                const tips: cc.Node = waitNode.getChildByName('Tips');
                if (tips) {
                    tips.active = showTip;
                }
            }
        });
    }

    //隐藏"别急"
    hideWaiting() {
        this.isLoading = false;
        let rootNode = this.networkLayer
        let old = rootNode.getChildByName("LOADING");
        if (old) {
            old.destroy();
        }
    }
}
