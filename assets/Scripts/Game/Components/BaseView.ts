import * as cc from "cc";

import { LoadUtils } from "../Utils/LoadUtils";
import { ViewUtil } from "../Utils/ViewUtils";
import { SoundId, SoundType } from "../GlobalComponent/SoundManager";
import BaseComponent from "./BaseComponent";

export enum BaseViewStatus {
    Loading,
    Loaded,
}

export default class BaseView extends BaseComponent {
    private _url: string = "";

    public loadStatus: BaseViewStatus = BaseViewStatus.Loading;

    protected showAnimation: boolean = false;

    protected uiNodeDic: cc.Node[] = [];

    public showMask: boolean = false;

    public isSingle: boolean = false;

    // close callback
    public closeCB: () => void = null;
    /** 透传参数 */
    viewData = null;

    public close() {
        this.removeAllEventListener();
        this.closeCB && this.closeCB();
        this.closeCB = null;
    }

    protected onLoad(): void {
        this.loadNodeInfo(this.node, "");
        if (this.showAnimation) {
            // 0秒：纵向-180 不透明度0 缩放0.98
            // 0.25秒：纵向-12 不透明度0.5 缩放0.98
            // 0.5秒：纵向0 不透明度1 缩放1

            let target = this.node;

            let nodeX = this.node.getPosition().x;
            let nodeY = this.node.getPosition().y;

            // 初始
            target.setPosition(nodeX, nodeY - 300); //纵向-180
            // target.scale = 0.98; //缩放0.98
            target.getComponent(cc.UIOpacity).opacity = 100;

            let act = cc.tween(target);
            cc.tween().sequence(
                // 0.25秒：纵向-12 不透明度0.5 缩放0.98
                cc.tween(target).to(0.15, { position: cc.v3(nodeX, nodeY - 12) }),
                cc.tween(target.getComponent(cc.UIOpacity)).to(0.15, { opacity: 128 }),

            // 0.5秒：纵向0 不透明度1 缩放1
                cc.tween(target).to(0.15, { position: cc.v3(nodeX, nodeY) }),
                cc.tween(target.getComponent(cc.UIOpacity)).to(0.15, { opacity: 255 }),
                cc.tween().call(() => {
                    this.onCompleteAnimation();
                }),
            ).start();
        } else {

            this.onCompleteAnimation();
        }
        this.addUIBindEvents();
        this.addAllEventListener();
    }

    /** 绑定UI事件 子类实现 */
    protected addUIBindEvents(): void { }

    /** 侦听事件 子类实现 */
    protected addAllEventListener(): void { }

    /** 移除事件 子类实现 */
    protected removeAllEventListener(): void { }


    private loadNodeInfo(node: cc.Node, path: string) {
        for (var i = 0; i < node.children.length; i++) {
            this.uiNodeDic[path + node.children[i].name] = node.children[i];
            this.loadNodeInfo(node.children[i], path + node.children[i].name + "/");
        }
    }

    public set url(url: string) {
        this._url = url;
    }

    public get url(): string {
        return this._url;
    }

    public onCompleteAnimation() {
        // 根据this.url 读Cfg_GameHelp -> 有数据-> 显示帮助按钮
    }

    /**
     * 关闭面板
     */
    closePanel() {
        this.scheduleOnce(() => {
            this.playClickEffect(SoundId.CLOSEUI, SoundType.COMMON);
        }, 100);
        LoadUtils.releaseAsset(this.url);
        ViewUtil.destroyNode(this.node);
    }

    /**
     * 黑色半透明底点击方法，子类实现逻辑
     */
    public onClickMatte(evet: cc.Event = null): void {
        if (evet) {
            evet.propagationStopped = true;
        }
    }
}
