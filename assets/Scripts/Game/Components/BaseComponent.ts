import * as cc from "cc";

import { PrefabComplete, LoadUtils } from "../Utils/LoadUtils";
import SoundManager, { SoundId, SoundType } from "../GlobalComponent/SoundManager";


export default class BaseComponent<T = any> extends cc.Component {

    protected _isStart: boolean = false;
    protected _uiData: T = null;
    protected _assetPathMap: { [key: string]: number };
    private _initPath: string;//脚本未初始化完设置的加载路径
    private _initPrefabComplete: PrefabComplete;
    private _lastPath: string;//最后加载的资源路径

    /**
     * 重写时super一下
     */
    protected onLoad(): void {
        // this.onNodeEvent();
        // this.onEvent();
    }

    /**
    * 重写时super一下
    */
    protected start(): void {
        this._isStart = true;
        if (this._initPath && this._initPrefabComplete) this.loadPrefabNode(this._initPath, this._initPrefabComplete);
        // this.onShow();
    }

    /**
     * 重写时super一下
     */
    protected onDisable(): void {
        //禁用脚本暂停事件
        // EventCenter.pause(this);
    }

    /**
     * 重写时super一下
     */
    protected onEnable(): void {
        //激活脚本恢复事件
        // EventCenter.resume(this);
    }

    /**
     * 重写时super一下
     */
    protected onDestroy(): void {
        //脚本销毁移除全部绑定事件
        // EventCenter.allOff(this);
        for (let key in this.assetPathMap) {
            LoadUtils.releaseAsset(key, this.assetPathMap[key]);
        }
    }

    /***----------自定义接口------------ */
    /**
     * 注册脚本节点事件
     */
    // protected onNodeEvent(): void {

    // }
    // /**
    //  * 注册全局事件
    //  */
    // protected onEvent(): void {

    // }
    /**
    //  * 设置ui数据
    //  */
    // public setUIData(data: T): void {
    //     this._uiData = data;
    //     if (this._isStart) {
    //         this.onShow();
    //     }
    // }
    // /**
    //  * 获取ui数据
    //  */
    // public get getUIData(): T {
    //     return this._uiData;
    // }

    protected get assetPathMap() {
        if (!this._assetPathMap) this._assetPathMap = cc.js.createMap();
        return this._assetPathMap;
    }

    /**
     * 加载预制体  不执行看看有没有调用super.start();
     * @param path 
     * @param onComplete 
     * @returns 
     */
    public loadPrefabNode(path: string, onComplete: PrefabComplete): void {
        if (!this._isStart) {
            this._initPath = path;
            return;
        }
        if (!cc.isValid(this, true)) return;
        if (this._lastPath == path) return;
        this._lastPath = path;
        let count: number = this.assetPathMap[path];
        if (count != undefined) {
            count++;
        }
        this.assetPathMap[path] = count || 1;
        LoadUtils.loadPrefabNode(path, (node: cc.Node) => {
            if (node) {
                onComplete(node);
            } else {
                onComplete(null!);
            }
        });
    }


    protected playClickEffect(name: string, type: SoundType) {
        SoundManager.instance().playEffect(name, type);
    }
    // /**
    //  * 刷新界面
    //  */
    // public onShow(): void {

    // }
    // /**
    //  * 隐藏界面
    //  */
    // public onHide(): void {

    // }

}