import * as cc from "cc";

const { ccclass, property, menu } = cc._decorator;

/**适配iPhoneX等类型的刘海屏组件,只适配顶部 */
@ccclass
@menu("自定义组件/AdaptedTopOfIphoneX")
export default class AdaptedTopOfIphoneX extends cc.Component {
    /**偏移量 */
    @property(cc.CCFloat)
    offset: number = 70;

    /**适配比例 */
    get aspectRatio() {
        const size: cc.Size = cc.view.getFrameSize();
        return size.height / size.width;
    }

    protected onLoad(): void {
        // console.log(`屏幕分辨率---宽：${cc.view.getFrameSize().width} 高：${cc.view.getFrameSize().height}`);

        // if (window['37sdk']) {
        //     this.offset += 70;
        // }

        if (this.aspectRatio > 2) {
            let comp: cc.Widget = this.node.getComponent(cc.Widget);
            comp.top += this.offset;
        }
    }
}
