import * as cc from "cc";

import { CommonUtils } from "../Utils/CommonUtils";
import SoundManager from "../GlobalComponent/SoundManager";


export enum UIButtonType {
    /** 普通按钮 */
    Normal = 0,
    /** 多选按钮 */
    CheckBox = 1,
    /** 单选按钮 */
    Radio = 2,
}

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("自定义组件/UIButton")
export default class UIButton extends cc.Component {
    @property({
        type: cc.Enum(UIButtonType),
        tooltip: "按钮类型\n Normal 普通按钮 \n CheckBox 多选按钮 \n Radio 单选按钮",
    })
    protected buttonType: UIButtonType = UIButtonType.Normal;

    @property(cc.Node)
    normalNode: cc.Node = null;

    @property(cc.Node)
    selectedNode: cc.Node = null;

    @property(cc.Node)
    selectedNodeArray: cc.Node[] = [];

    @property(cc.Node)
    disabledNode: cc.Node = null;

    /** 按钮文字 */
    @property(cc.Label)
    buttonTitle: cc.Label = null;

    @property
    scaleMin: number = 0.94;

    @property
    scaleMax: number = 1.12;

    @property
    scaleDuration: number = 0.1;

    @property
    allowMoveDistance: number = 30;

    /** 关联的ScrollView */
    @property(cc.ScrollView)
    scrollView: cc.ScrollView = null;
    /** 停止ScrollView滚动的X轴最小距离 */
    @property
    stopScrollDistanceX: number = 100;
    /** 停止ScrollView滚动的Y轴最小距离 */
    @property
    stopScrollDistanceY: number = 100;

    @property
    protected disabled_: boolean = false;

    @property
    protected disabled_CanClick: boolean = false;

    @property
    protected checked_: boolean = false;

    @property({
        tooltip: "触摸",
    })
    protected touchEnable_: boolean = true;
    /** 触控 */
    set touchEnable(enabled: boolean) {
        this.touchEnable_ = enabled;
    }

    @property({
        tooltip: "点击声音",
    })
    protected touchSoundEnable_: boolean = true;
    /** 点击声音 */
    set touchSoundEnable(enabled: boolean) {
        this.touchSoundEnable_ = enabled;
    }
    get touchSoundEnable(): boolean {
        return this.touchSoundEnable_;
    }

    @property({
        tooltip: "是否阻止传递",
    })
    protected stopPropagation: boolean = false;

    @property(cc.CCBoolean)
    protected _canPenetrate: boolean = false;

    @property({
        tooltip: "是否穿透",
    })
    public get canPenetrate(): boolean {
        return this._canPenetrate;
    }
    public set canPenetrate(v: boolean) {
        this._canPenetrate = v;
        let hackNode = this.node as any;
        if (cc.isValid(hackNode) && hackNode._touchListener) {
            hackNode._touchListener.setSwallowTouches(!v);
        }
    }

    //红点
    @property(cc.Sprite)
    protected redPoint: cc.Sprite = null;

    protected data;
    protected moved_: boolean = false;
    protected isGray_: boolean = false;

    protected targetClick_: any = null;
    protected onClick_: (event: cc.EventTouch) => void = null;
    protected onClickAdd_: Function[] = []; //追加的点击事件
    protected guideTargetClick_: any = null;
    protected onGuideClick_: (event: cc.EventTouch) => void = null;
    protected targetMove_: any = null;
    protected onMove_: (event: cc.EventTouch) => void = null;
    protected targetMoveEnd_: any = null;
    protected onMoveEnd_: (event: cc.EventTouch) => void = null;
    protected targetChecked_: any = null;
    protected checkedCallBack_: (button: UIButton) => void = null;

    protected onLongPress_: (event: cc.EventTouch) => void = null;
    protected longPressTime_ = 1;
    protected longPressHandle_ = 0;
    protected longPressExe_ = false;

    protected defaultScale_: number = 1;

    protected prevScrollOffset_: cc.Vec2 = cc.Vec2.ZERO;

    protected normalColor: cc.Color = null;
    protected selectColor: cc.Color = null;

    protected pressAction_: cc.Tween<cc.Node> = null;
    protected pressActionEnd_: boolean = false;
    protected restoreAction_: cc.Tween<cc.Node> = null;
    protected restoreActionEnd_: boolean = false;

    private onClickEnable = true;

    public get checked(): boolean {
        return this.checked_;
    }
    public set checked(v: boolean) {
        this.checked_ = v;

        this.handleNodes();

        if (this.buttonTitle && this.selectColor) {
            this.buttonTitle.node.getComponent(cc.Label).color = this.checked ? this.selectColor : this.normalColor;
        }

        if (this.buttonType == UIButtonType.Radio && this.checked_) {
            // 单选按钮，同一层级下的其他单选按钮checked变成false
            if (this.node.getParent()) {
                for (let child of this.node.getParent().children) {
                    let btn = child.getComponent(UIButton);
                    if (btn && btn.buttonType == UIButtonType.Radio && btn != this) {
                        btn.checked = false;
                    }
                }
            }
        }

        if (this.checkedCallBack_ != null) {
            this.checkedCallBack_.call(this.targetChecked_, this);
        }
    }

    public get disabled(): boolean {
        return this.disabled_;
    }
    public set disabled(v: boolean) {
        this.disabled_ = v;
        this.handleNodes();
    }

    public get isGray(): boolean {
        return this.isGray_;
    }

    protected handleNodes() {
        if (this.disabled_) {
            if (this.disabledNode) {
                this.disabledNode.active = true;

                if (this.normalNode) {
                    this.normalNode.active = false;
                }
                if (this.selectedNode) {
                    this.selectedNode.active = false;
                }
                if (this.selectedNodeArray) {
                    this.hideSelectedArray(false);
                }
            }
        } else {
            if (this.disabledNode) {
                this.disabledNode.active = false;
            }

            if (this.checked_) {
                if (this.selectedNode) {
                    this.selectedNode.active = true;

                    if (this.normalNode) {
                        this.normalNode.active = false;
                    }
                }

                if (this.selectedNodeArray) {
                    this.hideSelectedArray(true);
                }
            } else {
                if (this.selectedNode) {
                    this.selectedNode.active = false;
                }

                if (this.selectedNodeArray) {
                    this.hideSelectedArray(false);
                }
                if (this.normalNode) {
                    this.normalNode.active = true;
                }
            }
        }
    }

    setOnClickEnable(enable) {
        this.onClickEnable = enable;
    }

    setGuideClick(func: (event: cc.EventTouch) => void, target: any) {
        this.onGuideClick_ = func;
        this.guideTargetClick_ = target;
    }
    setOnClick(func: (event: cc.EventTouch) => void, target: any) {
        // this.data = arg;
        this.onClick_ = func;
        this.targetClick_ = target;
    }

    addOnClick(func: Function): void {
        this.onClickAdd_.push(func);
    }

    setOnMove(func: (event: cc.EventTouch) => void, target: any) {
        this.onMove_ = func;
        this.targetMove_ = target;
    }
    setOnMoveEnd(func: (event: cc.EventTouch) => void, target: any) {
        this.onMoveEnd_ = func;
        this.targetMoveEnd_ = target;
    }
    setCheckedCallBack(func: (button: UIButton) => void, target: any) {
        this.checkedCallBack_ = func;
        this.targetChecked_ = target;
    }

    /**
     * 设置长按的回调方法
     * @param func 方法
     * @param target 对象
     * @param sec 长按的时间
     */
    setOnLongPress(func: (event: cc.EventTouch) => void, target: any, sec: number = 1) {
        if (target) {
            this.onLongPress_ = func.bind(target);
        } else {
            this.onLongPress_ = func;
        }
        this.longPressTime_ = sec;
    }

    onLoad() {
        this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
        this.canPenetrate = this.canPenetrate;
        this.defaultScale_ = this.node.scale.x;

        if (this.buttonTitle) {
            this.normalColor = this.buttonTitle.node.getComponent(cc.Label).color;
        }

        this.handleNodes();
    }

    start() {
        // cc.log("Unit start");
    }
    update(dt) {
        if (this._isCoolDown) {
            this._coolDown -= dt;
            if (this._coolDown < 0) {
                this.stopCoolDown();
            } else {
                this.updateCoolDown();
            }
        }
    }

    private _isCoolDown: boolean = false;
    private _coolDown: number = 0;
    private _spSprite: cc.Label = null;
    /** 开始倒计时 */
    startCoolDown(time: number): void {
        this._isCoolDown = true;
        this._coolDown = time;

        if (!this._spSprite) {
            const node = new cc.Node();
            this.node.addChild(node);
            this._spSprite = node.addComponent(cc.Label);
            const labelOutline = node.addComponent(cc.LabelOutline);
            labelOutline.width = 5;
            labelOutline.color = new cc.Color(0, 0, 0, 0);
        }
        this._spSprite.node.active = true;
        this.updateCoolDown();
        this.setGrayEffect(true);
        this.touchEnable = false;
    }
    updateCoolDown(): void {
        if (!this._spSprite) return;
        this._spSprite.string = this._coolDown.toFixed(1);
    }
    /** 结束倒计时 */
    stopCoolDown(): void {
        this._isCoolDown = false;
        this.setGrayEffect(false);
        this.touchEnable = true;
        this._spSprite.node.active = false;
    }

    protected onTouchStart(event: cc.EventTouch) {
        this.longPressExe_ = false;
        if (this.longPressHandle_ > 0) {
            window.clearTimeout(this.longPressHandle_);
            this.longPressHandle_ = 0;
        }

        if (this.touchEnable_ == false) {
            return;
        }

        this.pressAction();

        if (this.disabled_) {
            return;
        }

        if (this.stopPropagation == true) {
            event.propagationStopped = true;
        }

        if (this.buttonType == UIButtonType.Normal) {
            this.checked = true;
        }

        if (this.scrollView) {
            this.prevScrollOffset_.set(this.scrollView.getScrollOffset());
            // cc.log("getScrollOffset %f, %f", this.prevScrollOffset_.x, this.prevScrollOffset_.y);
        }

        this.moved_ = false;

        if (this.onLongPress_ != null) {
            this.longPressHandle_ = window.setTimeout(
                function () {
                    if (cc.isValid(this) == false || this.node == null || this.node.activeInHierarchy == false) {
                        return;
                    }
                    this.onLongPress_(event);
                    this.longPressExe_ = true;
                }.bind(this),
                this.longPressTime_ * 1000,
            );
        }
    }

    protected onTouchMove(event: cc.EventTouch) {
        if (this.touchEnable_ == false) {
            return;
        }

        if (this.disabled_) {
            return;
        }

        if (this.stopPropagation == true) {
            event.propagationStopped = true;
        }

        let distance: cc.Vec2 = event.getDelta();
        distance.x = Math.abs(distance.x);
        distance.y = Math.abs(distance.y);

        if (distance.x > this.allowMoveDistance || distance.y > this.allowMoveDistance) {
            this.moved_ = true;

            if (this.longPressHandle_ > 0) {
                window.clearTimeout(this.longPressHandle_);
                this.longPressHandle_ = 0;
            }
        }

        if (this.scrollView) {
            if (distance.x > this.stopScrollDistanceX && distance.y > this.stopScrollDistanceY) {
                this.scrollView.scrollToOffset(new cc.Vec2(-this.prevScrollOffset_.x, this.prevScrollOffset_.y));
                this.scrollView.enabled = false;
            }
        }

        if (this.onMove_) {
            this.onMove_.call(this.targetMove_, event);
        }
    }

    protected onTouchEnd(event: cc.EventTouch) {
        console.log("onTouchEnd xxxxxx", event);

        // if (this.longPressHandle_ > 0) {
        //     window.clearTimeout(this.longPressHandle_);
        //     this.longPressHandle_ = 0;
        // }

        if (!this.touchEnable_) {
            return;
        }

        this.restore();

        if (this.disabled_ && this.disabled_CanClick == false) {
            return;
        }

        if (this.stopPropagation == true) {
            event.propagationStopped = true;
        }

        if (this.buttonType == UIButtonType.Normal) {
            this.checked = false;
        }

        if (!this.moved_) {
            if (this.buttonType == UIButtonType.CheckBox) {
                // 多选按钮，自己的checked相反的变
                this.checked = !this.checked_;
            } else if (this.buttonType == UIButtonType.Radio) {
                // 单选按钮，同一层级下的其他单选按钮checked变成false
                if (this.node.getParent()) {
                    for (let child of this.node.getParent().children) {
                        let btn = child.getComponent(UIButton);
                        if (btn && btn.buttonType == UIButtonType.Radio && btn != this) {
                            btn.checked = false;
                        }
                    }
                    this.checked = true;
                }
            }

            if (this.touchSoundEnable) {
                const soundCom = SoundManager.instance();
                soundCom && soundCom.playClickEffect();
            }
            if (!this.longPressExe_) {
                // 长按方法没有被执行
                
                if (this.onClick_ && this.onClickEnable) {
                    // 执行点击方法
                    for (let i: number = 0; i < this.onClickAdd_.length; i++) {
                        this.onClickAdd_[i]();
                    }
                    this.onClickAdd_.length = 0;
                    this.onClick_.call(this.targetClick_, event);
                    // if (DEV) {
                    //     const path = CommonUtils.getRecursiveName(this.node);
                    //     console.info("%c%s%s", "color:blue", "[btn]", path);
                    // }
                }
                if (this.onGuideClick_) {
                    this.onGuideClick_.call(this.guideTargetClick_, event);
                }
            }
        }

        if (!this.longPressExe_) {
            // 长按方法没有被执行

            if (this.onMoveEnd_) {
                // 执行移动结束方法
                this.onMoveEnd_.call(this.targetMoveEnd_, event);
            }
        }
        console.log("onTouchEnd xxxxxx done");
    }

    protected onTouchCancel(event: cc.EventTouch) {
        if (this.longPressHandle_ > 0) {
            window.clearTimeout(this.longPressHandle_);
            this.longPressHandle_ = 0;
        }

        if (this.touchEnable_ == false) {
            return;
        }

        this.restore();

        if (this.disabled_) {
            return;
        }

        if (this.stopPropagation == true) {
            event.propagationStopped = true;
        }

        if (this.buttonType == UIButtonType.Normal) {
            this.checked = false;
        }

        if (this.onMoveEnd_) {
            this.onMoveEnd_.call(this.targetMoveEnd_, event);
        }
    }

    protected pressAction() {
        if (this.pressAction_ == null || this.pressActionEnd_ ) {
            if (this.restoreAction_ == null || this.restoreActionEnd_ ) {
                this.defaultScale_ = this.node.scale.x;
            }
        }

        if (this.pressAction_) {
            this.pressAction_.stop();
        }
        this.pressActionEnd_ = false;
        const scaleRatio = this.scaleMin * this.defaultScale_;
        this.pressAction_ = cc.tween(this.node).to(
            this.scaleDuration, {scale: cc.v3(scaleRatio, scaleRatio, 1)}).call(
                () => {
                    this.pressActionEnd_ = true;
                }
            );
        this.pressAction_.start();
    }

    protected restore() {
        if (this.restoreAction_) {
            this.restoreAction_.stop();
        }

        this.restoreActionEnd_ = false;
        const scaleRatio = this.scaleMax * this.defaultScale_;
        this.restoreAction_ = cc.tween(this.node).to(
            this.scaleDuration, {scale: cc.v3(scaleRatio, scaleRatio, 1)}).to(
            this.scaleDuration, {scale: cc.v3(this.defaultScale_, this.defaultScale_, 1)}).call(
                () => {
                    this.restoreActionEnd_ = true;
                }
            ).call(() => {
                console.log("restore xxxxxx done");
            });

        this.restoreAction_.start();

        if (this.scrollView) {
            this.scrollView.enabled = true;

            // this.scrollView.scrollToOffset(new cc.Vec2(-this.prevScrollOffset_.x, this.prevScrollOffset_.y), 2);
            // cc.log("prevScrollOffset_ %f, %f", this.prevScrollOffset_.x, this.prevScrollOffset_.y);
        }
    }
    /** 设置按钮标题 */
    public setButtonTitle(title: string): void {
        this.buttonTitle.string = title;
    }
    /** 设置按钮是否灰化
     * @param flag 为true表示灰化显示 ，false为正常显示
     */
    public setGrayEffect(flag: boolean = true, force: boolean = false): void {
        this.isGray_ = flag;
        CommonUtils.setNodeSpriteGray(this.node, flag, force);
    }

    /**
     * 设置按钮选中的文字颜色
     * @param color
     */
    public setSelectColor(color: cc.Color): void {
        this.selectColor = color;
    }

    hideSelectedArray(show: boolean = true) {
        for (let node of this.selectedNodeArray) {
            node.active = show;
        }
    }

    protected onDestroy(): void {
        this.node.off(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.off(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.off(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.off(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);

        this.targetClick_ = null;
        this.onClick_ = null;
        this.guideTargetClick_ = null;
        this.onGuideClick_ = null;
        this.targetMove_ = null;
        this.onMove_ = null;
        this.targetMoveEnd_ = null;
        this.onMoveEnd_ = null;
        this.targetChecked_ = null;
        this.checkedCallBack_ = null;
        this.onLongPress_ = null;
        this.onClickAdd_.length = 0;
        // try {
        //     super.onDestroy();
        // } catch (error) {
        //     // console.error("onDestroy error = ", error)
        // }
    }

    setBtnRedPoint(state:boolean): void {
        this.redPoint.node.active = state;
    }
}
