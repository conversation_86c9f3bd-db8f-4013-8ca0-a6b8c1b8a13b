import * as cc from "cc";

import { LoadUtils } from "../Utils/LoadUtils";


const { ccclass, menu, disallowMultiple, requireComponent } = cc._decorator;

/**
 * 精灵组件，自动管理资源的引用计数
 */
@ccclass
@disallowMultiple
@menu("自定义组件/ResSprite")
export default class ResSprite extends cc.Component {
    private _sprite: cc.Sprite = null;
    private _lastUrl: string;//最后加载的资源路径（防止先后顺序导致资源错乱）
    private _lastKey: string;
    private _isInit: boolean;
    private _initArgs: IArguments;
    private _assetPathMap: { [key: string]: number } = cc.js.createMap();
    onLoad(): void {
        this._sprite = this.node.getComponent(cc.Sprite) || this.node.addComponent(cc.Sprite);
    }
    start(): void {
        this._isInit = true;
        if (this._initArgs) this.setSpriteFrame.apply(this, this._initArgs);
    }

    get spriteFrame(): cc.SpriteFrame {
        return this._sprite.spriteFrame;
    }
    set spriteFrame(v: cc.SpriteFrame) {
        if (!this._sprite) {
            return;
        }
        this._sprite.spriteFrame = v;
    }
    public clear(): void {
        this._lastUrl = null;
        this.spriteFrame = null;
    }

    protected onDestroy(): void {
        // this.clear();
        this._initArgs = null;
        //销毁时这里不处理 转调用 ResBase.onDestroy
        for (let key in this._assetPathMap) {
            LoadUtils.releaseAsset(key, this._assetPathMap[key]);
        }
    }

    /**
     * 加载并设置spriteFrame
     * @param url 图片或图集路径，规则同Res加载路径
     * @param key 如果需要加载的url为图集时，需传入图集的key
     */
    public setSpriteFrame(url: string, key: string = "", callback?: Function): void {
        if (!this._isInit) {
            this._initArgs = arguments;
            return;
        }
        this._initArgs = null;
        if (!cc.isValid(this, true)) return;
        if (key === "" || key === null) {
            if (this._lastUrl == url) return;
        }
        this.clear();
        this._lastUrl = url;
        this._lastKey = key;
        if (!url || url.length == 0) return;
        let resType = key ? cc.SpriteAtlas : cc.SpriteFrame;
        let count: number = this._assetPathMap[url];
        if (count != undefined) {
            count++;
        }
        this._assetPathMap[url] = count || 1;
        let self = this;
        LoadUtils.load(url, resType, (asset: cc.SpriteAtlas | cc.SpriteFrame) => {
            if (self._lastUrl == url) {
                if (self._lastKey == "" || self._lastKey == null) {
                    self.spriteFrame = (asset as cc.SpriteFrame);
                    callback && callback();
                } else {
                    self.spriteFrame = (asset as cc.SpriteAtlas).getSpriteFrame(self._lastKey);
                    callback && callback();
                }
            }
        });
    }
}
