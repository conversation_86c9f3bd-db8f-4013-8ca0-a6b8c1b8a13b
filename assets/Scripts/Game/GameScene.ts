import * as cc from 'cc';

import { AppConfig } from "../Config/GameConfig";
import { ViewId } from "../Config/ViewConst";
import CameraController, { MoveType, NORMAL_ZOOM_RATIO } from "./Module/Camera/CameraManager";
import SceneBase from "./Components/SceneBase";
import Env from "../Env";
import { Input, KeyCode } from "../Plugin/Input";
import ViewManager from "./GlobalComponent/ViewManagerComponent";

import GameController from "./Control/GameController";
import MapManager from './Module/MapInstance/Managers/MapManager';
import { HeroManager } from './Module/Fight/Managers/HeroManager';

// import { HeroManager } from "./Managers/HeroManager";
// import Npc from "./game/unit/npc/base/Npc";
// import MapManager from "./manager/map/MapManager";

const { ccclass, property } = cc._decorator;

/**
 * 主场景启动类.
 */
@ccclass
export default class GameScene extends SceneBase {

    @property(cc.Node)
    UILayer: cc.Node = null;

    @property(cc.Node)
    MapLayer: cc.Node = null;

    private _cameraChangeHandler: () => void = null;

    private MAX_ZOOM_RATIO: number = 0.97;
    private cameraZoomRatio: number = NORMAL_ZOOM_RATIO;
    private cameraZoomRatioSpeed: number = (this.MAX_ZOOM_RATIO - NORMAL_ZOOM_RATIO) / AppConfig.frameRate;
    private cameraZoomRatioSpeedFlag: number = 1;
    private cameraZoomStopFrame: number = AppConfig.frameRate;
    private timerTimerHandler: TimerHandler = null;

    onLoad() {
        super.onLoad();

        cc.PhysicsSystem2D.instance.enable = true;

        let debugDrawFlags = 0;
        if (Env.debug) {
            debugDrawFlags = cc.EPhysics2DDrawFlags.Aabb |
                cc.EPhysics2DDrawFlags.Joint |
                cc.EPhysics2DDrawFlags.Shape;
        }
        cc.PhysicsSystem.instance.debugDrawFlags = debugDrawFlags;
        // GameController.instance.init(this.UILayer, this.MapLayer);
    }


    /**处理npc解锁后镜头拉近npc的效果*/

    // uncLock(unlockNpc: Npc) {
    //     HeroManager.instance.lockAllHerosMove();
    //     this.cameraZoomRatio = NORMAL_ZOOM_RATIO;
    //     this.cameraZoomRatioSpeedFlag = 1;
    //     this.cameraZoomStopFrame = AppConfig.frameRate;
    //     if (this._cameraChangeHandler) {
    //         this.unschedule(this._cameraChangeHandler);
    //         this._cameraChangeHandler = null;
    //     }
    //     this._cameraChangeHandler = () => {
    //         this.cameraChange();
    //     }
    //     CameraController.instance.setTarget(unlockNpc.node, MoveType.QuickTween);
    //     this.schedule(this._cameraChangeHandler, 1000 / AppConfig.frameTime, cc.macro.REPEAT_FOREVER);
    // }

    private cameraChange() {
        if (this.cameraZoomRatioSpeedFlag >= 0 && this.cameraZoomRatio >= this.MAX_ZOOM_RATIO) {
            this.cameraZoomStopFrame--;

            if (this.cameraZoomStopFrame <= 0) {
                this.cameraZoomRatioSpeedFlag = -1;
            }
            else {
                this.cameraZoomRatioSpeedFlag = 0;
            }
            if (this.cameraZoomRatioSpeedFlag == 0) {
                return;
            }
        }
        else if (this.cameraZoomRatioSpeedFlag < 0 && this.cameraZoomRatio <= NORMAL_ZOOM_RATIO) {
            this.unschedule(this._cameraChangeHandler);
            this._cameraChangeHandler = null;
            CameraController.instance.setZoomValue(NORMAL_ZOOM_RATIO);
            HeroManager.instance.unLockAllHerosMove();
            CameraController.instance.setTarget(MapManager.curMap.mainPlayer.node)
            return;
        }

        this.cameraZoomRatio += (this.cameraZoomRatioSpeed * this.cameraZoomRatioSpeedFlag);
        CameraController.instance.setZoomValue(this.cameraZoomRatio);
    }

    start() {
        GameController.instance<GameController>().connectToGameServer();
    }

    update(dt) {
        if (!AppConfig.openGm) {
            return;
        }
        if (Input.getKeyDown(KeyCode.W)) {
            CameraController.instance.testZoomAdd();
        }
        if (Input.getKeyDown(KeyCode.S)) {
            CameraController.instance.testZoomSub();
        }
        if (Input.getKeyDown(KeyCode.Space)) {
            const viewManager = ViewManager.instance();
            if (!!viewManager.hasView(ViewId.GM)) {
                viewManager.closeByUri(ViewId.GM);
            } else {
                viewManager.show(ViewId.GM);
            }
        }
    }

    onDestroy() {
        //App.EventManager.removeEventListener(GameEvent.NPC_UNLOCK, this.uncLock, this);
        // GameController.instance.destroy();
    }

    /**
     * 返回登录界面 
     */
    reLogin() {
        const viewManager = ViewManager.instance();
        viewManager.closeAll();
        // GameController.instance.reLogin();
    }
}

