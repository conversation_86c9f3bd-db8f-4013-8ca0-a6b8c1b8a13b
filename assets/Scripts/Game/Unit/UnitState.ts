/**
 * unit状态
 */
export enum UnitState {
    /**
     * 无
     */
    none = 0,

    /**  待机 */
    idle = 1,

    /** 控制状态 */
    control = 20,

    /** 巡逻状态 */
    patrol = 21,

    /** 攻击状态 */
    attack = 3,

    /** 前往追击 */
    chase = 4,

    /**  死亡 */
    dead = 5,

    /**
    * 行走
    */
    walk = 2,
    /**
     * 回归守卫点
     */
    comeback = 6,


    /**  采集 */
    collect = 7,

    /**脱战 */
    disengage = 22,
    /** 
     * 眩晕 
     * */
    stun = 30,
    /** 
     * 冻结 
     * */
    freeze = 31,
    /** 
     * 浮空  
     */
    juggle = 32,
    /**
     * 突进
     */
    charge = 33,
    /**
     * 击退
     */
    pushBack = 34,


}