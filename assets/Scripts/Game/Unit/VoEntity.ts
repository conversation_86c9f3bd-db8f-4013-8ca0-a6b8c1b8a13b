import * as cc from "cc";

import { GameObject } from "../Module/Fight/GameObject";
import { PlayerCamp } from "./UnitConst";

/**
 * 实体数据类
 */
export class VoEntity extends GameObject {

    id: number = 0;

    /**区id */
    zoneId: number = 0;

    /**唯一id */
    unitId: number = 0;

    /**unit类型 */
    unitType: number = 0;

    /** 细分的类型 (树分为树和矿, 怪分为小怪,精英,Boss)*/
    subType: number = 0;

    /** 配置表id */
    idExcel: number = 0;

    /** 所在迷雾区域 没配置的话，就是0  */
    fogArea: number = 0;

    /** 名字 */
    name: string;

    /** 模型标识 */
    protected _identity: number = 0;   // 模型/标识

    /**阵营  1.己方  2.敌方   3.中立*/
    camp: PlayerCamp;

    /** 身体半径 */
    bodyRadius: number = 60;

    /**
     * 驻守点
     */
    point: cc.Vec3 = cc.Vec3.ZERO.clone();


    public set identity(value: number) {
        this._identity = value;
    }

    public get identity(): number {
        return this._identity;
    }


    /**
     * 血条高度
     * @returns 
     */
    getHpHeight(): number {
        return NaN;
    }


    reborn() {

    }
}