import * as cc from "cc";
import AppManager from "../Managers/AppManager";
import { BattleAttr } from "../../Config/GameDefine";
import CFG_ModelHeight from "../../Config/GameCfg/CFG_ModelHeight";
import { VoUnit } from "./VoUnit";

/**
 * 角色模板. 具有血量
 */
export class VoCharacter extends VoUnit {
    /**恢复量 */
    healPercent: number;
    /**恢复频率 */
    healInterval: number;
    /** 阻尼. 移动的阻挡系数  撞飞也生效 */
    linearDamping: number = 5;
    /** 密度 控制撞飞 */
    density: number = 5000;

    /**产出 */
    produceList = [];

    /**脱战开始时间 */
    outOfRangeTime: number;

    lastHealTime: number = 0;

    isOutOfRange: boolean = false;

    modelConfig: CFG_ModelHeight;

    /** 对象路径 */
    path: string;
    /** 对象Bundle */
    bundle?: string;

    protected skills: { id: number, level: number }[];

    override set identity(value: number) {
        if (this._identity != value) {
            this._identity = value;
            this.modelConfig = CFG_ModelHeight.get(this._identity);
        }
    }
    override get identity(): number {
        return this._identity;
    }

    /**设置脱战 */
    setOutOfRange() {
        this.isOutOfRange = true;
        this.outOfRangeTime = AppManager.instance().TimeManager.serverTimeMs;
        //Logger.trace("setOutOfRange ", this.outOfRangeTime, this.unitType, this.unitId, Date.now(), this.getHp());
    }

    /**更新恢复的血量 */
    updateHealHp() {
        if (this.isOutOfRange) {
            //Logger.trace("1 updateHealHp ", this.unitType, this.unitId, this.lastHealTime, this.outOfRangeTime, Date.now(), this.getHp());
            let time = this.lastHealTime == 0 ? this.outOfRangeTime : this.lastHealTime;
            time = AppManager.instance().TimeManager.serverTimeMs - time;
            let n = Math.floor(time / this.healInterval);
            let addValue: number = this.getHpMax() * n * this.healPercent;
            if (addValue > 0) {
                this.addHp(addValue);
                this.lastHealTime = AppManager.instance().TimeManager.serverTimeMs;
                //Logger.trace("2 updateHealHp ", addValue, this.unitType, this.unitId, this.lastHealTime, Date.now(), this.getHp());
            }
        }
    }

    getSkills(): { id: number, level: number }[] {
        return this.skills;
    }

    getHpHeight(): number {
        return this.modelConfig ? this.modelConfig.hpHeight * (this.scale / 100) : NaN;
    }

    reborn(): void {
        super.reborn();
        this.setAttr(BattleAttr.hp, this.getAttr(BattleAttr.maxHp));
    }
}
