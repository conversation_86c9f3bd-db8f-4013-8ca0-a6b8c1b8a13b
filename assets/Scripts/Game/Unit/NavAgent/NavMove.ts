import * as cc from "cc";
import { AppConfig } from "../../../Config/GameConfig";
import BattleConst from "../../../Config/GameCfgExt/BattleConst";
import Logger from "../../../Framework/Logger";
import PathFindingAgent from "../../Module/Map/road/PathFindingAgent";
import RoadNode from "../../Module/Map/road/RoadNode";


/**
 * 导航移动回调
 */
export interface NavMoveCallBack {

    /** 启动移动  */
    onStartNavMove();

    /** 停止移动 */
    onStopNavMove();

    /** 更新移动方向 */
    onNavMoveDirection(value: number);
}


/**
 * 导航移动组件.  作为工具类.辅助使用
 */
export class NavMove {

    private _isNavMove: boolean = false;

    /**
    * 移动的角度
    */
    protected _moveAngle: number = 0;

    /**
     * 行走的路径（路点列表）
     */
    protected _roadNodeArr: RoadNode[] = [];

    /**
     * 当前正在行走向第几个路点
     */
    public _nodeIndex: number = 0;


    protected _space: number = 0;


    protected _callback: NavMoveCallBack;

    protected _node: cc.Node;

    protected _speed: number;

    protected _destX: number;

    protected _destY: number;

    /** 最小误差. 不可能百分百跟目标重合,这里设置一个误差值. */
    public MIN_SPACE: number = 20;

    constructor(callback: NavMoveCallBack) {
        this._callback = callback;
        this.MIN_SPACE = Number(BattleConst.getValue("min_attack_range"))
    }


    /**
    * 是否在导航.
    * @returns 
    */
    isNavMove(): boolean {
        return this._isNavMove;
    }

    /**
     * 导航移动到目的地 
     * @param node 需要导航的节点. 必须使用刚体移动
     * @param speed 每秒移动速度
     * @param destX 目标x
     * @param destY 目标y
     * @param radius 身体半径
     * @param space  允许终止的距离
     */
    navTo(node: cc.Node, speed: number, destX: number, destY: number, radius: number, space: number = 0) {
        this._node = node;
        this._speed = speed;
        this._destX = destX;
        this._destY = destY;

        this._space = space > this.MIN_SPACE ? space : this.MIN_SPACE;
        var roadNodeArr: RoadNode[] = PathFindingAgent.instance.seekPath2(node.x, node.y, destX, destY, radius);

        if (roadNodeArr.length > 0) {
            this.walkByRoad(roadNodeArr);
        } else {
            //找不到路.
            Logger.trace("找不到路");
        }

        return roadNodeArr;
    }

    /**
     * 根据路节点路径行走
     * @param roadNodeArr 
     */
    private walkByRoad(roadNodeArr: RoadNode[]) {
        this._nodeIndex = 0;
        this._moveAngle = 0;
        this._roadNodeArr = roadNodeArr;

        this.walk();
        this.move();
    }

    private walk() {
        if (this._nodeIndex < this._roadNodeArr.length - 1) {
            this._nodeIndex++;
        }
    }

    private move() {
        if (this._roadNodeArr.length == 0) {
            this.stop();
            return;
        }

        this._isNavMove = true;
        this._moveAngle = 0;
        if (this._callback) {
            this._callback.onStartNavMove();
        }
    }

    private getNextPosition(): cc.Vec2 {
        if (this._nodeIndex <= this._roadNodeArr.length - 1) {
            let node = this._roadNodeArr[this._nodeIndex];
            return cc.v2(node.px, node.py);
        } else {
            return cc.v2(this._destX, this._destY);
        }
    }

    /**
     * 停止导航移动
     */
    public stop() {
        if (this.isNavMove()) {
            this._node = null;
            this._isNavMove = false;
            this._roadNodeArr.length = 0;

            if (this._callback) {
                this._callback.onStopNavMove();
            }
        }
    }

    public destroy() {
        if (this.isNavMove()) {
            this._isNavMove = false;
        }
        this._node = null;
        this._callback = null;
        this._roadNodeArr.length = 0;
    }

    update(dt: number) {
        if (!this.isNavMove()) return false;

        const currPosition = this._node.getPosition();
        const nextPosition = this.getNextPosition();
        const endPosition = cc.v2(this._destX, this._destY);
        const distance = cc.Vec2.distance(currPosition.toVec2(), endPosition);

        const dx = nextPosition.x - currPosition.x;
        const dy = nextPosition.y - currPosition.y;
        const speed = this._speed * dt;
        // Logger.trace("update 1>>", dx, dy, speed, this._nodeIndex);

        //是否靠近目标. 走最后一个格子的时候, 距离小于space时，允许停止移动.
        //const isNearest: boolean = this._nodeIndex == this._roadNodeArr.length - 1 && distance <= this._space;

        //不用判断最后一个格子. 只要距离够就停止
        const isNearest: boolean = distance <= this._space;

        if (!isNearest && dx * dx + dy * dy > speed * speed) {
            this._moveAngle = Math.atan2(dy, dx);
            var dire: number = Math.round((-this._moveAngle + Math.PI) / (Math.PI / 4));

            if (this._callback) this._callback.onNavMoveDirection(dire > 5 ? dire - 6 : dire + 2);

            const velocityDirection = nextPosition.subtract(currPosition.toVec2()).normalize();
            const linearVelocity = velocityDirection.multiplyScalar(this._speed * (dt / AppConfig.frameTime));
            const rigidBody = this._node.getComponent(cc.RigidBody2D);

            // Logger.trace("update 2>>", linearVelocity);
            if (rigidBody) rigidBody.linearVelocity = linearVelocity;
            return true;
        } else {
            // Logger.trace("update 3>>", this._nodeIndex, this._roadNodeArr, isNearest, distance, this._space);
            if (isNearest || this._nodeIndex == this._roadNodeArr.length - 1) {
                this.stop();
                return false;
            } else {
                this.walk();
                return true;
            }
        }
    }
}