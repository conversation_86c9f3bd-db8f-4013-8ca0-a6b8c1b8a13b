// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import * as cc from "cc";
// import TransferDoor from "../../../transfer/TransferDoor";

import Actor from "./Actor";

const { ccclass, property } = cc._decorator;

/**
 * 角色脚底触发器，用来检测脚底踩到什么东西,和角色配合使用
 */
@ccclass
export default class FootTrigger extends cc.Component {

    /**
     * 动画组件
     */
    @property(Actor)
    protected selfActor: Actor = null;

    public get actor(): Actor {
        if (!this.selfActor && this.node.parent) {
            this.selfActor = this.node.parent.getComponent(Actor);
        }
        return this.selfActor;
    }

    // LIFE-CYCLE CALLBACKS:

    // onLoad () {}

    start() {

    }

    // update (dt) {}

    public onCollisionEnter(other: cc.Collider2D, self: cc.Collider) {
        if (other.tag == 2) {
            // var transferDoor: TransferDoor = other.getComponent(TransferDoor);
            // if (transferDoor != null) //脚下触碰到传送门时
            // {
            //     transferDoor.onTriggerEnter(this.actor);
            // }
        }
    }

    public onCollisionExit(other: cc.Collider2D, self: cc.Collider) {
        if (other.tag == 2) {
            // var transferDoor: TransferDoor = other.getComponent(TransferDoor);
            // if (transferDoor != null) //脚下触碰到离开传送门时
            // {
            //     transferDoor.onTriggerExit(this.actor);
            // }
        }
    }
}
