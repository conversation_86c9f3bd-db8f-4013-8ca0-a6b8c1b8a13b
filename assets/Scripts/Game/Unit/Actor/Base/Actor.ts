import * as cc from "cc";
import { UnitAnim } from "../../../../Config/GameDefine";
import GameManager from "../../../Managers/Res/GameManager";
// import { RoleAttribute } from "../../../Modules/Fight/Common/RoleAttribute";
import RoadNode from "../../../Module/Map/road/RoadNode";
import { NavMove, NavMoveCallBack } from "../../NavAgent/NavMove";
import { UnitState } from "../../UnitState";
import { VoCharacter } from "../../VoCharacter";
import { VoEntity } from "../../VoEntity";
import Unit from "../../Base/Unit";
import UnitBuffIconList from "../../Base/UnitBuffIconList";

const { ccclass, property } = cc._decorator;

/**
 * 扮演者基类
 * 场景里的一切活体单位，玩家，宠物，怪，npc都是扮演者，都应该继承自这个类
 */
@ccclass
export default class Actor extends Unit implements NavMoveCallBack {

    /**
     *角色最近一次所站在的地图节点
     */
    protected _lastRoadNode: RoadNode = null;

    /** 导航移动组件 */
    protected _navMove: NavMove;
    /**显示bufficon列表的节点 */
    private _buffListNode: cc.Node;
    onLoad() {
        super.onLoad();
    }

    start() {
        super.start();
    }

    update(dt) {
        super.update(dt);

        if (this._navMove) {
            this._navMove.update(dt);
        }
    }

    /**
     * 导航移动
     * @param destX 
     * @param destY 
     * @param space 
     */

    protected roadNodeArr: RoadNode[] = [];
    navTo(destX: number, destY: number, space: number = 0) {
        if (!this._navMove) {
            this._navMove = new NavMove(this);
        }
        let radius: number = this.data.bodyRadius;
        // let speed: number = RoleAttribute.getMoveSpeed(this);
        let speed = 1;
        this.roadNodeArr = this._navMove.navTo(this.node, speed, destX, destY, radius, space);
        if (!this.roadNodeArr || this.roadNodeArr.length == 0) {
            console.log("没找到路");
        }
    }

    public changeBuffChange(data) {
        if (data.unitId != this.data.unitId) return;
        if (!this._buffListNode) return;
        let comp: UnitBuffIconList = this._buffListNode.getComponent(UnitBuffIconList);
        if (comp) {
            // let buffList = this.skillLearned.getAllBuffInfo();

            // if (buffList) {
            //     comp.updateBuffList(buffList)
            // }
        }
    }

    public addBuffListNode() {
        // this._buffListNode = GameManager.instance.otherMgr.getUnitBuffListPrefab();
        // this._buffListNode.parent = this.node;
        // this._buffListNode.y = this.getBodyHeight() * this.node.scale + 10;
    }
    /**
     * 停止导航移动.ss
     */
    stopNavMove() {
        if (this._navMove) {
            this._navMove.stop();
        }
    }

    isNavMove(): boolean {
        return this._navMove && this._navMove.isNavMove();
    }


    override set data(value: VoEntity) {
        this._data = value;
    }

    override get data(): VoCharacter {
        return this._data as VoCharacter;
    }

    public set_state(value: UnitState) {
        super.set_state(value);
        switch (this._state) {
            case UnitState.idle:
                this.play(UnitAnim.idle);
                break;
            case UnitState.walk:
                if (this.currentClipName != UnitAnim.walk) {
                    this.play(UnitAnim.walk, Math.random() * 0.3);
                }
                break;
            case UnitState.chase:
                if (this.currentClipName != UnitAnim.walk) {
                    this.play(UnitAnim.walk, Math.random() * 0.3);
                }
                break;

            case UnitState.attack:
                break;
            case UnitState.dead:
                this.play(UnitAnim.dead);
                break;
            case UnitState.comeback:
                if (this.currentClipName != UnitAnim.walk) {
                    this.play(UnitAnim.walk, Math.random() * 0.3);
                }
                break;
        }
    }

    /**
     * 设置线性力.
     * @param x 
     * @param y 
     */
    setLinearVelocity(x: number | cc.Vec2 | cc.Vec3, y?: number) {
        let velocity: cc.Vec2;
        if (typeof x === "number") {
            velocity = new cc.Vec2(x, y);
        } else if (x instanceof cc.Vec3) {
            velocity = new cc.Vec2(x.x, x.y);
        } else {
            velocity = x;
        }
        let rigidBody = this.node.getComponent(cc.RigidBody2D);
        if (rigidBody) {
            rigidBody.linearVelocity = velocity;
        }
    }

    removeSelf(): void {
        if (this._navMove) {
            this._navMove.destroy();
            this._navMove = null;
        }
        super.removeSelf();
    }

    onStartNavMove() {
        this.state = UnitState.walk;
    }

    onStopNavMove() {
        this.state = UnitState.idle;
        this.setLinearVelocity(cc.Vec2.ZERO);
    }

    /** 导航改变朝向  */
    onNavMoveDirection(value: number) {
        this.direction = value;
    }

    onBeAttack(): void {
        // super.onBeAttack();
    }
}
