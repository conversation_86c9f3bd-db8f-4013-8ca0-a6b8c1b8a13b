
/** 英雄密度 */
export const HERO_DENSITY = 10;
/** 英雄控制时的阻尼 */
export const HERO_CONTROL_DAMPING = 0;
/** 英雄不控制时的阻尼 */
export const HERO_NORMAL_DAMPING = 20;
/** 怪物的阻尼 */
export const MONSTER_DAMPING = [5, 5, 5, 5];
/** 怪物的密度 */
export const MONSTER_DENSITY = [5, 5, 5, 10];
/** 英雄的搜索范围Y轴系数. */
export const HERO_AXIS_RATE = 1;
/** 树木的半径 */
export const TREE_BODY_RADIUS = 30;
/** 英雄具体目标x像素视为靠近. 靠近后会按照时间去停止移动 */
export const HERO_NEAR_RANGE = 500;
/** 英雄返回的距离判断. 大于此距离会返回中心的 */
export const TEAM_NEAR_RANGE = 250;

/**
 * 怪物类型
 */
export enum MonsterType {
    /** 小怪 */
    Normal = 1,

    /** 精英 */
    Elite = 2,
    /** BOSS */
    BOSS = 3,
}

/**
 * 生命形态
 */
export enum LifeState {
    /**无 */
    None,
    /**存活中 */
    Survival,
    /**已死亡 */
    Die,
}

/**
 * 树类型
 */
export enum TreeType {
    /** 无 */
    None = 0,
    /**树 1 */
    Tree = 1,
    /**矿 2 */
    Ore = 2,
}

/**
 * 玩家类型
 *
 * 多人在线游戏时，用于区分玩家是谁的
 */
export enum PlayerType {
    /**
     * 无人认领
     */
    none = 0,

    /**
     * 属于玩家自己
     */
    my = 1,

    /**
     * 属于其他玩家
     */
    other = 1,
}

/**
 * 玩家阵营
 * 
 */
export enum PlayerCamp {
    /**己方阵营 */
    Own = 1,
    /**敌方阵营 */
    Enemy = 2,
    /**中立阵营 */
    Neutral = 3,
}

/**
 * 玩家控制的方式
 */
export enum PlayerControlType {
    /**
     * 无控制
     */
    none = 0,

    /**
     * 用户控制
     */
    user = 1,

    /**
     * ai控制
     */
    ai = 2,

    /**
     * 网络玩家控制
     */
    net = 3,
}

export class Player_Init_data {
    /** 移动速度 */
    static MoveSpeed: number = 300;
    /** 英雄安全区域回血间隔(毫秒) */
    static HeroSafeAreaRecoverInterval: number = 1000;
    /** 英雄安全区域回血百分比 */
    static HeroSafeAreaRecoverPercent: number = 0.1;
    /**团队范围 */
    static TeamRange: number = 235;
    /**砍树伤害 */
    static WoodDmg: number = 1;
    /**挖矿伤害 */
    static MineDmg: number = 1;
    /** 视野范围 */
    static SightRange: number = 1200;
    /** 团队视野范围. */
    static TeamSightRange: number = 2000;
    /** 初始命中 */
    static Hit: number = 10000;
    /** 初始闪避 */
    static Dodge: number = 800;
    /** 初始爆伤 */
    static CritDmgRate: number = 15000;
    /** 初始暴击 */
    static Crit: number = 800;
}

export class Player_collect {
    /**采集攻击范围 */
    static AttackRange: number = 240;
    /**采集攻击帧 */
    static AttackFrame: number = 470;
    /**采集攻击cd */
    static AttackCdTime: number = 600;
}