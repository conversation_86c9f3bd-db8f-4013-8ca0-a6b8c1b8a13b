import * as cc from "cc";

const { ccclass } = cc._decorator;

@ccclass
export default class Typewriting extends cc.Component {
    text: string;
    interval: number = 0.1;
    index: number = 0;
    textLen: number = 0;
    finsh: boolean = false;
    method: Function;
    caller: any;

    setFinishCallBack(caller, callback) {
        this.caller = caller;
        this.method = callback;
    }
    start() {
        this.index = 0;
        this.textLen = this.removeRichTextTags(this.text).length;
        this.schedule(this.showText, this.interval, this.textLen + 2);
        this.finsh = false;
    }

    showText() {
        if (this.finsh) return;
        if (this.index < this.text.length) {
            let char = this.text[this.index];
            if (char === '<') {
                let endIndex = this.text.indexOf('>', this.index);
                char = this.text.substring(this.index, endIndex + 1);
                this.index = endIndex + 1;
            } else {
                this.index++;
            }

            this.node.getComponent(cc.RichText).string = this.text.substring(0, this.index);
        }
        else {
            this.finsh = true;
            if (this.method)
                this.method.apply(this.caller);
        }
    }

    onDestroy() {
        this.unschedule(this.showText);
    }

    // 去除富文本标签的方法
    removeRichTextTags(str) {
        return str.replace(/<[^>]+>/g, ''); // 使用正则表达式去除所有的HTML标签
    }
}
