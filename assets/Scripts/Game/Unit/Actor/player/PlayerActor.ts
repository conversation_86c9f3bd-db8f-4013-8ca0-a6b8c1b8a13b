import * as cc from "cc";

import { AppConfig } from "../../../../Config/GameConfig";
import { UnitType } from "../../../../Config/GameDefine";
import { GameEvent } from "../../../../GameEvent/Events";
// import BubblingCache, { CFG_Bubbling } from "../../../../config/game_config/BubblingCache";
import CFG_Constant from "../../../../Config/GameCfgExt/BattleConst";
import  Logger  from "../../../../Framework/Logger";
import { EffectSingleCase } from "../../../Utils/animator-effect/EffectSingleCase";
import { LoadUtils } from "../../../Utils/LoadUtils";
import { ViewUtil } from "../../../Utils/ViewUtils";
import { MAP_FRICTION } from "../../../Module/MapInstance/Base/MapDefine";
import { MapHelper } from "../../../Module/MapInstance/Base/MapHelper";
// import MapManager from "../../../Module/MapInstance/Managers/MapManager";
// import NpcManager from "../../../Managers/Npc/NpcManager";
import { SoundId, SoundType } from "../../../Module/Sound/SoundManagers";
import CameraController from "../../../Module/Camera/CameraManager";
// import BattleController from "../../../Control/BattleController";
import JoystickController from "../../../Control/JoystickController";
import { Bundles } from "../../../Module/Bundles";
// import { FloatingTextType } from "../../../modules/fight/FloatingText";
// import { SkillRangeWay } from "../../../modules/fight/SkillType";
// import { RoleAttribute } from "../../../modules/fight/common/RoleAttribute";
// import { RoleSelector, SearchCondition, SearchParam } from "../../../modules/fight/common/RoleSelector";
// import { HeroManager } from "../../../Manager/Fight/HeroManager";
import CountDown from "../../../Module/Map/CountDown";
import HpBar from "../../../Module/Map/HpBar";
import MBossName from "../../../Module/Map/MBossName";
// import { CollectSkill } from "../../../skill/CollectSkill";
import { MathUtils } from "../../../Utils/MathUtils";
import RandomUtils from "../../../Utils/RandomUtils";
import { UIUtils } from "../../../Utils/UIUtils";
import { UnitState } from "../../UnitState";
import { VoHero } from "../../VoHero";
import { IUnit } from "../../Base/IUnit";
import Unit from "../../Base/Unit";
import { HERO_CONTROL_DAMPING, HERO_NEAR_RANGE, HERO_NORMAL_DAMPING, PlayerCamp, Player_Init_data, Player_collect } from "../PlayerType";
import Actor from "../Base/Actor";
import Typewriting from "./Typewriting";
import EventManagerComponent from "../../../GlobalComponent/EventManagerComponent";
import Timer from "../../../Utils/Timer";
import AppManager from "../../../Managers/AppManager";
import SoundManager from "../../../GlobalComponent/SoundManager";


const { ccclass, property } = cc._decorator;

export enum QiPaoType {
    Dead = 1,//死亡触发
    ChangeScene = 2,//切换场景
    Idle = 3,//待机
    EnterSafeZone = 4,//进入安全区
    ExitSafeZone = 5,//退出安全区
}
@ccclass
export default class PlayerActor extends Actor {
    unitType: UnitType = UnitType.Hero;
    // static qipaoCfgMap: Map<QiPaoType, CFG_Bubbling[]> = null;
    @property(cc.RigidBody2D)
    public rigidBody: cc.RigidBody2D = null;

    /** 是否休眠 */
    sleep: boolean = false;

    // /**
    //  * 采集技能
    //  * */
    // private collectSkill: CollectSkill;

    /**
     * 采集的目标
     */
    private collectTarget: Unit;
    /**是否处于跟随状态*/
    public isFollow: boolean = false;
    /** 跟随的目标位置*/
    protected followPoint: cc.Vec3;
    /** 是否打完怪回归  */
    protected isComeback: boolean = false; //

    /** 导航间距时间 */
    protected navMaxTime: number = 0.5;

    protected navTime: number = 0;

    /** 当前英雄是否靠近中心点  */
    private nearDest: boolean = false;
    /** 经过多少时间后玩家本次不再回归  */
    private nearTime: number = 0;

    /** 是否追击移动过.  追击移动过才需要返回 */
    private isChaseMove: boolean = false;

    /** 露营时候的视野范围  */
    private _enCampSight: number = 0;

    private _lastCheckPos: cc.Vec3 = cc.Vec3.ZERO;
    private _lastCheckTime: number = 0;

    protected idleTime: number = 0;
    private idleQiPaoMaxTime: number = 15000;
    private physicsCircleColliderRadius: number = 0;
    public MIN_SPACE: number = 20;
    private _followIndex: number = -1;

    public set followIndex(value: number) {
        this._followIndex = value;
        // this.nameNode.setMonsterLevel(this._followIndex)

    }

    public get followIndex() {
        return this._followIndex;
    }

    public set chaseMove(value: boolean) {
        this.isChaseMove = value;
    }

    public getFollowPoint() {
        return this.followPoint;
    }

    onStop(): void {
        this.isFollow = false;
        this.isComeback = false;
        this.state = UnitState.idle;
        this._navMove.stop();
    }

    protected createHpBar(): HpBar {
        let node = null;
        node = ViewUtil.createPrefabNode(Bundles.COMMON + "HpBar", Bundles.COMMON);

        let hpHeight: number = this.data.getHpHeight();
        node.y = isNaN(hpHeight) ? this.getBodyHeight() - 19 : hpHeight;
        node.x = 0;
        node.parent = this.node;
        return node.getComponent(HpBar);
    }
    strokeColor: cc.Color = new cc.Color(0xa9, RandomUtils.random(50, 200), RandomUtils.random(200, 255), 255);

    fillColor: cc.Color = null;

    lineWidth: number = 8;

    graphics: cc.Graphics;

    graphicsNode: cc.Node;

    /**
    *  画图形
    *  */
    draw() {
        // this.strokeColor = new cc.Color(0xa9, 0x0f, 0x0f, 255);

        this.fillColor = cc.Color.TRANSPARENT;
        if (!this.graphicsNode) {
            this.graphicsNode = new cc.Node();
            const anchorC = this.graphicsNode.getComponent(cc.UITransform);
            anchorC.setAnchorPoint(0.5, 0.5);
            this.graphicsNode.name = "graphicsNode";
            this.graphicsNode.parent = this.node;
        }

        if (this.graphics) {
            this.graphics.clear();
        } else {
            this.graphics = this.graphicsNode.addComponent(cc.Graphics);
            this.graphics.strokeColor = this.strokeColor;
            this.graphics.fillColor = this.fillColor;
            this.graphics.lineWidth = this.lineWidth;
        }
        this.drawShape();
        this.graphics.stroke();
        this.graphics.fill();
    }

    /**
    * 绘制图形
    * @param point 
    * @param target 
    * @param args 
    * @param charge 
    */
    drawShape() {
        this.graphicsNode.setPosition(this.physicsCircleCollider.offset.x, this.physicsCircleCollider.offset.y);
        let range: number = this.physicsCircleCollider.radius;
        this.graphics.circle(0, 0, range);
    }

    private nodeList = [];
    drawRoad() {
        this.fillColor = cc.Color.TRANSPARENT;

        if (this.roadNodeArr && this.roadNodeArr.length <= 0) return;
        for (var k = 0; k < this.nodeList.length; k++) {
            this.nodeList[k].removeFromParent();
        }
        this.nodeList = [];
        for (var i = 0; i < this.roadNodeArr.length; i++) {
            let p = this.roadNodeArr[i];
            let range: number = 10;

            let graphicsNode = new cc.Node();
            const anchorC = graphicsNode.addComponent(cc.UITransform);
            anchorC.setAnchorPoint(0.5, 0.5);
            graphicsNode.name = "graphicsNode";
            graphicsNode.parent = this.node.parent;

            let graphics = graphicsNode.addComponent(cc.Graphics);
            graphics.strokeColor = this.strokeColor;
            graphics.fillColor = this.fillColor;
            graphics.lineWidth = this.lineWidth;
            graphicsNode.setPosition(p.px, p.py)
            graphics.circle(0, 0, range);
            graphics.stroke();
            graphics.fill();
            this.nodeList.push(graphicsNode);
        }
    }

    onLoad() {
        super.onLoad();
        this.MIN_SPACE = Number(CFG_Constant.getValue("min_attack_range"))
        let rigidBody = this.node.getComponent(cc.RigidBody2D);
        if (rigidBody) {
            rigidBody.bullet = true;
            rigidBody.allowSleep = false;
            rigidBody.type = cc.ERigidBody2DType.Dynamic;
            rigidBody.enabledContactListener = true;
            rigidBody.linearDamping = HERO_NORMAL_DAMPING;
        }

        let physicsCircleCollider = this.node.getComponent(cc.CircleCollider2D);
        if (physicsCircleCollider && this.data) {
            this.physicsCircleCollider = physicsCircleCollider;
            physicsCircleCollider.density = this.data.density;
            physicsCircleCollider.friction = MAP_FRICTION;
            physicsCircleCollider.restitution = 0;
            this.normalPhysicsCircleColliderRadius = 20;//physicsCircleCollider.radius;
            physicsCircleCollider.radius = this.maxPhysicsCircleColliderRadius;
            this.physicsCircleCollider.offset.x = 0;
            this.physicsCircleCollider.offset.y = 0;
            this.physicsCircleCollider.apply();
        }

        if (!physicsCircleCollider && this.data) {
            let physicsBoxCollider = this.node.getComponent(cc.BoxCollider2D);
            physicsBoxCollider.apply();
        }

        this._enCampSight = Number(CFG_Constant.getValue("camp_team_sight_range"))
        let ss = [
            new cc.Color(0xff, 0, 0, 255),
            new cc.Color(0, 0xff, 0, 255),
            new cc.Color(0, 0, 0xff, 255)]
        this.strokeColor = ss[PlayerActor.tttt % 3];
        PlayerActor.tttt++;
        EventManagerComponent.instance().addEventListener(
            GameEvent.ACTOR_BUFF_CHANGE, this.changeBuffChange, this)
        this.addBuffListNode();
    }



    protected createName(): MBossName {
        let node = null;
        node = ViewUtil.createPrefabNode(Bundles.COMMON + "MBossName", Bundles.COMMON);

        node.y = this.getBodyHeight() * 2 / 5;
        node.x = 30;
        node.parent = this.node;

        return node.getComponent(MBossName);
    }

    private static tttt = 0;
    private maxPhysicsCircleColliderRadius = 38;
    private normalPhysicsCircleColliderRadius = 0;
    updatePhysicsCircleColliderRadius(isStop: boolean = true) {
        this.physicsCircleCollider = this.node.getComponent(cc.CircleCollider2D);
        if (this.physicsCircleCollider && this.data) {
            let value = isStop ? this.maxPhysicsCircleColliderRadius : this.normalPhysicsCircleColliderRadius;
            if (value == this.physicsCircleColliderRadius) {
                this.physicsCircleCollider.radius = value;
                this.physicsCircleCollider.apply();
            }
            else {
                if (this.physicsCircleColliderRadius != value) {
                    this.physicsCircleColliderRadius = value;
                    Timer.clear(this, this.framePhysicsCircleColliderRadius)
                    Timer.frameLoop(1, this, this.framePhysicsCircleColliderRadius)
                }
            }
        }
    }

    framePhysicsCircleColliderRadius() {
        if(!this.node) return;
        this.physicsCircleCollider = this.node.getComponent(cc.CircleCollider2D);
        this.physicsCircleCollider.radius += this.physicsCircleColliderRadius / 10;
        this.physicsCircleCollider.apply();
        if (this.physicsCircleCollider.radius >= this.physicsCircleColliderRadius) {
            Timer.clear(this, this.framePhysicsCircleColliderRadius)
        }
    }

    /** 显示倒计时 */
    showCountDown(rebornTime: number) {
        let time = (rebornTime - AppManager.instance().TimeManager.serverTimeMs) / 1000;

        if (time > 0) {
            let countDownNode = ViewUtil.createPrefabNode(Bundles.COMMON + "Countdown");
            countDownNode.setScale(cc.v3(0.5, 0.5, 1));
            countDownNode.parent = this.node;
            countDownNode.setPosition(0, 60);
            this.countDownNode = countDownNode;

            let countDown: CountDown = countDownNode.getComponent(CountDown);
            countDown.setTime(time);
            countDown.setUnit(this);
        }
    }

    /**
     * 移除复活倒计时
     */
    hideCountDown() {
        if (this.countDownNode) {
            this.countDownNode.destroy();
            this.countDownNode = null;
        }
    }

    /**
     * 更新坐标
     */
    updatePosition() {
        // let mianHero: Player = this.context.mainPlayer;
        // this.node.setPosition(mianHero.node.x, mianHero.node.y);
    }

    /**
     * 开启物理碰撞
     */
    override openCollision() {
        let physicsCircleCollider = this.node.getComponent(cc.CircleCollider2D);
        if (physicsCircleCollider) {
            physicsCircleCollider.sensor = false;
            physicsCircleCollider.apply();
        }
    }

    override set data(value: VoHero) {
        this._data = value;
        this.name = value.name + "-" + this._data.idExcel;
        console.log("player set data", value);
    }

    override get data(): VoHero {
        return this._data as VoHero;
    }

    /**
     * 属性变动
     */
    updateAttr() {
        let skills = this.data.getSkills();
        // for (let i = 0; i < skills.length; i++) {
        //     let skill = this.skillLearned.getSkill(skills[i].id);
        //     if (skill) skill.upgrade(skill.skillId, skills[i].level);
        // }
    }

    start() {
        super.start();
        this.state = UnitState.idle;
    }

    /**
     * 变速
     * @param distance
     * @param dest
     */
    protected changeSpeed(distance: number, dest: cc.Vec3) {
        // if (distance >= Player_Init_data.TeamRange) {
        //     this.updateMoveSpeed(distance);
        // } else {
        //     let self: IUnit = this as unknown as IUnit;
        //     this.setMoveSpeedDown(RoleAttribute.getMoveSpeed(self));
        // }
    }

    /**
     * 移动速度
     * @returns
     */
    protected getMoveSpeed(): number {
        // if (isNaN(this.moveSpeed)) {
        //     let self: IUnit = this as unknown as IUnit;
        //     this.moveSpeed = RoleAttribute.getMoveSpeed(self);
        // }
        // return this.moveSpeed;
        return 1;
    }

    onUpdateHeal(): void {
        // 回血
        if (this.getHp() < this.getHpMax() && AppManager.instance().TimeManager.serverTimeMs - this.data.lastHealTime > this.data.healInterval) {
            this.data.lastHealTime = AppManager.instance().TimeManager.serverTimeMs;
            let addHp = Math.floor(this.getHpMax() * this.data.healPercent);
            if (addHp > 0) {
                this.addHp(addHp);
                // this.fly(FloatingTextType.heal, addHp);
            }
        }
    }

    /**
     * 搜索到可攻击的树木 返回true, 准备砍树
     * @returns 
     */
    private searchTree(): boolean {
        // let param = this.getSearchTreeParam();
        // if (param == null) {
        //     return false;
        // }
        // let self: IUnit = this as unknown as IUnit;

        // let target: IUnit[] = RoleSelector.search(param);

        // if (target == null) {
        //     return false;
        // }
        // this.collectResources(target[0]);
        return true;
    }
    protected lastRefreshTime: number = 0;

    update(dt) {
        // this.draw();
        // this.drawRoad();

        // TODO: use map context
        // if (MapManager.curMap.isPause()) return;

        // if (!this.isDie() && this.skillLearned) {
        //     this.skillLearned.onBuffUpdate();
        // }
        //休眠中不执行任何逻辑
        if (this.sleep) {
            return;
        }
        // if (this.isImprisoned()) {
        //     return;
        // }

        super.update(dt);
        //安全区回血
        if (this._env.isSafeArea()) {
            this.onUpdateHeal();
        }
        //移动更新
        if ((this.isFollow || this.isComeback) && !this.isDie()) {
            this.moveTo(this.followPoint, dt);
            this.idleTime = 0;

            return;
        }
        if (this._state != UnitState.idle) {
            this.idleTime = 0;
        }

        // let curTime = Date.now();
        // if (curTime - this.lastRefreshTime <= 300) {
        //     return;
        // }

        // this.lastRefreshTime = curTime;
        //采集技能更新
        // if (this.collectSkill) {
        //     this.collectSkill.update(dt);
        // }

        switch (this._state) {
            case UnitState.idle:
                if (!this._env.isSafeArea()) {
                    this.onIdleLogic(dt);
                }
                let serverTime = AppManager.instance().TimeManager.serverTimeMs;
                if (this.idleTime == 0) {
                    this.idleTime = serverTime;
                }
                else if (serverTime - this.idleTime >= this.idleQiPaoMaxTime) {
                    RandomUtils.seedRandom(RandomUtils.rand());
                    let random = RandomUtils.randomInt(0, 100)
                    if (random < 30) {
                        this.triggerQiPao(QiPaoType.Idle, 0);
                    }

                    this.idleTime = 0
                }
                break;
            case UnitState.walk:

                if (this._lastCheckPos == null) {
                    this._lastCheckPos = this.node.getPosition();
                    this._lastCheckTime = AppManager.instance().TimeManager.localTime;
                } else {
                    if (AppManager.instance().TimeManager.localTime - this._lastCheckTime >= 1) {
                        if (cc.Vec2.distance(this.node.getPosition(), this._lastCheckPos) <= 10) {
                            this.isChaseMove = true;
                            this.set_state(UnitState.idle);
                            this.onIdleLogic(dt);
                        }
                        else {
                            this.updatePhysicsCircleColliderRadius(false);
                        }
                        this._lastCheckPos = this.node.getPosition();
                        this._lastCheckTime = AppManager.instance().TimeManager.localTime;
                    }
                }
                break;
            case UnitState.chase:
                if (JoystickController.Touching && this.data.camp == PlayerCamp.Own) {
                    this.chaseTarget = null;
                    this.stopNavMove();
                    this.state = UnitState.idle;
                    return;
                }
                // 追击中
                if (this.chaseTarget && this.chaseTarget.node && !this.chaseTarget.isDie()) {
                    this.updatePhysicsCircleColliderRadius(false);
                    //前往采集
                    if (this.chaseTarget.unitType == UnitType.Tree) {
                        this.collectResources(this.chaseTarget);
                        return;
                    }
                    // this.tryAttack(this.chaseTarget);
                } else {
                    this.chaseTarget = null;
                    this.stopNavMove();
                    this.state = UnitState.idle;
                }
                break;
            case UnitState.attack:
                // 攻击中
                break;
            case UnitState.collect:
                //采集中
                break;
            case UnitState.comeback:
                // 回归中
                break;
            case UnitState.dead:
                //死亡后进入安全区.
                if (this.getHp() > 0) {
                    // 移除倒计时
                    if (this.countDownNode) {
                        this.countDownNode.destroy();
                        this.countDownNode = null;
                    }

                    this.openCollision();
                    this.updatePosition();
                    this.state = this.isFollow ? UnitState.walk : UnitState.idle;
                }
                break;
        }
    }

    /** 设置跟随 */
    public setFollow(point: cc.Vec3) {
        if (point == null) {
            return;
        }
        this.followPoint = point.clone();
    }

    onDead() {
        this.cancelCollision();
        // BattleController.instance.onHeroDie(this);
        // super.onDead();
        this.setLinearVelocity(cc.Vec2.ZERO);
    }

    private clearEffect(node: cc.Node) {
        if (!node) return;
        while (node.children.length) {
            let child = node.children.shift();
            EffectSingleCase.instance.put(child);
        }
    }

    startControl() {
        // if (this.isImprisoned()) return;
        // this.clearEffect(this.effectNode);
        // this.clearEffect(this.effect2Node);
        // this.clearEffect(this.effect3Node);

        //走路时停止不再显示的技能.

        // this.skillLearned.stopSkills();
        // this.stopSkill();

        let rigidBody = this.node.getComponent(cc.RigidBody);
        if (rigidBody) {
            rigidBody.linearDamping = HERO_CONTROL_DAMPING;
        }
        // let collider = this.node.getComponent(cc.PhysicsCircleCollider);
        let collider = this.node.getComponent(cc.CircleCollider2D);
        if (collider && this.data) {
            // collider.radius = this.data.bodyRadius;
            collider.apply();
        }

        let collider1 = this.node.getComponent(cc.CircleCollider2D);
        if (collider1 && this.data) {
            // collider.radius = this.data.bodyRadius;
            collider1.apply();
        }
    }

    stopControl() {
        this.isChaseMove = false;
        let rigidBody = this.node.getComponent(cc.RigidBody);
        if (rigidBody) {
            rigidBody.linearDamping = HERO_NORMAL_DAMPING;
        }

        let collider = this.node.getComponent(cc.CircleCollider2D);
        if (collider && this.data) {
            collider.apply();
        }

        let collider1 = this.node.getComponent(cc.CircleCollider2D);
        if (collider1 && this.data) {
            collider1.apply();
        }

        this.followIndex = -1;
    }

    /** 在村庄的待机行为 */
    onIdleInVillage() {
        // 回血
        this.onUpdateHeal();
    }

    lateUpdate(dt) {
        super.lateUpdate(dt);
        if (this.chaseTarget && (!this.chaseTarget.isExist || this.chaseTarget.isDie())) {
            if (this.chaseTarget.isDie()) {
                this.chaseTarget = null;
            }
        }
    }

    /** 根据距离，刷新速度  */
    updateMoveSpeed(distance: number) {
        // let rate: number = 1;// (distance - Player_Init_data.TeamRange) / Player_Init_data.TeamRange
        // this.moveSpeed = RoleAttribute.getMoveSpeed(this) * (1 + rate);
    }

    setMoveSpeedDown(speed: number) {
        this.moveSpeed = speed;
    }

    collectResources(unit: any) {
        let enemyDis = this.getDistance(unit);
        if (enemyDis > Player_collect.AttackRange) {
            if (this.state == UnitState.idle) {// 移动到敌人附近
                this.moveToTarget(unit); // todo优化站立点
            }
            return;
        }
        //采集物
        this.collectTarget = unit;
        //停止移动.
        this.stopNavMove();
        //状态变为空闲状态
        this.state = UnitState.idle;
        // 在攻击范围内，攻击
        this.stopMove();

        // if (!this.collectSkill) {
        //     // this.collectSkill = new CollectSkill();
        //     // this.collectSkill.init(this);
        // }
        // if (!this.collectSkill.isCd()) {
        //     this.collectSkill.skillRelease(unit);
        // }
    }

    /**采集动作完成 */
    collectAttackFinish() {
        //采集物死亡.
        if (this.collectTarget && (!this.collectTarget.isValid || this.collectTarget.isDie())) {
            this.collectTarget = null;
        }
        this.chaseTarget = null;
        this.state = UnitState.idle;
    }

    onIdleLogic(dt: number): void {
        // if (this.canUseRebornSkill()) {
        //     var param = this.getSearchRebirthParam()
        //     let result: IUnit[] = RoleSelector.search(param);
        //     if (result == null) {
        //         return;
        //     }
        //     if (this.tryAttack(result[0], param)) {
        //         return;
        //     }
        // }
        // if (this.searchEnemy()) {
        //     return;
        // }
        // //持续采集物
        // let collectTarget = this.getCollectTarget();
        // if (collectTarget) {
        //     this.collectResources(collectTarget);
        //     return;
        // }

        // if (this.searchTree()) {
        //     return;
        // }
        // this.comeBackGuardPoint();

    }

    private getCollectTarget() {
        //优先攻击范围内的采集物
        if (this.collectTarget && this.collectTarget.node && !this.collectTarget.isDie()) {
            // if (!NpcManager.instance.isActiveFogNpc(this.collectTarget.data.fogArea)) { //采集物在迷雾内
            //     return null;
            // }
            let distance = MathUtils.getDistance(
                this.node.getPosition().toVec2(), 
                this.collectTarget.node.getPosition().toVec2()
            );
            //在采集范围内继续采集.
            if (distance < Player_collect.AttackRange) {
                return this.collectTarget;
            }
        }
        //采集物距离过远.清理采集物
        if (this.collectTarget) {
            this.collectTarget = null;
        }
        return null;
    }

    override removeSelf(): void {
        this.collectTarget = null;
        EventManagerComponent.instance().removeEventListener(
            GameEvent.ACTOR_BUFF_CHANGE, this.changeBuffChange, this)
        super.removeSelf();
    }

    // override getSearchEnemyParam(): SearchParam {
    //     let range: number = App.EnCampContrller.isOnCamp ? this._enCampSight : this.data.sensorRange;
    //     this.selfBattleRange = range;
    //     let searchParam = new SearchParam(this);
    //     searchParam.camp = [PlayerCamp.Enemy, PlayerCamp.Neutral];
    //     searchParam.rangeArgs = [SkillRangeWay.Circle, range];
    //     searchParam.origin = cc.v3(this.data.point.x, this.data.point.y);
    //     searchParam.unitType = [UnitType.Hero, UnitType.Monster];
    //     return searchParam;
    // }

    // //查找复活对象
    // override getSearchRebirthParam(): SearchParam {
    //     let searchParam = new SearchParam(this);
    //     searchParam.camp = [PlayerCamp.Own];
    //     searchParam.rangeArgs = [SkillRangeWay.Circle, this.data.sensorRange];
    //     searchParam.origin = cc.v3(this.data.point.x, this.data.point.y);
    //     searchParam.unitType = [UnitType.Hero];
    //     searchParam.condition = SearchCondition.death;
    //     return searchParam;
    // }

    /**
     * 搜索攻击范围内的树
     * @returns 
     */
    // protected getSearchTreeParam(): SearchParam {
    //     let range: number = App.EnCampContrller.isOnCamp ? this._enCampSight : this.data.battleRange;
    //     this.selfBattleRange = range;
    //     let searchParam = new SearchParam(this);
    //     searchParam.camp = [PlayerCamp.Neutral];
    //     searchParam.rangeArgs = [SkillRangeWay.Circle, range];
    //     searchParam.origin = cc.v3(this.data.point.x, this.data.point.y);
    //     searchParam.unitType = [UnitType.Tree];
    //     return searchParam;
    // }

    /**
     * 前往 靠近/追击 目标 ------待删除
     */
    moveToTarget(target: IUnit) {
        // 控制追击间隔2秒
        if (Date.now() - this.lastChaseTime > 1000) {
            this.isChaseMove = true;
            this.chaseTarget = target;
            this.lastChaseTime = Date.now(); // 当前时间
            let self: IUnit = this as unknown as IUnit;
            let distance = MapHelper.getNearestDistance(self, target);
            this.navTo(target.node.x, target.node.y, distance);
            this.state = UnitState.chase;
        }
    }



    /** 回到中心驻守点 */
    // override comeBackGuardPoint() {
    //     let isMainPlayer = HeroManager.instance.mainPlayer._data.unitId == this._data.unitId;
    //     if (!isMainPlayer && !this.followPoint) {
    //         return;
    //     }

    //     if (this.isChaseMove) {
    //         this.nearDest = false;
    //         this.isComeback = true;
    //         this.isChaseMove = false;
    //     }
    // }

    /**
     * 停止移动
     */
    private stopMoveTo() {
        this.nearDest = false;
        this.isComeback = false;
        this.isChaseMove = false;
        this.stopNavMove();
        if (this.state != UnitState.dead)
            this.state = UnitState.idle;
    }

    stopMove(): void {
        this.nearDest = false;
        this.isComeback = false;
        this.stopNavMove();
        if (this.state != UnitState.dead)
            this.state = UnitState.idle;
    }

    stopNavMove(): void {
        super.stopNavMove()
        this.setLinearVelocity(cc.Vec2.ZERO);
        this.updatePhysicsCircleColliderRadius(true);
    }
    userMoveStop(): void {
        this.bounceOut();
        this.moveToGuardPoint();

    }

    /* 弹开 */
    public bounceOut(): void {
        if (!this.rigidBody || !this.rigidBody.enabled) return;
        // let _bounceDirct: cc.Vec3 = new cc.Vec3(0, 0, 0);
        // HeroManager.instance.allPlayers.forEach(_player => {
        //     if (_player.data.idExcel != this.data.idExcel && cc.Vec2.distance(_player.node.position, this.node.position) < 50) { //英雄间的距离暂定100
        //         let _subDicrction: cc.Vec3 = this.node.position.sub(_player.node.position);
        //         _bounceDirct = _bounceDirct.add(_subDicrction);
        //     }
        // });
        // _bounceDirct.normalizeSelf();
        // let _speedDir: cc.Vec2 = new cc.Vec2(_bounceDirct.x, _bounceDirct.y);
        // this.rigidBody.linearVelocity = _speedDir.mul(800);
        // cc.tween(this.rigidBody).to(0.2, { linearVelocity: new cc.Vec2(0, 0) }).start();

    }

    /** 去警戒中心  */
    public moveToGuardPoint(): void {
        if (this.followPoint == null)
            return

        if (cc.Vec2.distance(this.followPoint, this.node.position) >= 600) {
            this.isComeback = true;
        }
        //this.isComeback = true;
    }


    /**
     * 返回目标点.
     * @param dest 
     * @param dt 
     * @returns 
     */
    protected checkCount: number = 0;
    public moveTo(dest: cc.Vec3, dt: number) {
        if (!dest) return;

        dest = dest.clone();
        this.updatePhysicsCircleColliderRadius(false);
        let point: cc.Vec3 = new cc.Vec3();
        this.node.getPosition(point);
        let distance: number = cc.Vec3.distance(point, dest);
        this.checkCount++;

        let centerPoint = CameraController.instance.getCenterPosition();
        let distance1: number = cc.Vec3.distance(point, centerPoint);
        if (distance1 > 600) { // 暂定1200距离
            this.changeSpeed(distance, dest);
            this.navTo(dest.x, dest.y);
            return;
        }

        //回走控制.
        if (this.isComeback && this.isNearBackPoint(dt, distance1)) {
            return;
        }

        //导航中先走一段时间,重新检测.
        if (this.isNavMove()) {
            this.navTime -= dt;
            if (this.navTime > 0) {
                return;
            } else {
                this.stopMove();
            }
        }

        // //变速
        this.changeSpeed(distance1, dest);
        let moveSpeed: number = this.getMoveSpeed();
        //补偿帧率. 卡的时候走路能够平滑
        let rate: number = 1; dt / AppConfig.frameTime;
        let self: IUnit = this as unknown as IUnit;
        //可直接通行过去.
        if (MapHelper.isPassable(self, dest)) {
            let dx = Math.floor(dest.x - this.node.x);
            let dy = Math.floor(dest.y - this.node.y);

            this.direction = MathUtils.getDirection(dy, dx);

            let dis = cc.Vec3.distance(new cc.Vec3(this.node.x, this.node.y), dest)
            if (dis <= this.MIN_SPACE) {
                this.set_state(UnitState.idle)
                // this.node.x = dest.x;
                // this.node.y = dest.y;
                this.setLinearVelocity(cc.Vec2.ZERO);
                return;
            }
            let moveDir: cc.Vec3 = dest.subtract(point).normalize();
            let linearVelocity = moveDir.multiplyScalar(moveSpeed * rate);
            this.setLinearVelocity(linearVelocity);
            this.set_state(UnitState.walk)
        } else {
            this.navTo(dest.x, dest.y);
            this.navTime = this.navMaxTime;
        }
    }

    /** 是否距离过近，不用回归  */
    private isNearBackPoint(dt: number, distance: number): boolean {
        // if (distance <= this.MIN_SPACE) {
        //     Logger.trace("20 stopMoveTo");
        //     this.stopMoveTo();
        //     return true;
        // }
        // if (this.nearDest) {
        //     this.nearTime -= dt;
        //     if (this.nearTime <= 0) {
        //         Logger.trace("nearTime = 0 stopMoveTo");
        //         this.stopMoveTo();
        //         return true;
        //     }
        // } else {
        //     if (distance <= HERO_NEAR_RANGE) {
        //         this.nearDest = true;
        //         let self: IUnit = this as unknown as IUnit;
        //         this.nearTime = (distance / RoleAttribute.getMoveSpeed(self)) + 0.2;
        //     }
        // }
        return false;
    }

    private getBodyEffectBack(): cc.Node {
        const TOWER_PORTAL_BACK = "TowerPortalBack";
        let node: cc.Node = this.body.getChildByName(TOWER_PORTAL_BACK);
        if (node) return node;
        node = new cc.Node(TOWER_PORTAL_BACK);
        this.body.addChild(node);
        return node;
    }

    private getBodyEffectFront(): cc.Node {
        const TOWER_PORTAL_FRONT = "TowerPortalFront";
        let node: cc.Node = this.body.getChildByName(TOWER_PORTAL_FRONT);
        if (node) return node;
        node = new cc.Node(TOWER_PORTAL_FRONT);
        this.body.addChild(node);
        return node;
    }

    private getBubbleNode(): cc.Node {
        let node: cc.Node = this.node.getChildByName("BubbleNode");
        if (node) return node;
        node = new cc.Node("BubbleNode");
        node.x = 0;
        node.y = 150;
        this.node.addChild(node);
        return node;
    }

    /** 通过回城传送 */
    public transferByTownPortal(x, y, callback: Function = null): void {
        const EFFECT_TIME = 0.1;
        const TARGET_OFFSET = 50;

        const HC_TELEPORT_PATH = "game/effect/bag/";
        const HC_TELEPORT_BACK = HC_TELEPORT_PATH + "Hc_teleport_back";
        const HC_TELEPORT_BACK_DI = HC_TELEPORT_PATH + "Hc_teleport_back_di";
        const HC_TELEPORT_DI = HC_TELEPORT_PATH + "Hc_teleport_di";
        const HC_TELEPORT_GO = HC_TELEPORT_PATH + "Hc_teleport_go";
        LoadUtils.loadPrefabArray([HC_TELEPORT_BACK, HC_TELEPORT_BACK_DI, HC_TELEPORT_DI, HC_TELEPORT_GO], () => {
            const prefabBack = ViewUtil.createPrefabNode(HC_TELEPORT_BACK);
            const prefabBackDi = ViewUtil.createPrefabNode(HC_TELEPORT_BACK_DI);
            const prefabDi = ViewUtil.createPrefabNode(HC_TELEPORT_DI);
            const prefabGo = ViewUtil.createPrefabNode(HC_TELEPORT_GO);

            const nodeBack = this.getBodyEffectBack();
            const nodeFront = this.getBodyEffectFront();
            prefabDi.parent = nodeBack;
            prefabGo.parent = nodeFront;

            const prefabGoAni = prefabGo.getComponent(cc.Animation);
            prefabGoAni.play("Hc_teleport_go_start");
            prefabGoAni.once(cc.Animation.EventType.FINISHED, () => {
                prefabGoAni.stop();
                prefabGoAni.play("Hc_teleport_go_loop");

                this.scheduleOnce(() => {
                    prefabGoAni.stop();
                    prefabGoAni.play("Hc_teleport_go_end");

                    //开始播放end动画0.43秒后执行
                //     this.scheduleOnce(() => {
                //         // this.skin.setSharedMaterial(this.context.otherMgr.whiteMaterial, 0);
                //         // material.setProperty("u_change", new cc.Vec4(20, 20, 20, 20));
                //         this.scheduleOnce(() => {
                //             cc.tween().
                //             cc.tween(this.skin.node).to(0.1, { opacity: 0 }, cc.easeIn(3.0)).start();
                //         }, 0.2);

                //         this.scheduleOnce(() => {
                //             self.skin.node.opacity = 255;
                //             self.skin.setMaterial(0, self.context.otherMgr.normalMaterial);
                //         }, 0.5);
                //     }, 0.43);

                //     prefabGoAni.once("finished", () => {
                //         prefabDi.active = false;
                //         self.skin.node.active = false;
                //         self.node.setPosition(
                //             cc.v3(
                //                 x + RandomUtils.random(- TARGET_OFFSET, TARGET_OFFSET), 
                //                 y + RandomUtils.random(- TARGET_OFFSET, TARGET_OFFSET),
                //             ));
                //         self.setFollow(cc.v3(x + RandomUtils.random(- TARGET_OFFSET, TARGET_OFFSET), y + RandomUtils.random(- TARGET_OFFSET, TARGET_OFFSET)));
                //         callback && callback();
                //         App.SoundManager.playEffect(SoundId.CS, SoundType.COMMON);
                //         self.scheduleOnce(() => {
                //             self.skin.node.active = true;
                //             prefabBack.parent = nodeFront;
                //             prefabBackDi.parent = nodeBack;

                //             const prefabBackAni = prefabBack.getComponent(cc.Animation);
                //             prefabBackAni.play("Hc_teleport_back");
                //             prefabBackAni.once("finished", () => {
                //                 ViewUtil.destroyNode(prefabBack);
                //                 ViewUtil.destroyNode(prefabBackDi);
                //                 ViewUtil.destroyNode(prefabDi);
                //                 ViewUtil.destroyNode(prefabGo);

                //                 nodeBack.removeFromParent(true);
                //                 nodeFront.removeFromParent(true);
                //                 App.hideMask();
                //             });
                //         }, 0.5);
                //     });
                 }, EFFECT_TIME);
            });
        });
    }

    /** 通过回城跨服传送 */
    public crossTransferByTownPortal(): void {
        const EFFECT_TIME = 0.1;

        const HC_TELEPORT_PATH = "game/effect/bag/";
        const HC_TELEPORT_DI = HC_TELEPORT_PATH + "Hc_teleport_di";
        const HC_TELEPORT_GO = HC_TELEPORT_PATH + "Hc_teleport_go";
        let self = this;
        LoadUtils.loadPrefabArray([HC_TELEPORT_DI, HC_TELEPORT_GO], () => {
            const prefabDi = ViewUtil.createPrefabNode(HC_TELEPORT_DI);
            const prefabGo = ViewUtil.createPrefabNode(HC_TELEPORT_GO);

            const nodeBack = self.getBodyEffectBack();
            const nodeFront = self.getBodyEffectFront();
            prefabDi.parent = nodeBack;
            prefabGo.parent = nodeFront;

            const prefabGoAni = prefabGo.getComponent(cc.Animation);
            prefabGoAni.play("Hc_teleport_go_start");
            prefabGoAni.once(cc.Animation.EventType.FINISHED, () => {
                prefabGoAni.stop();
                prefabGoAni.play("Hc_teleport_go_loop");

                self.scheduleOnce(() => {
                    prefabGoAni.stop();
                    prefabGoAni.play("Hc_teleport_go_end");

                    prefabGoAni.once(cc.Animation.EventType.FINISHED, () => {
                        prefabDi.active = false;
                        self.skin.node.active = false;
                        SoundManager.instance().playEffect(SoundId.CS, SoundType.COMMON);
                        // TODO
                        // App.hideMask();
                        self.scheduleOnce(() => {
                            self.skin.node.active = true;
                            ViewUtil.destroyNode(prefabDi);
                            ViewUtil.destroyNode(prefabGo);

                            nodeBack.removeFromParent();
                            nodeFront.removeFromParent();
                            nodeBack.destroy();
                            nodeFront.destroy();
                        }, 0.5);
                    });
                }, EFFECT_TIME);
            });
        });
    }

    /** 播放传送结束效果  */
    public playTranferEndEffect(callback: Function = null): void {
        const HC_TELEPORT_PATH = "game/effect/bag/";
        const HC_TELEPORT_BACK = HC_TELEPORT_PATH + "Hc_teleport_back";
        const HC_TELEPORT_BACK_DI = HC_TELEPORT_PATH + "Hc_teleport_back_di";

        const nodeBack = this.getBodyEffectBack();
        const nodeFront = this.getBodyEffectFront();
        let self = this;
        LoadUtils.loadPrefabArray([HC_TELEPORT_BACK, HC_TELEPORT_BACK_DI], () => {
            const prefabBack = ViewUtil.createPrefabNode(HC_TELEPORT_BACK);
            const prefabBackDi = ViewUtil.createPrefabNode(HC_TELEPORT_BACK_DI);

            self.skin.node.active = false;

            self.scheduleOnce(() => {
                self.skin.node.active = true;
                prefabBack.parent = nodeFront;
                prefabBackDi.parent = nodeBack;

                const prefabBackAni = prefabBack.getComponent(cc.Animation);
                prefabBackAni.play("Hc_teleport_back");
                prefabBackAni.once(cc.Animation.EventType.FINISHED, () => {
                    ViewUtil.destroyNode(prefabBack);
                    ViewUtil.destroyNode(prefabBackDi);
                    // TODO
                    // App.hideMask();
                    CameraController.instance.setZoomToNormal();
                    // App.SoundManager.playEffect(SoundId.CS , SoundType.COMMON);
                    if (callback != null) {
                        callback();
                    }
                });
            }, 0.5)
        }
        );
    }

    public hideSkin(): void {
        // this.skin.node.active = false;
    }

    /**根据类型触发气泡 */
    public triggerQiPao(qpType: QiPaoType, dalayTime: number) {
        // if ((qpType == QiPaoType.ChangeScene || qpType == QiPaoType.EnterSafeZone || qpType == QiPaoType.ExitSafeZone) && !App.UserController.isUnlockVillage()) return;
        // if (PlayerActor.qipaoCfgMap == null) {
        //     PlayerActor.qipaoCfgMap = new Map<number, CFG_Bubbling[]>();

        //     let cfgList = BubblingCache.Instance.all()
        //     for (var cfg of cfgList) {
        //         let type = cfg.getObjectType();
        //         if (!Player.qipaoCfgMap[type]) Player.qipaoCfgMap[type] = [];
        //         Player.qipaoCfgMap[type].push(cfg)
        //     }
        // }

        // if (Player.qipaoCfgMap[qpType] && Player.qipaoCfgMap[qpType].length > 0) {
        //     let len = Player.qipaoCfgMap[qpType].length;
        //     let index = RandomUtils.randomInt(0, len - 1);
        //     let qipaoCfg: CFG_Bubbling = Player.qipaoCfgMap[qpType][index];
        //     this.showDialog(App.getLan(qipaoCfg.getDialogueLang()), 2000, dalayTime);
        // }
    }
    /** 显示对白 
     * @param dialog 对白
     * @param durationTime 持续时间 ms
      */
    public showDialog(dialog: string, durationTime: number = 1000, dalayTime: number = 0) {
        this.scheduleOnce(() => {
            let _bubbleUrl: string = "ui/duplicate/playerBubble";
            LoadUtils.load(_bubbleUrl, cc.Prefab, () => {
                let _bubble: cc.Node = ViewUtil.createPrefabNode(_bubbleUrl);
                _bubble.parent = this.getBubbleNode();
                const _lblTxt: cc.Node = UIUtils.find("lblTxt", _bubble);
                let typewriting: Typewriting = _lblTxt.addComponent(Typewriting);
                typewriting.text = dialog;
                // _lblTxt.getComponent(cc.RichText).string = dialog;
                // _lblTxt.height = 82;
                this.scheduleOnce(() => {
                    _bubble.removeFromParent();
                    _bubble.destroy();
                    ViewUtil.destroyNode(_bubble);
                }, durationTime)
            });
        }, dalayTime);
    }

    /** 被Boss技能攻击显示对白 
     * @param dialog 对白
     * @param durationTime 持续时间 ms
      */
    public showBossSkillAttDialog(dialog: string, durationTime: number, delayTime: number) {
        this.scheduleOnce(() => {
            let _bubbleUrl: string = "ui/duplicate/playerBattreBubble";
            LoadUtils.load(_bubbleUrl, cc.Prefab, () => {
                let _bubble: cc.Node = ViewUtil.createPrefabNode(_bubbleUrl);
                _bubble.parent = this.getBubbleNode();
                const imgBubble: cc.Node = UIUtils.find("imgBubble", _bubble);
                let anim = imgBubble.getComponent(cc.Animation);
                anim.play("shake_ruo");
                // typewriting.text = dialog;
                // _lblTxt.height = 36;
                this.scheduleOnce(() => {
                    _bubble.removeFromParent();
                    _bubble.destroy();
                    ViewUtil.destroyNode(_bubble);
                }, durationTime);
            });
        }, delayTime);
    }

    /** 上次受击时间  */
    private _lastBetAttackTime: number = 0

    /** 受击  */
    public override onBeAttack(): void {
        super.onBeAttack();
        this._lastBetAttackTime = AppManager.instance().TimeManager.localTime;
    }

    /** 是否在被打 如果当前时间 - 上次被打时间少于s，则认为在被打  */
    public isBeAttackIng(): boolean {
        if (this._lastBetAttackTime == 0) {
            return false;
        }
        if (AppManager.instance().TimeManager.localTime - this._lastBetAttackTime <= 1) {
            return true;
        }
        return false;
    }

    /** 通过传送阵传送 */
    public transferByTeleport(x, y): void { }
}
