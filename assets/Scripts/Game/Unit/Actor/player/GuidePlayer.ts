import * as cc from 'cc';

import CFG_Constant from "../../../../Config/GameCfgExt/BattleConst";
import { NavMove } from "../../NavAgent/NavMove";
import { MathUtils } from "../../../Utils/MathUtils";
import { UnitState } from "../../UnitState";
import { IUnit } from "../../Base/IUnit";
import { HERO_CONTROL_DAMPING, HERO_NORMAL_DAMPING, Player_Init_data } from "../PlayerType";
import Player from "./PlayerActor";
import { MapHelper } from '../../../Module/MapInstance/Base/MapHelper';
import MapManager from '../../../Module/MapInstance/Managers/MapManager';
import CameraController from '../../../Module/Camera/CameraManager';

const { ccclass, property } = cc._decorator;

@ccclass
export default class GuidePlayer extends Player {
    strokeColor: cc.Color = new cc.Color(0xff, 0, 0, 255);

    onLoad() {
        this.MIN_SPACE = Number(CFG_Constant.getValue("min_attack_range"))
        let rigidBody = this.node.addComponent(cc.RigidBody2D);
        if (rigidBody) {
            rigidBody.bullet = true;
            rigidBody.allowSleep = false;
            rigidBody.gravityScale = 0;
            rigidBody.type = cc.ERigidBody2DType.Dynamic;
            rigidBody.enabledContactListener = true;
            rigidBody.linearDamping = HERO_NORMAL_DAMPING;
            rigidBody.linearDamping = 0;
        }

        let physicsCircleCollider = this.node.addComponent(cc.CircleCollider2D);
        this.physicsCircleCollider = physicsCircleCollider;
        if (physicsCircleCollider) {
            physicsCircleCollider.density = 0;
            physicsCircleCollider.friction = 0;
            physicsCircleCollider.restitution = 0;
            physicsCircleCollider.sensor = true;
            physicsCircleCollider.radius = 5
            physicsCircleCollider.apply();
        }
        this.node.getComponent(cc.UITransform).setContentSize(
            1,1
        );
    }

    public setPos(pos) {
        this.node.x = pos.x;
        this.node.y = pos.y;
    }
    updatePhysicsCircleColliderRadius(isStop: boolean = true) {
    }
    drawShape() {
        let physicsCircleCollider = this.node.getComponent(cc.CircleCollider2D);
        let range: number = physicsCircleCollider.radius;
        this.graphics.circle(0, 0, range);
    }

    start() {

    }
    /** 设置跟随 */
    public setFollow(point: cc.Vec3) {
        if (point == null) {
            this.isFollow = false;
            this.setLinearVelocity(cc.Vec2.ZERO);
            this.set_state(UnitState.idle)
            this.followPoint = null;
            return;
        }
        this.isFollow = true;
        this.followPoint = point.clone();
    }

    public getFollowPoint(): cc.Vec3 {
        return this.followPoint
    }

    update(dt) {
        this.draw();
        if (MapManager.curMap.isPause()) return;

        //休眠中不执行任何逻辑
        if (this.sleep) {
            this.setFollow(null)
            return;
        }
        if (this._navMove) {
            this._navMove.update(dt);
        }

        let out = cc.Vec3.ZERO;
        out = this.getPosition(out)
        CameraController.instance.setViewToPoint(out.x, out.y, false);
        //移动更新
        if (this.isFollow) {
            this.moveTo(this.followPoint, dt);
            this.idleTime = 0;
            return;
        }

        if (this._state != UnitState.idle) {
            this.idleTime = 0;
        }

        switch (this._state) {
            case UnitState.idle:
                break;
            case UnitState.walk:
                break;
            case UnitState.chase:
                break;
            case UnitState.attack:
                // 攻击中
                break;
            case UnitState.collect:
                //采集中
                break;
            case UnitState.comeback:
                // 回归中
                break;
            case UnitState.dead:
                break;
        }
    }

    navTo(destX: number, destY: number, space: number = 0) {
        if (!this._navMove) {
            this._navMove = new NavMove(this);
        }
        let radius: number = 5;
        let speed: number = this.getMoveSpeed();
        this.roadNodeArr = this._navMove.navTo(this.node, speed, destX, destY, radius, space);
    }

    lateUpdate(dt) {

    }

    public set_state(value: UnitState) {
    }

    protected cancelCollision() {
    }

    /**
     * 开启物理碰撞
     */
    public openCollision() {

    }

    protected changeSpeed(distance: number, dest: cc.Vec3) {
    }

    startControl() {
        let rigidBody = this.node.getComponent(cc.RigidBody);
        if (rigidBody) {
            rigidBody.linearDamping = HERO_CONTROL_DAMPING;
        }
    }

    stopControl() {
        let rigidBody = this.node.getComponent(cc.RigidBody);
        if (rigidBody) {
            rigidBody.linearDamping = HERO_NORMAL_DAMPING;
        }
    }

    public moveTo(dest: cc.Vec3, dt: number) {
        if (!dest) return;
        dest = dest.clone();
        let point: cc.Vec3 = new cc.Vec3();
        this.node.getPosition(point);
        this.checkCount++;
        //导航中先走一段时间,重新检测.
        if (this.isNavMove()) {
            this.navTime -= dt;
            if (this.navTime > 0) {
                return;
            } else {
                this.stopMove();
            }
        }
        let moveSpeed: number = this.getMoveSpeed();
        // //补偿帧率. 卡的时候走路能够平滑
        let rate: number = 1;
        let self: IUnit = this as unknown as IUnit;
        //可直接通行过去.
        if (MapHelper.isPassablePoint(new cc.Vec3(self.node.x, self.node.y), dest)) {
            let dx = dest.x - this.node.x;
            let dy = dest.y - this.node.y;
            this.direction = MathUtils.getDirection(dy, dx);
            let dis = cc.Vec3.distance(new cc.Vec3(this.node.x, this.node.y), dest)
            if (dis <= this.MIN_SPACE / 5) {
                this.state = UnitState.idle;
                this.setLinearVelocity(cc.Vec2.ZERO);
                return;
            }
            let moveDir: cc.Vec3 = dest.subtract(point).normalize();
            let linearVelocity = moveDir.multiplyScalar(moveSpeed * rate);
            this.setLinearVelocity(linearVelocity);
            this.set_state(UnitState.walk)
        } else {
            this.navTo(dest.x, dest.y);
            this.navTime = this.navMaxTime;
        }
    }

    getMoveSpeed() {
        return Player_Init_data.MoveSpeed
    }

}