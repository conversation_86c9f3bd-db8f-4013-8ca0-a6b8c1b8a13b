import * as cc from "cc";

import { ViewUtil } from "../../Utils/ViewUtils";
import { VoEntity } from "../VoEntity";

export class Entity extends cc.Component {

    protected _data: VoEntity;

    /**
     * 数据源
     */
    set data(value: VoEntity) {
        this._data = value;
    }

    get data(): VoEntity {
        return this._data;
    }

    /**
     * 删除自己
     */
    removeSelf() {
        ViewUtil.destroyNode(this.node);
    }

}
