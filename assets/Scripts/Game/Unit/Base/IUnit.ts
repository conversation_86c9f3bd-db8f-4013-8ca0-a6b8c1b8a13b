import * as cc from "cc";
import { UnitType } from "../../../Config/GameDefine";
import  { CFG_SkillLevel } from "../../../Config/GameCfg/CFGManager";
// import { BattBuff } from "../../../Config/Struct/BattBuff";
// import { FightContext } from "../../modules/fight/FightContext";
// import { FloatingTextType } from "../../modules/fight/FloatingText";
// import { SkillDamageResult } from "../../modules/fight/SkillDamageResult";
// import { Buff } from "../../modules/fight/buff/Buff";
// import { BuffCureResult } from "../../modules/fight/buff/BuffCureResult";
// import { BuffDamageResult } from "../../modules/fight/buff/BuffDamageResult";
// import { UnitStatus } from "../../modules/fight/buff/UnitStatus";
// import { SkillLearned } from "../../modules/fight/skill/SkillLearned";
// import { VoSkill } from "../../skill/VoSkill";
import { UnitState } from "../UnitState";
import { VoUnit } from "../VoUnit";
import { QiPaoType } from "../UnitConst";

/**
 * 是否实现了IUnit.
 * @param target 
 * @returns 
 */
export function isUnit(target: any): boolean {
    return target && target.discriminator === "Unit";
}


/**
 * 单位基类.
 * 
 * <li>重构第一步。使用接口.
 */
export interface IUnit {
    /** 鉴别器 */
    discriminator: "Unit";

    unitType: UnitType,

    // skillLearned: SkillLearned;

    // context: FightContext;

    // data: VoUnit;
    bodyRadius: number

    direction: number;

    state: UnitState;

    alpha: number;

    scale: number;
    body: cc.Node;

    position: cc.Vec3;

    node: cc.Node;
    chaseMove;

    isValidUnit(): boolean;
    setFollow(point: cc.Vec3);
    triggerQiPao(qpType: QiPaoType, dalayTime: number);
    isValidUnit(): boolean;

    // checkHealSkillUse(skillVo: VoSkill): boolean;
    // checRebornSkillUse(skillVo: VoSkill): boolean;

    getPosition(out?: cc.Vec3): cc.Vec3;

    getBaseAttr(type: number, defaultValue?: number): number;

    getAttrValue(type: number): number;

    getHeight(): number;

    getBodyHeight(): number;

    lookAtTarget(target: IUnit | cc.Node | cc.Vec3);

    playAttackAnim(name: string);

    // canChangeStatusTo(newStatus: UnitStatus): boolean;
    // changeStatusTo(newStatus: UnitStatus): void;
    // removeStatus(oldStatus: UnitStatus);
    // beAttack(damageResult: SkillDamageResult);

    // isImprisoned();
    // onBeAttack();
    // onDamage(value: number);

    // addBuffs(caster: IUnit, battBuffs: BattBuff[], skillCfg: CFG_SkillLevel);
    // addBuffsEx(caster: IUnit, buffId: number, skillCfg: CFG_SkillLevel): Buff;

    // takeBuffDamage(damageResult: BuffDamageResult);
    // takeBuffCure(cureResult: BuffCureResult): void;

    stopMove();
    // stopSkill();

    // beHitEffect();
    // onDead();

    // onDeadVanish();

    // getHp(): number;

    // getHpMax(): number;

    // refreshHp();

    // addHp(addValue: number);

    // reborn();

    // isDie(): boolean;

    // finishNormalAttack();

    // getFireNode(): cc.Node;

    // getWorldFirePosition(out?: cc.Vec3, index?: number): cc.Vec3;
    // getWorldAimingPoint(out?: cc.Vec3): cc.Vec3;

    // getEffectPosition(out?: cc.Vec3): cc.Vec3;

    // getEffectNode(type?: number): cc.Node;

    // getEffect1Node(): cc.Node;

    // getEffect2Node(): cc.Node;

    // getEffect3Node(): cc.Node;

    // getHitNode(): cc.Node;

    // getWorldHitPosition(out?: cc.Vec3): cc.Vec3;

    // getWorldPosition(node?: cc.Node, out?: cc.Vec3, rotate?: boolean): cc.Vec3;

    // // fly(type: FloatingTextType, value: number);

    removeSelf();

    // getComponent(value: any): cc.Component;

    // addComponent<T extends cc.Component>(type: new () => T): T;
    // getAttr(type: number): number;
    // getFogArea(): number;//所在迷雾，如果是树，就是0
}