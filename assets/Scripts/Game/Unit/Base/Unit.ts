import * as cc from "cc";

import { BattleAttr, UnitAnim, UnitType } from "../../../Config/GameDefine";
import { GameEvent } from "../../../GameEvent/Events";
import CFG_ModelHeight from "../../../Config/GameCfg/CFG_ModelHeight";
// import { CFG_SkillLevel } from "../../../Config/GameCfgExt/SkillLevelCache";
// import { BattBuff } from "../../../Config/Struct/BattBuff";
import { LoadUtils } from "../../Utils/LoadUtils";
import MapManager from "../../Module/MapInstance/Managers/MapManager";
import { Bundles } from "../../Module/Bundles";
// import { BattleType } from "../../Modules/Challenge/Model/BattleType";
// import { FightContext } from "../../Modules/Fight/FightContext";
// import { FloatingText, FloatingTextType } from "../../Modules/Fight/FloatingText";
// import { SkillDamageResult } from "../../modules/fight/SkillDamageResult";
// import { AttackType, SkillDamageType, SkillEffectNodeType, SkillTargetType, SkillType } from "../../modules/fight/SkillType";
// import { Buff } from "../../modules/fight/buff/Buff";
// import { BuffCureResult } from "../../modules/fight/buff/BuffCureResult";
// import { BuffDamageResult } from "../../modules/fight/buff/BuffDamageResult";
// import { UnitStatus } from "../../modules/fight/buff/UnitStatus";
// import { RoleSelector, SearchParam } from "../../modules/fight/common/RoleSelector";
// import { DetectingEllipse } from "../../modules/fight/common/bll/DetectionEllipse";
// import { Skill } from "../../modules/fight/skill/Skill";
// import { SkillLearned } from "../../modules/fight/skill/SkillLearned";
// import SkillManager from "../../modules/fight/skill/SkillManager";
// import { RoleSkill } from "../../modules/fight/skill/bll/RoleSkill";
// import { SkillState } from "../../modules/fight/skill/model/SkillState";
import HpBar from "../../Module/Map/HpBar";
import MBossName from "../../Module/Map/MBossName";
import PathFindingAgent from "../../Module/Map/road/PathFindingAgent";
import RoadNode from "../../Module/Map/road/RoadNode";
// import { VoSkill } from "../../Module/Skill/VoSkill";
import { UnitState } from "../UnitState";
import { VoEntity } from "../VoEntity";
// import { VoMonster } from "../VoMonster";
import { VoUnit } from "../VoUnit";
// import { MonsterType } from "../actor/PlayerType";
import { QiPaoType } from "../Actor/player/PlayerActor";
import Shadow from "../Shadow";
import { Entity } from "./Entity";
import { IUnit } from "./IUnit";
import { AppConfig } from "../../../Config/GameConfig";
import { Env } from "./Env";

const { ccclass, property } = cc._decorator;

/**
 * 起个别名
 */
export type Role = Unit;

/**
 * 单位基类。
 * 单位就是存在场景里的各种个体对象， 所有场景里的单位（角色，建筑，植物等对象）都继承它。
 */
@ccclass
export default class Unit extends Entity implements IUnit {

    debugTxt: cc.Label = null;

    debugNode: cc.Node = null;
    /** 类型鉴别器 */
    readonly discriminator: "Unit" = "Unit";

    public unitType: UnitType = UnitType.None;

    /** 当前学会的技能 */
    // public skillLearned: SkillLearned = new SkillLearned(this);

    // public unitStatus: UnitStatus[] = [];

    /**战斗环境 处理战斗相关 */
    protected _env: Env;

    /** 飘子组件 */
    // protected fightFlyer: FloatingText;

    public currentClipName: string = "";

    /** 发射坐标 */
    protected fireNodes: cc.Node[] = [];
    /**子弹的瞄准点 */
    protected aimingPoint: cc.Node = null;

    /** 人物前1 特效位置 */
    protected effectNode: cc.Node = null;
    /** 人物后1 特效位置 */
    protected effect2Node: cc.Node = null;
    /** 人物身上3 特效位置  (跟随人物转向)*/
    protected effect3Node: cc.Node = null;
    /** Buff挂点 */
    protected buffNode: cc.Node = null;

    /** 受击位置偏移点 */
    protected beAttackNode: cc.Node = null;

    /** 血条 */
    protected hpBar: HpBar = null;

    /** 影子 */
    protected shadowNode: cc.Node = null;

    /** 名字 */
    protected nameNode: MBossName = null;

    /** 倒计时 */
    protected countDownNode: cc.Node = null;

    /** 物理碰撞圈 */
    protected physicsCircleCollider: cc.CircleCollider2D = null;

    /** 上次追击时间 */
    protected lastChaseTime: number = 0;

    /** 追击的目标 */
    protected chaseTarget: any = null;

    /** 透明度 */
    private _alpha: number = 1;

    /** 单位状态 */
    protected _state: UnitState = UnitState.none;

    /** 方向 */
    protected _direction: number = 0;

    protected _scale: number = 1;

    /** Skin上的动画 */
    @property(cc.Animation)
    public anim: cc.Animation = null;

    /** Skin对象 */
    @property(cc.Sprite)
    public skin: cc.Sprite = null;

    /** 显示在Skin后面的动画 */
    @property(cc.Animation)
    public anim_back: cc.Animation = null;

    /** 显示在Skin前面的动画 */
    @property(cc.Animation)
    public anim_front: cc.Animation = null;

    @property(cc.Node)
    public body: cc.Node = null;

    /** 移动速度 */
    @property(cc.CCInteger)
    public moveSpeed: number = 200;

    /** 当前使用的技能 */
    // @property()
    // public curSKillSkill: Skill = null;

    /** 自己的感知范围  */
    protected selfBattleRange: number = 0;

    private _bodyRadius: number = 0;

    public setEnv(env: Env) {
        this._env = env;
    }

    public get bodyRadius(): number {
        if (this._bodyRadius == 0) {
            this._bodyRadius = CFG_ModelHeight.get(this.data.identity).bodyRadius
        }
        return this._bodyRadius;
    }

    public set chaseMove(value: boolean) {

    }

    triggerQiPao(qpType: QiPaoType, dalayTime) {

    }
    /**战斗环境 处理战斗相关 */
    // public get context(): FightContext {
    //     return this._context;
    // }

    /** 数据源 */
    override get data(): VoUnit {
        return this._data as VoUnit;
    }

    public set data(value: VoEntity) {
        this._data = value;
    }

    /** 获取基础属性 */
    public getBaseAttr(type: number, defaultValue: number = 0): number {
        return (this.data && this.data.getAttr(type)) || defaultValue;
    }

    /**设置基础属性 */
    public setBaseAttr(type: number, value: number) {
        this.data.setAttr(type, value);
    }

    /** 获取当前身上Buff附加的属性值 */
    // public getBuffAttr(type: number): number {
    //     return this.skillLearned.getBuffAttrByType(type);
    // }

    /** 获取最终的属性 */
    public getAttr(type: number): number {
        // return this.getBaseAttr(type) + this.getBuffAttr(type);
        return 0;
    }

    // public getMoveSpeed(): number {
    //     return this.getAttr()
    // }

    /**
     * 获取最终属性
     * @param type
     * @returns
     */
    public getAttrValue(type: number): number {
        return this.getBaseAttr(type);
    }

    /** Body高  */
    getBodyHeight() {
        if (this.skin) {
            const uitransform = this.skin.node.getComponent(cc.UITransform);
            return uitransform.contentSize.height * this.skin.node.scale.y;
        }
        return 50;
    }

    /**
     * 设置单位方向
     * 方向值范围为 0-7，方向值设定如下，0是下，1是左下，2是左，3是左上，4是上，5是右上，6是右，7是右下
     *        4
     *      3   5
     *    2   *   6
     *      1   7
     *        0
     */
    public get direction(): number {
        return this._direction;
    }

    public set direction(value: number) {
        if (this.direction != value) {
            this.set_direction(value);
        }
    }

    get position(): cc.Vec3 {
        if (this.node && this.node.isValid) {
            return this.node.position;
        }
        return cc.v3(0, 0, 0);
    }

    isValidUnit(): boolean {
        return this.node && this.node.isValid;
    }

    /**
     * 设置单位方向
     */
    protected set_direction(value: number) {
        this._direction = value;
        this.changeDirection();
    }


    /** 当前单位状态   不要重写此方法. 请重写get_state*/
    public get state(): UnitState {
        return this.get_state();
    }

    protected get_state(): UnitState {
        return this._state;
    }

    /** 单位状态    不要重写此方法.请重写 set_state */
    public set state(value: UnitState) {
        if (this._state != value) {
            this._state = value;
            this.set_state(value);
        }
    }

    protected set_state(value: UnitState) {
        this._state = value;
    }

    /**
     * 单位透明度 0-1
     */
    public get alpha(): number {
        return this._alpha;
    }

    public set alpha(value: number) {
        if (this._alpha != value) {
            this._alpha = value;
            this.node.getComponent(cc.UIOpacity).opacity = Math.floor(255 * (value / 1));
        }
    }

    /**
     * 缩放
     */
    public set scale(value: number) {
        if (this._scale != value) {
            this._scale = value;
            this.changeDirection();
            this.body && (this.body.setScale(this.scale,  this.scale));
        }
    }

    public get scale(): number {
        return this._scale;
    }

    private changeDirection() {
        if (this.direction > 4) {
            if (this.body) {
                this.body.setScale(-1 * this.scale, this.scale);
            }
        } else if (this.direction > 0 && this.direction < 8) {
            if (this.body) {
                this.body.setScale(this.scale, this.scale);
            }
        }
    }

    /**
     * 单位当前所站在的路点
     */
    public get roadNode(): RoadNode {
        return PathFindingAgent.instance.getRoadNodeByPixel(this.node.x, this.node.y);
    }

    onLoad() {
        if (AppConfig.isDebug) {
            this.debugNode = new cc.Node("DebugNode");
            this.debugNode.parent = this.node;
            this.debugTxt = this.debugNode.addComponent(cc.Label);
            this.debugTxt.string = "Debug Info";
            this.debugTxt.fontSize = 30;
            this.debugTxt.color = cc.Color.RED;
            this.debugTxt.node.y = 50;
        }

        // 初始子弹发射点
        this.fireNodes.push(this.body.getChildByName("fire"));
        this.fireNodes.push(this.body.getChildByName("fire1"));
        this.fireNodes.push(this.body.getChildByName("fire2"));
        this.effectNode = this.node.getChildByName("effect");
        this.effect2Node = this.node.getChildByName("effect2");
        this.effect3Node = this.body.getChildByName("effect3");
        this.beAttackNode = this.node.getChildByName("beAttack");
    }

    protected createNode(x: number = 0, y: number = 0, name?: string, parent?: cc.Node): cc.Node {
        if (!name) name = "node";
        if (!parent) parent = this.body;
        let node = new cc.Node();
        node.addComponent(cc.UITransform).setAnchorPoint(0.5, 0.5);
        node.parent = parent;
        node.x = x;
        node.y = y;
        node.name = name;
        return node;
    }

    /**
     * 是否包含动画帧
     * @param clipName
     * @param anim
     * @returns
     */
    protected hasAnimClip(clipName: string, anim: cc.Animation): boolean {
        let arr: cc.AnimationClip[] = anim.clips
        for (let i = 0; i < arr.length; i++) {
            if (arr[i] && arr[i].name == clipName) {
                return true;
            }
        }
        return false;
    }

    /** 播放动画 */
    protected play(name: string, startTime: number = 0) {
        this.currentClipName = name;
        let animState: cc.AnimationState;
        if (this.anim) {
            this.anim.getState(name).setTime(startTime);
            this.anim.play(name);
        }
        if (this.anim_back) {
            let clipName = name + "_back";
            if (this.hasAnimClip(clipName, this.anim_back)) {
                this.anim_back.getState(clipName).setTime(startTime);
                this.anim_back.play(clipName);
            } else {
                this.anim_back.getState(clipName).setTime(999);
            }
        }
        if (this.anim_front) {
            let clipName = name + "_front";
            if (this.hasAnimClip(clipName, this.anim_front)) {
                this.anim_front.getState(clipName).setTime(startTime);
                this.anim_front.play(clipName);
            } else {
                this.anim_front.getState(clipName).setTime(999);
            }
        }
        let animTime = 0.5;
        if (animState) {
            animTime = animState.duration / animState.speed;
        }
        return animTime;
    }

    start() {
        this.state = UnitState.idle; //默认待机状态
    }

    update(dt) {
        //调试信息
        // if (App.isDebug) {
        //     let distance = this.context.getPlayerDistance(this);
        //     let str: string = "ID:" + this.data.unitId + "\n";
        //     str += "HD:" + Math.floor(distance) + "\n";
        //     distance = this.context.getCenterDistance(this);
        //     str += "HC:" + Math.floor(distance) + "\n";
        //     str += "AP:" + this.data.getAttr(BattleAttr.atk) + "\n";
        //     str += "HP:" + this.data.getHp() + "/" + this.data.getHpMax() + "\n";
        //     str += Math.floor(this.node.x) + "#" + Math.floor(this.node.y);
        //     this.debugTxt.string = str;
        // }

        // this.resetcurSkill()
    }

    lastUpdateCd: number = 0;
    sumDt = 0;//考虑到可能由于卡帧等，可能相应时间超过300毫秒，这里做一个累加，传入真实的两次相应的时间差
    /**
     * 待机状态的逻辑
     * */
    protected onIdleLogic(dt: number) {
        //这里优化下怪物的待机的响应时间为300毫秒响应一次
        let nowTime = Date.now();
        if (nowTime - this.lastUpdateCd < 300) {
            this.sumDt += dt;
            return;
        }
        else {
            dt = this.sumDt;
            this.lastUpdateCd = nowTime;
            this.sumDt = 0;
        }

        if (this.searchEnemy()) {
            return;
        }
        this.comeBackGuardPoint();
    }

    /**
     * 搜索到可攻击的敌人 返回true, 准备攻击敌人
     * @returns 
     */
    protected searchEnemy(): boolean {
        // let param: SearchParam = this.getSearchEnemyParam();
        // if (param == null) {
        //     return false;
        // }

        // let self: IUnit = this as unknown as IUnit;
        // let result: IUnit[] = RoleSelector.search(param);
        // if (result == null) {
        //     return false;
        // }

        // let target = result[0];
        // if (this.unitType == UnitType.Monster && target != null) {
        //     console.info(">找到目标");
        // }
        // if (this.tryAttack(target, param)) {
        //     return true;
        // }
        return false;
    }

    public hasEnemy(): boolean {
        // let param: SearchParam = this.getSearchEnemyParam();
        // if (param == null) {
        //     return false;
        // }

        // let self: IUnit = this as unknown as IUnit;
        // let result: IUnit[] = RoleSelector.search(param);
        // if (result == null || result.length == 0) {
        //     return false;
        // }
        return true;
    }
    /**
     * 是否在范围内
     * @param target 
     * @returns 
     */
    protected isEllipseRange(target: IUnit): boolean {
        if (target == null) {
            return false;
        }
        // return DetectingEllipse.isEllipseRange(this.data.rangeAxisRate, target.position, this.selfBattleRange, this.data.point);
        return false;
    }


    protected tryRebirthRecoverSkill(): boolean {
        return false;
    }

    // public checkHealSkillUse(skillVo: VoSkill): boolean {
    //     if (skillVo.cfgSkill.getDmgType() == SkillDamageType.Heal) {
    //         let self: IUnit = this as unknown as IUnit;
    //         let searchParam: SearchParam;
    //         let result: IUnit[] = RoleSelector.nearestBySkill(self, skillVo.cfgSkill, searchParam);
    //         if (!result) return false;
    //         for (var player of result) {
    //             if (player instanceof Unit && player.getHp() <= player.getHpMax()) {
    //                 return true;
    //             }
    //         }
    //         return false;
    //     }
    //     return true;
    // }

    // public checRebornSkillUse(skillVo: VoSkill): boolean {
    //     if (skillVo.cfgSkill.getDmgType() == SkillDamageType.RebirthAndHeal) {
    //         let self: IUnit = this as unknown as IUnit;
    //         let searchParam: SearchParam;
    //         let result: IUnit[] = RoleSelector.nearestBySkill(self, skillVo.cfgSkill, searchParam);
    //         if (!result) return false;
    //         for (var player of result) {
    //             if (player instanceof Unit && player.isDie()) {
    //                 return true;
    //             }
    //         }
    //         return false;
    //     }
    //     return true;
    // }

    // public canUseRebornSkill() {
    //     if (this.curSKillSkill && this.curSKillSkill.state != SkillState.None)
    //         return null
    //     if (!this.skillLearned) return;
    //     let skills: VoSkill[] = this.skillLearned.skills;
    //     let time: number = App.TimeManager.serverTimeMs;
    //     for (let i = skills.length - 1; i >= 0; i--) {
    //         let skill = skills[i];
    //         if (!skill) continue;
    //         if (!skill.isActive) continue;
    //         if (skill.cfgSkill.getTargetType() == SkillTargetType.None) continue;
    //         if (skill.cfgSkill.getSkillType() == SkillType.PassiveSkills) continue;
    //         if (skill.cdTime === -1) continue;
    //         if (skill.cdTime > time) continue;
    //         if (skill.cfgSkill.getDmgType() == SkillDamageType.RebirthAndHeal
    //             && this.unitType == UnitType.Hero)
    //             return this.checRebornSkillUse(skill)

    //     }
    //     return null;
    // }

    /**
     * 尝试攻击
     * @param target 
     * @returns 
     */
    // protected tryAttack(target: IUnit, searchParam?: SearchParam): boolean {
    //     if (App.Socket.checkConnected()) {
    //         //搜索技能
    //         if (this.curSKillSkill && this.curSKillSkill.state > SkillState.None) {
    //             return true;
    //         }

    //         let self: IUnit = this as unknown as IUnit;
    //         let skillVo = RoleSkill.getUseSkill(self, target);
    //         if (skillVo == null) {
    //             return this.isEllipseRange(target);
    //         }

    //         //技能目标改变
    //         if (!RoleSkill.check(self, target, skillVo)) {

    //             let result: IUnit[] = RoleSelector.nearestBySkill(self, skillVo.cfgSkill, searchParam);
    //             if (!result) {
    //                 skillVo.cdTime = -1;
    //                 return false;
    //             }
    //             target = result[0];
    //         }

    //         if (RoleSelector.checkDistance(self, skillVo, target)) {
    //             this.stopMove();
    //             if (this.curSKillSkill) {
    //                 this.curSKillSkill.stopSkill()
    //             }

    //             this.curSKillSkill = SkillManager.instance.createSkill(this.data.unitId, skillVo.cfgSkill.getSkillId(), skillVo.cfgSkill.getLevel());
    //             this.curSKillSkill.start(self, target, searchParam);

    //             if (this.unitType == UnitType.Monster) {
    //                 let voData = this.data as VoMonster;
    //                 if (voData && voData.cfgMonster.type == MonsterType.BOSS) {
    //                     App.EventManager.emitEvent(GameEvent.BOSS_BE_CHALLENGE, voData.cfgMonster.id);
    //                 }
    //             }
    //             return true;
    //         }

    //         //不在攻击范围内就追过去.
    //         if (this.isEllipseRange(target)) {
    //             this.moveToTarget(target);
    //             return true;
    //         }
    //         return false;
    //     }
    //     else {
    //         return false;
    //     }
    // }

    // public stopSkill() {
    //     if (this.curSKillSkill) {
    //         this.curSKillSkill.stopSkill()
    //         SkillManager.instance.removeSkill(this.curSKillSkill);
    //     }
    // }

    protected _setFaceDir(dx: number, dy: number) {
        var moveAngle: number = Math.atan2(dy, dx);
        var dire: number = Math.round((-moveAngle + Math.PI) / (Math.PI / 4));
        var dir: number = 8 - dire;

        //策划要求不用显示 (4 上方向) (0 方向)  因为只在4，0时不会切换左右方向
        if (dir == 4 || dir == 0) {
            dir = dx > 0 ? dir + 1 : dir - 1;
        }
        this.direction = dir;
    }

    /**
     * 设置单位朝向
     * @param dir
     */
    public setFaceDir(dir: cc.Vec3) {
        this._setFaceDir(dir.y, dir.x);
    }

    public getPosition(out?: cc.Vec3): cc.Vec3 {
        if (!out) out = cc.v3();
        if (this.node && this.node.isValid) {
            this.node.getPosition(out);
        }
        return out;
    }

    /**
     * 单位望向目标对象
     * @param target
     */
    public lookAtTarget(target: IUnit | cc.Node | cc.Vec3) {
        let point: cc.Vec3;
        if (target instanceof cc.Node) {
            point = new cc.Vec3();
            target.getPosition(point);
        } else if (target instanceof Unit) {
            point = new cc.Vec3();
            target.node.getPosition(point);
        } else if (target instanceof cc.Vec3) {
            point = target;
        }
        var dir: cc.Vec3 = point.subtract(this.node.position);
        this.setFaceDir(dir);
    }

    /**
     * 获得单位周围相邻的所有路点
     * @returns
     */
    public getRoundRoadNodes(): RoadNode[] {
        var nodeArr: RoadNode[] = PathFindingAgent.instance.getRoundRoadNodes(this.roadNode);
        return nodeArr;
    }

    /**
     *
     * @param name 播放技能动画
     * @returns
     */
    playAttackAnim(name): number {
        return this.play(name);
    }

    /**
     * 被攻击
     * @param damage 伤害值,此接口目前只用于砍树、挖矿类的
     */
    // public beAttack(damageResult: SkillDamageResult) {
    //     if (!damageResult.getHit()) {
    //         this.fly(FloatingTextType.miss, 0);
    //         return;
    //     }
    //     this.beHitEffect();
    //     let damage = damageResult.getDamage();

    //     if (damageResult.type == AttackType.Collect) {
    //         this.fly(FloatingTextType.collect, damage);
    //     } else if (damageResult.type == AttackType.Mine) {
    //         this.fly(FloatingTextType.collect, damage);
    //     } else {
    //         if (damageResult.getCrit()) {
    //             this.fly(FloatingTextType.crit, damage);
    //         } else {
    //             this.fly(FloatingTextType.hp, damage);
    //         }
    //     }
    //     let oldHp = this.getHp();
    //     let newHp = oldHp - damage;
    //     newHp = newHp <= 0 ? 0 : newHp;
    //     this.setHp(newHp);
    //     if (oldHp > 0 && newHp <= 0) {
    //         this.onDead();
    //     }
    //     if (this.isDie() || newHp >= this.getHpMax()) {
    //         this.hideHpBar();
    //     } else {
    //         this.showHpBar();
    //     }
    // }

    // public onBeAttack() {
    //     if (!this.isDie) {
    //         this.skillLearned.onBeAttack();
    //         if (this.unitType == UnitType.Monster) {
    //             let voData = this.data as VoMonster;
    //             if (voData && voData.cfgMonster.type == MonsterType.BOSS) {
    //                 App.EventManager.emitEvent(GameEvent.BOSS_BE_CHALLENGE, voData.cfgMonster.id);
    //             }
    //         }
    //     }
    // }
    // /**
    //  * 承受Buff伤害
    //  * @param damageResult 
    //  */
    // public takeBuffDamage(damageResult: BuffDamageResult): void {
    //     let oldHp = this.getHp();
    //     let newHp = oldHp - damageResult.damage;
    //     newHp = newHp <= 0 ? 0 : newHp;
    //     this.fly(FloatingTextType.hp, damageResult.damage);
    //     this.setHp(newHp);
    //     if (oldHp > 0 && newHp <= 0) {
    //         this.onDead();
    //     }
    //     if (this.isDie() || newHp >= this.getHpMax()) {
    //         this.hideHpBar();
    //     } else {
    //         this.showHpBar();
    //     }
    // }

    // /**
    //  * 接受Buff治疗
    //  * @param cureResult 
    //  */
    // public takeBuffCure(cureResult: BuffCureResult): void {
    //     // this.beHealEffect();
    //     let oldHp = this.getHp();
    //     let newHp = oldHp + cureResult.cure;
    //     this.fly(FloatingTextType.heal, cureResult.cure);
    //     newHp = newHp >= this.getHpMax() ? this.getHpMax() : newHp;
    //     this.setHp(newHp);
    //     if (oldHp <= 0 && newHp > 0) {
    //         this.state = UnitState.idle;
    //         MapManager.curMap.rebornUnit(this);

    //     }
    //     if (this.isDie() || newHp >= this.getHpMax()) {
    //         this.hideHpBar();
    //     } else {
    //         this.showHpBar();
    //     }
    // }

    /**
     * 受伤害
     * @param value 负数扣伤害. 正数回血
     */
    // public onDamage(value: number) {
    //     if (this.isDie()) {
    //         return;
    //     }
    //     let oldHp = this.getHp();
    //     let maxHp = this.getHpMax();
    //     let newHp = oldHp + value;
    //     if (newHp <= 0) newHp = 0;
    //     if (newHp >= maxHp) newHp = maxHp;
    //     this.setHp(newHp);
    //     if (oldHp > 0 && newHp <= 0) {
    //         this.onDead();
    //     }
    //     if (this.isDie() || newHp >= maxHp) {
    //         this.hideHpBar();
    //     } else {
    //         this.showHpBar();
    //     }
    // }

    /** 停止移动 */
    stopMove() { }

    /**
     * 受击效果
     */
    // public beHitEffect() {
    //     if (this.skin) {
    //         const material = this.skin.setMaterial(0, this.context.otherMgr.whiteMaterial);
    //         material.setProperty("u_change", new cc.Vec4(255, 0.1, 0.1, 1));
    //         this.scheduleOnce(() => {
    //             this.skin.setMaterial(0, this.context.otherMgr.normalMaterial);
    //         }, 0.12);
    //     }
    // }

    /**
     * 创建名字
     * @returns 
     */
    protected createName(): MBossName {
        return null;
    }

    /**
     * 创建血条
     * @returns 
     */
    protected createHpBar(): HpBar {
        return null;
    }

    /**
     * 显示血条
     */
    protected showHpBar() {
    //     if ((this.data as VoMonster).isBoss) {
    //         if (MapManager.curMap.battleType === BattleType.GoldenPig || MapManager.curMap.battleType === BattleType.Dungeon)
    //             return;
    //     }
    //     if (this.hpBar == null) {
    //         this.hpBar = this.createHpBar();
    //     }
    //     if (this.hpBar) {
    //         this.hpBar.node.active = true;
    //         let hpMax = this.getHpMax();

    //         if (hpMax > 0) {
    //             let hp = this.getHp();
    //             this.hpBar.setProcess(hp / hpMax);
    //         }

    //         const cfgMonster = (this.data as VoMonster).cfgMonster;
    //         if ((this.data as VoMonster).isBoss) {
    //             this.hpBar.setLevel(cfgMonster.level);
    //         }
    //     }
    }

    /**
     * 隐藏血条
     */
    public hideHpBar() {
    //     if (this.hpBar) {
    //         if ((this.data as VoMonster).isBoss || (this.data as VoMonster).isElite) {
    //             this.hpBar.node.active = true;
    //         } else {
    //             this.hpBar.node.active = false;
    //         }

    //     }
    }

    /**
     * 死亡
     */
    // onDead() {
    //     if (this.data) {
    //         this.data.dieX = this.node.x;
    //         this.data.dieY = this.node.y;
    //     }

    //     this.stopMove();

    //     this.state = UnitState.dead;
    //     this.play(UnitAnim.dead);

    //     let rigidBody = this.node.getComponent(cc.RigidBody);
    //     if (rigidBody) {
    //         rigidBody.enabled = false;
    //     }

    //     // 清理身上buff
    //     if (this.beAttackNode) {
    //         this.beAttackNode.removeAllChildren();
    //     }

    //     if (this.chaseTarget) {
    //         this.chaseTarget = null;
    //     }

    //     if (this.unitType == UnitType.Hero) {
    //         this.triggerQiPao(1, 0);
    //     }
    //     this.skillLearned.removeBuffByCaster(this)
    //     App.EventManager.emitEvent(GameEvent.UNIT_DIE, this);
    // }

    /**
       * 死亡后消失处理
       */
    // onDeadVanish(): void {
    //     this.alpha = 0;
    //     //this.removeSelf();
    //     if (this.shadowNode) {
    //         this.shadowNode.destroy();
    //         this.shadowNode = null;
    //     }

    //     this.stopSkill();
    //     this.resetcurSkill()
    // }

    // resetcurSkill() {
    //     if (this.curSKillSkill && this.curSKillSkill.state == SkillState.None) {
    //         //this.curSKillSkill = null
    //     }
    // }

    getHp(): number {
        return this.data ? this.data.getHp(): 1;
    }

    protected setHp(hp: number) {
        this.data.setHp(hp);
        let hpEvent: cc.Event = new cc.Event("HP_CHANGE", true);
        this.node.dispatchEvent(hpEvent);
    }

    getHpMax(): number {
        return this.getBaseAttr(BattleAttr.maxHp);
    }

    /**
     * 刷新血量
     */
    refreshHp() {
        let hp: number = this.getHp();
        let hpMax: number = this.getHpMax();
        if (hp < hpMax) {
            this.showHpBar();
        } else {
            this.hideHpBar();
        }
    }

    /** 增加血量 */
    addHp(addValue: number) {
        let hp = this.data.getHp();
        let hpMax = this.data.getHpMax();
        if (hp + addValue > hpMax) {
            this.setHp(hpMax);
        } else {
            this.setHp(hp + addValue);
        }
        this.refreshHp();
    }
    /**重生 */
    reborn() {
        // this.setHp(this.getHpMax());
        this.state = UnitState.idle;

        this.hideHpBar();

        // 移除倒计时
        if (this.countDownNode) {
            this.countDownNode.destroy();
            this.countDownNode = null;
        }

        this.openCollision();
    }

    isDie(): boolean {
        return this.state == UnitState.dead;
    }

    protected onUpdateHeal() { }

    protected lateUpdate(dt: number): void {
        //死亡不更新影子
        if (this.get_state() == UnitState.dead) return;

        // if (!this.shadowNode) {
        //     let shadow: cc.Prefab = LoadUtils.get(Bundles.COMMON + "prefab/shadow", cc.Prefab);
        //     this.shadowNode = cc.instantiate(shadow);
        //     this.shadowNode.getComponent(Shadow).init(this.data);
        //     this.shadowNode.parent = this.context.SkillBottomLayer;
        //     // setNodeZIndex(this.shadowNode, -10);
        // }
        if (this.shadowNode) {
            this.shadowNode.setPosition(this.node.x, this.node.y);
        }
        if (this.state != UnitState.dead && this.getHp() <= 0) {
            this.state = UnitState.dead;
        }

        if (!this.nameNode && this.unitType === UnitType.Monster) {
            // const cfgMonster = (this.data as VoMonster).cfgMonster;
            // if (cfgMonster.type == MonsterType.Elite) {
            //     this.nameNode = this.createName()
            //     this.nameNode.setMonsterLevel(cfgMonster.level)
            //     this.nameNode.setMonsterName(this.data.name)
            // }
        }
        // if ((this.data as VoMonster).isBoss || (this.data as VoMonster).isElite) {
        //     this.showHpBar();
        // }
    }

    /** 完成了普攻 */
    finishNormalAttack() {
        // this.skillLearned && this.skillLearned.onNormalAttack();
    }

    public getHeight(): number {
        if (this.data) {
            let height = this.data.getHpHeight();
            if (!isNaN(height)) {
                return height;
            }
        }
        return this.getBodyHeight();
    }

    getFireNode(index: number = 0): cc.Node {
        if (!this.fireNodes[index]) {
            this.fireNodes[index] = this.createNode(0, this.getHeight() * 2 / 5);
            this.fireNodes[index].setScale(this.scale, this.scale);
        }
        return this.fireNodes[index];
    }

    /**
     * 获取世界开火点
     * @param out 
     * @returns 
     */
    getWorldFirePosition(out?: cc.Vec3, index?: number): cc.Vec3 {
        return this.getWorldPosition(this.getFireNode(index), out, true);
    }


    /**
     * 子弹瞄准点
     * @param out 
     * @returns 
     */
    getWorldAimingPoint(out?: cc.Vec3) {

        if (!this.aimingPoint) {
            this.aimingPoint = this.createNode(0, this.scale * this.getHeight() * 1 / 2);
        }
        return this.getWorldPosition(this.aimingPoint, out, true);
    }


    /** 特效位置 */
    getEffectPosition(out?: cc.Vec3): cc.Vec3 {
        if (!out) out = new cc.Vec3();

        if (this.effectNode) {
            this.effectNode.getPosition(out);
        } else {
            //先定死一个固定位置.
            out.x = 0;
            out.y = this.data.getHpHeight() >> 1;
        }
        if (this.body.scale.x < 0) out.x = out.x * -1;
        out.x *= this.node.scale.x;
        out.y *= this.node.scale.y;
        return out;
    }

    /**
     * 获取类型对应的节点
     * @param type 
     */
    getEffectNode(type?: number): cc.Node {
        // switch (type) {
        //     case SkillEffectNodeType.Back:
        //         return this.getEffect2Node();
        //     case SkillEffectNodeType.Body:
        //         return this.getEffect3Node();
        //     case SkillEffectNodeType.Front:
        //         return this.getEffect1Node();
        //     default:
        //         return this.node;
        // }
        return null;
    }

    /** 人物前1 效果挂载的节点 */
    getEffect1Node(): cc.Node {
        if (!this.effectNode) {
            let height = this.getHeight();
            this.effectNode = this.createNode(0, height * 2 / 5, "effect");
            this.effectNode.setScale(this.scale, this.scale);
        }
        else {
            let height = this.getHeight();
            this.effectNode.y = height * 2 / 5;
        }
        return this.effectNode;
    }

    /** 人物后2 效果挂载的节点  */
    getEffect2Node(): cc.Node {
        if (!this.effect2Node) {
            let height = this.getHeight();
            this.effect2Node = this.createNode(0, height * 2 / 5, "effect2");
            // setNodeZIndex(this.effect2Node, -1);
            this.effect2Node.setScale(this.scale, this.scale);
        }
        else {
            let height = this.getHeight();
            this.effect2Node.y = height * 2 / 5;
        }
        return this.effect2Node;
    }

    /** 人物身上3 挂载的节点 (跟随人物转向) */
    getEffect3Node(): cc.Node {
        if (!this.effect3Node) {
            let height = this.getHeight();
            this.effect3Node = this.createNode(0, height * 2 / 5, "effect3", this.body);
        }
        else {
            let height = this.getHeight();
            this.effect3Node.y = height * 2 / 5;
        }
        return this.effect3Node;
    }

    /**
     * 受击节点 受击效果放置的位置
     */
    getHitNode(): cc.Node {
        if (!this.beAttackNode) {
            let height = this.getHeight();
            this.beAttackNode = this.createNode(0, height * 2 / 5, "beAttack");
            this.beAttackNode.setScale(this.scale, this.scale);
        }
        return this.beAttackNode;
    }

    /**
     * 获取世界坐标下的受击点
     * @param out 
     * @returns 
     */
    getWorldHitPosition(out?: cc.Vec3): cc.Vec3 {
        return this.getWorldPosition(this.getHitNode(), out);
    }

    /**
     * 世界坐标信息
     * @param node 对应的节点. 不传参表示当前节点
     * @param out  
     * @returns 
     */
    getWorldPosition(node?: cc.Node, out?: cc.Vec3, rotate: boolean = false): cc.Vec3 {
        if (!out) out = new cc.Vec3();
        if (!node) node = this.node;

        if (this.node != node) {
            this.body.getComponent(cc.UITransform);
            if (!rotate || this.body.scale.x > 0) {
                out.x = node.x;
            } else {
                out.x = node.x * -1;
            }
            out.y = node.y;
            out.z = node.z;
        }
        out.x += this.node.x;
        out.y += this.node.y;
        out.z += this.node.z;
        return out;
    }

    /** 本体和目标的距离 */
    protected getDistance(target: Unit): number {
        return this.node.getPosition().subtract(target.node.getPosition()).length();
    }

    /**
    * 获取搜索参数.
    */
    // protected getSearchParam(): SearchParam[] {
    //     return null;
    // }

    /**
    * 搜索感知范围内的敌人.
    */
    // protected getSearchEnemyParam(): SearchParam {
    //     return null;
    // }

    /**
    * 搜索复活技能的复活对象.
    */
    // protected getSearchRebirthParam(): SearchParam {
    //     return null;
    // }



    /**采集 */
    protected collectResources(unit: any) { }

    /**
     * 前往 靠近/追击 目标
     */
    public moveToTarget(target: IUnit) { }

    /** 返回驻守点 */
    public comeBackGuardPoint() {
        // do nothing
    }

    /** 设置跟随 */
    public setFollow(point: cc.Vec3) {
    }

    protected physicsCircleColliderSsensor: boolean = false;
    /**
     * 取消物理碰撞
     */
    protected cancelCollision() {
        let physicsCircleCollider = this.node.getComponent(cc.CircleCollider2D);
        if (physicsCircleCollider) {
            // this.physicsCircleColliderSsensor = physicsCircleCollider.sensor;
            physicsCircleCollider.sensor = true;
            physicsCircleCollider.apply();
        }
    }

    /**
     * 开启物理碰撞
     */
    public openCollision() {

        let physicsCircleCollider1 = this.node.getComponent(cc.CircleCollider2D);
        if (physicsCircleCollider1) {
            physicsCircleCollider1.sensor = false;
            physicsCircleCollider1.apply();
        }
    }

    /**
     * 添加buff(buff逻辑)
     */
    // public addBuffs(caster: IUnit, battBuffs: BattBuff[], skillCfg: CFG_SkillLevel): void {
    //     let self: IUnit = this as unknown as IUnit;
    //     this.skillLearned.addBuff(caster, self, battBuffs, skillCfg);
    // }

    /**
     * 添加buff(buff逻辑)
     */
    // public addBuffsEx(caster: IUnit, buffId: number, skillCfg: CFG_SkillLevel): Buff {
    //     let self: IUnit = this as unknown as IUnit;
    //     return this.skillLearned.addBuffEx(caster, self, buffId, skillCfg);
    // }

    /**
     * 是否可以达到某个状态
     * @param newStatus 
     * @returns 
     */
    // public canChangeStatusTo(newStatus: UnitStatus): boolean {
    //     for (let status of this.unitStatus) {
    //         if (!status.canReach(newStatus)) {
    //             return false;
    //         }
    //     }
    //     return true;
    // }
    /***
     * 是否被禁锢
     */
    // public isImprisoned() {
    //     for (let status of this.unitStatus) {
    //         if (!status.canAction) {
    //             return true;
    //         }
    //     }
    //     return false;
    // }

    /**
     * 转换到某个状态
     * @param newStatus 
     */
    // public changeStatusTo(newStatus: UnitStatus): void {
    //     console.log("状态 add:", newStatus.name)
    //     this.unitStatus.push(newStatus);
    // }

    /**
     * 移除某个状态
     * @param oldStatus 
     */
    // public removeStatus(oldStatus: UnitStatus) {
    //     let index: number = this.unitStatus.indexOf(oldStatus);
    //     if (index >= 0) {
    //         console.log("状态 remove:", oldStatus.name)
    //         this.unitStatus.splice(index, 1);
    //     }
    // }

    public getFogArea(): number {
        return 0;
    }

    /**
     * 飘字.
     * @param type 
     * @param value 
     */
    // public fly(type: FloatingTextType, value: number) {
    //     if (!this.fightFlyer) {
    //         this.fightFlyer = new FloatingText(this);
    //     }
    //     if (this.unitType === UnitType.Monster) {
    //         const cfgMonster = (this.data as VoMonster).cfgMonster;
    //         if (cfgMonster.maxHurt > 0) {
    //             value = cfgMonster.maxHurt;
    //         }
    //     }
    //     this.fightFlyer.fly(type, value);
    // }

    protected stopEffect(node: cc.Node) {
        if (!node) return;
        if (node && node.isValid) {
            let effect = node.getComponent(cc.Animation);
            if (effect) effect.stop();
            let particle = node.getComponent(cc.ParticleSystem2D);
            if (particle) particle.stopSystem();
            for (let i = 0; i < node.children.length; i++) {
                this.stopEffect(node.children[i]);
            }
        }
    }

    /**
    * 销毁自身
    */
    override removeSelf() {
        // if (this.curSKillSkill) {
        //     this.curSKillSkill.stopSkill()
        //     this.curSKillSkill = null;
        // }

        // if (this.anim) {
        //     this.anim.stop();
        // }
        // if (this.anim_back) {
        //     this.anim_back.stop();
        // }
        // if (this.anim_front) {
        //     this.anim_front.stop();
        // }
        // // 移除影子
        // if (this.shadowNode) {
        //     this.shadowNode.destroy();
        //     this.shadowNode = null;
        // }
        // if (this.skillLearned) {
        //     this.skillLearned.clear();
        //     this.skillLearned = null;
        // }

        // if (this.fightFlyer) {
        //     this.fightFlyer.destory();
        //     this.fightFlyer = null;
        // }

        // if (this.hpBar) {
        //     this.hpBar.destroy();
        // }

        // if (this.nameNode) {
        //     this.nameNode.destroy();
        // }
        // this.stopEffect(this.effectNode);
        // this.stopEffect(this.effect2Node);
        // this.stopEffect(this.effect3Node);
        // this.stopEffect(this.beAttackNode);
        // this.resetcurSkill();
        super.removeSelf();
    }
}
