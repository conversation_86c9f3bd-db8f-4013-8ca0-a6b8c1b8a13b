import * as cc from "cc";
import UnitBuffIcon from "./UnitBuffIcon";

const { ccclass, property } = cc._decorator;
@ccclass
export default class UnitBuffIconList extends cc.Component {

    @property(cc.Prefab)
    public buffPrefab: cc.Prefab;

    private buffIconMap = {};
    /**
     * 构造函数
     */
    private _init: boolean = false;
    private _buffList: any[];

    public constructor() {
        super();
    }

    onLoad(): void {
        this._init = true;
        if (this._buffList)
            this.updateBuffList(this._buffList);
    }

    public updateBuffList(buffList: any[]) {
        this._buffList = buffList;
        if (!this._init) return;
        for (var key in this.buffIconMap) {
            (this.buffIconMap[key] as cc.Node).parent = null;
        }
        if (buffList) {
            for (var i = 0; i < buffList.length; i++) {
                let key = buffList[i]["buffKey"];
                let data = buffList[i]["data"];
                let buffIconNode: cc.Node;
                if (this.buffIconMap[key]) {
                    buffIconNode = this.buffIconMap[key]
                }
                else {
                    buffIconNode = cc.instantiate(this.buffPrefab);
                    this.buffIconMap[key] = buffIconNode
                }
                if (data.count > 0)
                    buffIconNode.parent = this.node;
                else
                    buffIconNode.parent = null;
                buffIconNode.getComponent(UnitBuffIcon).setData(data);
            }
        }
    }

}