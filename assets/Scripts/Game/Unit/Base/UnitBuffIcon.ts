import * as cc from "cc";

import ResSprite from "../../Components/ResSprite";
import { Bundles } from "../../Module/Bundles";

export interface IBuffIconInfo {
    count: number,
    icon: string
}

const { ccclass, property } = cc._decorator;
@ccclass
export default class UnitBuffIcon extends cc.Component {

    @property(ResSprite)
    public icon: ResSprite;

    @property(cc.Label)
    public count: cc.Label;

    private _buffIconInfo: IBuffIconInfo;
    private _init: boolean = false;
    /**
     * 构造函数
     */
    public constructor() {
        super();
    }

    onLoad(): void {
        this._init = true;
        if (this._buffIconInfo) {
            this.setData(this._buffIconInfo)
        }
    }

    setData(buffIconInfo: IBuffIconInfo) {
        this._buffIconInfo = buffIconInfo;
        if (!this._init) return;
        this.count.string = `${this._buffIconInfo.count}`;
        this.icon.setSpriteFrame(Bundles.BUFF_CION + this._buffIconInfo.icon, null, () => {
            const transform = this.icon.node.getComponent(cc.UITransform);
            transform.setContentSize(42, 42);
        });
    }
}