import * as cc from "cc";
import  Logger  from "../../Framework/Logger";
import { VoUnit } from "./VoUnit";
import { MonsterType, PlayerCamp } from "./Actor/PlayerType";
// import HeroData from "../../modules/heroManage/model/HeroData";
// import { VoMonster } from "../VoMonster";

const { ccclass, property } = cc._decorator;

@ccclass
export default class Shadow extends cc.Component {

    /**影子品质 */
    @property(cc.SpriteFrame)
    sf_shadowArr: cc.SpriteFrame[] = [];


    public init(data: VoUnit): void {
        //影子品质数组 0:灰色 1:绿色 2:蓝色 3:紫色 4:黄色
        const shadowSprite: cc.Sprite = this.node.getComponent(cc.Sprite);
        if (data.camp != PlayerCamp.Own) {
            //不是己方英雄阵营一律灰色
            // if ((data as VoMonster).cfgMonster && (data as VoMonster).cfgMonster.type == MonsterType.Elite) {
            //     shadowSprite.spriteFrame = this.sf_shadowArr[6];
            // } else {
            //     shadowSprite.spriteFrame = this.sf_shadowArr[0];
            // }
        } else {
            // const heroData: HeroData = App.UserController.getHeroById(data.identity);
            // shadowSprite.spriteFrame = this.sf_shadowArr[heroData.heroQuality];
            // Logger.trace(`当前上阵英雄品质：${heroData.heroQuality}`);
        }
    }
}
