import * as cc from "cc";import { BattleAttr } from "../../Config/GameDefine";
import { VoEntity } from "./VoEntity";



/**
 * unit的基础数据
 */
export class VoUnit extends VoEntity {

    rebornTime: number = 0; // 复活时间
    rebornPosX: number = 0; // 复活点x
    rebornPosY: number = 0; // 复活点y

    /** 死亡时的X  */
    dieX: number = 0;
    /** 死亡时的Y */
    dieY: number = 0;

    /**放大系数 */
    scale: number = 200;


    /** 脱战范围   >>中心点*/
    outOfRange: number = 0;
    /** 感知范围   >>人物点*/
    sensorRange: number = 0;
    /** 追击范围    >>人物点 */
    chaseRange: number = 0;
    /** 战斗圈      >>中心点 */
    battleRange: number;

    /** Y轴搜索系数 */
    rangeAxisRate = 1;

    private attrObj: object;

    constructor() {
        super();
        this.attrObj = {};
    }

    clone(): VoUnit {
        let data: VoUnit = new VoUnit();
        data.unitId = this.unitId;
        data.unitType = this.unitType;
        data.idExcel = this.idExcel;
        data.identity = this.identity;
        data.rebornTime = this.rebornTime;
        data.rebornPosX = this.rebornPosX;
        data.rebornPosY = this.rebornPosY;
        data.dieX = this.dieX;
        data.dieY = this.dieY;
        return data;
    }

    /**
     * 当前血量
     * @returns 
     */
    getHp(): number {
        return this.getAttr(BattleAttr.hp);
    }

    setHp(value: number) {
        this.setAttr(BattleAttr.hp, value);
    }

    /**
     * 增加血量
     * @param value 
     */
    addHp(value: number) {
        let hp: number = this.getHp();
        let hpMax: number = this.getHpMax();
        if (hp + value > hpMax) {
            value = hpMax - hp;
        }
        this.setAttr(BattleAttr.hp, hp + value);
    }

    /**
     * 血量上限
     * @returns 
     */
    getHpMax(): number {
        return this.getAttr(BattleAttr.maxHp);
    }

    setAttr(type: number, value: number) {
        this.attrObj[type] = value;
    }

    getAttr(type: number, defaultValue: number = 0): number {
        return this.attrObj[type] || defaultValue;
    }

    cloneAttr(): object {
        let obj = {};
        for (let key in this.attrObj) {
            obj[key] = this.attrObj[key];
        }
        return obj;
    }
}
