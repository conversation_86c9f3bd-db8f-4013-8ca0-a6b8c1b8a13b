import { BattleAttr, UnitType } from "../../Config/GameDefine";
import {
    CFG_HeroType,
} from "../../Config/GameCfg/CFGManager";
import HeroLevel from "../../Config/GameCfgExt/HeroGrade";
import Monster from "../../Config/GameCfgExt/Monster";
import HeroData from "../../GameData/Hero/HeroData";
import { pb } from "../../Proto/pb";
import { VoCharacter } from "./VoCharacter";
import {
    HERO_AXIS_RATE,
    HERO_DENSITY,
    HERO_NORMAL_DAMPING,
    PlayerCamp,
    Player_Init_data
} from "./Actor/PlayerType";

export class VoHero extends VoCharacter {

    private _level: number = 0;

    isMainPlayer: boolean;

    heroIndex: number;

    /** 是否是援军  */
    private _isAid: boolean = false;

    init(heroData: pb.IPbSprite) {
        this.skills = [];
        this.rangeAxisRate = HERO_AXIS_RATE;

        let heroType = CFG_HeroType.get(heroData.identity);
        if (!heroType) {
            console.error("找不到英雄配置", heroData.identity);
        }

        this.unitId = heroData.id;
        this.unitType = UnitType.Hero;
        this.idExcel = heroData.identity;
        this.identity = heroData.identity;
        this.scale = heroType.scale;
        this.camp = PlayerCamp.Own;
        this.name = heroType.name;
        this.level = heroData.level;
        this.bodyRadius = heroType.volume;
        this.linearDamping = HERO_NORMAL_DAMPING;
        this.density = HERO_DENSITY;


        if (heroData.battleAttr) {
            Object.entries(BattleAttr).forEach(([key, value]) => {
                let attrType: number = Number(key);
                let attrName: string = String(value);
                //不更新血量
                if (heroData.battleAttr[attrName]) {
                    this.setAttr(attrType, heroData.battleAttr[attrName]);
                } else {
                    //console.error("未知的属性", attrName);
                }
            });

            this.setAttr(BattleAttr.hp, this.getAttr(BattleAttr.maxHp));
            // this.setAttr(BattleAttr.maxHp, heroData.maxHp);
        }
    }

    initAid(heroData: pb.IPbSprite) {
        let _cfgMonster = Monster.get(heroData.configId);
        if (!_cfgMonster) {
            console.error("找不到怪物配置", 7001188);
            return;
        }

        this._isAid = true;
        this.unitType = UnitType.Hero;
        this.idExcel = heroData.identity;
        this.identity = _cfgMonster.data.identity;
        this.rebornTime = 0;
        this.scale = _cfgMonster.data.scale;
        this.camp = PlayerCamp.Own;
        // //复制cfgMonster中得属性值
        Object.entries(BattleAttr).forEach(([key, value]) => {
            let attrType: number = Number(key);
            let attrName: string = String(value);
            //不更新血量
            if (heroData.battleAttr[attrName]) {
                this.setAttr(attrType, heroData.battleAttr[attrName]);
            } else {
                this.setAttr(attrType, _cfgMonster.getAtt(attrType));
            }
        });
        this.setAttr(BattleAttr.hp, this.getAttr(BattleAttr.maxHp));
        this.chaseRange = 0;
        this.battleRange = this.getAttr(BattleAttr.battleRange);
        this.outOfRange = this.getAttr(BattleAttr.battleRange);
        this.sensorRange = Player_Init_data.SightRange;

        this.skills = _cfgMonster.getSkill();
    }

    public get level(): number {
        return this._level;
    }

    /**英雄等级 */
    public set level(value: number) {
        if (this._level == value) {
            return;
        }
        this._level = value;
        // let cfgHeroGrade = HeroLevel.getByLevel(this.identity, this.level);
        // this.skills = cfgHeroGrade.getSkills();
    }

    updateAttr(hero: HeroData) {
        //拥有的英雄才更新等级.
        if (hero.getHeroStatus() != 0) {
            this.level = hero.heroLevel;
        }

        Object.entries(BattleAttr).forEach(([key, value]) => {
            let attrType: number = Number(key);
            let attrName: string = String(value);
            //不更新血量
            if (attrType != BattleAttr.hp) {
                this.setAttr(attrType, hero.getAttr(attrName));
            }
        });

        //修正寻敌范围
        if (this.getAttr(BattleAttr.battleRange) == 0) {
            let viewRange: number = Player_Init_data.TeamRange;
            this.setAttr(BattleAttr.battleRange, viewRange);
        }
        this.chaseRange = 0;
        this.battleRange = this.getAttr(BattleAttr.battleRange);
        this.outOfRange = this.getAttr(BattleAttr.battleRange);
        this.sensorRange = Player_Init_data.SightRange;

        //修正采集伤害
        if (this.getAttr(BattleAttr.woodDmg) == 0) {
            this.setAttr(BattleAttr.woodDmg, Player_Init_data.WoodDmg);
        }

        //修正挖矿伤害
        if (this.getAttr(BattleAttr.mineDmg) == 0) {
            this.setAttr(BattleAttr.woodDmg, Player_Init_data.MineDmg);
        }

        if (this.getAttr(BattleAttr.hit) == 0) {
            this.setAttr(BattleAttr.hit, Player_Init_data.Hit);
        }
        if (this.getAttr(BattleAttr.dodge) == 0) {
            this.setAttr(BattleAttr.dodge, Player_Init_data.Dodge);
        }
        if (this.getAttr(BattleAttr.critDmgRate) == 0) {
            this.setAttr(BattleAttr.critDmgRate, Player_Init_data.CritDmgRate);
        }
        if (this.getAttr(BattleAttr.crit) == 0) {
            this.setAttr(BattleAttr.crit, Player_Init_data.Crit);
        }

        this.healPercent = Player_Init_data.HeroSafeAreaRecoverPercent;
        this.healInterval = Player_Init_data.HeroSafeAreaRecoverInterval;

        // this.setAttr(BattleAttr.atk, 0);
        //this.setAttr(BattleAttr.hp, 1000000000);
        //this.setAttr(BattleAttr.maxHp, 1000000000);
        //this.setAttr(BattleAttr.moveSpd, 2000);
    }

    public get isAid(): boolean {
        return this._isAid;
    }
}