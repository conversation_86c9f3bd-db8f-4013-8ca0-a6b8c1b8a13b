import * as cc from "cc";
import { GameEvent, MapEvent } from "../../GameEvent/Events";
import MapManager from "../Module/MapInstance/Managers/MapManager";
import GameController from "./GameController";
import Joystick from "./Joystick";
import EventManagerComponent from "../GlobalComponent/EventManagerComponent";
import { MapController } from "../Module/MapInstance/Components/MapController";

const { ccclass, property } = cc._decorator;

/**
 * 摇杆控制器
 */
@ccclass
export default class JoystickController extends cc.Component {
    /**
     * 触摸状态
     */
    public static Touching: boolean = false;

    @property(Joystick)
    public joyStick: Joystick = null;

    @property(cc.Node)
    public touchNode: cc.Node = null;

    public eanble : boolean = true;

    touchPos: cc.Vec2 = cc.Vec2.ZERO.clone();
    joyMoveDir: cc.Vec2 = cc.Vec2.ZERO.clone();

    private mapController: MapController | null = null;

    onLoad() {

    }

    start() {
        EventManagerComponent.instance().addEventListener(MapEvent.Map_INIT_COMPLETE, this.onMapInitComp, this);
        EventManagerComponent.instance().addEventListener(GameEvent.HIDE_JOYSTICK, this.onHideJoystick, this);
        this.onHideJoystick()
    }

    /**
     * 地图初始化完成
     * @param sceneData
     */
    private onMapInitComp() {
        this.addEvent()
    }

    public addEvent() {
        var touchNode: cc.Node = this.touchNode;
        touchNode.on(cc.Node.EventType.TOUCH_START, this.onJoystickTouchStart, this);
        touchNode.on(cc.Node.EventType.TOUCH_MOVE, this.onJoystickTouchMove, this);
        touchNode.on(cc.Node.EventType.TOUCH_END, this.onJoystickTouchEnd, this);
        touchNode.on(cc.Node.EventType.TOUCH_CANCEL, this.onJoystickTouchEnd, this);
    }

    public removeEvent() {
        var touchNode: cc.Node = this.touchNode;
        touchNode.on(cc.Node.EventType.TOUCH_START, this.onJoystickTouchStart, this);
        touchNode.on(cc.Node.EventType.TOUCH_MOVE, this.onJoystickTouchMove, this);
        touchNode.on(cc.Node.EventType.TOUCH_END, this.onJoystickTouchEnd, this);
        touchNode.on(cc.Node.EventType.TOUCH_CANCEL, this.onJoystickTouchEnd, this);
    }


    private onHideJoystick() {
        this.joyStick.hidden();
    }

    protected update(dt: number): void {

    }

    /**
     * Finds and caches the MapController instance
     * @returns The MapController instance or null if not found
     */
    private getMapController(): MapController | null {
        if (!this.mapController) {
            // Try to find MapController in the scene
            const mapControllerNode = cc.find("MapController");
            if (mapControllerNode) {
                this.mapController = mapControllerNode.getComponent(MapController);
            }
        }
        return this.mapController;
    }

    public onJoystickTouchStart(event: cc.EventTouch) {
        if (!this.eanble) {
            return;
        }

        const winSize = cc.view.getDesignResolutionSize()

        this.touchPos = event.getLocation();
        this.joyStick.setStartPoint(
            cc.v2(event.getLocation().x - winSize.width * 0.5, event.getLocation().y - winSize.height * 0.5)
        )
        this.joyStick.show();

        // Notify MapController that joystick control started
        const mapController = this.getMapController();
        if (mapController) {
            mapController.onJoystickStartControl();
        }
    }

    public onJoystickTouchMove(event: cc.EventTouch) {
        if (!this.eanble) {
            return;
        }

        var currentPos = event.getLocation();
        var moveDir: cc.Vec2 = currentPos.subtract(this.touchPos).normalize();
        this.joyMoveDir.x = moveDir.x;
        this.joyMoveDir.y = moveDir.y;
        const winSize = cc.view.getDesignResolutionSize()
        this.joyStick.cursorTo(
            cc.v2(event.getLocation().x - winSize.width * 0.5, event.getLocation().y - winSize.height * 0.5), new cc.Vec3(moveDir.x, moveDir.y));

        // Notify MapController of joystick movement
        const mapController = this.getMapController();
        if (mapController) {
            mapController.onJoystickMoved(moveDir);
        }
    }

    public onJoystickTouchEnd(event: cc.EventTouch) {
        this.joyStick.resetCursor();

        if (!this.eanble) {
            return;
        }

        // Notify MapController that joystick control ended
        const mapController = this.getMapController();
        if (mapController) {
            mapController.onJoystickEndControl();
        }
    }
}
