import * as cc from "cc";
import Singleton from "../../Framework/Utils/Singleton";
import EventManagerComponent from "../GlobalComponent/EventManagerComponent";
import { EventDispatcher, GameEvent, LoginEvent } from "../../GameEvent/Events";
import {
    CFG_SceneTeleport,
    CFG_SkillBuff,
    CFG_Harvest,
    CFG_HeroType,
    CFG_HeroLevel,
    CFG_MainlineTask,
    CFG_PlayerLevel,
    CFG_FunctionOpen,
    CFG_BattleConst,
    CFG_NpcBuilding,
    CFG_HomeStoreHouse,
    CFG_GameHelp,
    CFG_SceneZone,
    CFG_SceneNpc,
    CFG_SceneFog,
    CFG_Dungeon,
    CFG_ComboAttr,
    CFG_ChallengeCopy as CFG_Challenge,
    CFG_BattleType,
    CFG_Tower as CFG_TowerInfo,
    CFG_ModelHeight,
    CFG_Access,
    CFG_GoldenPigStage,
    CFG_Plot,
    CFG_Guide,
    CFG_RankReward as CFG_RankAward,
    CFG_ExpLimit,
} from "../../Config/GameCfg/CFGManager";
import { GameRole } from "../../GameData";
import pb from "../../Proto/pb";
import MapManager from "../Module/MapInstance/Managers/MapManager";
import AppManager from "../Managers/AppManager";
import { AppConfig } from "../../Config/GameConfig";
import { LoadUtils } from "../Utils/LoadUtils";
import Logger from "../../Framework/Logger";
import { Bundles } from "../Module/Bundles";
import ZipUtil from "../Utils/ZipUtil";
import SceneZoneCache from "../../Config/GameCfgExt/SceneZone";

const { ccclass, property } = cc._decorator;

@ccclass
export default class GameController extends Singleton {

    isReLogin: boolean = false;

    init(uiLayer: cc.Node, mapLayer: cc.Node) {

        /*
        ReconnectManager.instance.init();
        let self = this;
        //主界面
        LoadUtils.loadPrefabNode(ViewId.MAINUI, (node: any) => {
            node.parent = UILayer;
            self.mainUIControler = node.getComponent(MainUIControler);
            App.gameScene.layerParent = self.mainUIControler.container;
            App.gameScene.tipsContiner = self.mainUIControler.tipsContainer;
            App.gameScene.init();
        });

        //断线提示界面. 需要提前加载.
        LoadUtils.loadPrefab(ViewId.OffLineUI, () => {
            Logger.trace("OffLineUI loaded", Date.now());
        });

        LoadUtils.loadPrefab(ViewId.UIWaiting, () => {
            Logger.trace("UIWaiting loaded", Date.now());
        });

        //切换场景的loading界面
        LoadUtils.loadPrefab(ViewId.MAP_LOADING, () => {
            Logger.trace("MapLoading loaded", Date.now());
        });
        */

    }

    public start() {
        AppManager.instance().ResManager.init();
        let remoteUrl = AppConfig.remoteUrl;
        if (remoteUrl && remoteUrl.length > 0) {
            if (remoteUrl.lastIndexOf("/") != 0) {
                remoteUrl += "/";
            }
        }
        // Logger.zz_log("zhangzhen_GameController_start_111:" + Date.now());
        Promise.all([this.loadConfig("gamecfg", Bundles.CONFIG), this.loadConfig("map", Bundles.CONFIG)])
            .then((result) => {
                Logger.zz_log("zhangzhen_GameController_start_Config decode success:" + Date.now());
                LoadUtils.releaseAsset(Bundles.CONFIG + "gamecfg");
                LoadUtils.releaseAsset(Bundles.CONFIG + "map");
                this.onDecodeComplete();
            })
            .catch((error) => {
                console.error("加载异常", error);
            });
    }

    /**
 * 当前是否能控制玩家
 * @returns 
 */
    public isCanControlPlayer(): boolean {
        return false;
        // if (App.Socket.checkConnected() == false) {
        //     //断网中
        //     return false;
        // }
        // if (!MapManager.curMap) {
        //     //没有地图
        //     return false;
        // }
        // if (!MapManager.curMap.mainPlayer) {
        //     //没有玩家
        //     return false;
        // }
        // if (App.EnCampContrller.isOnCamp) { //露营中
        //     return false;
        // }

        // if (CameraController.instance.isMove) {
        //     return false;
        // }
        // return HeroManager.instance.canMove();
    }

    connectToGameServer() {
        const eventManager = EventManagerComponent.instance();
        eventManager.addEventListener(LoginEvent.LOGIN_SUCCESS, this.onLoginSuccess, this);
        GameRole.instance().loginToGameServer();
    }

    private onDecodeComplete() {
        Logger.logBusiness("GameController", "onDecodeComplete", "配置解码完成");
        //解压完成。 初始化对应的表数据.  
        if (this.isReLogin) {
            // MessageRegister.__init__();
            // BattleController.instance.reload();
            this.onConnectServer();
            return;
        }
        //-->这里只做游戏进入必要的数据初始化, 其他系统请在各自系统使用时初始化.<--
        CFG_SceneTeleport.reload();
        CFG_HeroType.reload();
        CFG_HeroLevel.reload();
        CFG_Harvest.reload();
        CFG_PlayerLevel.reload();
        CFG_FunctionOpen.reload();
        CFG_BattleConst.reload();
        CFG_NpcBuilding.reload();
        CFG_HomeStoreHouse.reload();
        CFG_MainlineTask.reload();
        CFG_GameHelp.reload();
        SceneZoneCache.reload();
        CFG_SceneNpc.reload();
        CFG_SceneFog.reload();
        CFG_Dungeon.reload();
        CFG_ComboAttr.reload();
        CFG_Challenge.reload();
        CFG_BattleType.reload();
        CFG_TowerInfo.reload();
        CFG_ModelHeight.reload();
        CFG_Access.reload();
        CFG_GoldenPigStage.reload();
        CFG_Plot.reload();
        CFG_Guide.reload();
        CFG_RankAward.reload();
        CFG_ExpLimit.reload();

        // MessageRegister.__init__();
        // BattleController.instance.init();
        // FightConfig.__init__();

        EventManagerComponent.instance().dispatch(GameEvent.GAME_INIT_COMPLETE, null);
        this.onConnectServer();
    }
    private onConnectServer() {
        EventManagerComponent.instance().removeEventListener(
            LoginEvent.LOGIN_SUCCESS, this.onLoginSuccess); //登录成功
        EventManagerComponent.instance().addEventListener(LoginEvent.LOGIN_SUCCESS, this.onLoginSuccess); //登录成功
        // App.HallSocket.toConnectHallSocket(AppConfig.serverUrl);
    }

    private onLoginBlocked(data: { status: pb.LoginStatus }) {
        console.log("onLoginBlocked", data);
    }

    private onLoginSuccess() {
        console.log("onLoginSuccess");
    }

    private loadConfig(name: string, bundle: string) {
        return new Promise((resolve, reject) => {
            LoadUtils.load(bundle + name, cc.BufferAsset, (assets: cc.BufferAsset) => {
                Logger.trace("loadConfig", bundle + name, assets);
                ZipUtil.unZipToMemory(
                    assets["_buffer"],
                    name + "/"
                ).then((cfgs) => {
                    Logger.zz_log(`zhangzhen_GameController_loadConfig_${name}_decode success: ${Date.now()}`);
                    const data = {};
                    const decoder = new TextDecoder('utf-8');
                    for (const key in cfgs) {
                        data[key] = decoder.decode(cfgs[key]);
                    }
                    AppManager.instance().ResManager.setChromeCfg(data);
                    resolve(`${name}解码成功`);
                }).catch((error) => {
                    console.error(`${name}解码失败`, error);
                    reject(`${name}解码失败`);
                });
            });
        });
    }

}