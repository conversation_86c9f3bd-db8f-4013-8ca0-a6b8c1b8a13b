import * as cc from 'cc';
import { EDITOR, PREVIEW } from "cc/env";

import Logger from "../Framework/Logger";

const { ccclass, property } = cc._decorator;

/**
 * 自定义输入接口（和Unity的Input输入接口用法一样）
 */
export class Input {
    /**
     * 鼠标位置
     */
    public static mousePosition: cc.Vec2 = cc.Vec2.ZERO;

    /**
     * 遥感接口（键盘方向键或者wasd键）
     */
    public static axis: cc.Vec2 = cc.Vec2.ZERO;

    public static keyDic: { [key: number]: number } = {};

    public static mouseBtnDic: { [key: number]: number } = {};

    /**
     * 多点触屏数据
     */
    public static touches: TouchData[] = [];

    /*public static getAxis(axisName:string):cc.Vec2
    {
        return this.axis;
    }*/

    /**
     * 获得键盘按键按下状态
     */
    public static getKeyDown(keyCode: KeyCode): boolean {
        if (Input.keyDic[keyCode] == KeyStatus.keyDown) {
            return true;
        }

        return false;
    }

    /**
     * 获得键盘按键按住状态
     */
    public static getKey(keyCode: KeyCode): boolean {
        if (Input.keyDic[keyCode] == KeyStatus.keyDown || Input.keyDic[keyCode] == KeyStatus.press) {
            return true;
        }

        return false;
    }

    /**
     * 获得键盘按键按并释放状态
     */
    public static getKeyUp(keyCode: KeyCode): boolean {
        if (Input.keyDic[keyCode] == KeyStatus.keyUp) {
            return true;
        }

        return false;
    }

    /**
     * 获得鼠标按键按下状态，参数 0 代表左键，1代表右键，2代表中键
     */
    public static getMouseButtonDown(mouseBtnCode: KeyCode): boolean {
        if (Input.mouseBtnDic[mouseBtnCode] == KeyStatus.keyDown) {
            return true;
        }

        return false;
    }

    /**
     * 获得鼠标按键按住状态，参数 0 代表左键，1代表右键，2代表中键
     */
    public static getMouseButton(mouseBtnCode: KeyCode): boolean {
        if (Input.mouseBtnDic[mouseBtnCode] == KeyStatus.keyDown || Input.mouseBtnDic[mouseBtnCode] == KeyStatus.press) {
            return true;
        }

        return false;
    }

    /**
     * 获得鼠标按键按并释放状态，参数 0 代表左键，1代表右键，2代表中键
     */
    public static getMouseButtonUp(mouseBtnCode: KeyCode): boolean {
        if (Input.mouseBtnDic[mouseBtnCode] == KeyStatus.keyUp) {
            return true;
        }

        return false;
    }

    /**
     * 根据手指id获得触屏数据
     * @param fingerId 
     * @returns 
     */
    public static getTouch(fingerId: number): TouchData {
        for (var i = 0; i < this.touches.length; i++) {
            if (this.touches[i].fingerId == fingerId) {
                return this.touches[i];
            }
        }

        return null;
    }

}

/**
 * 输入管理器
 */
@ccclass
export default class InputManager extends cc.Component {

    private static _instance: InputManager;
    public static get instance(): InputManager {
        if (this._instance == null) {
            var node: cc.Node = new cc.Node("InputManager");
            cc.director.addPersistRootNode(node);
            node.addComponent(cc.UITransform);
            this._instance = node.addComponent(InputManager);
            this._instance.init();
        }
        return this._instance;
    }

    private discardKeyList: number[] = [];

    private mouseDiscardKeyList: number[] = [];

    private axisDir: cc.Vec2 = cc.Vec2.ZERO;

    private init() {

    }

    onLoad() {
        cc.input.on(cc.Input.EventType.KEY_DOWN, this.onKeyDown, this);
        cc.input.on(cc.Input.EventType.KEY_UP, this.onKeyUp, this);
    }

    start() {
        this.node.getComponent(cc.UITransform).width = cc.view.getDesignResolutionSize().width;
        this.node.getComponent(cc.UITransform).height = cc.view.getDesignResolutionSize().height;
        this.node.position.set(
            new cc.Vec3(
                cc.view.getDesignResolutionSize().width / 2,
                cc.view.getDesignResolutionSize().height / 2,
                10000
            )
        );

        /*this.node.on(cc.Node.EventType.MOUSE_DOWN,this.onMouseDown,this,false);
        this.node.on(cc.Node.EventType.MOUSE_UP,this.onMouseUp,this,false);
        this.node.on(cc.Node.EventType.MOUSE_MOVE,this.onMouseMove,this,false);
        this.node.on(cc.Node.EventType.MOUSE_LEAVE,this.onMouseLeave,this,false);*/

        this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this, false);
        this.node.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this, false);
        this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this, false);
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancle, this, false);
    }

    /**
     * 启动时间
     */
    public startup() {
        Logger.trace("InputManager 启动输入管理");
        //cc.log("keycode?? ",cc.macro.KEY.num0,cc.macro.KEY.num1,cc.macro.KEY.num3)
    }

    private onKeyDown(event: cc.EventKeyboard) {
        var keyCode: number = event.keyCode;

        if (event.keyCode >= 48 && event.keyCode <= 57) {
            keyCode += 48;
        }

        if (!Input.keyDic[keyCode]) {
            Input.keyDic[keyCode] = KeyStatus.keyDown;
        }

    }

    private onKeyUp(event: cc.EventKeyboard) {
        var keyCode: number = event.keyCode;

        if (event.keyCode >= 48 && event.keyCode <= 57) {
            keyCode += 48;
        }

        Input.keyDic[keyCode] = KeyStatus.keyUp;
    }

    private onMouseDown(event: cc.EventMouse) {
        if (event.getButton() == cc.EventMouse.BUTTON_LEFT) {
            Input.mouseBtnDic[MouseButtonCode.Left] = KeyStatus.keyDown;
        } else if (event.getButton() == cc.EventMouse.BUTTON_RIGHT) {
            Input.mouseBtnDic[MouseButtonCode.Right] = KeyStatus.keyDown;
        } else if (event.getButton() == cc.EventMouse.BUTTON_MIDDLE) {
            Input.mouseBtnDic[MouseButtonCode.Midden] = KeyStatus.keyDown;
        }
    }

    private onMouseUp(event: cc.EventMouse) {
        if (event.getButton() == cc.EventMouse.BUTTON_LEFT) {
            Input.mouseBtnDic[MouseButtonCode.Left] = KeyStatus.keyUp;
        } else if (event.getButton() == cc.EventMouse.BUTTON_RIGHT) {
            Input.mouseBtnDic[MouseButtonCode.Right] = KeyStatus.keyUp;
        } else if (event.getButton() == cc.EventMouse.BUTTON_MIDDLE) {
            Input.mouseBtnDic[MouseButtonCode.Midden] = KeyStatus.keyUp;
        }
    }

    private onMouseMove(event: cc.EventMouse) {
        Input.mousePosition = event.getLocation();
    }

    private onMouseLeave(event: cc.EventMouse) {
        Input.mousePosition = cc.Vec2.ZERO;

    }

    private onTouchStart(event: cc.EventTouch) {
        event.preventSwallow = true;
        var toucheList: cc.Touch[] = event.getTouches();

        var touchDatas: TouchData[] = [];

        for (var i = 0; i < toucheList.length; i++) {
            var touchData: TouchData = new TouchData();
            touchData.touch = toucheList[i];
            touchData.fingerId = i;
            touchData.phase = TouchPhase.Began;

            touchDatas.push(touchData);
        }

        Input.touches = touchDatas;
    }

    private onTouchMove(event: cc.EventTouch) {
        event.preventSwallow = true;
        var toucheList: cc.Touch[] = event.getTouches();

        var touchDatas: TouchData[] = [];

        for (var i = 0; i < toucheList.length; i++) {
            var touchData: TouchData = new TouchData();
            touchData.touch = toucheList[i];
            touchData.fingerId = i;
            touchData.phase = TouchPhase.Moved;

            touchDatas.push(touchData);
        }

        Input.touches = touchDatas;
    }

    private onTouchEnd(event: cc.EventTouch) {
        event.preventSwallow = true;
        for (var i = 0; i < Input.touches.length; i++) {
            var touchData: TouchData = Input.touches[i];
            touchData.phase = TouchPhase.Ended;
        }
    }

    private onTouchCancle(event: cc.EventTouch) {
        event.preventSwallow = true;
        for (var i = 0; i < Input.touches.length; i++) {
            var touchData: TouchData = Input.touches[i];
            touchData.phase = TouchPhase.Canceled;
        }
    }


    update(dt: number) {
        this.axisDir = cc.Vec2.ZERO;

        if (Input.getKey(KeyCode.LeftArrow) || Input.getKey(KeyCode.A)) {
            this.axisDir.x = -1;
        }

        if (Input.getKey(KeyCode.RightArrow) || Input.getKey(KeyCode.D)) {
            this.axisDir.x = 1;
        }

        if (Input.getKey(KeyCode.UpArrow) || Input.getKey(KeyCode.W)) {
            this.axisDir.y = 1;
        }

        if (Input.getKey(KeyCode.DownArrow) || Input.getKey(KeyCode.S)) {
            this.axisDir.y = -1;
        }

        Input.axis = this.axisDir;

    }

    lateUpdate() {
        //-------------------------处理键盘按键-----------------------------
        for (var i = 0; i < this.discardKeyList.length; i++) {
            delete Input.keyDic[this.discardKeyList[i]];
        }

        this.discardKeyList.length = 0;

        for (var keyCode in Input.keyDic) {
            if (Input.keyDic[keyCode] == KeyStatus.keyDown) {
                Input.keyDic[keyCode] = KeyStatus.press;
            }

            if (Input.keyDic[keyCode] == KeyStatus.keyUp) {
                Input.keyDic[keyCode] = KeyStatus.none;
                this.discardKeyList.push(Number(keyCode));
            }
        }


        //-------------------------处理鼠标按键-----------------------------
        for (var i = 0; i < this.mouseDiscardKeyList.length; i++) {
            delete Input.mouseBtnDic[this.mouseDiscardKeyList[i]];
        }

        this.mouseDiscardKeyList.length = 0;

        for (var mouseBtnCode in Input.mouseBtnDic) {
            if (Input.mouseBtnDic[mouseBtnCode] == KeyStatus.keyDown) {
                Input.mouseBtnDic[mouseBtnCode] = KeyStatus.press;
            }

            if (Input.mouseBtnDic[mouseBtnCode] == KeyStatus.keyUp) {
                Input.mouseBtnDic[mouseBtnCode] = KeyStatus.none;
                this.mouseDiscardKeyList.push(Number(mouseBtnCode));
            }
        }

        //-------------------------处理触屏-----------------------------

        for (var i = 0; i < Input.touches.length; i++) {
            var touchData: TouchData = Input.touches[i];

            if (touchData.phase == TouchPhase.Began || touchData.phase == TouchPhase.Moved) {
                touchData.phase = TouchPhase.Stationary;
            }

            if (touchData.phase == TouchPhase.Ended || touchData.phase == TouchPhase.Canceled) {
                Input.touches.splice(i, 1);
                i--;
            }
        }

    }

}

//if(!(cc.sys.platform == cc.sys.EDITOR_PAGE))
if (!EDITOR) {
    InputManager.instance.startup();//启动输入管理
}

export enum KeyStatus {
    none,
    keyDown,
    press,
    keyUp,
}

export enum KeyCode {
    None = 0,

    Space = 32,
    Enter = 13,
    Ctrl = 17,
    Alt = 18,
    Escape = 27,

    LeftArrow = 37,
    UpArrow = 38,
    RightArrow = 39,
    DownArrow = 40,

    A = 65,
    B,
    C,
    D,
    E,
    F,
    G,
    H,
    I,
    J,
    K,
    L,
    M,
    N,
    O,
    P,
    Q,
    R,
    S,
    T,
    U,
    V,
    W,
    X,
    Y,
    Z,

    F1 = 112,
    F2,
    F3,
    F4,
    F5,
    F6,
    F7,
    F8,
    F9,
    F10,
    F11,
    F12,

    Num0 = 96,
    Num1,
    Num2,
    Num3,
    Num4,
    Num5,
    Num6,
    Num7,
    Num8,
    Num9,

}

export enum MouseButtonCode {
    Left = 0,
    Right = 1,
    Midden = 2,
}

/**
 * 触摸数据
 */
export class TouchData {
    /**
     * 触摸信息
     */
    public touch: cc.Touch = null;

    /**
     * 触摸的手指Id
     */
    public fingerId: number = 0;

    /**
     * 触摸阶段
     */
    public phase: TouchPhase;
}

/**
 * 触摸阶段
 */
export enum TouchPhase {
    /**
     * 手指刚触摸屏幕时触发一次
     */
    Began = 0,

    /**
     * 手指在屏幕滑动时一直触发
     */
    Moved = 1,

    /**
     *手指长按屏幕时一直触发 
     */
    Stationary = 2,

    /**
     * 手指从屏幕移开时触发一次
     */
    Ended = 3,

    /**
     * 触摸被取消时触发
     */
    Canceled = 4
}

