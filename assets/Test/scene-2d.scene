[{"__type__": "cc.SceneAsset", "_name": "scene-2d", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "scene-2d", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [], "_prefab": {"__id__": 64}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 73}, "_id": "8f0849be-2376-40c1-8cda-19faa3b02a1e"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 6}, {"__id__": 19}, {"__id__": 50}], "_active": true, "_components": [{"__id__": 61}, {"__id__": 62}, {"__id__": 63}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 384, "y": 683, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "beI88Z2HpFELqR4T5EMHpg"}, {"__type__": "cc.Node", "_name": "Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 5}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 2, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ebFwiq8gBFaYpqYbdoDODe"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 0, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 683, "_near": 0, "_far": 100, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 7, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1108360256, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "63WIch3o5BEYRlXzTT0oWc"}, {"__type__": "c5ef3VxQwRGLqCooT5NiTu4", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": null, "camera": {"__id__": 4}, "_id": "67ggRTH6FF2qcdnV4qAfGr"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 7}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 6}, "asset": {"__uuid__": "45437f0e-f687-4d65-84b8-56ffa840ba77", "__expectedType__": "cc.Prefab"}, "fileId": "fe77wQkCNFFJgI/jMULvdE", "instance": {"__id__": 8}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "05N6SIwClGo7OMkUuRFcJX", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 9}, {"__id__": 11}, {"__id__": 13}, {"__id__": 15}, {"__id__": 17}, {"__id__": 22}, {"__id__": 24}, {"__id__": 26}, {"__id__": 28}, {"__id__": 30}, {"__id__": 32}, {"__id__": 34}, {"__id__": 36}, {"__id__": 38}, {"__id__": 40}, {"__id__": 42}, {"__id__": 44}, {"__id__": 46}, {"__id__": 48}, {"__id__": 49}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 10}, "propertyPath": ["_name"], "value": "TestUI"}, {"__type__": "cc.TargetInfo", "localID": ["fe77wQkCNFFJgI/jMULvdE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 12}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["fe77wQkCNFFJgI/jMULvdE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 14}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["fe77wQkCNFFJgI/jMULvdE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 16}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["fe77wQkCNFFJgI/jMULvdE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 18}, "propertyPath": ["mapRoot"], "value": {"__id__": 19}}, {"__type__": "cc.TargetInfo", "localID": ["95Xh2D5GZAxbZHEKssDY1b"]}, {"__type__": "cc.Node", "_name": "MapRoot", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 20}, {"__id__": 21}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "a0PtD4SJ1E5q55Y0HW/YP+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 36000, "height": 21600}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "5eZFeBYG9C1rcppRnNsTFi"}, {"__type__": "19ea8wVfPtN/KVtV9dezRM8", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": null, "_id": "6dlwUZ/zBJcpbVRbaUxKhy"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 23}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 750, "height": 1366}}, {"__type__": "cc.TargetInfo", "localID": ["cfTyZwRaFAm4LnB9b6GdS1"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 25}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 750, "height": 1366}}, {"__type__": "cc.TargetInfo", "localID": ["0dBXc9DFZHzIGowxv3OrN+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 27}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 702, "height": 966}}, {"__type__": "cc.TargetInfo", "localID": ["6c5Ts4o+5DmKOZjSwjoU03"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 29}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 635, "height": 782}}, {"__type__": "cc.TargetInfo", "localID": ["05KMoI2f5KQ6FfUf7ko4CV"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 31}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 640, "height": 845}}, {"__type__": "cc.TargetInfo", "localID": ["eemuNZdMlAcJrvbMvvQluw"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 33}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["fe77wQkCNFFJgI/jMULvdE"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 35}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["d7ncO3CTtAeoWIwRD+oNQ3"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 37}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["43RPbLtDBA75gJqgIdK5jN"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 39}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["afaSEi4u5A7bFdgeJ2yPkp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 41}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["32MGle8ZNG96dBAbqeGauX"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 43}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["ceQzqm8HNEVpIIW2NkR3Cd"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 45}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["57oeQ35D1BU5DlsD+jL+wb"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 47}, "propertyPath": ["_layer"], "value": 33554432}, {"__type__": "cc.TargetInfo", "localID": ["80PjcRL+JMFbzhQ8iZZf5C"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 33}, "propertyPath": ["_active"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 39}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.Node", "_name": "GameController", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 51}], "_active": true, "_components": [{"__id__": 59}, {"__id__": 60}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ebpWRWvYlLJYnv03xmPw2X"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 50}, "_prefab": {"__id__": 52}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 51}, "asset": {"__uuid__": "22c37503-0905-4ae7-8329-b1c5cdf525ea", "__expectedType__": "cc.Prefab"}, "fileId": "4bzJDLNfRAzLOS5/JQ4JaV", "instance": {"__id__": 53}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "66xZFoYtlIbIoJAym/fiIL", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 54}, {"__id__": 56}, {"__id__": 57}, {"__id__": 58}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 55}, "propertyPath": ["_name"], "value": "joystick"}, {"__type__": "cc.TargetInfo", "localID": ["4bzJDLNfRAzLOS5/JQ4JaV"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 55}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 55}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 55}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 50}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "dfZ29tzCtJlrOjsTMyZb1j"}, {"__type__": "322eaNvZw5L+IraxkjyAZ6/", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 50}, "_enabled": true, "__prefab": null, "joyStick": null, "touchNode": null, "_id": "adbK8PLo1Aj5/TsTuuWiSI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 768, "height": 1366}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d6rUX5yfhMlKoWX2bSbawx"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 4}, "_alignCanvasWithScreen": true, "_id": "12O/ljcVlEqLmVm3U2gEOQ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 5.684341886080802e-14, "_bottom": 5.684341886080802e-14, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "c5V1EV8IpMtrIvY1OE9t2u"}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "8f0849be-2376-40c1-8cda-19faa3b02a1e", "instance": null, "targetOverrides": [{"__id__": 65}, {"__id__": 68}, {"__id__": 71}], "nestedPrefabInstanceRoots": [{"__id__": 6}, {"__id__": 51}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 6}, "sourceInfo": {"__id__": 66}, "propertyPath": ["closeBtn"], "target": {"__id__": 6}, "targetInfo": {"__id__": 67}}, {"__type__": "cc.TargetInfo", "localID": ["95Xh2D5GZAxbZHEKssDY1b"]}, {"__type__": "cc.TargetInfo", "localID": ["b3R0fBMe5Lm6sv1QqJhFiM"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 6}, "sourceInfo": {"__id__": 69}, "propertyPath": ["container"], "target": {"__id__": 6}, "targetInfo": {"__id__": 70}}, {"__type__": "cc.TargetInfo", "localID": ["95Xh2D5GZAxbZHEKssDY1b"]}, {"__type__": "cc.TargetInfo", "localID": ["80PjcRL+JMFbzhQ8iZZf5C"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 60}, "sourceInfo": null, "propertyPath": ["joyStick"], "target": {"__id__": 51}, "targetInfo": {"__id__": 72}}, {"__type__": "cc.TargetInfo", "localID": ["75+kqAUIVPVbaGyxwgaHRT"]}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 74}, "shadows": {"__id__": 75}, "_skybox": {"__id__": 76}, "fog": {"__id__": 77}, "octree": {"__id__": 78}, "skin": {"__id__": 79}, "lightProbeInfo": {"__id__": 80}, "postSettings": {"__id__": 81}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 1}, "_skyIllumLDR": 20000, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_planeBias": 1, "_shadowColor": {"__type__": "cc.Color", "r": 76, "g": 76, "b": 76, "a": 255}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 512, "y": 512}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.SkinInfo", "_enabled": false, "_blurRadius": 0.01, "_sssIntensity": 3}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null, "_lightProbeSphereVolume": 1}, {"__type__": "cc.PostSettingsInfo", "_toneMappingType": 0}]