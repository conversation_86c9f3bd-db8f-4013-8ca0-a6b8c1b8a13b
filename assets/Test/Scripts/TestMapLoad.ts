import * as cc from "cc";

import MapManager from "../../Scripts/Game/Module/MapInstance/Managers/MapManager";
import UIButton from "../../Scripts/Game/Components/UIButton";

const { ccclass, property } = cc._decorator;
@ccclass
export class TestMapLoad extends cc.Component {
    @property(UIButton)
    btn: UIButton = null;

    @property(cc.Label)
    label: cc.Label = null;

    private _callBack:Function;

    private _target: any;

    private _data: object;

    private _loaded: boolean = false;

    protected onLoad(): void {
        this.btn.setOnClick(async () => {
            if (this._data) {
                MapManager.enter(this._data["sceneId"], this._data["zoneId"]);
                // MapManager.showMap(new TestMap());
            } else {
                console.error("未找到对应的区域数据");
            }
            if(this._callBack) {
                this._callBack.apply(this._target);
            }
        }, this);
        this._loaded = true;
        this.updateUI();
    }

    public init(data: object, callBack:Function, target:any) {
        this._data = data;
        this._target = target;
        this._callBack = callBack;

        this.updateUI();
    }
    
    private updateUI() {
        if(this._loaded && this._data) {
            this.label.string = this._data["name"];
        }
    }
}