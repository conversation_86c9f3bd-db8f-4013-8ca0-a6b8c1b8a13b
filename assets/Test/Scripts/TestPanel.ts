import * as cc from "cc";

import BaseView from "../../Scripts/Game/Components/BaseView";
import UIButton from "../../Scripts/Game/Components/UIButton";
import { TestMapLoad } from "./TestMapLoad";
import ViewManager from "../../Scripts/Game/GlobalComponent/ViewManagerComponent";
import GameController from "../../Scripts/Game/Control/GameController";
import MapManager from "../../Scripts/Game/Module/MapInstance/Managers/MapManager";
import { Loader, LoadInfo } from "../../Scripts/Game/Module/Loading/Loader";
import { LoadUtils } from "../../Scripts/Game/Utils/LoadUtils";
import { Bundles } from "../../Scripts/Game/Module/Bundles";

const { ccclass, property } = cc._decorator;

// class BagItem {
//     type = 1;// 1道具 2装备
//     itemId = 0; // 道具id
//     url = ""; // 图片
//     num = 1;
// }

@ccclass
export default class TestPanel extends BaseView {

    @property(UIButton)
    closeBtn: UIButton = null;

    @property(cc.Node)
    container: cc.Node = null;

    @property(cc.Prefab)
    testItemPrefab: cc.Prefab = null;

    @property(cc.Node)
    mapRoot: cc.Node = null;

    dataList: Array<object> = [
        { "name": "洛水平原", "sceneId": 1001, "zoneId": 1 },
        { "name": "沙漠副本第1层", "sceneId": 1001, "zoneId": 1 },
        { "name": "沙漠副本第2层", "sceneId": 1001, "zoneId": 2 },
        { "name": "沙漠副本第3层", "sceneId": 1001, "zoneId": 3 },
        { "name": "沙漠副本第4层", "sceneId": 1001, "zoneId": 4 },
    ]

    onLoad() {
        const loader = new Loader();
        const bundles: LoadInfo[] = [
            //加载配置表.
            {
                bundleName: "res", options: {}, res: [
                    //配置
                    { "url": "config/gamecfg", "type": cc.BufferAsset },
                    { "url": "config/map", "type": cc.BufferAsset },
                    //界面通用
                    { "url": "ui/common", "dir": true, "loadType": true },
                    // 主界面
                    // { "url": "ui/mainUI/mainUI", "type": cc.Prefab },
                    // //战斗相关
                    // { "url": "ui/fight", "dir": true, "loadType": true },
                    //李逍遥
                    { "url": "mapUnit/model/1010004", "dir": true, "loadType": true },
                    // //主game
                    // { "url": "game", "type": cc.SceneAsset },
                    //主场景img
                    // { "url": "map/img_2000001", "dir": true, "loadType": true },
                    //主场景tmx
                    { "url": "map/tmx_2000001/2000001", "type": cc.TiledMapAsset },
                    // //转场动画1
                    // { "url": "ui/map/MapLoading_zong", "type": cc.Prefab },
                    // //转场动画2
                    // { "url": "ui/map/LoadingHeimu1", "type": cc.Prefab },
                ]
            },
        ];

        const preloadPrefabs = () => {
            const prefabPaths: string[] = [
                "mapUnit/model/1010004/1010004",
            ]
            return new Promise<void>((resolve) => {
                LoadUtils.loadPrefabArray(prefabPaths, (prefab: cc.Prefab) => {
                    console.log("Preloaded prefab:", prefab);
                    resolve();
                });
            });
        }

        loader.load(bundles, (percent) => {
        }, async () => {
            console.log("All resources loaded successfully.");

            await preloadPrefabs();
            GameController.instance().start();
        }, this);


        this.closeBtn.setOnClick(() => {
            ViewManager.instance().close(this);
        }, this)


        MapManager.instance.rootNode = this.mapRoot;
    }

    onDestroy() {
    }

    start() {
        let layout = this.container.getComponent(cc.Layout);
        layout.enabled = true;

        for (let i = 0; i < this.dataList.length; i++) {
            let itemNode = cc.instantiate(this.testItemPrefab);
            let bagItem = itemNode.getComponent(TestMapLoad);
            bagItem.init(this.dataList[i], this.closeTestPanel, this);
            this.container.addChild(itemNode);
        }
    }

    private closeTestPanel() {
        this.node.removeFromParent();
        this.node.destroy();
        // ViewManager.instance().close(this);
    }
}
